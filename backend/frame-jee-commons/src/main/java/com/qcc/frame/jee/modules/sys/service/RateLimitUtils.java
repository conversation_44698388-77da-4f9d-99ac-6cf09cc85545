package com.qcc.frame.jee.modules.sys.service;

import com.alibaba.fastjson.JSON;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.utils.WebContextHolder;
import com.qcc.frame.jee.modules.sys.model.LimitConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 通用限频限流服务
 * @date 2025-06-11
 * <AUTHOR>
 */
@Service
public class RateLimitUtils {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    
    @Autowired
    private RedisServUtils redisServUtils;
    
    // 统一的限流key前缀
    private static final String LIMIT_PREFIX = "rate_limit";
    private static final String BLOCK_SUFFIX = "blocked";
    private static final String DAILY_SUFFIX = "daily";
    private static final String WINDOW_SUFFIX = "window";

    /**
     * 组合检查：每日限制 + 滑动窗口限制
     * @param config 限制配置
     * @return true-超过限制，false-未超过限制
     */
    public boolean checkCombinedLimits(LimitConfig config) {
        if (config == null || StringUtils.isBlank(config.getCheckKey()) || StringUtils.isBlank(config.getLimitKey())) {
            logger.error("checkCombinedLimits config param error: {}", config == null ? null : JSON.toJSONString(config));
            return false;
        }

        try {
            long currentTime = System.currentTimeMillis() / 1000;

            // 检查是否已被限制
            if (isBlocked(config.getLimitKey(), config.getCheckKey())) {
                logger.warn("{} 已被限制访问", config.getCheckKey());
                return true;
            }

            // 检查每日限制
            if (config.getDailyLimit() > 0) {
                if (checkDailyLimit(config.getLimitKey(), config.getCheckKey(), config.getDailyLimit())) {
                    // 每日限制的，直接锁到次日
                    blockTemporarily(config.getLimitKey(), config.getCheckKey(), getSecondsUntilMidnight());
                    return true;
                }
            }

            // 检查滑动窗口限制
            if (config.getWindowLimits() != null && !config.getWindowLimits().isEmpty()) {
                for (LimitConfig.WindowLimit windowLimit : config.getWindowLimits()) {
                    if (checkSlidingWindow(config.getLimitKey(), windowLimit.getWindowName(), config.getCheckKey(),
                            currentTime, windowLimit.getWindowSeconds(), windowLimit.getMaxCount())) {
                        // 可选择是否自动block
                        if (config.isAutoBlockOnWindowLimit()) {
                            blockTemporarily(config.getLimitKey(), config.getCheckKey(), config.getBlockSeconds());
                        }
                        return true;
                    }
                }
            }

            // 记录访问
            if (config.isRecordAccess()) {
                recordDailyAccess(config.getLimitKey(), config.getCheckKey());
            }

            return false;
        } catch (Exception e) {
            logger.error("组合限制检查失败: checkKey={}", config.getCheckKey(), e);
            return false;
        }
    }

    /**
     * 检查指定key是否被限制访问
     * @param limitKey 限流key
     * @param checkKey 校验key
     * @return true-被限制，false-未被限制
     */
    public boolean isBlocked(String limitKey, String checkKey) {
        if (StringUtils.isBlank(checkKey)) {
            return false;
        }

        try {
            String blockKey = buildBlockKey(limitKey);
            String blockedFlag = redisServUtils.getStringById(blockKey, checkKey);
            return StringUtils.isNotBlank(blockedFlag);
        } catch (Exception e) {
            logger.error("检查限制状态失败: limitKey={}, checkKey={}", limitKey, checkKey, e);
            return false;
        }
    }


    /**
     * 临时限制指定key访问
     * @param limitKey 限流key
     * @param checkKey 校验key
     * @param seconds 限制时间(秒)
     */
    public void blockTemporarily(String limitKey, String checkKey, long seconds) {
        if (StringUtils.isBlank(checkKey)) {
            return;
        }
        
        try {
            String blockKey = buildBlockKey(limitKey);
            redisServUtils.saveString(blockKey, checkKey, "blocked", (int) seconds);
            logger.warn("{} 已被限制访问 {}小时, {}分钟, {}秒", checkKey, seconds / 3600, (seconds % 3600) / 60, seconds % 60);
        } catch (Exception e) {
            logger.error("设置限制失败: limitKey={}, checkKey={}", limitKey, checkKey, e);
        }
    }

    /**
     * 移除指定key的访问限制
     * @param limitKey 限流key
     * @param checkKey 校验key
     */
    public void removeBlock(String limitKey, String checkKey) {
        if (StringUtils.isBlank(checkKey)) {
            return;
        }
        
        try {
            String blockKey = buildBlockKey(limitKey);
            redisServUtils.deleteById(blockKey, checkKey);
            logger.info("移除限制: limitKey={}, checkKey={}", limitKey, checkKey);
        } catch (Exception e) {
            logger.error("移除限制失败: limitKey={}, checkKey={}", limitKey, checkKey, e);
        }
    }

    /**
     * 检查每日访问限制
     * @param limitKey 限流key
     * @param checkKey 校验key
     * @param maxCount 每日最大访问次数
     * @return true-超过限制，false-未超过限制
     */
    public boolean checkDailyLimit(String limitKey, String checkKey, int maxCount) {
        if (StringUtils.isBlank(checkKey)) {
            return false;
        }
        
        try {
            String dailyKey = buildDailyKey(limitKey);
            String dateKey = generateDateKey(checkKey);
            long count = redisServUtils.getCount(dailyKey, dateKey);
            
            if (count >= maxCount) {
                logger.warn("{} 每日访问次数超限: {}/{}", checkKey, count, maxCount);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("检查每日限制失败: limitKey={}, checkKey={}", limitKey, checkKey, e);
            return false;
        }
    }

    /**
     * 记录每日访问
     * @param limitKey 限流key
     * @param checkKey 校验key
     */
    public void recordDailyAccess(String limitKey, String checkKey) {
        if (StringUtils.isBlank(checkKey)) {
            return;
        }
        
        try {
            String dailyKey = buildDailyKey(limitKey);
            String dateKey = generateDateKey(checkKey);
            long secondsUntilMidnight = getSecondsUntilMidnight();
            
            // 增加一些随机时间，避免大量key同时过期
            long expireTime = redisServUtils.addRandomExpireTime(secondsUntilMidnight, 300, 1800);
            
            redisServUtils.incrAndSetExpire(dailyKey, dateKey, expireTime);
            
        } catch (Exception e) {
            logger.error("记录访问失败: limitKey={}, checkKey={}", limitKey, checkKey, e);
        }
    }

    /**
     * 获取每日访问次数
     * @param limitKey 限流key
     * @param checkKey 校验key
     * @return 当日访问次数
     */
    public long getDailyAccessCount(String limitKey, String checkKey) {
        if (StringUtils.isBlank(checkKey)) {
            return 0;
        }
        
        try {
            String dailyKey = buildDailyKey(limitKey);
            String dateKey = generateDateKey(checkKey);
            return redisServUtils.getCount(dailyKey, dateKey);
        } catch (Exception e) {
            logger.error("获取每日访问次数失败: limitKey={}, checkKey={}", limitKey, checkKey, e);
            return 0;
        }
    }

    /**
     * 检查滑动窗口内的访问次数
     * @param limitKey 限流key
     * @param windowName 窗口名称
     * @param checkKey 校验key
     * @param currentTime 当前时间戳(秒)
     * @param windowSeconds 窗口时间长度（秒）
     * @param maxCount 最大允许次数
     * @return true-超过限制，false-未超过限制
     */
    public boolean checkSlidingWindow(String limitKey, String windowName, String checkKey, long currentTime, long windowSeconds, int maxCount) {
        if (StringUtils.isBlank(checkKey)) {
            return false;
        }
        
        try {
            long windowStart = currentTime - windowSeconds;
            long windowEnd = currentTime;
            
            String windowKey = buildWindowKey(limitKey, windowName);
            String key = windowKey + ":" + checkKey;
            String reqId = generateReqId();

            // 1. 先清理过期数据
            redisServUtils.cleanupExpiredLimitData(key, 0, windowStart - 1);

            // 2. 检查当前窗口内的访问次数
            boolean isLimited = redisServUtils.addLimitApiByNoScript(key, reqId, maxCount, currentTime, windowStart, windowEnd);

            if (isLimited) {
                // 如果超限，删除当前请求记录
                redisServUtils.deleteLimitApiForNoScript(key, reqId);
            } else {
                // 3. 为sorted set设置过期时间，防止key永久存在
                redisServUtils.expireLimitApi(key, (int) (windowSeconds * 2));
            }

            if (isLimited) {
                logger.warn("{} 在{}窗口内访问次数超限: 超过{}", checkKey, windowName, maxCount);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("检查滑动窗口失败: limitKey={}, windowName={}, checkKey={}", limitKey, windowName, checkKey, e);
            return false;
        }
    }


    /**
     * 实时检查滑动窗口内的访问次数（不记录当前请求）
     * @param limitKey 限流key
     * @param windowName 窗口名称
     * @param checkKey 校验key
     * @param currentTime 当前时间戳(秒)
     * @param windowSeconds 窗口时间长度（秒）
     * @param maxCount 最大允许次数
     * @return true-超过限制，false-未超过限制
     */
    public boolean checkSlidingWindowRealtime(String limitKey, String windowName, String checkKey, long currentTime, long windowSeconds, int maxCount) {
        if (StringUtils.isBlank(checkKey)) {
            return false;
        }
        
        try {
            long windowStart = currentTime - windowSeconds;
            long windowEnd = currentTime;
            
            String windowKey = buildWindowKey(limitKey, windowName);
            String key = "api_lmt:" + windowKey + ":" + checkKey;
            
            // 先清理过期数据
            redisServUtils.cleanupExpiredLimitData(key, 0, windowStart - 1);
            
            // 检查当前窗口内的访问次数，但不添加新记录
            Long count = redisServUtils.getCountInScoreRange("api_lmt:" + windowKey, checkKey, windowStart, windowEnd);
            boolean isLimited = count != null && count >= maxCount;
            
            if (isLimited) {
                logger.warn("{} 在{}窗口内访问次数超限(实时检查): {}/{}", checkKey, windowName, count, maxCount);
            }
            
            return isLimited;
        } catch (Exception e) {
            logger.error("实时检查滑动窗口失败: limitKey={}, windowName={}, checkKey={}", limitKey, windowName, checkKey, e);
            return false;
        }
    }

    /**
     * 记录滑动窗口访问（不检查限制）
     * @param limitKey 限流key
     * @param windowName 窗口名称
     * @param checkKey 校验key
     * @param currentTime 当前时间戳(秒)
     * @param windowSeconds 窗口时间长度（秒）
     */
    public void recordSlidingWindowAccess(String limitKey, String windowName, String checkKey, long currentTime, long windowSeconds) {
        if (StringUtils.isBlank(checkKey)) {
            return;
        }
        
        try {
            String windowKey = buildWindowKey(limitKey, windowName);
            String key = windowKey + ":" + checkKey;
            String reqId = generateReqId();
            
            // 仅记录访问，不检查限制
            addSlidingWindowRecord(key, reqId, currentTime, windowSeconds);
            
        } catch (Exception e) {
            logger.error("记录滑动窗口访问失败: limitKey={}, windowName={}, checkKey={}", limitKey, windowName, checkKey, e);
        }
    }



    // ============== 私有方法 ==============

    /**
     * 获取到次日0点的秒数
     * @return 到次日0点的秒数
     */
    private long getSecondsUntilMidnight() {
        Calendar now = Calendar.getInstance();

        Calendar midnight = Calendar.getInstance();
        midnight.setTime(now.getTime());
        midnight.add(Calendar.DAY_OF_MONTH, 1);
        midnight.set(Calendar.HOUR_OF_DAY, 0);
        midnight.set(Calendar.MINUTE, 0);
        midnight.set(Calendar.SECOND, 0);
        midnight.set(Calendar.MILLISECOND, 0);

        return (midnight.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    /**
     * 构建block key
     */
    private String buildBlockKey(String limitKey) {
        return LIMIT_PREFIX + ":" + limitKey + ":" + BLOCK_SUFFIX;
    }

    /**
     * 构建daily key
     */
    private String buildDailyKey(String limitKey) {
        return LIMIT_PREFIX + ":" + limitKey + ":" + DAILY_SUFFIX;
    }

    /**
     * 构建window key
     */
    private String buildWindowKey(String limitKey, String windowName) {
        return LIMIT_PREFIX + ":" + limitKey + ":" + WINDOW_SUFFIX + ":" + windowName;
    }

    /**
     * 生成日期key
     */
    private String generateDateKey(String checkKey) {
        String formatDate = DateUtils.formatDate(new Date(), DateUtils.DATE_FORMAT_YMD);
        return checkKey + ":" + formatDate;
    }


    /**
     * 仅记录滑动窗口访问
     */
    private void addSlidingWindowRecord(String key, String reqId, long currentTime, long windowSeconds) {
        try {
            redisServUtils.addToSortedSet("api_lmt:" + key, "", currentTime, reqId);
            redisServUtils.expireLimitApi(key, (int) (windowSeconds * 2));
            
        } catch (Exception e) {
            logger.error("记录滑动窗口访问失败: " + key, e);
        }
    }

    private static String generateReqId() {
        String reqId;
        if (Objects.nonNull(WebContextHolder.getRequest()) &&
                StringUtils.isNotBlank(WebContextHolder.getRequest().getRequestUUID())) {
            reqId = WebContextHolder.getRequest().getRequestUUID();
        } else {
            reqId =  IdGenUtil.uuid();
        }
        return reqId;
    }
} 