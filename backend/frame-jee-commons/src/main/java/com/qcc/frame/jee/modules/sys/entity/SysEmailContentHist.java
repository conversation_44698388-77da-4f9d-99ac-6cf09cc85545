package com.qcc.frame.jee.modules.sys.entity;

import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.util.Date;

/**
 * KNZT-254
 */
public class SysEmailContentHist extends DataEntity<SysEmailContentHist> {
	
	private String emailTo;
	private String emailCc;
	private String emailBcc;
	private String emailSubject;
	private String emailContent;
	private Date sendDate;
	private Short status;
	private String errorText;
	private byte[] attachment1;
	private String attachment1FileName;
	private byte[] attachment2;
	private String attachment2FileName;
	private short actualFileQty = 0;

	public byte[] getAttachment1() {
		return attachment1;
	}
	public void setAttachment1(byte[] attachment1) {
		this.attachment1 = attachment1;
	}
	public String getAttachment1FileName() {
		return attachment1FileName;
	}
	public void setAttachment1FileName(String attachment1FileName) {
		this.attachment1FileName = attachment1FileName;
	}
	public byte[] getAttachment2() {
		return attachment2;
	}
	public void setAttachment2(byte[] attachment2) {
		this.attachment2 = attachment2;
	}
	public String getAttachment2FileName() {
		return attachment2FileName;
	}
	public void setAttachment2FileName(String attachment2FileName) {
		this.attachment2FileName = attachment2FileName;
	}
	public short getActualFileQty() {
		return actualFileQty;
	}
	public void setActualFileQty(short actualFileQty) {
		this.actualFileQty = actualFileQty;
	}
	public String getErrorText() {
		return errorText;
	}
	public void setErrorText(String errorText) {
		this.errorText = errorText;
	}
	public String getEmailTo() {
		return emailTo;
	}
	public void setEmailTo(String emailTo) {
		this.emailTo = emailTo;
	}
	public String getEmailCc() {
		return emailCc;
	}
	public void setEmailCc(String emailCc) {
		this.emailCc = emailCc;
	}
	public String getEmailBcc() {
		return emailBcc;
	}
	public void setEmailBcc(String emailBcc) {
		this.emailBcc = emailBcc;
	}
	public String getEmailSubject() {
		return emailSubject;
	}
	public void setEmailSubject(String emailSubject) {
		this.emailSubject = emailSubject;
	}
	public String getEmailContent() {
		return emailContent;
	}
	public void setEmailContent(String emailContent) {
		this.emailContent = emailContent;
	}
	public Date getSendDate() {
		return sendDate;
	}
	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}
	public Short getStatus() {
		return status;
	}
	public void setStatus(Short status) {
		this.status = status;
	}
}
