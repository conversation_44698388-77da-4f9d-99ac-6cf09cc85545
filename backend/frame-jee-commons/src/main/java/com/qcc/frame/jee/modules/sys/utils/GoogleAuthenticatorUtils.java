package com.qcc.frame.jee.modules.sys.utils;

import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.apache.commons.codec.binary.Base32;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;


/**
 * added for v2.0.4 chenbl KNZT-5371
 * Google Authenticator 工具类
 */
public class GoogleAuthenticatorUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleAuthenticatorUtils.class);

    private static final int CODE_DIGITS = 6;
    private static final String ALGORITHM = "HmacSHA1";

    /**
     * 创建一个密钥
     */
    public static String generateSecretKey() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[20];
        random.nextBytes(bytes);
        return new Base32().encodeToString(bytes).toLowerCase();
    }

    /**
     * 生成绑定二维码的URL
     *
     * @param username
     * @param secretKey
     * @return
     */
    public static String generateQrUrl(String username, String secretKey) {
        return String.format("otpauth://totp/QCCKYC(%s)?secret=%s", username, secretKey);
    }

    public static boolean checkCode(String secret, String code) throws MessageException {
        return checkCode(secret, code, System.currentTimeMillis(), 1);
    }

    public static boolean checkCode(String secret, String code, int window) throws MessageException {
        return checkCode(secret, code, System.currentTimeMillis(), window);
    }

    /**
     * This method implements the algorithm specified in RFC 6238 to check if a
     * validation code is valid in a given instant of time for the given secret
     * key.
     *
     * @param secret    the Base32 encoded secret key.
     * @param code      the code to validate.
     * @param timestamp the instant of time to use during the validation process.
     * @param window    the window size to use during the validation process.
     * @return <code>true</code> if the validation code is valid,
     * <code>false</code> otherwise.
     */
    public static boolean checkCode(String secret, String code, long timestamp, int window) throws MessageException {

        // convert unix time into a 30 second "window" as specified by the
        // TOTP specification. Using Google's default interval of 30 seconds.
        final long timeWindow = getTimeWindowFromTime(timestamp);

        // Calculating the verification code of the given key in each of the
        // time intervals and returning true if the provided code is equal to
        // one of them.
        for (int i = -((window - 1) / 2); i <= window / 2; ++i) {
            // Calculating the verification code for the current time interval.
            String hash = calculateCode(secret, timeWindow + i);

            // Checking if the provided code is equal to the calculated one.
            if (StringUtils.equalsAny(hash, code)) {
                // The verification code is valid.
                return true;
            }
        }

        // The verification code is invalid.
        return false;
    }

    private static long getTimeWindowFromTime(long time) {
        return time / (30 * 1000);
    }

    private static String calculateCode(String secretKey, long tm) throws MessageException {
        return calculateCode(secretKey, tm, ALGORITHM, CODE_DIGITS);
    }

    /**
     * 算法参考：https://github.com/wstrange/GoogleAuth com.warrenstrange.googleauth.GoogleAuthenticator#calculateCode(byte[], long)
     * Calculates the verification code of the provided key at the specified
     * instant of time using the algorithm specified in RFC 6238.
     *
     * @param secretKey  the secret key.
     * @param tm         the instant of time. 是timeWindow
     * @param algorithm  algorithm.
     * @param codeDigits number of digits to return.
     * @return the validation code for the provided key at the specified instant
     * of time.
     */
    private static String calculateCode(String secretKey, long tm, String algorithm, int codeDigits) throws MessageException {
        byte[] secretKeyBytes = decodeSecret(secretKey);
        // Allocating an array of bytes to represent the specified instant
        // of time.
        byte[] data = new byte[8];
        long value = tm;

        // Converting the instant of time from the long representation to a
        // big-endian array of bytes (RFC4226, 5.2. Description).
        for (int i = 8; i-- > 0; value >>>= 8) {
            data[i] = (byte) value;
        }

        // Building the secret key specification for the HmacSHA1 algorithm.
        SecretKeySpec signKey = new SecretKeySpec(secretKeyBytes, algorithm);

        try {
            // Getting an HmacSHA1/HmacSHA256 algorithm implementation from the JCE.
            Mac mac = Mac.getInstance(algorithm);

            // Initializing the MAC algorithm.
            mac.init(signKey);

            // Processing the instant of time and getting the encrypted data.
            byte[] hash = mac.doFinal(data);

            // Building the validation code performing dynamic truncation
            // (RFC4226, 5.3. Generating an HOTP value)
            int offset = hash[hash.length - 1] & 0xF;

            // We are using a long because Java hasn't got an unsigned integer type
            // and we need 32 unsigned bits).
            long truncatedHash = 0;

            for (int i = 0; i < 4; ++i) {
                truncatedHash <<= 8;

                // Java bytes are signed but we need an unsigned integer:
                // cleaning off all but the LSB.
                truncatedHash |= (hash[offset + i] & 0xFF);
            }

            // Clean bits higher than the 32nd (inclusive) and calculate the
            // module with the maximum validation code value.
            truncatedHash &= 0x7FFFFFFF;
            truncatedHash %= ((int) Math.pow(10, codeDigits));

            // Returning the validation code to the caller.
            return StringUtils.leftPad(Long.toString(truncatedHash), codeDigits, '0');
        } catch (NoSuchAlgorithmException | InvalidKeyException ex) {
            // We're not disclosing internal error details to our clients.
            LOGGER.error("生成MFA验证码失败", ex);
            throw new MessageException("err.access");
        }
    }

    private static byte[] decodeSecret(String secret) {
        // Decoding the secret key to get its raw byte representation.
        Base32 codec32 = new Base32();
        // See: https://issues.apache.org/jira/browse/CODEC-234
        // Commons Codec Base32::decode does not support lowercase letters.
        return codec32.decode(secret.toUpperCase());
    }
}