package com.qcc.frame.jee.modules.sys.model.saas;

import com.qcc.frame.jee.modules.sys.entity.MenuHome;

import java.util.ArrayList;
import java.util.List;
public class SaasMenuHome implements java.io.Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3629896646009180193L;
	private List<MenuHome> headerList = new ArrayList<>();
	private List<MenuHome> moduleHomeList = new ArrayList<>();
	private List<MenuHome> advancedAppList = new ArrayList<>();
	private List<SaasMenuTO> menuList;

	public List<SaasMenuTO> getMenuList() {
		return menuList;
	}

	public void setMenuList(List<SaasMenuTO> menuList) {
		this.menuList = menuList;
	}

	public List<MenuHome> getHeaderList() {
		return headerList;
	}

	public void setHeaderList(List<MenuHome> headerList) {
		this.headerList = headerList;
	}

	public List<MenuHome> getModuleHomeList() {
		return moduleHomeList;
	}

	public void setModuleHomeList(List<MenuHome> moduleHomeList) {
		this.moduleHomeList = moduleHomeList;
	}

	public List<MenuHome> getAdvancedAppList() {
		return advancedAppList;
	}

	public void setAdvancedAppList(List<MenuHome> advancedAppList) {
		this.advancedAppList = advancedAppList;
	}


	
}
