package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.modules.sys.dao.SysCommonDao;
import com.qcc.frame.jee.modules.sys.entity.SysCommon;
import org.springframework.stereotype.Service;

@Service
public class SysCommonService extends CrudService<SysCommonDao, SysCommon> {

	public void updateOfflineActionStatus(String userId, String refTableSign, String refTableId, String status) {
		dao.updateOfflineActionStatus(userId, refTableSign, refTableId, status);
	}
}
