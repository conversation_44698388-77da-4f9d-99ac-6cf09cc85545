package com.qcc.frame.jee.commons.model;

public class RequestTO implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8935115365157177543L;
	private String requestUrl;
	private String requestUUID;
	private String monitorCd;
	private String browserUserAgent;
	private String clientIp;
	private long beginTimestamp;
	private String extraKey;//目标放置的是companyId
	private boolean disabledLog;
	private String remark="";
	private String limitRedisKey;
	private String testId;
	private LockResultTO lockResultTO;
	private boolean companyCategoryFlag;// // updated for v2.2.6 fengsw KNZT-7660 非银行客户隐藏股东国籍信息
	private boolean mockRequest;// added for v2.2.8 fengsw KNZT-7656 是否mock请求
	private String redisCountKey;// redis请求计数key，针对访问key，redis会做一个请求计数
	private String reqParam;//added songld for KNZT-8347

	public String getReqParam() {
		return reqParam;
	}

	public void setReqParam(String reqParam) {
		this.reqParam = reqParam;
	}

	public String getTestId() {
		return testId;
	}

	public void setTestId(String testId) {
		this.testId = testId;
	}

	public String getLimitRedisKey() {
		return limitRedisKey;
	}

	public void setLimitRedisKey(String limitRedisKey) {
		this.limitRedisKey = limitRedisKey;
	}

	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public boolean isDisabledLog() {
		return disabledLog;
	}
	public void setDisabledLog(boolean disabledLog) {
		this.disabledLog = disabledLog;
	}
	public String getClientIp() {
		return clientIp;
	}
	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}
	public String getRequestUrl() {
		return requestUrl;
	}
	public void setRequestUrl(String requestUrl) {
		this.requestUrl = requestUrl;
	}
	public String getRequestUUID() {
		return requestUUID;
	}
	public void setRequestUUID(String requestUUID) {
		this.requestUUID = requestUUID;
	}
	public String getMonitorCd() {
		return monitorCd;
	}
	public void setMonitorCd(String monitorCd) {
		this.monitorCd = monitorCd;
	}
	public String getBrowserUserAgent() {
		return browserUserAgent;
	}
	public void setBrowserUserAgent(String browserUserAgent) {
		this.browserUserAgent = browserUserAgent;
	}
	public long getBeginTimestamp() {
		return beginTimestamp;
	}
	public void setBeginTimestamp(long beginTimestamp) {
		this.beginTimestamp = beginTimestamp;
	}
	public String getExtraKey() {
		return extraKey;
	}
	public void setExtraKey(String extraKey) {
		this.extraKey = extraKey;
	}

	public boolean isMockRequest() {
		return mockRequest;
	}

	public void setMockRequest(boolean mockRequest) {
		this.mockRequest = mockRequest;
	}

	public String getRedisCountKey() {
		return redisCountKey;
	}

	public void setRedisCountKey(String redisCountKey) {
		this.redisCountKey = redisCountKey;
	}

	public LockResultTO getLockResultTO() {
		return lockResultTO;
	}

	public void setLockResultTO(LockResultTO lockResultTO) {
		this.lockResultTO = lockResultTO;
	}

	public boolean isCompanyCategoryFlag() {
		return companyCategoryFlag;
	}

	public void setCompanyCategoryFlag(boolean companyCategoryFlag) {
		this.companyCategoryFlag = companyCategoryFlag;
	}
}
