package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class TblTranslateHist extends DataEntity<TblTranslateHist> {
	private String textMd5;
	private String text;
	private String textEn;
	private String supplier;

	private String keyNo;
	private String name;
	private String requestId;
	private String functionCd;

	public String getFunctionCd() {
		return functionCd;
	}

	public void setFunctionCd(String functionCd) {
		this.functionCd = functionCd;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getKeyNo() {
		return keyNo;
	}

	public void setKeyNo(String keyNo) {
		this.keyNo = keyNo;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTextMd5() {
		return textMd5;
	}

	public void setTextMd5(String textMd5) {
		this.textMd5 = textMd5;
	}

	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getTextEn() {
		return textEn;
	}
	public void setTextEn(String textEn) {
		this.textEn = textEn;
	}
	public String getSupplier() {
		return supplier;
	}
	public void setSupplier(String supplier) {
		this.supplier = supplier;
	}
}
