package com.qcc.frame.jee.modules.sys.utils;

import com.alibaba.fastjson.JSONArray;
import com.aliyun.oss.common.utils.IOUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

// added for v1.9.1 KNZT-4258
public class JSONFileConfigReaderUtil {

    private final static Logger LOGGER = LoggerFactory.getLogger(JSONFileConfigReaderUtil.class);

    private final static ConcurrentHashMap<TypeEnum, JSONArray> CACHE_MAP = new ConcurrentHashMap<>();

    private static final String ROOT_PATH = "/translation/";

    private static JSONArray getOrInit(TypeEnum typeEnum) {
        return CACHE_MAP.computeIfAbsent(typeEnum,
                key -> readFromResource(ROOT_PATH + typeEnum.getFileName()));
    }

    private static JSONArray readFromResource(String filePath) {
        ClassPathResource resource = new ClassPathResource(filePath);
        try (InputStream is = resource.getInputStream();) {
            String str = IOUtils.readStreamAsString(is, "UTF-8");
            if (StringUtils.isNotBlank(str)) {
                return JsonUtils.parseArray(str);
            }
        } catch (IOException e) {
            LOGGER.error("读取文件失败，" + filePath, e);
        }
        return null;
    }

    public static <T> List<T> getConfigs(TypeEnum typeEnum, Class<T> clazz) {
        JSONArray ja = getOrInit(typeEnum);
        if (ja == null) {
            return null;
        }
        try {
            return ja.toJavaList(clazz);
        } catch (Exception e) {
            LOGGER.error("转换javaList失败", e);
        }
        return null;
    }

    public enum TypeEnum {
        HK_INDUSTRY("hk_industry.json"),
        PINYIN_TRANSLATION_WHITELIST("pinyin_translation_whitelist.json"), // 拼音翻译白名单 added for v2.1.3 chenbl KNZT-6297
        ;

        private final String fileName;

        TypeEnum(String fileName) {
            this.fileName = fileName;
        }

        public String getFileName() {
            return fileName;
        }
    }
}
