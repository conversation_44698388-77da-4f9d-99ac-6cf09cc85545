package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysUserExt extends DataEntity<SysUserExt> {
	
	private String userId;
	private String type;
	private String value;
	private String valueDesc;

	public SysUserExt() {
	}

	public SysUserExt(String userId, String type, String value, String valueDesc) {
		this.userId = userId;
		this.type = type;
		this.value = value;
		this.valueDesc = valueDesc;
	}

	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getValueDesc() {
		return valueDesc;
	}
	public void setValueDesc(String valueDesc) {
		this.valueDesc = valueDesc;
	}
}
