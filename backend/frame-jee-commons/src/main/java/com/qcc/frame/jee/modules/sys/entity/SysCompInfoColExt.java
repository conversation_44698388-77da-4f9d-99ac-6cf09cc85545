package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysCompInfoColExt extends DataEntity<SysCompInfoColExt> {
	
	private String extType;
	private String extKey;
	private String extValue;
	private String remark;

	public String getExtType() {
		return extType;
	}
	public void setExtType(String extType) {
		this.extType = extType;
	}
	public String getExtKey() {
		return extKey;
	}
	public void setExtKey(String extKey) {
		this.extKey = extKey;
	}
	public String getExtValue() {
		return extValue;
	}
	public void setExtValue(String extValue) {
		this.extValue = extValue;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
}
