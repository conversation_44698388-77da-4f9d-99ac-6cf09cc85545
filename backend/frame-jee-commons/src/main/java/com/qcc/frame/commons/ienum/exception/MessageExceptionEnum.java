package com.qcc.frame.commons.ienum.exception;

import com.qcc.frame.commons.Constants;

// added for v2.1.8 chenbl KNZT-6662
public enum MessageExceptionEnum {
    WARN_IMPORT_USER_FILE_NULL(Constants.Result.FRONT_YELLOW_WARNING, "msg:Please submit at least one valid email address."),
    WARN_IMPORT_USER_OVER_500(Constants.Result.FRONT_YELLOW_WARNING, "msg:You can upload up to 500 emails each time."),
    WARN_IMPORT_USER_FILE_TYPE_INVALID(Constants.Result.FRONT_YELLOW_WARNING, "msg:Incorrect document format. Please upload a csv file only."),
    WARN_IMPORT_USER_OVER_COMPANY_LIMIT(Constants.Result.FRONT_YELLOW_WARNING, "msg:The number of users imported have reached your account limit."),
    FIN_TAX_DONE(Constants.Result.FIN_TAX_DONE, "msg:The authorization has been completed."),
    FIN_TAX_CANCEL(Constants.Result.FIN_TAX_DONE, "msg:The authorization has been canceled."),

    // added for v2.1.8 chenbl KNZT-6800
    ERROR_EMAIL_ADDRESS_INVALID(Constants.Result.FALSE_STR, "msg:Please enter an accessible email address."),

    WARN_GROUP_NAME_DUPLICATE(Constants.Result.FRONT_YELLOW_WARNING, "msg:The group name is occupied, please try another one."),

    MONITOR_PORTFOLIO_OVER_50(Constants.Result.FRONT_YELLOW_WARNING, "msg:A limit of 50 portfolios has been reached."),
    MONITOR_PORTFOLIO_NAME_DUPLICATE(Constants.Result.FRONT_YELLOW_WARNING, "msg:The portfolio name is occupied. Please try another one."),
    MONITOR_SERVICE_NOT_ENABLED(Constants.Result.SESSION_TIMEOUT_1002, "msg:The monitor service is not in your current plan. Please contact us for details."),
    MONITOR_SERVICE_EXPIRED(Constants.Result.SESSION_TIMEOUT_1002, "msg:The monitor service has expired. Please contact us to renew the plan."),
    WARN_MONITOR_OVER_COMPANY_LIMIT(Constants.Result.EXCEED_MAX, "msg:The monitor slots have reached the limit. Please contact the sales team for details."),
    WARN_MONITOR_OVER_PORTFOLIO_LIMIT(Constants.Result.FRONT_YELLOW_WARNING, "warn.monitor.over.portfolio.limit"),

    WARN_SEARCH_KEY_INVALID(Constants.Result.FRONT_YELLOW_WARNING, "warn.search.key.invalid"),
    WARN_RECAPTCHA_INVALID(Constants.Result.RECAPTCHA_INVALID, "msg:We noticed unusual activity. Please slow down and try again later, or log in to continue searching."),
    WARN_API_LIMIT_EXCEEDED(Constants.Result.API_LIMIT, "msg:We noticed unusual activities. Please slow down and try again later, or log in to continue searching."),
    TOO_MANY_RESULTS(Constants.Result.TOO_MANY_RESULTS, "msg:Your search terms are too broad. Try using more specific keywords."),
    DUPLICATE_RULE_NAME(Constants.Result.DUPLICATE_RULE_NAME, "msg:You have created another rule template with the same name. Please modify and try again."),
    HK_IRD_SEARCH_TOO_FREQUENTLY(Constants.Result.FRONT_YELLOW_WARNING, "msg:You're searching too frequently. Please wait a few seconds and try again."),
    HK_IRD_MAINTAIN(Constants.Result.HK_IRD_MAINTAIN, "msg:The IRD search service is temporarily unavailable."),
    MAP_TRIAL_BENEFIT_AUTH_ERROR(Constants.Result.MAP_TRIAL_BENEFIT_AUTH_ERROR, "msg:Only users with the <strong>Owner<strong/> or <strong>Admin<strong/> role can activate the Free Trial of Data Map Workspace.\nPlease contact the Owner or Admin of your organization to activate Free Trial."),
    ;

    private final String errCode;
    private final String message; // 非msg:开头的话，则会从messages_xx.properties找对应的映射

    MessageExceptionEnum(String errCode, String message) {
        this.errCode = errCode;
        this.message = message;
    }

    public String getErrCode() {
        return errCode;
    }

    public String getMessage() {
        return message;
    }
}
