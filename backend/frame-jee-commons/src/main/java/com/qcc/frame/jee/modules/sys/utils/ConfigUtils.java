package com.qcc.frame.jee.modules.sys.utils;

import com.google.common.collect.Maps;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.SysConfig;
import com.qcc.frame.jee.modules.sys.service.SysConfigService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConfigUtils {
	private static SysConfigService sysConfigService = SpringContextHolder.getBean(SysConfigService.class);
	
	public static String getConfigValueByTypeAndKey(String configType, String configKey, String defaultValue) {
		SysConfig config = sysConfigService.getConfigByTypeAndKey(configType, configKey);
		if(config != null && StringUtils.isNotBlank(config.getConfigValue())) {
			return config.getConfigValue();
		}
		return defaultValue;
	}
	
	public static List<SysConfig> listConfigByType(String configType) {
		return sysConfigService.listConfigByType(configType);
	}
	
	public static List<String> listValueByTypeAndKey(String configType, String configKey) {
		return sysConfigService.listValueByTypeAndKey(configType, configKey);
	}
	
	public static String getConfigValueByListAndKey(List<SysConfig> list, String configKey, String defaultValue) {
		if(list != null) {
			for(SysConfig config : list) {
				if(StringUtils.equals(configKey, config.getConfigKey())) {
					if(StringUtils.isNotBlank(config.getConfigValue())) {
						return config.getConfigValue();
					}
					break;
				}
			}
		}
		return defaultValue;
	}


	public static Map<String, String> getConfigMap(String configType) {
		HashMap<String, String> res = Maps.newHashMap();
		List<SysConfig> sysConfigs = listConfigByType(configType);
		if (CollectionUtils.isEmpty(sysConfigs)) {
			return res;
		}
		for (SysConfig sysConfig : sysConfigs) {
			res.put(sysConfig.getConfigKey(), sysConfig.getConfigValue());
		}
		return res;
	}
}
