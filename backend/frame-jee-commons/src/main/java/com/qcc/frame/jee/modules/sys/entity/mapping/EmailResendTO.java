package com.qcc.frame.jee.modules.sys.entity.mapping;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qcc.frame.commons.Constants;
import lombok.Data;

import java.util.Date;

/*
* added for V2.3.7 KNZT-8702
* */
@Data
public class EmailResendTO {
	private String id;
	private String emailTo;
	private String emailCc;
	private String emailBcc;
	private String emailSubject;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone= Constants.DEFAULT_TIMEZONE)
	private Date sendDate;
	private Short status;
	private String errorText;
	private String attachment1FileName;
	private String attachment2FileName;
	private short actualFileQty = 0;
}
