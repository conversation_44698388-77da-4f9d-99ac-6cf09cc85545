package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.jee.commons.cache.QccRedisManager;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.service.ServiceException;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class RedisServUtils {
	protected Logger logger = LoggerFactory.getLogger(getClass());
	@Autowired
	private QccRedisManager redisManager;
	@Autowired
	private RedisService redisService;

	static final byte[] NX = StringUtils.getBytes("NX");
	static final byte[] EX = StringUtils.getBytes("EX");
	static final byte[] LOCK_VALUE = StringUtils.getBytes("Y");
	
	public String getStringById(String key, String id) throws MessageException {
		key = RedisService.getKey(key, id);
		byte [] arr = redisManager.get(StringUtils.getBytes(key));
		if(arr == null) {
			return "";
		}
		return StringUtils.toString(arr);
	}
	
	/**
	 * 保存String对象到Redis, 数据永久有效, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param value - 需要保存到Redis的String对象
	 * @throws MessageException
	 */
	public void saveString(String key, String id, String value) throws MessageException {
		saveString(key, id, value, -1);
	}
	
	/**
	 * 保存String对象到Redis, 数据有效时间由timeout(秒)决定, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param value - 需要保存到Redis的String对象
	 * @param timeout - 数据失效时间(秒)
	 * @throws MessageException
	 */
	public void saveString(String key, String id, String value, int timeout) throws MessageException {
		if(StringUtils.isBlank(id)) {
			throw new MessageException("id.required");
		}
		key = RedisService.getKey(key, id);
		redisManager.set(StringUtils.getBytes(key), StringUtils.getBytes(value), timeout);
	}
	
	public void deleteById(String key, String id) throws MessageException {
		key = RedisService.getKey(key, id);
		redisManager.del(StringUtils.getBytes(key));
	}

	/**
	 * Redis锁, 如果锁成功, 返回true, 如果锁失败, 返回false
	 * @param key
	 * @param id
	 * @param timeoutSec
	 * @return
	 */
	public boolean lockKey(String key, String id, long timeoutSec) {
		key = RedisService.getKey(key, id);
		String result = redisManager.setWithExtParam(StringUtils.getBytes(key), LOCK_VALUE, timeoutSec);
		return StringUtils.isNotBlank(result);
	}

	/**
	 * Redis解锁
	 * @param key
	 * @param id
	 */
	public void unlockKey(String key, String id, boolean lockResult) {
		if(lockResult) {
			key = RedisService.getKey(key, id);
			redisManager.del(StringUtils.getBytes(key));
		}
	}

	/**
	 * 通过Lua Script添加请求并判断是否达到限制数量, 跟deleteLimitApiForScript配套使用
	 * @param script
	 * @param key
	 * @param reqId
	 * @param maxCount
	 * @param currentTimestampSec
	 * @param timeoutSecBegin
	 * @param timeoutSecEnd
	 * @return
	 */
	public boolean addLimitApiByScript(String script, String key, String reqId, int maxCount, long currentTimestampSec, long timeoutSecBegin, long timeoutSecEnd) {
		if(StringUtils.isBlank(script)) {
			throw new ServiceException("script is blank");
		}
		key = "api_lmt:" + key;

		byte[] minScoreByte = StringUtils.getBytes(String.valueOf(timeoutSecBegin));
		byte[] maxScoreByte = StringUtils.getBytes(String.valueOf(timeoutSecEnd));
		byte[] maxCountByte = StringUtils.getBytes(String.valueOf(maxCount));
		byte[] addScoreByte = StringUtils.getBytes(String.valueOf(currentTimestampSec));
		byte[] addMemberByte = StringUtils.getBytes(reqId);
		byte [] objArr = (byte[])redisManager.eval(StringUtils.getBytes(script), 1, StringUtils.getBytes(key), minScoreByte, maxScoreByte, maxCountByte, addScoreByte, addMemberByte);
		String result = objArr != null ? new String(objArr) : null;

		boolean lockFlag = "L".equals(result);
		if(lockFlag) {
			logger.info(key + "===lockscript==" + result);
		}
		return lockFlag;
	}

	/**
	 * 释放请求限制数量, 跟addLimitApiByScript配套使用
	 * @param key
	 * @param reqId
	 * @return
	 */
	public Long deleteLimitApiForScript(String key, String reqId) {
		key = "api_lmt:" + key;
		byte[] delMemberByte = StringUtils.getBytes(reqId);
		return redisManager.zrem(StringUtils.getBytes(key), delMemberByte);
	}

	/**
	 * 通过非Script添加请求并判断是否达到限制数量, 跟deleteLimitApiForNoScript配套使用
	 * @param key
	 * @param reqId
	 * @param maxCount
	 * @param currentTimestampSec
	 * @param timeoutSecBegin
	 * @param timeoutSecEnd
	 * @return
	 */
	public boolean addLimitApiByNoScript(String key, String reqId, int maxCount, long currentTimestampSec, long timeoutSecBegin, long timeoutSecEnd) {
		key = "api_lmt:" + key;
		byte[] keyByte = StringUtils.getBytes(key);
		byte[] minScoreByte = StringUtils.getBytes(String.valueOf(timeoutSecBegin));
		byte[] maxScoreByte = StringUtils.getBytes(String.valueOf(timeoutSecEnd));
		byte[] addMemberByte = StringUtils.getBytes(reqId);

		Long count = redisManager.zcount(keyByte, minScoreByte, maxScoreByte);
		if(count != null && count.longValue() >= maxCount) {
			return true;
		}

		redisManager.zadd(keyByte, currentTimestampSec, addMemberByte);
		return false;
	}

	/**
	 * 释放请求限制数量, 跟addLimitApiByNoScript配套使用
	 * @param key
	 * @param reqId
	 * @return
	 */
	public Long deleteLimitApiForNoScript(String key, String reqId) {
		key = "api_lmt:" + key;
		byte[] delMemberByte = StringUtils.getBytes(reqId);
		return redisManager.zrem(StringUtils.getBytes(key), delMemberByte);
	}

	/**
	 * 设置限流API的过期时间
	 * @param key Redis key
	 * @param timeout 过期时间(秒)
	 */
	public void expireLimitApi(String key, int timeout) {
		key = "api_lmt:" + key;
		redisManager.expire(StringUtils.getBytes(key), timeout);
	}

	
	/**
	 * 清理限流API的过期数据
	 * @param key Redis key
	 * @param minScore 最小分数（包含）
	 * @param maxScore 最大分数（包含）
	 * @return 删除的元素数量
	 */
	public Long cleanupExpiredLimitData(String key, long minScore, long maxScore) {
		try {
			key = "api_lmt:" + key;
			byte[] keyBytes = StringUtils.getBytes(key);
			byte[] minScoreBytes = StringUtils.getBytes(String.valueOf(minScore));
			byte[] maxScoreBytes = StringUtils.getBytes(String.valueOf(maxScore));
			return redisManager.zremrangebyscore(keyBytes, minScoreBytes, maxScoreBytes);
		} catch (Exception e) {
			logger.error("清理过期限流数据失败: " + key, e);
			return 0L;
		}
	}

	/**
	 * 保存非String对象到Redis, 数据有效时间由timeout(秒)决定, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param valueObj - 需要保存到Redis的非String对象
	 * @param timeout - 数据失效时间(秒)
	 * @throws MessageException
	 */
	public void saveObject(String key, String id, Serializable valueObj, int timeout) throws MessageException {
		if(StringUtils.isBlank(id)) {
			throw new MessageException("id.required");
		}
		key = RedisService.getKey(key, id);
		redisManager.set(StringUtils.getBytes(key), SerializationUtils.serialize(valueObj), timeout);
	}

	/**
	 * 保存非String对象到Redis, 数据永久有效, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param valueObj - 需要保存到Redis的非String对象
	 * @throws MessageException
	 */
	public void saveObject(String key, String id, Serializable valueObj) throws MessageException {
		saveObject(key, id, valueObj, -1);
	}

	/**
	 * 通过key和id得到保存的非字符串对象
	 * 读取有可能出现异常, 请做好异常处理
	 * @param key - 保存数据的key值
	 * @param id - 保存数据的id值
	 * @return
	 * @throws MessageException
	 */
	public <T> T getObjectById(String key, String id, Class<T> clazz) throws MessageException {
		key = RedisService.getKey(key, id);
		byte [] arr = redisManager.get(StringUtils.getBytes(key));
		if(arr == null) {
			return null;
		}
		return (T)SerializationUtils.deserialize(arr);
	}


	/**
	 * 通过key和id生成redis key，后对value递增加一，并且设置过期时间
	 * added for v1.9.7 KNZT-4708
	 *
	 * @param key
	 * @param expireSeconds
	 * @return long
	 */
	public long incrAndSetExpire(String key, String id, long expireSeconds) {
		String redisKey = RedisService.getKey(key, id);
		byte[] keyBytes = StringUtils.getBytes(redisKey);
		return redisManager.incrAndSetExpire(keyBytes, expireSeconds);
	}

	/**
	 * 通过key和id查询对应的次数
	 * added for v1.9.7 KNZT-4708
	 *
	 * @param key
	 * @param id
	 * @return long
	 */
	public long getCount(String key, String id) {
		try {
			String val = this.getStringById(key, id);
			return NumberUtils.getLong(val, 0);
		} catch (Exception e) {
			logger.error("", e);
			return 0;
		}
	}

	 /**
     * 通用编号生成方法
     * @param numberPrefix 前缀
     * @param redisKey Redis计数器key前缀
     * @return 生成的编号
      */
	 public String generateNumber(String numberPrefix, String redisKey) {
		 String dateStr = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS);
		 return generateNumber(numberPrefix, redisKey, dateStr);
	 }

	 /**
     * 通用编号生成方法
     * @param numberPrefix 前缀
     * @param redisKey Redis计数器key前缀
     * @param dateStr 日期字符串
     * @return 生成的编号
      */
    public String generateNumber(String numberPrefix, String redisKey, String dateStr) {
        String number;
        try {
			if (Objects.isNull(numberPrefix)) {
				numberPrefix = "";
			}
            // 从Redis中获取当前计数并递增，以每秒为key，记录每秒内的数量，key值有效期1分钟
            long count = incrAndSetExpire(redisKey + ":number_count", dateStr, 10 * 6);
            String counterPart = String.format("%03d", count);
            
            // 组合前缀、时间、计数部分
            String base = numberPrefix + dateStr + counterPart;
            String validate = StringUtils.getSha256Token4Validate(base);
            if (StringUtils.isNotBlank(validate)) {
                number = base + Character.toUpperCase(validate.charAt(0));
            } else {
                throw new MessageException("number is null");
            }
            logger.info("generate number:{} success", number);
        } catch (Exception e) {
            logger.error("generate number failed", e);
			return StringUtils.generateNo();
        }
        return number;
    }

	public void expire(String redisKey, String id, int timeout) {
		redisKey = RedisService.getKey(redisKey, id);
		redisManager.expire(StringUtils.getBytes(redisKey), timeout);
	}


	/**
	 * 增加随机过期时间，避免大量key同时过期
	 * @param baseSeconds 基础秒数
	 * @param minRandomSeconds 最小随机秒数
	 * @param maxRandomSeconds 最大随机秒数
	 * @return 添加随机时间后的秒数
	 */
	public long addRandomExpireTime(long baseSeconds, long minRandomSeconds, long maxRandomSeconds) {
		return baseSeconds + ThreadLocalRandom.current().nextLong(minRandomSeconds, maxRandomSeconds);
	}

	/**
	 * 获取指定分数范围内的元素数量
	 * @param key Redis key前缀
	 * @param id ID
	 * @param minScore 最小分数
	 * @param maxScore 最大分数
	 * @return 元素数量
	 */
	public Long getCountInScoreRange(String key, String id, long minScore, long maxScore) {
		String fullKey = RedisService.getKey(key, id);
		byte[] keyByte = StringUtils.getBytes(fullKey);
		byte[] minScoreByte = StringUtils.getBytes(String.valueOf(minScore));
		byte[] maxScoreByte = StringUtils.getBytes(String.valueOf(maxScore));
		return redisManager.zcount(keyByte, minScoreByte, maxScoreByte);
	}

	/**
	 * 向有序集合添加元素
	 * @param key Redis key前缀
	 * @param id ID
	 * @param score 分数
	 * @param member 成员值
	 * @return 添加成功的元素数量
	 */
	public Long addToSortedSet(String key, String id, double score, String member) {
		String fullKey = RedisService.getKey(key, id);
		byte[] keyBytes = StringUtils.getBytes(fullKey);
		byte[] memberBytes = StringUtils.getBytes(member);
		return redisManager.zadd(keyBytes, score, memberBytes);
	}

	/**
	 * set if not exists
	 * @param key
	 * @param id
	 * @param value
	 * @return true: if the key was set, false: if the key was not set
	 */
	public boolean setnx(String key, String id, String value) {
		String fullKey = RedisService.getKey(key, id);
		byte[] keyBytes = StringUtils.getBytes(fullKey);
		byte[] valueBytes = StringUtils.getBytes(value);
		return redisManager.setnx(keyBytes, valueBytes)==1;
	}

	/**
	 * 获取key失效时间ttl(秒)
	 *
	 * @param key
	 * @param id
	 * @return
	 */
	public long ttl(String key, String id) {
		String fullKey = RedisService.getKey(key, id);
		byte[] keyBytes = StringUtils.getBytes(fullKey);
		return redisManager.ttl(keyBytes);
	}
}
