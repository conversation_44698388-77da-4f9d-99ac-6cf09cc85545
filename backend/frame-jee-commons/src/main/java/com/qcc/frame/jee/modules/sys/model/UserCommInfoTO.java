package com.qcc.frame.jee.modules.sys.model;

import com.qcc.frame.commons.Constants;

public class UserCommInfoTO {
	
	private String userId;
	private String companyId;
	private String compGroupId;
	private String dataScope;
	private Integer type;
	
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getCompanyId() {
		return companyId;
	}
	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	public String getCompGroupId() {
		return compGroupId;
	}
	public void setCompGroupId(String compGroupId) {
		this.compGroupId = compGroupId;
	}
	public String getDataScope() {
		return dataScope;
	}
	public void setDataScope(String dataScope) {
		if(Constants.DataScope.MANAGER.equals(dataScope) || Constants.DataScope.USER_ADMIN.equals(dataScope) || Constants.DataScope.PERSONNEL.equals(dataScope)) {
			this.dataScope = dataScope;
		}
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}
