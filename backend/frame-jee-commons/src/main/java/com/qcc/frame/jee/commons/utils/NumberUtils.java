package com.qcc.frame.jee.commons.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.List;
import java.util.regex.Pattern;


public class NumberUtils extends org.apache.commons.lang3.math.NumberUtils {

    private static final Logger logger = LoggerFactory.getLogger(NumberUtils.class);

	public static boolean isInt(String val) {
		try {
			new Integer(val);
			return true;
		} catch(Exception e) {
			
		}
		return false;
	}
	
	public static boolean isLong(String val) {
		try {
			new Long(val);
			return true;
		} catch(Exception e) {
			
		}
		return false;
	}


	public static boolean isDec(String val) {
		try {
			new BigDecimal(val);
			return true;
		} catch(Exception e) {
			
		}
		return false;
	}
	
	public static String formatPercent3Dig(BigDecimal dec) {
		if(dec != null) {
			DecimalFormat df = new DecimalFormat("##0.###");
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(dec);
		}
		return "0";
	}
	
	public static String formatPercent4Dig(BigDecimal dec) {
		if(dec != null) {
			DecimalFormat df = new DecimalFormat("##0.####");
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(dec);
		}
		return "0";
	}

	public static BigDecimal toDec(String val) {
		try {
			return new BigDecimal(val);
		} catch(Exception e) {
			return null;
		}
	}
	
	public static int max(int a, int b, int c) {
		int max1 = Math.max(a, b);
		int max = Math.max(max1, c);
		return max;
	}
	
	public static int max(List<Integer> list) {
		if(list != null && !list.isEmpty()) {
			int max = list.get(0);
			for(int i=1; i < list.size(); i++) {
				max = Math.max(max, list.get(i));
			}
			return max;
		}
		return 0;
	}
	
	public static boolean checkSameIntegralPart(BigDecimal a, BigDecimal b) {
		if(a != null && b != null) {
			return a.toBigInteger().compareTo(b.toBigInteger()) == 0;
		}
		return false;
	}

	// updated for v1.9.5 KNZT-4510 codeReview
	public static String formatRptUnit(BigDecimal dec) {
		if (dec != null) {
			DecimalFormat df = new DecimalFormat("#0.##");
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(dec);
		}
		return "0";
	}

	/**
	 * 格式化单位，带正负号
	 * @param dec
	 * @return
	 */	
	public static String formatRptUnitWithSign(BigDecimal dec) {
		if (dec != null) {
			return dec.compareTo(BigDecimal.ZERO) > 0 ? "+" + formatRptUnit(dec) : formatRptUnit(dec);
		}
		return "";
	}

	// added for v1.9.4 KNZT-4514
	public static String formatRptUnit2c(BigDecimal dec) {
		if (dec != null) {
			DecimalFormat df = new DecimalFormat("#0.00");
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(dec);
		}
		return "0";
	}
	
	public static String formatYuan(BigDecimal dec) {
		if(dec != null) {
			DecimalFormat df = new DecimalFormat("#0.##");
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(dec);
		}
		return "0.00";
	}
	
	public static String formatDec(BigDecimal dec, String format) {
		if(dec != null) {
			DecimalFormat df = new DecimalFormat(format);
			df.setRoundingMode(RoundingMode.HALF_UP);
			return df.format(dec);
		}
		return "0.000";
	}
    
    public static String formatFinanceData(String data) {
    	if(StringUtils.isNotBlank(data) && isDec(data)) {
    		BigDecimal dec = new BigDecimal(data);
    		if(dec.compareTo(new BigDecimal("100000000")) != -1 || dec.compareTo(new BigDecimal("-100000000")) != 1) {
    			dec = dec.divide(new BigDecimal("100000000"));
    			return formatDec(dec, "#0.##") + "亿";
    		} else if(dec.compareTo(new BigDecimal("10000")) != -1 || dec.compareTo(new BigDecimal("-10000")) != 1) {
    			dec = dec.divide(new BigDecimal("10000"));
    			return formatDec(dec, "#0.##") + "万";
    		} else {
    			return data;
    		}
    	}
    	return data;
    }
    
    public static String formatFinanceDataByUnitYi(String data) {
    	String retData = Pattern.compile("[\u4e00-\u9fa5]").matcher(data).replaceAll("");
    	if(StringUtils.isNotBlank(data) && isDec(retData)) {
    		BigDecimal amt = new BigDecimal(retData);
    		if(StringUtils.endsWith(data, "万亿")) {
    			amt = amt.multiply(new BigDecimal("10000"));
    		} else if(StringUtils.endsWith(data, "万")) {
    			amt = amt.divide(new BigDecimal("10000"));
    		}
    		return formatDec(amt, "#0.##");
    	}
    	return "";
    }
    
    public static String multiply(String data, int unitNum) {
    	if(StringUtils.isNotBlank(data) && isDec(data)) {
    		BigDecimal dec = new BigDecimal(data);
    		dec = dec.multiply(new BigDecimal(unitNum));
    		return formatDec(dec, "#0.##");
    	}
    	return "";
    }
    
    /**
     * data * unitNum
     * @param data
     * @param unitNum
     * @return
     */
    public static String formatFinanceData(String data, int unitNum) {
    	if(StringUtils.isNotBlank(data) && isDec(data)) {
    		BigDecimal dec = new BigDecimal(data);
    		dec = dec.multiply(new BigDecimal(unitNum));
    		if(dec.compareTo(new BigDecimal("100000000")) != -1 || dec.compareTo(new BigDecimal("-100000000")) != 1) {
    			dec = dec.divide(new BigDecimal("100000000"));
    			return formatDec(dec, "#0.##") + "亿";
    		} else if(dec.compareTo(new BigDecimal("10000")) != -1 || dec.compareTo(new BigDecimal("-10000")) != 1) {
    			dec = dec.divide(new BigDecimal("10000"));
    			return formatDec(dec, "#0.##") + "万";
    		} else {
    			return formatDec(dec, "#0.##");
    		}
    	}
    	return data;
    }
    
    /**
     * 扫描测试
     * @param val
     * @return
     */
    public static String scanTest(BigDecimal dec) {
    	return dec.setScale(3).toPlainString();
    }
    
    /**
     * 将字符串转成int型, 如果str为空或者非int型, 返回defaultInt
     * @param str
     * @param defaultInt
     * @return
     */
    public static int getInt(String str, int defaultInt) {
    	if(StringUtils.isBlank(str)) {
    		return defaultInt;
    	}
    	try {
    		return new Integer(str);
    	} catch(Exception e) {
    		return defaultInt;
    	}
    }

	public static long getLong(String str, long defaultInt) {
		if(StringUtils.isBlank(str)) {
			return defaultInt;
		}
		try {
			return new Long(str);
		} catch(Exception e) {
			return defaultInt;
		}
	}

	public static double getDouble(String str, double defaultInt) {
		if(StringUtils.isBlank(str)) {
			return defaultInt;
		}
		try {
			return new Double(str);
		} catch(Exception e) {
			return defaultInt;
		}
	}
    /**
     * 将字符串转成short型, 如果str为空或者非short型, 返回defaultShort
     * @param str
     * @param defaultShort
     * @return
     */
    public static short getShort(String str, short defaultShort) {
    	if(StringUtils.isBlank(str)) {
    		return defaultShort;
    	}
    	try {
    		return new Short(str);
    	} catch(Exception e) {
    		return defaultShort;
    	}
    }
    
    public static BigDecimal fen2Yuan(BigDecimal fen) {
    	BigDecimal result = fen.divide(new BigDecimal(100));
    	return new BigDecimal(formatDec(result, "#0.00"));
    }
    
    public static String formatDec4Dig(String decStr) {
    	if(StringUtils.isNotBlank(decStr) && isDec(decStr)) {
    		return formatPercent4Dig(new BigDecimal(decStr));
    	}
    	return "0";
    }
    
    public static BigDecimal divide5Dig(BigDecimal dividend, BigDecimal divisor) {
    	BigDecimal zero = new BigDecimal("0");
    	if(divisor.compareTo(zero) == 0) {
    		return zero;
    	}
    	return dividend.divide(divisor, 5, RoundingMode.HALF_UP);
    }
    
    public static BigDecimal divide4Dig(BigDecimal dividend, BigDecimal divisor) {
    	BigDecimal zero = new BigDecimal("0");
    	if(divisor.compareTo(zero) == 0) {
    		return zero;
    	}
    	return dividend.divide(divisor, 4, RoundingMode.HALF_UP);
    }


	public static BigDecimal getPositiveBigDecimal(BigDecimal val) {
		if (val == null) {
			return BigDecimal.ZERO;
		} else if (val.compareTo(BigDecimal.ZERO) < 0) {
			return BigDecimal.ZERO;
		} else {
			return val;
		}
	}

	public static BigDecimal getBigDecimal(BigDecimal val) {
		if (val == null) {
			return BigDecimal.ZERO;
		} else {
			return val;
		}
	}

	public static String getPositiveBigDecimalStr(BigDecimal val) {
		return getPositiveBigDecimal(val).toString();
	}

	public static String format2ScaleWithThousandSplit(BigDecimal number) {
		if (number == null) {
			return "0.00";
		} else {
			DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
			return decimalFormat.format(number);
		}
	}


	public static String getFormatTotalUnit(BigDecimal val) {
		if (val != null) {
			BigDecimal totalUnit = val.multiply(BigDecimal.valueOf(-1));
			String formatRptUnit = formatRptUnit(totalUnit);
			if (totalUnit.compareTo(BigDecimal.ZERO) > 0) {
				formatRptUnit = "+" + formatRptUnit;
			}
			return formatRptUnit;
		}
		return null;
	}

	public static String format(Number value, String pattern) {
		if (value == null) {
			return null;
		}
		return new DecimalFormat(pattern).format(value);
	}

	public static void main(String []args) {
//		BigDecimal data = new BigDecimal(3.3);
//		BigDecimal setScale = data.setScale(3);
//		System.out.println(setScale);
		
//		System.out.println(obj.getBigDecimal("dec"));
//		
//		System.out.println(formatPercent3Dig(data));
//		System.out.println(max(Arrays.asList(3+4, 6, 30,1,7,0)));
//		System.out.println(checkSameIntegralPart(new BigDecimal("240.1212"), new BigDecimal("240.12")));
//		System.out.println(formatFinanceData("100000000"));
//		System.out.println(formatFinanceData("100000000.543"));
//		System.out.println(formatFinanceData("9999100000000"));
//		System.out.println(formatFinanceData("90005500"));
//		System.out.println(formatFinanceData("90005500.5432"));
//		System.out.println(formatFinanceData("9999.33"));
//		System.out.println(formatFinanceData("-42343234"));
//		System.out.println(formatFinanceData("-100000000"));
//		System.out.println(formatFinanceData("-90000000"));
		
		System.out.println(formatPercent4Dig(new BigDecimal("0.6000")));
	}

    /**
     * added for v2.1.3 chenbl KNZT-6276
     * 转成英文序数词
     * @param num
     * @return
     */
    public static String number2Ordinal(Integer num) {
        if (num == null || num <= 0) {
            logger.error("num must be a positive integer.");
            return null;
        }
        String ordinal = null;
        // 特殊规则：后两位为11-13的序数词都是以th结尾
        if (num % 100 >= 11 && num % 100 <= 13) {
            ordinal = num + "th";
        } else {
            // 根据个位数确定序数词后缀
            switch (num % 10) {
                case 1:
                    ordinal = num + "st";
                    break;
                case 2:
                    ordinal = num + "nd";
                    break;
                case 3:
                    ordinal = num + "rd";
                    break;
                default:
                    ordinal = num + "th";
            }
        }
        return ordinal;
    }

	/**
	 * 将"前 45%"的描述转换为对应的百分位数"55th percentile"。
	 *
	 * @param percentage 前 x% 的百分比值（例如 45 表示前 45%）
	 * @return 对应的百分位数
	 */
	public static String number2PercentileWithSuffix(Integer percentage) {
		if (percentage == null || percentage < 0 || percentage > 100) {
			logger.error("Percentage must be between 0 and 100");
			return null;
		}
		int percentile = 100 - percentage;
		return number2Ordinal(percentile) + " percentile";
	}

	public static Number parseNumber(String numberStr) throws NumberFormatException {
		if (StringUtils.isBlank(numberStr)) {
			throw new NumberFormatException("Input string is null or empty");
		}
		// 检查是否是十六进制
		if (numberStr.toLowerCase().startsWith("0x")) {
			return Long.parseLong(numberStr.substring(2), 16);
		}
		// 去掉前导的正号
		if (numberStr.startsWith("+")) {
			numberStr = numberStr.substring(1);
		}
		try {
			// 使用NumberFormat解析数字
			NumberFormat format = NumberFormat.getInstance();
			if (format instanceof DecimalFormat) {
				((DecimalFormat) format).setParseBigDecimal(true);
			}
			return format.parse(numberStr);
		} catch (ParseException e) {
			NumberFormatException nfe = new NumberFormatException(e.getMessage());
			nfe.initCause(e);
			throw nfe;
		}
	}

	public static Number parseNumber(String numberStr, Number defaultValue) {
		if (StringUtils.isBlank(numberStr)) {
			return defaultValue;
		}
		try {
			return parseNumber(numberStr);
		} catch (NumberFormatException e) {
			logger.warn("数字解析失败, input: " + numberStr);
			return defaultValue;
		}
	}
}
