package com.qcc.frame.jee.commons.model;

import com.alibaba.fastjson.annotation.JSONField;

public class LogAccessTO implements java.io.Serializable {
	@JSONField(ordinal=5)
	private long timestamp;
	@JSONField(ordinal=10)
	private String logType;//acs/excp
	@JSONField(ordinal=15)
	private String customerType;//试用/签约
	@JSONField(ordinal=20)
	private String key;
	@JSONField(ordinal=25)
	private String url;
	@JSONField(ordinal=30)
	private long took;
	@JSONField(ordinal=35)
	private String remark;
	@JSONField(ordinal=40)
	private String appType;
	@JSONField(ordinal=50)
	private String reqUuid="";
	@JSO<PERSON>ield(ordinal=60)
	private String sign;
	@JSONField(ordinal=70)
	private String clientIp;
	@JSONField(ordinal=80)
	private String userAgent;
	@JSONField(ordinal=90)
	private String reqP;//added for songld KNZT-8347


	public String getReqP() {
		return reqP;
	}

	public void setReqP(String reqP) {
		this.reqP = reqP;
	}

	public String getClientIp() {
		return clientIp;
	}
	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}
	public String getUserAgent() {
		return userAgent;
	}
	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
	public String getReqUuid() {
		return reqUuid;
	}
	public void setReqUuid(String reqUuid) {
		this.reqUuid = reqUuid;
	}
	public long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public long getTook() {
		return took;
	}
	public void setTook(long took) {
		this.took = took;
	}
	public String getLogType() {
		return logType;
	}
	public void setLogType(String logType) {
		this.logType = logType;
	}
	public String getCustomerType() {
		return customerType;
	}
	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}
	public String getAppType() {
		return appType;
	}
	public void setAppType(String appType) {
		this.appType = appType;
	}

}
