package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CommDelayedTaskTypeEnum;
import com.qcc.frame.jee.commons.cache.QccRedisManager;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.resps.Tuple;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class RedisService {
	
	@Autowired
	private QccRedisManager redisManager;
	private static String prefix = "glb:" + Global.getConfig("project.env") + ":";
	private static final String DELAYED_TASK_REDIS_KEY_PREFIX = prefix + "delayed_task:"; // added for v1.9.6 KNZT-4646
	
	/**
	 * 保存非String对象到Redis, 数据永久有效, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param valueObj - 需要保存到Redis的非String对象
	 * @throws MessageException
	 */
	public void saveObject(String key, String id, Serializable valueObj) throws MessageException {
		saveObject(key, id, valueObj, -1);
	}
	
	/**
	 * 保存String对象到Redis, 数据永久有效, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param value - 需要保存到Redis的String对象
	 * @throws MessageException
	 */
	public void saveString(String key, String id, String value) throws MessageException {
		saveString(key, id, value, -1);
	}
	
	/**
	 * 保存非String对象到Redis, 数据有效时间由timeout(秒)决定, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param valueObj - 需要保存到Redis的非String对象
	 * @param timeout - 数据失效时间(秒)
	 * @throws MessageException
	 */
	public void saveObject(String key, String id, Serializable valueObj, int timeout) throws MessageException {
		if(StringUtils.isBlank(id)) {
			throw new MessageException("id.required");
		}
		key = getKey(key, id);
		redisManager.set(StringUtils.getBytes(key), SerializationUtils.serialize(valueObj), timeout);
	}
	
	/**
	 * 设置过期时间
	 * @param key
	 * @param id
	 * @param timeout
	 * @throws MessageException
	 */
	public void setExpire(String key, String id, int timeout) throws MessageException {
		if(StringUtils.isBlank(id)) {
			throw new MessageException("id.required");
		}
		key = getKey(key, id);
		redisManager.expire(StringUtils.getBytes(key), timeout);
	}
	
	/**
	 * 保存String对象到Redis, 数据有效时间由timeout(秒)决定, 通过key和id可以定位
	 * 保存有可能出现异常, 请做好异常处理
	 * @param key - Redis key中的关键字, 区分功能模块, 可以通过:来分隔
	 * @param id - Redis key中的唯一ID, 用于定位唯一记录
	 * @param value - 需要保存到Redis的String对象
	 * @param timeout - 数据失效时间(秒)
	 * @throws MessageException
	 */
	public void saveString(String key, String id, String value, int timeout) throws MessageException {
		if(StringUtils.isBlank(id)) {
			throw new MessageException("id.required");
		}
		key = getKey(key, id);
		redisManager.set(StringUtils.getBytes(key), StringUtils.getBytes(value), timeout);
	}

	/**
	 * 通过key和id得到保存的非字符串对象
	 * 读取有可能出现异常, 请做好异常处理
	 * @param key - 保存数据的key值
	 * @param id - 保存数据的id值
	 * @param t - 保存数据的Class, 例如类名.class
	 * @return
	 * @throws MessageException
	 */
	public <T> T getObjectById(String key, String id, Class<T> clazz) throws MessageException {
		key = getKey(key, id);
		byte [] arr = redisManager.get(StringUtils.getBytes(key));
		if(arr == null) {
			return null;
		}
		return (T)SerializationUtils.deserialize(arr);
	}
	
	/**
	 * 通过key和idPrefix得到保存的非字符串对象列表
	 * 读取有可能出现异常, 请做好异常处理
	 * @param key - 保存数据的key值
	 * @param idPrefix - 保存数据的id值的前部分的值
	 * @param t - 保存数据的Class, 例如类名.class
	 * @return
	 * @throws MessageException
	 */
	public <T> List<T> listObjectByIdPrefix(String key, String idPrefix, Class<T> clazz) throws MessageException {
		if(StringUtils.isBlank(idPrefix)) {
			throw new MessageException("idPrefix.required");
		}
		key = getKey(key, idPrefix);
		Set<byte[]> keys = redisManager.keys(StringUtils.getBytes(key + "*"));
		if (keys != null && keys.size() > 0) {
			List<T> resultList = new ArrayList<>();
			byte[] valueArr = null;
			for (byte[] keyArr : keys) {
				valueArr = redisManager.get(keyArr);
				if(valueArr != null) {
					resultList.add((T)SerializationUtils.deserialize(valueArr));
				}
			}
			return resultList;
		}
		return new ArrayList<>(0);
	}
	
	
	/**
	 * 通过key和id得到保存的字符串
	 * 读取有可能出现异常, 请做好异常处理
	 * @param key - 保存数据的key值
	 * @param id - 保存数据的id值
	 * @return
	 * @throws MessageException
	 */
	public String getStringById(String key, String id) throws MessageException {
		key = getKey(key, id);
		byte [] arr = redisManager.get(StringUtils.getBytes(key));
		if(arr == null) {
			return "";
		}
		return StringUtils.toString(arr);
	}
	
	/**
	 * 通过key和idPrefix得到保存的字符串列表
	 * 读取有可能出现异常, 请做好异常处理
	 * @param key - 保存数据的key值
	 * @param idPrefix - 保存数据的id值的前部分的值
	 * @return
	 * @throws MessageException
	 */
	public List<String> listStringByIdPrefix(String key, String idPrefix) throws MessageException {
		if(StringUtils.isBlank(idPrefix)) {
			throw new MessageException("idPrefix.required");
		}
		key = getKey(key, idPrefix);
		Set<byte[]> keys = redisManager.keys(StringUtils.getBytes(key + "*"));
		if (keys != null && keys.size() > 0) {
			List<String> resultList = new ArrayList<>();
			byte[] valueArr = null;
			for (byte[] keyArr : keys) {
				valueArr = redisManager.get(keyArr);
				if(valueArr != null) {
					resultList.add(StringUtils.toString(valueArr));
				}
			}
			return resultList;
		}
		return new ArrayList<>(0);
	}
	
	/**
	 * 只返回对应key下的所有id
	 * @param key
	 * @param idPrefix
	 * @return
	 * @throws MessageException
	 */
	public List<String> listRedisKeyId(String key, String idPrefix) throws MessageException {
		if(idPrefix == null || StringUtils.isBlank(idPrefix)) {
			idPrefix = "";
		}
		String keyWithoutId = getKeyWithoutId(key);
		key = getKey(key, idPrefix);
		Set<byte[]> keys = redisManager.keys(StringUtils.getBytes(key + "*"));
		List<String> resultList = new ArrayList<>();

		if (keys != null && keys.size() > 0) {
			for (byte[] keyArr : keys) {
				String keyStr = StringUtils.toString(keyArr);
				if(keyStr != null && keyStr.indexOf(keyWithoutId) != -1) {
					resultList.add(keyStr.substring(keyStr.indexOf(keyWithoutId) + keyWithoutId.length()));
				}
			}
		}
		return resultList;
	}

	/**
	 * 通过key和id删除数据
	 * @param key - 保存数据的key值
	 * @param id - 保存数据的id值
	 * @throws MessageException
	 */
	public void deleteById(String key, String id) throws MessageException {
		key = getKey(key, id);
		redisManager.del(StringUtils.getBytes(key));
	}
	
	/**
	 * 通过key和idPrefix删除相似数据
	 * @param key - 保存数据的key值
	 * @param idPrefix - 保存数据的id值的前部分的值
	 * @throws MessageException
	 */
	public void deleteByIdPrefix(String key, String idPrefix) throws MessageException {
		key = getKey(key, idPrefix);
		Set<byte[]> keys = redisManager.keys(StringUtils.getBytes(key + "*"));
		if (keys != null && keys.size() > 0) {
			for (byte[] keyArr : keys) {
				redisManager.del(keyArr);
			}
		}
	}
	
	/**
	 * 通过key和idPrefix统计数量
	 * @param key
	 * @param idPrefix
	 * @return
	 * @throws MessageException
	 */
	public int countByIdPrefix(String key, String idPrefix) throws MessageException {
		key = getKey(key, idPrefix);
		Set<byte[]> keys = redisManager.keys(StringUtils.getBytes(key + "*"));
		if(keys != null) {
			return keys.size();
		}
		return 0;
	}

	// 延时队列 added for v1.9.6 KNZT-4646
	private static String getDelayedTaskQueueKey(String taskQueueName) {
		return DELAYED_TASK_REDIS_KEY_PREFIX + taskQueueName;
	}

	/**
	 * added for v1.9.6 KNZT-4646
	 * 添加需要指定延迟时间后执行的任务
	 * 注意：实际执行时间依赖扫描任务的定时器频率
	 *
	 * @param taskQueueName
	 * @param taskId
	 * @param delayedSec
	 */
	public void addDelayedTask(String taskQueueName, String taskId, int delayedSec) {
		long executeTimestamp = System.currentTimeMillis() + (delayedSec * 1000L);
		addDelayedTask(taskQueueName, taskId, executeTimestamp);
	}

	/**
	 * added for v1.9.6 KNZT-4646
	 * 添加需要指定时间执行的任务
	 * 注意：实际执行时间依赖扫描任务的定时器频率
	 *
	 * @param taskQueueName
	 * @param taskId
	 * @param timestamp
	 */
	public void addDelayedTask(String taskQueueName, String taskId, long timestamp) {
		String delayedTaskQueueKey = getDelayedTaskQueueKey(taskQueueName);
		redisManager.zadd(StringUtils.getBytes(delayedTaskQueueKey), timestamp, StringUtils.getBytes(taskId));
	}

	/**
	 * added for v1.9.6 KNZT-4646
	 * 获取指定延迟时间内的任务列表
	 *
	 * @param taskQueueName
	 * @param delayedSec
	 * @return
	 */
	public List<Tuple> getDelayedTasksByDelay(String taskQueueName, int delayedSec) {
		long executeTimestamp = System.currentTimeMillis() + (delayedSec * 1000L);
		return getDelayedTasksByEnd(taskQueueName, executeTimestamp);
	}

	/**
	 * added for v1.9.6 KNZT-4646
	 * 获取指定时间内的任务列表
	 *
	 * @param taskQueueName
	 * @param execTimeEnd
	 * @return
	 */
	public List<Tuple> getDelayedTasksByEnd(String taskQueueName, long execTimeEnd) {
		String delayedTaskQueueKey = getDelayedTaskQueueKey(taskQueueName);
		return redisManager.zrangeByScoreWithScores(StringUtils.getBytes(delayedTaskQueueKey), 0, execTimeEnd);
	}

	/**
	 * added for v1.9.6 KNZT-4646
	 * 删除指定延时任务，任务完成后需要调用此方法
	 *
	 * @param taskQueueName
	 * @param taskId
	 */
	public void removeDelayedTask(String taskQueueName, String taskId) {
		String delayedTaskQueueKey = getDelayedTaskQueueKey(taskQueueName);
		redisManager.zrem(StringUtils.getBytes(delayedTaskQueueKey), StringUtils.getBytes(taskId));
	}

	// 通用延时任务-获取commTaskId added for v1.9.6 KNZT-4646
	public static String generateCommTaskId(String taskType, String taskId) {
		return taskType + "_" + taskId;
	}

	// 通用延时任务-获取taskType added for v1.9.6 KNZT-4646
	public static String pickTaskType4CommTask(String commTaskId) {
		List<String> strings = StringUtils.split2List(commTaskId, "_");
		if (strings.size() > 1) {
			return strings.get(0);
		}
		return null;
	}

	// 通用延时任务-获取taskId added for v1.9.6 KNZT-4646
	public static String pickTaskId4CommTask(String commTaskId) {
		List<String> strings = StringUtils.split2List(commTaskId, "_");
		if (strings.size() > 1) {
			return StringUtils.join(strings.subList(1, strings.size()), "_");
		}
		return null;
	}

	/**
	 * updated for v2.0.8 chenbl KNZT-5830
	 * added for v1.9.6 KNZT-4646
	 * updated for v2.1.0 chenbl KNZT-5830
	 * 通用延时任务-添加需要指定延迟时间后执行的任务
	 * 注意：实际执行时间依赖扫描任务的定时器频率
	 *
	 * @param commTaskId
	 * @param delayedSec
	 */
	public void addDelayedCommTask(String commTaskId, int delayedSec) {
		addDelayedTask(Constants.RedisKey.DelayedTaskQueue.QUEUE_NAME_COMMON, commTaskId, delayedSec);
	}

	/**
	 * 通用延时任务-添加需要指定延迟时间后执行的任务
	 * 注意：实际执行时间依赖扫描任务的定时器频率
	 *
	 * @param taskTypeEnum
	 * @param businessId
	 * @param delayedSec
	 * @return
	 */
	public void addDelayedCommTask(CommDelayedTaskTypeEnum taskTypeEnum, String businessId, int delayedSec) {
		addDelayedCommTask(generateCommTaskId(taskTypeEnum.getCode(), businessId), delayedSec);
	}

	/**
	 * updated for v2.0.8 chenbl KNZT-5830
	 * added for v1.9.6 KNZT-4646
	 * updated for v2.1.0 chenbl KNZT-5830
	 * 通用延时任务-添加需要指定时间执行的任务
	 * 注意：实际执行时间依赖扫描任务的定时器频率
	 *
	 * @param commTaskId
	 * @param timestamp
	 */
	public void addDelayedCommTask(String commTaskId, long timestamp) {
		addDelayedTask(Constants.RedisKey.DelayedTaskQueue.QUEUE_NAME_COMMON, commTaskId, timestamp);
	}

	/**
	 * added for lvcy v2.0.8 KNZT-5811
	 * 通用延时任务-添加需要指定时间执行的任务
	 * 注意：实际执行时间依赖扫描任务的定时器频率
	 *
	 * @param taskTypeEnum
	 * @param businessId
	 * @param delayedSec
	 */
	public void addDelayedCommTask(CommDelayedTaskTypeEnum taskTypeEnum, String businessId, long delayedSec) {
		addDelayedCommTask(generateCommTaskId(taskTypeEnum.getCode(), businessId), delayedSec);
	}


	/**
	 * updated for v2.0.8 chenbl KNZT-5830
	 * added for v1.9.6 KNZT-4646
	 * updated for v2.1.0 chenbl KNZT-5830
	 * 通用延时任务-获取指定延迟时间内的任务列表
	 *
	 * @param delayedSec
	 * @return
	 */
	public List<Tuple> getDelayedCommTasksByDelay(int delayedSec) {
		return getDelayedTasksByDelay(Constants.RedisKey.DelayedTaskQueue.QUEUE_NAME_COMMON, delayedSec);
	}

	/**
	 * updated for v2.0.8 chenbl KNZT-5830
	 * added for v1.9.6 KNZT-4646
	 * updated for v2.1.0 chenbl KNZT-5830
	 * 通用延时任务-获取指定时间内的任务列表
	 *
	 * @param execTimeEnd
	 * @return
	 */
	public List<Tuple> getDelayedCommTasksByEnd(long execTimeEnd) {
		return getDelayedTasksByEnd(Constants.RedisKey.DelayedTaskQueue.QUEUE_NAME_COMMON, execTimeEnd);
	}

	/**
	 * updated for v2.0.8 chenbl KNZT-5830
	 * added for v1.9.6 KNZT-4646
	 * updated for v2.1.0 chenbl KNZT-5830
	 * 通用延时任务-删除指定延时任务，任务完成后需要调用此方法
	 *
	 * @param commTaskId
	 */
	public void removeDelayedCommTask(String commTaskId) {
		removeDelayedTask(Constants.RedisKey.DelayedTaskQueue.QUEUE_NAME_COMMON, commTaskId);
	}

	/**
	 * 删除指定延时任务，任务完成后需要调用此方法
	 *
	 * @param taskTypeEnum
	 * @param businessId
	 * @return
	 */
	public void removeDelayedCommTask(CommDelayedTaskTypeEnum taskTypeEnum, String businessId) {
		String commTaskId = generateCommTaskId(taskTypeEnum.getCode(), businessId);
		removeDelayedCommTask(commTaskId);
	}

	private String getKeyWithoutId(String key) {
		return prefix + key + ":";
	}
	
	public static String getKey(String key, String id) {
		return prefix + key + ":" + id;
	}

	/**
	 * added for v2.3.2 fengsw KNZT-7930 批量获取redis中的值
	 * ids查询和结果一一对应，没有数据就是null值
	 * 必须要保证传入的ids顺序不变否则取值会出问题
	 * 
	 * @param key
	 * @param ids
	 * @return
	 */
	public List<String> listStringByKeyAndIds(String key, List<String> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return null;
		}
		List<String> keys = new ArrayList<>();
		for (String id : ids) {
			String key1 = getKey(key, id);
			keys.add(key1);
		}
		return redisManager.mget(keys.toArray(new String[0]));
	}
}
