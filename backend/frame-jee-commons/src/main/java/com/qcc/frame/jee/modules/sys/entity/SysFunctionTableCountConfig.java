package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

//added v6.7.1 KNZT-38 修复保存到sys_comp_info_func_count表计费方式错误的问题
public class SysFunctionTableCountConfig extends DataEntity<SysFunctionTableCountConfig> {

	private String roleEnname; // added for v1.9.8 KNZT-4538
	private String functionTableId;
	private String supportCountStd;

	public String getRoleEnname() {
		return roleEnname;
	}

	public void setRoleEnname(String roleEnname) {
		this.roleEnname = roleEnname;
	}

	public String getFunctionTableId() {
		return functionTableId;
	}

	public void setFunctionTableId(String functionTableId) {
		this.functionTableId = functionTableId;
	}

	public String getSupportCountStd() {
		return supportCountStd;
	}

	public void setSupportCountStd(String supportCountStd) {
		this.supportCountStd = supportCountStd;
	}
}
