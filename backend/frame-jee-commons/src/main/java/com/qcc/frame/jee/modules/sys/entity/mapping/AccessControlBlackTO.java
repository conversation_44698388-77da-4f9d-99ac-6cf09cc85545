package com.qcc.frame.jee.modules.sys.entity.mapping;

public class AccessControlBlackTO {
	private String userId;
	private String loginName;
	private String companyId;
	private String companyName;
	private Integer customerId;
	private String type;
	private String category;
	private int recordCount;
	private int reqTimes;
	private String menuHomeGroupId;

	public String getMenuHomeGroupId() {
		return menuHomeGroupId;
	}
	public void setMenuHomeGroupId(String menuHomeGroupId) {
		this.menuHomeGroupId = menuHomeGroupId;
	}
	public Integer getCustomerId() {
		return customerId;
	}
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getCompanyId() {
		return companyId;
	}
	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}
	public int getRecordCount() {
		return recordCount;
	}
	public void setRecordCount(int recordCount) {
		this.recordCount = recordCount;
	}
	public int getReqTimes() {
		return reqTimes;
	}
	public void setReqTimes(int reqTimes) {
		this.reqTimes = reqTimes;
	}
}
