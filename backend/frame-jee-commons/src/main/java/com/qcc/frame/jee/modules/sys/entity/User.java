package com.qcc.frame.jee.modules.sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.excel.ExcelField;
import com.qcc.frame.jee.commons.persistence.DataEntity;
import com.qcc.frame.jee.commons.utils.Collections3;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 用户Entity
 */
public class User extends DataEntity<User> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 4270415064129175676L;
	private Company company;	// 归属公司
	private String loginName;// 登录名
	private String password;// 密码
	private String no;		// 工号
	private String name;	// 名
	private String lastName; // 姓 added for v2.0.7 chenbl KNZT-6595
	private String email;	// 邮箱
	private String phone;	// 电话
	private String country;	// 电话 added for v2.0.7 chenbl KNZT-6595
	private String mobile;	// 手机
	private String msgReceivedType;
	private String msgReceivedPeriod;
	private Date prevNoticeDate;
	private Date nextNoticeDate;
	private String periodExtendValue;
	private String periodTime;
	private String userType;// 用户类型
	private String loginIp;	// 最后登陆IP
	private Date loginDate;	// 最后登陆日期
	private String loginFlag;	// 是否允许登陆
	private String photo;	// 头像
	private String status;
	private String lang;

	private String oldLoginName;// 原登录名
	private String oldMobile;
	private String newPassword;	// 新密码
	
	private String oldLoginIp;	// 上次登陆IP
	private Date oldLoginDate;	// 上次登陆日期
	
	private Role role;	// 根据角色查询用户条件
	
	private List<Role> roleList = Lists.newArrayList(); // 拥有角色列表
	
	private String compGroupId;//只有通过UserUtils.get方法得到的User对象才会赋值 
	private List<CompGroup> groupList = new ArrayList<CompGroup>();
	private String statusDesc;
	private String dataScope;
	private String msgReceivedTypeRecovery;
	private String encryptUserId; // added for lvcy v2.0.0 KNZT-5142
	private String mfaSecret; // MFA Secret added for v2.0.4 chenbl KNZT-5371


	public User() {
		super();
		this.loginFlag = Global.YES;
	}
	
	public User(String id){
		super(id);
	}

	public User(String id, String loginName){
		super(id);
		this.loginName = loginName;
	}

	public User(Role role){
		super();
		this.role = role;
	}

	public String getMsgReceivedPeriod() {
		return msgReceivedPeriod;
	}

	public void setMsgReceivedPeriod(String msgReceivedPeriod) {
		this.msgReceivedPeriod = msgReceivedPeriod;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getPrevNoticeDate() {
		return prevNoticeDate;
	}

	public void setPrevNoticeDate(Date prevNoticeDate) {
		this.prevNoticeDate = prevNoticeDate;
	}

	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getNextNoticeDate() {
		return nextNoticeDate;
	}

	public void setNextNoticeDate(Date nextNoticeDate) {
		this.nextNoticeDate = nextNoticeDate;
	}


	public String getPeriodExtendValue() {
		return periodExtendValue;
	}

	public void setPeriodExtendValue(String periodExtendValue) {
		this.periodExtendValue = periodExtendValue;
	}

	public String getPeriodTime() {
		return periodTime;
	}

	public void setPeriodTime(String periodTime) {
		this.periodTime = periodTime;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public String getLoginFlag() {
		return loginFlag;
	}

	public void setLoginFlag(String loginFlag) {
		this.loginFlag = loginFlag;
	}

	@ExcelField(title="ID", type=1, align=2, sort=1)
	public String getId() {
		return id;
	}

	@JsonIgnore
	@ExcelField(title="归属公司", align=2, sort=20)
	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	@Length(min=1, max=100, message="登录名长度必须介于 1 和 100 之间")
	@ExcelField(title="登录名", align=2, sort=30)
	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	@JsonIgnore
	@Length(min=1, max=100, message="密码长度必须介于 1 和 100 之间")
	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Length(min=1, max=100, message="姓名长度必须介于 1 和 100 之间")
	@ExcelField(title="姓名", align=2, sort=40)
	public String getName() {
		return name;
	}
	
	@Length(min=1, max=100, message="工号长度必须介于 1 和 100 之间")
	@ExcelField(title="工号", align=2, sort=45)
	public String getNo() {
		return no;
	}

	public void setNo(String no) {
		this.no = no;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	@Email(message="邮箱格式不正确")
	@Length(min=0, max=200, message="邮箱长度必须介于 1 和 200 之间")
	@ExcelField(title="邮箱", align=1, sort=50)
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	@ExcelField(title="电话", align=2, sort=60)
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	@ExcelField(title="手机", align=2, sort=70)
	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	@ExcelField(title="备注", align=1, sort=900)
	public String getRemarks() {
		return remarks;
	}
	
	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	@ExcelField(title="创建时间", type=0, align=1, sort=90)
	public Date getCreateDate() {
		return createDate;
	}

	@ExcelField(title="最后登录IP", type=1, align=1, sort=100)
	public String getLoginIp() {
		return loginIp;
	}

	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone=Constants.DEFAULT_TIMEZONE)
	@ExcelField(title="最后登录日期", type=1, align=1, sort=110)
	public Date getLoginDate() {
		return loginDate;
	}

	public void setLoginDate(Date loginDate) {
		this.loginDate = loginDate;
	}

	public String getOldLoginName() {
		return oldLoginName;
	}

	public void setOldLoginName(String oldLoginName) {
		this.oldLoginName = oldLoginName;
	}

	public String getNewPassword() {
		return newPassword;
	}

	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}

	public String getOldLoginIp() {
		if (oldLoginIp == null){
			return loginIp;
		}
		return oldLoginIp;
	}

	public void setOldLoginIp(String oldLoginIp) {
		this.oldLoginIp = oldLoginIp;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getOldLoginDate() {
		if (oldLoginDate == null){
			return loginDate;
		}
		return oldLoginDate;
	}

	public void setOldLoginDate(Date oldLoginDate) {
		this.oldLoginDate = oldLoginDate;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	@JsonIgnore
	public List<Role> getRoleList() {
		return roleList;
	}
	
	public void setRoleList(List<Role> roleList) {
		this.roleList = roleList;
	}

//	@JsonIgnore
	public List<String> getRoleIdList() {
		List<String> roleIdList = Lists.newArrayList();
		for (Role role : roleList) {
			roleIdList.add(role.getId());
		}
		return roleIdList;
	}

	public void setRoleIdList(List<String> roleIdList) {
		roleList = Lists.newArrayList();
		for (String roleId : roleIdList) {
			if(StringUtils.isNotBlank(roleId)) {
			Role role = new Role();
			role.setId(roleId);
			roleList.add(role);
			}
		}
	}
	
	/**
	 * 用户拥有的角色名称字符串, 多个角色名称用','分隔.
	 */
	public String getRoleNames() {
		return Collections3.extractToString(roleList, "name", ",");
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOldMobile() {
		return oldMobile;
	}

	public void setOldMobile(String oldMobile) {
		this.oldMobile = oldMobile;
	}
	
	

	public String getMsgReceivedType() {
		return msgReceivedType;
	}

	public void setMsgReceivedType(String msgReceivedType) {
		this.msgReceivedType = msgReceivedType;
	}

	@Override
	public String toString() {
		return id;
	}
	/**
	 * 只有通过UserUtils.get方法得到的User对象才会赋值
	 * @return
	 */
	public String getCompGroupId() {
		return compGroupId;
	}

	public void setCompGroupId(String compGroupId) {
		this.compGroupId = compGroupId;
	}
	
	public List<CompGroup> getGroupList() {
		return groupList;
	}
	
	public void setGroupList(List<CompGroup> groupList) {
		this.groupList = groupList;
	}

	public String getLang() {
		return lang;
	}

	public void setLang(String lang) {
		this.lang = lang;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	public String getDataScope() {
		return dataScope;
	}

	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}

	public String getMsgReceivedTypeRecovery() {
		return msgReceivedTypeRecovery;
	}

	public void setMsgReceivedTypeRecovery(String msgReceivedTypeRecovery) {
		this.msgReceivedTypeRecovery = msgReceivedTypeRecovery;
	}

	public String getEncryptUserId() {
		return encryptUserId;
	}

	public void setEncryptUserId(String encryptUserId) {
		this.encryptUserId = encryptUserId;
	}

	public String getMfaSecret() {
		return mfaSecret;
	}

	public void setMfaSecret(String mfaSecret) {
		this.mfaSecret = mfaSecret;
	}
}