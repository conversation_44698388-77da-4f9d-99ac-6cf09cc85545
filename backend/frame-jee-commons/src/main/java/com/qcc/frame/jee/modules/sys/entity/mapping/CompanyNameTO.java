package com.qcc.frame.jee.modules.sys.entity.mapping;

public class CompanyNameTO {
	private String id;
	private String name;
	private String mainLoginName;

	
	
	public String getMainLoginName() {
		return mainLoginName;
	}
	public void setMainLoginName(String mainLoginName) {
		this.mainLoginName = mainLoginName;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	@Override
	public boolean equals(Object obj) {
		if (null == obj) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (!getClass().equals(obj.getClass())) {
            return false;
        }
        CompanyNameTO that = (CompanyNameTO) obj;
        return null == this.getId() ? false : this.getId().equals(that.getId());
	}
	
	
}
