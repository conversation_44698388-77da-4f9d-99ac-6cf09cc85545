package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.dao.SysCompInfoColExtDao;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoColExt;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysCompInfoColExtService extends CrudService<SysCompInfoColExtDao, SysCompInfoColExt> {

	public String getColExtValueByTypeAndKey(String companyId, String extType, String extKey) {
		return dao.getColExtValueByTypeAndKey(companyId, extType, extKey);
	}
	
	public List<SysCompInfoColExt> listColExtByType(String companyId, String extType) {
		return dao.listColExtByType(companyId, extType);
	}

	public SysCompInfoColExt getSysCompInfoColExt(String companyId, String extType, String extKey) {
		return dao.getSysCompInfoColExt(companyId, extType, extKey);
	}

	// added for v2.0.4 chenbl KNZT-5371
	public List<SysCompInfoColExt> listByCompanyIds(List<String> companyIdList, String extType, String extKey) {
		return dao.listByCompanyIds(companyIdList, extType, extKey);
	}

	public void saveColExtValue(String companyId, String extType, String extKey, String extValue) {
		SysCompInfoColExt ext = this.getSysCompInfoColExt(companyId, extType, extKey);
		if(ext != null) {
			ext.setExtValue(extValue);
		} else {
			ext = new SysCompInfoColExt();
			ext.setCompanyId(companyId);
			ext.setExtType(extType);
			ext.setExtKey(extKey);
			ext.setExtValue(extValue);
		}
		this.save(ext);
	}

	// added for v2.0.0 chenbl KNZT-5094
	public void deleteColExtValue(String companyId, String extType, String extKey) {
		SysCompInfoColExt ext = this.getSysCompInfoColExt(companyId, extType, extKey);
		if (ext != null) {
			dao.delete(ext);
		}
	}

	public static String getExtValueFromList(List<SysCompInfoColExt> list, String extType, String extKey) {
		if (CollectionUtils.isEmpty(list) || StringUtils.isAnyBlank(extType, extKey)) {
			return null;
		}
		return list.stream()
				.filter(sysCompInfoColExt -> StringUtils.equals(extType, sysCompInfoColExt.getExtType()) && StringUtils.equals(extKey, sysCompInfoColExt.getExtKey()))
				.map(SysCompInfoColExt::getExtValue).findFirst().orElse(null);
	}
	
	// added for v2.2.8 fengsw KNZT-7656
	public SysCompInfoColExt getByTypeAndKey(String extType, String extKey) {
		return dao.getByTypeAndKey(extType, extKey);
	}

	public List<String> listCompanyIdsWithValue(String extType, String extKey, String extValue) {
		return dao.listCompanyIdsWithValue(extType, extKey, extValue);
	}
}
