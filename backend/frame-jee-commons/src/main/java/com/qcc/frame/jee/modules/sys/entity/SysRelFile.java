package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysRelFile extends DataEntity<SysRelFile> {
	
	private String relId;
	private String relType;
	private String fileUrl;
	private String fileName;
	private String source;

	public enum RelTypeEnum {
		TRIAL("TA"),
		TRIAL_SIGN("TA_S"),
		;
		private String code;
		private RelTypeEnum(String code) {
			this.code = code;
		}
		public String getCode() {
			return code;
		}
	}

	public String getRelId() {
		return relId;
	}
	public void setRelId(String relId) {
		this.relId = relId;
	}
	public String getRelType() {
		return relType;
	}
	public void setRelType(String relType) {
		this.relType = relType;
	}
	public String getFileUrl() {
		return fileUrl;
	}
	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
}
