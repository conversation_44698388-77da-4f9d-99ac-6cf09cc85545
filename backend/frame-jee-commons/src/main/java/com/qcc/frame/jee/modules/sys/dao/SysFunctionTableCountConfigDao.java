package com.qcc.frame.jee.modules.sys.dao;

import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;
import com.qcc.frame.jee.modules.sys.entity.SysFunctionTableCountConfig;
import com.qcc.frame.jee.modules.sys.entity.mapping.SysFunctionTableCountConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface SysFunctionTableCountConfigDao extends CrudDao<SysFunctionTableCountConfig> {
    //added v6.7.1 KNZT-38 修复保存到sys_comp_info_func_count表计费方式错误的问题
    SysFunctionTableCountConfig getFunctionTableCountConfigInfoByFunctionTableId(@Param("functionTableId") String functionTableId);

    List<SysFunctionTableCountConfigPO> listFunctionTableCountConfigInfo();
}
