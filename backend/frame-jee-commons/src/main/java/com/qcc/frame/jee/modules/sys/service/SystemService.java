package com.qcc.frame.jee.modules.sys.service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.condition.SysRoleCondition;
import com.qcc.frame.jee.modules.sys.entity.*;
import com.qcc.frame.jee.modules.sys.form.SysRoleForm;
import com.qcc.frame.jee.modules.sys.model.SysRoleMenu;
import com.qcc.frame.jee.modules.sys.model.SysRoleTypeTO;
//import org.activiti.engine.IdentityService;
//import org.activiti.engine.identity.Group;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.security.Digests;
//import com.qcc.frame.jee.commons.security.shiro.session.SessionDAO;
import com.qcc.frame.jee.commons.service.BaseService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.service.ServiceException;
import com.qcc.frame.jee.commons.web.Servlets;
import com.qcc.frame.jee.modules.sys.dao.MenuDao;
import com.qcc.frame.jee.modules.sys.dao.RoleDao;
import com.qcc.frame.jee.modules.sys.dao.UserDao;
import com.qcc.frame.jee.modules.sys.model.RedisLoginAttemptTO;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;

import javax.annotation.Resource;

/**
 * 系统管理，安全相关实体的管理类,包括用户、角色、菜单.
 */
@Service
public class SystemService extends BaseService {

	public static final String HASH_ALGORITHM = "SHA-1";
	public static final int HASH_INTERATIONS = 1024;
	public static final int SALT_SIZE = 8;

	@Resource
	private UserDao userDao;
	@Resource
	private RoleDao roleDao;
	@Resource
	private MenuDao menuDao;

	@Autowired
	private RedisService redisService;


	/**
	 * 获取用户
	 * @param id
	 * @return
	 */
	public User getUser(String id) {
		return UserUtils.get(id);
	}

	public User getUserAndRole(String id) {
		User user = getUser(id);
		if(user != null) {
			Role role = new Role();
			role.setUser(new User(id));
			user.setRoleList(findRole(role));
		}
		return user;
	}

	/**
	 * 根据登录名获取用户
	 * @param loginName
	 * @return
	 */
	public User getUserByLoginName(String loginName) {
		return UserUtils.getByLoginName(loginName);
	}


	public void checkUserValid4Login(User user) throws MessageException {
		if(Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
			throw new MessageException("err.system.account.inactive");
		} else if(Constants.Sys.USER_STATUS_LOCKED.equals(user.getStatus())) {
			throw new MessageException("err.system.account.locked");
		}

		if(user.getCompany() == null || !StringUtils.equals(user.getCompany().getDelFlag(), "0")) {
			throw new MessageException("err.system.account.company.deleted");
		}

		Date curDt = DateUtils.getCurDate();
		if(user.getCompany().getBeginDate() != null) {
			if(user.getCompany().getBeginDate().after(curDt)) {
				throw new MessageException("err.system.account.company.not.in.date.range");
			}
		}

		if(user.getCompany().getEndDate() != null) {
			if(user.getCompany().getEndDate().before(curDt)) {
				throw new MessageException("err.system.account.company.not.in.date.range");
			}
		}

	}

	public Page<User> findUser(Page<User> page, User user) {
		// 生成数据权限过滤条件（dsf为dataScopeFilter的简写，在xml中使用 ${sqlMap.dsf}调用权限SQL）
//		user.getSqlMap().put("dsf", dataScopeFilter(user.getCurrentUser(), "o", "a"));
		// 设置分页参数
		user.setPage(page);
		// 执行分页查询
		page.setList(userDao.findList(user));
		return page;
	}

	public int findUserCountByCompanyId(String companyId) {
		if(StringUtils.isBlank(companyId)) {
			return 0;
		}
		User user = new User();
		user.setCompany(new Company(companyId));
		long count = userDao.findAllCount(user);
		return Integer.parseInt(count+"");
	}

	public List<User> findUserByCompanyId4Select(String companyId) {
		User user = new User();
		user.setCompany(new Company(companyId));
		return userDao.findUserByCompanyId4Select(user);
	}

	/**
	 * 无分页查询人员列表
	 * @param user
	 * @return
	 */
	public List<User> findUser(User user){
		// 生成数据权限过滤条件（dsf为dataScopeFilter的简写，在xml中使用 ${sqlMap.dsf}调用权限SQL）
//		user.getSqlMap().put("dsf", dataScopeFilter(user.getCurrentUser(), "o", "a"));
		List<User> list = userDao.findList(user);
		return list;
	}

	public void saveUserInfo(User user) {
		if (StringUtils.isBlank(user.getId())){
			user.preInsert();
			userDao.insert(user);
		}else{
			// 清除原用户机构用户缓存
//			User oldUser = userDao.get(user.getId());
//			if (oldUser.getOffice() != null && oldUser.getOffice().getId() != null){
//				CacheUtils.remove(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + oldUser.getOffice().getId());
//			}
			// 更新用户数据
			user.preUpdate();
			userDao.update(user);
		}
	}

	//@Transactional(readOnly = false)
	public void saveUser(User user) {
		if (StringUtils.isBlank(user.getId())){
			user.preInsert();
			userDao.insert(user);
		}else{
			// 清除原用户机构用户缓存
//			User oldUser = userDao.get(user.getId());
//			if (oldUser.getOffice() != null && oldUser.getOffice().getId() != null){
//				CacheUtils.remove(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + oldUser.getOffice().getId());
//			}
			// 更新用户数据
			user.preUpdate();
			userDao.update(user);
		}
		if (StringUtils.isNotBlank(user.getId())){
			// 更新用户与角色关联
			userDao.deleteUserRole(user);
			if (user.getRoleList() != null && user.getRoleList().size() > 0){
				userDao.insertUserRole(user);
			}else{
				throw new ServiceException(user.getLoginName() + "没有设置角色！");
			}

		}
	}

	public void insertUserRole(String userId, List<String> roleIdList) {
		User user = new User(userId);
		for(String roleId : roleIdList) {
			if(StringUtils.isNotBlank(roleId)) {
				Role role = new Role();
				role.setId(roleId);
				user.getRoleList().add(role);
			}
		}
		userDao.insertUserRole(user);
	}

	public void deleteUserRole(String userId) {
		User user = new User(userId);
		userDao.deleteUserRole(user);
	}

	public void deleteUserByCompanyId(String companyId) {
		User user = new User();
		user.setCompany(new Company(companyId));
		user.preUpdate();
		userDao.deleteUserByCompanyId(user);
	}

	public void updateUser(User user) {
		user.preUpdate();
		userDao.update(user);
		// 清除用户缓存
//		UserUtils.clearCache(user);
//		// 清除权限缓存
//		systemRealm.clearAllCachedAuthorizationInfo();
	}

	//@Transactional(readOnly = false)
	public void deleteUser(User user) {
		user.preUpdate();
		userDao.delete(user);
	}

	//@Transactional(readOnly = false)
	public void updatePasswordById(String id, String loginName, String newPassword) {
		User user = new User(id);
		user.setPassword(entryptPassword(newPassword));
		userDao.updatePasswordById(user);
		// 清除用户缓存
		user.setLoginName(loginName);
	}

	//@Transactional(readOnly = false)
	public void updateUserLoginInfo(User user) {
		// 保存上次登录信息
		user.setOldLoginIp(user.getLoginIp());
		user.setOldLoginDate(user.getLoginDate());
		// 更新本次登录信息
		user.setLoginIp(StringUtils.getRemoteAddr(Servlets.getRequest()));
		user.setLoginDate(new Date());
		userDao.updateLoginInfo(user);
	}

	/**
	 * 生成安全的密码，生成随机的16位salt并经过1024次 sha-1 hash
	 */
	public static String entryptPassword(String plainPassword) {
		String plain = Encodes.unescapeHtml(plainPassword);
		byte[] salt = Digests.generateSalt(SALT_SIZE);
		byte[] hashPassword = Digests.sha1(plain.getBytes(), salt, HASH_INTERATIONS);
		return Encodes.encodeHex(salt)+Encodes.encodeHex(hashPassword);
	}

	/**
	 * 验证密码
	 * @param plainPassword 明文密码
	 * @param password 密文密码
	 * @return 验证成功返回true
	 */
	public static boolean validatePassword(String plainPassword, String password) {
		String plain = Encodes.unescapeHtml(plainPassword);
		byte[] salt = Encodes.decodeHex(password.substring(0,16));
		byte[] hashPassword = Digests.sha1(plain.getBytes(), salt, HASH_INTERATIONS);
		return password.equals(Encodes.encodeHex(salt)+Encodes.encodeHex(hashPassword));
	}

	//-- Role Service --//

	public Role getRole(String id) {
		return roleDao.get(id);
	}

	public Role getRoleByName(String name) {
		Role r = new Role();
		r.setName(name);
		return roleDao.getByName(r);
	}

	public Role getRoleByEnname(String enname) {
		Role r = new Role();
		r.setEnname(enname);
		return roleDao.getByEnname(r);
	}

	public List<Role> findRole(Role role){
		return roleDao.findList(role);
	}

	public List<Role> findUserRole(String userId, String roleType){
		Role role = new Role();
		role.setUser(new User(userId));
		List<Role> roleList = roleDao.findList(role);
		if(StringUtils.isBlank(roleType)) {
			return roleList;
		}

		List<Role> resultList = new ArrayList<>();
		if(roleList != null) {
			for(Role r : roleList) {
				if(StringUtils.equalsIgnoreCase(roleType, r.getRoleType())) {
					resultList.add(r);
				}
			}
		}
		return resultList;
	}

	public List<Role> findRoleByRoleIdList(List<String> roleIdList) {
		if(roleIdList == null || roleIdList.isEmpty()) {
			return new ArrayList<>();
		}
		Role role = new Role();
		role.setRoleIdList(roleIdList);
		List<Role> roleList = roleDao.findListByIdList(role);

		return roleList;
	}

	public List<Role> findRole(List<String> moduleList, String roleType) {
		Role role = new Role();
		if(moduleList == null || moduleList.isEmpty()) {
			return new ArrayList<>();
		}
		role.setModuleList(moduleList);
		role.setRoleType(roleType);
		return roleDao.findListByModuleListAndRoleType(role);
	}

	public Page<Role> findRole(Page<Role> page,Role role){
//		role.getSqlMap().put("dsf", dataScopeFilter(role.getCurrentUser(), "o", "a"));
		// 设置分页参数
		role.setPage(page);
		// 执行分页查询
		page.setList(roleDao.findListPage(role));
		return page;
	}

	// added for v1.9.8 KNZT-4538
	public void deleteSysUserRoleWithRoleId(String roleId){
		roleDao.deleteSysUserRoleWithRoleId(roleId);
	}

	// added for v1.9.8 KNZT-4538
	public void deleteRole(String roleId){
		roleDao.deleteRole(roleId);
	}

	//-- Menu Service --//

	public Menu getMenu(String id) {
		return menuDao.get(id);
	}

	public boolean hasPrefixEngNameRole(List<String> roleIdList, String prefixEngName) {
		if(roleIdList == null || roleIdList.size() == 0) {
			return false;
		}
		int count = roleDao.countPrefixEngNameRole(roleIdList, prefixEngName);
		return count > 0;
	}

	public int getLoginAttemptCount() {
		String count = Global.getConfig("login.attempt.count");
		if(StringUtils.isNotBlank(count)) {
			return Integer.parseInt(count);
		}
		return 10;
	}



	/**
	 * 检查账号登录出错次数
	 * @param loginName
	 * @throws MessageException
	 */
//	public void checkFailCount4AccountLogin(String loginName) throws MessageException {
//		try {
//			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, loginName.trim());
//			RedisLoginAttemptTO loginAtmpTO = null;
//			if(StringUtils.isNotBlank(jsonStr)) {
//				JSONObject jsonObj = JsonUtils.parseObject(jsonStr);
//				if(jsonObj != null) {
//					loginAtmpTO = jsonObj.toJavaObject(RedisLoginAttemptTO.class);
//				}
//			}
//			if(loginAtmpTO == null) {
//				loginAtmpTO = new RedisLoginAttemptTO();
//			}
//
//			String curDate = DateUtils.getDate();
//			if(StringUtils.equals(curDate, DateUtils.getDateFromTs(loginAtmpTO.getAcctLoginAtmpTs())) && loginAtmpTO.getAcctLoginAtmpCount() >= getLoginAttemptCount()) {
//				throw new MessageException("您今天账号登录出错超过"+getLoginAttemptCount()+"次, 请尝试使用动态登录或忘记密码重置!");
//			}
//		} catch(MessageException e) {
//			throw e;
//		} catch(Exception e) {
//			logger.error("", e);
//		}
//	}
//	/**
//	 * 累加账号登录出错次数, 返回剩余次数
//	 * @param loginName
//	 * @return
//	 * @throws MessageException
//	 */
//	public int addFailCount4AccountLogin(String loginName) throws MessageException {
//		try {
//			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, loginName);
//			RedisLoginAttemptTO loginAtmpTO = null;
//			if(StringUtils.isNotBlank(jsonStr)) {
//				JSONObject jsonObj = JsonUtils.parseObject(jsonStr);
//				if(jsonObj != null) {
//					loginAtmpTO = jsonObj.toJavaObject(RedisLoginAttemptTO.class);
//				}
//			}
//			if(loginAtmpTO == null) {
//				loginAtmpTO = new RedisLoginAttemptTO();
//			}
//
//			String curDate = DateUtils.getDate();
//			if(StringUtils.equals(curDate, DateUtils.getDateFromTs(loginAtmpTO.getAcctLoginAtmpTs()))) {
//				loginAtmpTO.setAcctLoginAtmpTs(System.currentTimeMillis());
//				loginAtmpTO.setAcctLoginAtmpCount(loginAtmpTO.getAcctLoginAtmpCount() + 1);
//			} else {
//				loginAtmpTO.setAcctLoginAtmpTs(System.currentTimeMillis());
//				loginAtmpTO.setAcctLoginAtmpCount(1);
//				loginAtmpTO.setMobileLoginAtmpTs(System.currentTimeMillis());
//				loginAtmpTO.setMobileLoginAtmpCount(0);
//			}
//
//			redisService.saveString(Constants.RedisKey.LOGIN_ATTEMPT, loginName, JSON.toJSONString(loginAtmpTO));
//			int remainder = getLoginAttemptCount() - loginAtmpTO.getAcctLoginAtmpCount();
//			if(remainder <= 0) {
//				throw new MessageException("您今天账号登录出错超过"+getLoginAttemptCount()+"次, 请尝试使用动态登录或忘记密码重置!");
//			}
//			return remainder;
//		} catch(MessageException e) {
//			throw e;
//		} catch(Exception e) {
//			logger.error("", e);
//		}
//		return -1;
//	}
	/**
	 * 检查动态登录出错次数
	 * @param loginName
	 * @throws MessageException
	 */
	/*public void checkFailCount4MobileLogin(String loginName) throws MessageException {
		try {
			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, loginName.trim());
			RedisLoginAttemptTO loginAtmpTO = null;
			if(StringUtils.isNotBlank(jsonStr)) {
				JSONObject jsonObj = JsonUtils.parseObject(jsonStr);
				if(jsonObj != null) {
					loginAtmpTO = jsonObj.toJavaObject(RedisLoginAttemptTO.class);
				}
			}
			if(loginAtmpTO == null) {
				loginAtmpTO = new RedisLoginAttemptTO();
			}
			String curDate = DateUtils.getDate();
			if(StringUtils.equals(curDate, DateUtils.getDateFromTs(loginAtmpTO.getMobileLoginAtmpTs())) && loginAtmpTO.getMobileLoginAtmpCount() >= getLoginAttemptCount()) {
				throw new MessageException("您今天动态登录出错超过"+getLoginAttemptCount()+"次, 请尝试使用账号登录或忘记密码重置!");
			}
		} catch(MessageException e) {
			throw e;
		} catch(Exception e) {
			logger.error("", e);
		}
	}*/

	/**
	 * 累加动态登录出错次数, 返回剩余次数
	 * @param loginName
	 * @return
	 */
	/*public int addFailCount4MobileLogin(String loginName) throws MessageException {
		try {
			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, loginName);
			RedisLoginAttemptTO loginAtmpTO = null;
			if(StringUtils.isNotBlank(jsonStr)) {
				JSONObject jsonObj = JsonUtils.parseObject(jsonStr);
				if(jsonObj != null) {
					loginAtmpTO = jsonObj.toJavaObject(RedisLoginAttemptTO.class);
				}
			}
			if(loginAtmpTO == null) {
				loginAtmpTO = new RedisLoginAttemptTO();
			}

			String curDate = DateUtils.getDate();
			if(StringUtils.equals(curDate, DateUtils.getDateFromTs(loginAtmpTO.getMobileLoginAtmpTs()))) {
				loginAtmpTO.setMobileLoginAtmpTs(System.currentTimeMillis());
				loginAtmpTO.setMobileLoginAtmpCount(loginAtmpTO.getMobileLoginAtmpCount() + 1);
			} else {
				loginAtmpTO.setMobileLoginAtmpTs(System.currentTimeMillis());
				loginAtmpTO.setMobileLoginAtmpCount(1);
				loginAtmpTO.setAcctLoginAtmpTs(System.currentTimeMillis());
				loginAtmpTO.setAcctLoginAtmpCount(0);
			}
			redisService.saveString(Constants.RedisKey.LOGIN_ATTEMPT, loginName, JSON.toJSONString(loginAtmpTO));
			int remainder = getLoginAttemptCount() - loginAtmpTO.getMobileLoginAtmpCount();
			if(remainder <= 0) {
				throw new MessageException("您今天动态登录出错超过"+getLoginAttemptCount()+"次, 请尝试使用账号登录或忘记密码重置!");
			}
			return remainder;
		} catch(MessageException e) {
			throw e;
		} catch(Exception e) {
			logger.error("", e);
		}
		return -1;
	}*/

	public void resetFailCount4Login(String loginName) {
		try {
			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, loginName);
			if(StringUtils.isNotBlank(jsonStr)) {
				redisService.deleteById(Constants.RedisKey.LOGIN_ATTEMPT, loginName);
			}
		} catch(Exception e) {
			logger.error("", e);
		}
	}

	// updated for v2.0.8 ZS-480 【角色管理】查询所有角色
	public List<Role> getAllRoleList() {
		return roleDao.getAllRoleList();
	}

	public List<SysMenu> getAllMenuList() {
		return menuDao.getAllMenuList();
	}

	public List<Menu> getMenuListByRoleId(String roleId) {
		return menuDao.findByRoleIdList(Collections.singletonList(roleId));
	}

	/**
	 * 获取角色类型字典项
	 * updated for V2.1.0 ZS-536【产业招商云平台II】【WEB】-优化-权限管理优化
	 * 过滤平台管理员字段项 1-平台管理员 10-用户管理员 20-普通用户 value-label
	 *
	 * @return List<SysRoleTypeTo>
	 */
	public List<SysRoleTypeTO> getRoleTypeDict() {
		List<SysRoleTypeTO> roleType = roleDao.getSysRoleDictByType("sys_role_type");
		return roleType.stream().filter(item->!"1".equals(item.getValue())).collect(Collectors.toList());
	}

	/**
	 * 删除角色
	 * 先校验该角色是否在使用中
	 * updated for V2.1.0 ZS-536【产业招商云平台II】【WEB】-优化-权限管理优化
	 * 删除的菜单，如果其他角色也绑定了这个菜单，如果存在两个角色同时分配给了某个公司，
	 * 则需要判断公司下是否有自定义的菜单，也包含了这个菜单，没有，则删除角色菜单绑定关系
	 *
	 * @param ids
	 * @throws MessageException
	 */
	/*public void deleteRoleInfo(List<String> ids) throws MessageException {
		if (ids != null && ids.size() > 0) {
			if (checkRoleIdIsContainsUnDeleteRoles(ids)) {
				throw new MessageException("msg:需要删除的角色中存在默认角色，不能删除！");
			}
			for (String roleId : ids) {
				List<String> menuIdList = roleDao.getSysRoleMenuIdListByRoleId(roleId); // 先获取要删除的角色绑定的菜单信息
				roleDao.deleteRoleMenu(new Role(roleId));// 删除角色与菜单的绑定关系
				roleDao.deleteRole(roleId); // 删除角色
				roleDao.deleteSysUserRoleWithRoleId(roleId); // 删除该角色的用户角色绑定关系
				if (UserUtils.isSuperManager()) {
					for (String menuId : menuIdList) {
						List<String> mainUserIds = menuDao.getMainUserIdByRoleAndMenuId(menuId);
						for (String mainUserId : mainUserIds) {
							int count = menuDao.countRoleNumByRoleIdAndMenuId(roleId, menuId, mainUserId);
							if (count > 0) {
								menuDao.deleteSysRoleMenuByRoleIdAndMainUserId(menuId, mainUserId);
							}
						}
					}
				}
			}
		} else {
			throw new MessageException("msg:数据为空，请检查数据是否正确");
		}
	}*/

	/**
	 * added for V2.0.9 ZS-523【产业招商云平台II】【WEB】-优化-角色管理，增加多层菜单的树形结构支持
	 * 校验是否包含了父节点，当前系统中配置角色子菜单需要有父菜单信息，否则无法渲染正确的角色下菜单信息
	 *
	 * @param menuIdList
	 * @return
	 */
	public boolean checkIsSelectedFatherNode(List<String> menuIdList) {
		// 数据库中sys_menu菜单表中，parentsIds中包含 "0,1"这个两个虚位顶层菜单，需要去除，
		String[] needRemove = new String[]{"0", "1"};
		List<String> parentIds = getParentIdsByMenuId(menuIdList);
		HashSet<String> parentIdSet = new HashSet<>();
		for (String parentId : parentIds) {
			String[] strings = parentId.substring(0, parentId.lastIndexOf(",")).split(",");
			parentIdSet.addAll(Arrays.asList(strings));
		}
		parentIdSet.removeAll(Arrays.asList(needRemove));
		for (String parentId : parentIdSet) {
			if (!menuIdList.contains(parentId)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * added for V2.0.9 ZS-523【产业招商云平台II】【WEB】-优化-角色管理，增加多层菜单的树形结构支持
	 * 根据当前菜单id 查询所有父节点id（相同parents_ids去重）
	 *
	 * @param menuIdList List<String>
	 * @return List<String>
	 */
	public List<String> getParentIdsByMenuId(List<String> menuIdList) {
		return menuDao.getParentIdsByMenuId(menuIdList);
	}

	/**
	 * updated for V2.0.9 ZS-523【产业招商云平台II】【WEB】-优化-角色管理，增加多层菜单的树形结构支持 抽取公共方法
	 * 根据角色id去获取获取角色下的菜单数据，渲染成菜单树，并标记角色绑定的菜单
	 * updated for V2.1.0 ZS-536【产业招商云平台II】【WEB】-优化-权限管理优化，优化查询角色对应的菜单树
	 *
	 * @param roleId 角色id
	 * @return SysMenu 菜单树
	 */
	public SysMenu getRoleMenuTreeByRoleId(String roleId) {
		SysMenu menuTree = new SysMenu();
		String rootMenuId = Global.getConfig("qcc.menu.root");
		menuTree.setId(rootMenuId);
		List<SysMenu> menus;
		if(UserUtils.isSuperManager()){
			// 获取所有菜单
			menus = this.getAllMenuList();
		}else {
			String companyId = UserUtils.getUserCompanyId();
			// 排除掉用户主账号的角色菜单，不做展示
			menus = this.getCompMenuList(companyId);
		}
		// 获取角色对应的菜单
		List<Menu> roleMenu = this.getMenuListByRoleId(roleId);
		if (roleMenu != null && roleMenu.size() > 0) {
			Map<String, List<Menu>> listMap = roleMenu.stream().collect(Collectors.groupingBy(Menu::getId));
			for (SysMenu sysMenu : menus) {
				// 给角色下绑定的菜单加上选中状态
				if (listMap.containsKey(sysMenu.getId())) {
					sysMenu.setSelected("1");
				}
			}
		}
		this.mapToTreeNodes(menuTree, menus);
		return menuTree;
	}

	/**
	 * updated for V2.0.9 ZS-523【产业招商云平台II】【WEB】-优化-角色管理，增加多层菜单的树形结构支持
	 * 将菜单数据渲染成树状结构
	 * 公共方法优化 将渲染菜单树的方法放到service层
	 *
	 * @param root
	 * @param menus
	 */
	public void mapToTreeNodes(SysMenu root, List<SysMenu> menus) {
		for (SysMenu menu : menus) {
			if (StringUtils.equals(menu.getParentId(), root.getId())) {
				root.getChildren().add(menu);
			}
		}

		if (!root.getChildren().isEmpty()) {
			for (SysMenu child : root.getChildren()) {
				mapToTreeNodes(child, menus);
			}
		}
	}

	/**
	 * added for V2.1.0 ZS-536【产业招商云平台II】【WEB】-优化-权限管理优化，保存或更新角色信息
	 *
	 * @param form
	 * @throws MessageException
	 */
	/*public void saveCompanyRoleInfo(SysRoleForm form) throws MessageException {
		boolean saveFlag = checkRoleInfoBeforeSave(form);
		if (saveFlag) {
			String companyId = UserUtils.getUserCompanyId();
			List<String> menuIdList = form.getMenuIdList();
			if (StringUtils.isBlank(form.getId())) {
				Role role = new Role();
				role.setEnname(form.getEnname());
				role.setRemarks(form.getRemarks());
				role.setName(form.getName());
				role.setCompanyId(companyId);
				role.setRoleType(form.getRoleType());
				role.setModule("20");
				role.setId(IdGenUtil.uuid());
				role.setIsNewRecord(true);
				role.setUseable("1");
				role.setDelFlag("0");
				role.setCreateBy(new User(UserUtils.getUserId()));
				role.setCreateDate(new Date());
				role.setUpdateBy(role.getCreateBy());
				role.setUpdateDate(role.getCreateDate());
				roleDao.insert(role);
				saveRoleMenuInfo(role.getId(), menuIdList);
			} else {
				Role role = roleDao.get(form.getId());
				role.setEnname(form.getEnname());
				role.setRemarks(form.getRemarks());
				role.setName(form.getName());
				role.setRoleType(form.getRoleType());
				role.setUpdateBy(new User(UserUtils.getUserId()));
				role.setUpdateDate(new Date());
				roleDao.update(role);
				saveRoleMenuInfo(role.getId(), menuIdList);
			}
		}
	}*/

	/**
	 * added for V2.1.0 ZS-536【产业招商云平台II】【WEB】-优化-权限管理优化，保存或编辑角色前的公共校验
	 * 1.角色表单数据不为空
	 * 2.角色绑定的菜单、角色英文名、角色名称、角色类型不能为空,且角色类型只能为20-普通用户
	 * 3.角色英文名不能超过256长度
	 * 4.角色英文名格式为字母和下划线
	 * 5.角色名不能重复
	 * 6.角色绑定的菜单完整性校验：勾选了子菜单，对应的父菜单也必须勾选
	 * 7.检查用户管理员编辑的角色下绑定的菜单是否在该公司的默认角色范围内
	 * @param form
	 * @return boolean
	 * @throws MessageException
	 */
	/*public boolean checkRoleInfoBeforeSave(SysRoleForm form) throws MessageException {
		if (Objects.isNull(form)) {
			logger.error("form is null");
			throw new MessageException("msg:数据不能为空");
		}
		List<String> menuIdList = form.getMenuIdList();
		if (menuIdList == null || menuIdList.size() <= 0 || StringUtils.isAnyBlank(form.getEnname(), form.getName(), form.getRoleType()) || !"20".equals(form.getRoleType())) {
			throw new MessageException("msg:参数不准确，请检查参数");
		}

		// 限定英文名长度256
		if (form.getEnname().length() > 256) {
			throw new MessageException("msg:英文名长度不正确，长度不超过256字符");
		}

		// 判断英文名称格式
		Pattern pattern = Pattern.compile("^[A-Za-z_]+$");
		Matcher matcher = pattern.matcher(form.getEnname());
		if (!matcher.matches()) {
			logger.info("角色名称-{}，对应的英文名-{}格式不规范", form.getName(), form.getEnname());
			throw new MessageException("msg:角色英文名格式不正确，只允许字母和下划线!");
		}

		// 判断是否存在同名角色情况
		String companyId = UserUtils.getUserCompanyId();
		Role roleByName = roleDao.getRoleByNameAndCompanyId(companyId, form.getName());
		if (Objects.nonNull(roleByName) && !roleByName.getId().equals(form.getId())) {
			logger.info("新增角色名-{}的角色时，存在同名角色", roleByName.getName());
			throw new MessageException("msg:该角色名已经存在!");
		}

		// 角色对应的菜单需要判断是否完整，即子菜单需要有父菜单，否则无法渲染该角色的菜单内容。
		boolean flag = checkIsSelectedFatherNode(menuIdList);
		if (!flag) {
			throw new MessageException("msg:菜单权限配置不正确，缺少父节点菜单!");
		}

		// 检查用户管理员编辑的角色下绑定的菜单是否在该公司的默认角色范围内
		String rootMenuId = Global.getConfig("qcc.menu.root");
		menuIdList.remove(rootMenuId);
		boolean menuIdFlag = checkCompanyRoleMenuIsCorrect(menuIdList, companyId);
		if(!menuIdFlag){
			logger.error("需要保存的角色对应的菜单列表数据不准确，存在未分配给公司的菜单信息");
			throw new MessageException("msg:菜单列表数据不准确,角色菜单保存失败！");
		}
		return true;
	}*/

	/**
	 * 检查用户管理员编辑的角色下绑定的菜单是否在该公司的默认角色范围内
	 *
	 * @param menuIdList-菜单集合
	 * @param companyId-公司Id
	 * @return boolean
	 */
	public boolean checkCompanyRoleMenuIsCorrect(List<String> menuIdList, String companyId) {
		if (menuIdList == null || menuIdList.size() <= 0 || StringUtils.isBlank(companyId)) return false;
		List<String> compMenuIdList = roleDao.getMenuIdForCompany(companyId);
		if (compMenuIdList != null && compMenuIdList.size() > 0) {
			for (String menuId : menuIdList) {
				// 判断是否有配置的菜单不在租户角色菜单范围内的情况
				if (!compMenuIdList.contains(menuId)) {
					return false;
				}
			}
		} else {
			return false;
		}
		return true;
	}

	/**
	 * added for V2.1.0 ZS-536【产业招商云平台II】【WEB】-优化-权限管理优化
	 * 用户管理员删除自定义角色，需要校验是否存在 默认角色被删除的情况，默认角色不允许删除
	 *
	 * @param roleIdList
	 * @throws MessageException
	 */
	/*public void deleteCompRoleInfo(List<String> roleIdList) throws MessageException{
		if(!UserUtils.isSuperManager()) {
			if (roleIdList != null && roleIdList.size() > 0) {
				String companyId = UserUtils.getUserCompanyId();
				if (checkRoleIdIsCompDefaultRole(companyId, roleIdList)) {
					throw new MessageException("msg:需要删除的角色中存在默认角色，不能删除！");
				}
				for (String roleId : roleIdList) {
					roleDao.deleteRoleMenu(new Role(roleId)); // 删除角色与菜单的绑定关系
					roleDao.deleteRole(roleId); // 删除角色
					roleDao.deleteCompSysUserRole(companyId, roleId); // 删除分配给公司下用户角色的绑定关系
				}
			} else {
				throw new MessageException("msg:数据为空，请检查数据是否正确");
			}
		} else {
			throw new MessageException("msg:平台管理员不得删除租户下创建的角色！");
		}
	}*/

	/**
	 * 校验角色集合是否包含平台管理员分配给公司租户的默认角色
	 *
	 * @param roleIdList
	 * @return
	 * @throws MessageException
	 */
	/*public boolean checkRoleIdIsCompDefaultRole(String companyId, List<String> roleIdList) throws MessageException {
		List<String> defaultRoles = roleDao.getDefaultRoleIdsForCompany(companyId);
		if(defaultRoles == null || defaultRoles.size() == 0){
			throw new MessageException("msg:用户下的角色列表不存在！");
		}
		for (String roleId : roleIdList) {
			if(defaultRoles.contains(roleId)){
				return true;
			}
		}
		return false;
	}*/

	/**
	 * 校验角色id是否包含了不能删除的角色id
	 *
	 * @param roleIdList
	 * @return
	 */
	public boolean checkRoleIdIsContainsUnDeleteRoles(List<String> roleIdList) throws MessageException {
		// 如果没有配置不可删除角色，取空字串
		String unDeleteRole = Global.getConfigDefault("qcc.menu.unDelete","");
		List<String> defaultRoles = Arrays.asList(unDeleteRole.split(","));
		for (String roleId : roleIdList) {
			if(defaultRoles.contains(roleId)){
				return true;
			}
		}
		return false;
	}


	/**
	 * 获取租户下的所有角色对应的菜单集合
	 *
	 * @param companyId
	 * @return
	 */
	public List<SysMenu> getCompMenuList(String companyId) {
		return menuDao.getCompMenuList(companyId);
	}
}
