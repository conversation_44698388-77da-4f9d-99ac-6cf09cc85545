package com.qcc.frame.jee.modules.sys.utils;

import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompShowCreditEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.company.CompCategoryEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.BaseService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.utils.WebContextHolder;
import com.qcc.frame.jee.modules.sys.entity.*;
import com.qcc.frame.jee.modules.sys.model.UserCommInfoTO;
import com.qcc.frame.jee.modules.sys.model.saas.RedisSaasMenuTO;
import com.qcc.frame.jee.modules.sys.model.saas.RedisSaasRoleTO;
import com.qcc.frame.jee.modules.sys.service.RedisUserSessionService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 用户工具类
 */
public class UserUtils {
	protected static Logger logger = LoggerFactory.getLogger(UserUtils.class);

	private static UserService userService = SpringContextHolder.getBean(UserService.class);
	private static RedisUserSessionService redisUserSessionService = SpringContextHolder.getBean(RedisUserSessionService.class);

	public static final String CACHE_ROLE_LIST = "roleList";
	public static final String CACHE_MENU_LIST = "menuList";

	
	/**
	 * 根据ID获取用户
	 * @param id
	 * @return 取不到返回null
	 */
	public static User get(String id){
		User user = null;//(User)CacheUtils.get(USER_CACHE, USER_CACHE_ID_ + id);
		if (user ==  null){
			user = userService.get(id);
			if (user == null){
				return null;
			}
			user.setCompGroupId(userService.getCompGroupIdByUserId(id));
//			if(roleDao == null) {
//				logger.error("roleDao is null with user id=" + id);
//			}
			user.setRoleList(userService.findRole(new Role(user)));
//			CacheUtils.put(USER_CACHE, USER_CACHE_ID_ + user.getId(), user);
//			CacheUtils.put(USER_CACHE, USER_CACHE_LOGIN_NAME_ + user.getLoginName(), user);
		}
		return user;
	}
	
	public static boolean enableApi() {
		User user = getUser();
		if(user != null && user.getCompany() != null && user.getCompany().getEnableApi() != null && user.getCompany().getEnableApi()) {
			return true;
		}
		return false;
	}
	
	/**
	 * 根据登录名获取用户
	 * @param loginName
	 * @return 取不到返回null
	 */
	public static User getByLoginName(String loginName){
		User user = null;
		if (user == null){
			user = userService.getByLoginName(new User(null, loginName));
			if (user == null){
				return null;
			}
			user.setRoleList(userService.findRole(new Role(user)));
		}
		return user;
	}

	
	/**
	 * 获取当前用户
	 * @return 取不到返回 new User()
	 */
	public static User getUser(){
		User user_ = WebContextHolder.getCurrentUser();
		if(user_ != null) {
			return user_;
		}

		return new User();
	}
	
	
	public static UserCommInfoTO getUserCommInfo(){
		UserCommInfoTO userCommInfoTO = new UserCommInfoTO();
		User user = getUser();
		if(user != null && user.getCompany() != null) {
			userCommInfoTO.setUserId(getUser().getId());
			userCommInfoTO.setCompanyId(user.getCompany().getId());
			userCommInfoTO.setCompGroupId(getUser().getCompGroupId());
			userCommInfoTO.setDataScope(user.getDataScope());
			userCommInfoTO.setType(user.getCompany().getType());
		}
		return userCommInfoTO;
	}
	
	public static String getUserLang() {
		User user = getUser();
		if(StringUtils.isBlank(user.getLang())) {
			return Constants.Sys.USER_LANG_CN;
		}
		return user.getLang();
	}
	
	public static String getUserId(){
		return getUser().getId();
	}
	
	public static String getUserLoginName() {
		return getUser().getLoginName();
	}
	
	public static String getUserCompanyId() {
		if("job".equals(Global.getConfig("application.type"))) {
			return WebContextHolder.getJobCompanyId();
		}
		User user = getUser();
		if(user != null && user.getCompany() != null) {
			return user.getCompany().getId() != null ? user.getCompany().getId() : "";
		}
		return "";
	}

	public static String getUserAcctType() {
		User user = getUser();
		if(user != null && user.getCompany() != null) {
			return user.getCompany().getType() + "";
		}
		return "1";
	}
    
    public static long getUserEndDate() {
        User user = getUser();
        if(user != null && user.getCompany() != null && user.getCompany().getEndDate() != null) {
            return user.getCompany().getEndDate().getTime() / 1000;
        }
        return 0;
    }
	
	
	public static boolean isSignedUser() {
		User user = getUser();
		if(user != null && user.getCompany() != null && user.getCompany().getType() == 2) {
			return true;
		}
		return false;
	}

	public static String getCompanyMainUser() {
		User user = getUser();
		if(user != null && user.getCompany() != null) {
			return user.getCompany().getMainUserId();
		}
		return "";
	}
	
	public static boolean isCompanyMainUser() {
		User user = getUser();
		if(user != null && user.getCompany() != null && StringUtils.equals(user.getId(), user.getCompany().getMainUserId())) {
			return true;
		}
		return false;
	}
	
	/**
	 * 判断是否是插件的临时用户
	 * @param loginName
	 * @return
	 */
	public static boolean isPluginTempUser(String loginName) {
		if(StringUtils.length(loginName) > 20) {
			return true;
		}
		return false;
	}

	/**
	 *  dataScope为0 ，包括系统管理员，业务管理员
	 * @return
	 */
	public static boolean isCompanyAdmin() {
		User user = getUser();
		// updated for v1.6.9 KNZT-3009
		if (user != null && isCompanyAdmin(user.getDataScope())) {
			return true;
		}
		return false;
	}

	public static boolean isCompanyAdmin(String dataScope) {
		return StringUtils.isBlank(dataScope) || StringUtils.equals(dataScope, Constants.DataScope.USER_ADMIN);
	}
	
	/**
	 *  dataScope为10 ，主管
	 * @return
	 */
	public static boolean isCompanyManger() {
		User user = getUser();
		if(user != null && StringUtils.equals(user.getDataScope(), Constants.DataScope.MANAGER)) {
			return true;
		}
		return false;
	}	
	
	public static Integer getPurchaseAccountNum(){
		User user = getUser();
		if(user.getCompany() != null) {
			return user.getCompany().getPurchaseAccountNum() ;
		}
		return 0;
	}
	
	public static String getUserModule(String pageModule) {
		List<String> moduleList = getUserModuleList();
		if(moduleList != null) {
			if(StringUtils.isBlank(pageModule) || moduleList.size() == 1) {
				return moduleList.get(0);
			}
		}
		return pageModule;
	}
	
	/**
	 * 获取用户主模块，即第一个模块
	 * @return
	 */
	public static String getUserModule() {
		List<String> moduleList = getUserModuleList();
		if(moduleList != null && !moduleList.isEmpty()) {
			return moduleList.get(0);
		}
		return "";
	}
	
	/**
	 * 返回登录用户的模块
	 * @return
	 */
	public static List<String> getUserModuleList() {
		List<String> moduleList = new ArrayList<String>();
		List<RedisSaasRoleTO> roleList = getRedisSaasRoleList();
		if(roleList != null) {
			for(RedisSaasRoleTO role : roleList) {
				if(Constants.Sys.ROLE_TYPE_QCC_PLATFORM.equals(role.getRoleType())) {
					List<Dict> dictList = DictUtils.getDictList("sys_module");
					if(dictList != null) {
						moduleList.clear();
						for(Dict dict : dictList) {
							moduleList.add(dict.getValue());
						}
						return moduleList;
					}
				}
				if(!moduleList.contains(role.getModule())) {
					moduleList.add(role.getModule());
				}
			}
		}
		return moduleList;
	}

	
	public static List<RedisSaasRoleTO> getRedisSaasRoleList(){
		@SuppressWarnings("unchecked")
		List<RedisSaasRoleTO> roleList = null;
		if("webapi".equals(Global.getConfig("application.type"))) {
			roleList = (List<RedisSaasRoleTO>)getUserSession(CACHE_ROLE_LIST);
		}
		if (roleList == null || roleList.isEmpty()){
//			User user = getUser();
//			if (user.isAdmin()){
//				roleList = roleDao.findAllList(new Role());
//			}else{
				Role role = new Role();
				role.getSqlMap().put("dsf", BaseService.dataScopeUserFilter("u"));
				List<Role> roleDbList = userService.findRole(role);
				roleList = userService.convertRoleList2Redis(roleDbList);
//			}
			if("webapi".equals(Global.getConfig("application.type"))) {
				setUserSession(CACHE_ROLE_LIST, roleList);
			}
		}
		return roleList;
	}
	
	/**
	 * 判断当前角色中的英文名称是否有以prefix开头的角色
	 * @param prefix
	 * @return
	 */
	public static boolean hasRoleWithEnglishPrefix(String prefix) {
		List<String> enList = getEnglishRoleList();
		if(enList != null) {
			for(String en : enList) {
				if(StringUtils.startsWith(en, prefix)) {
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * 获取当前角色的英文名称
	 * @return
	 */
	public static List<String> getEnglishRoleList() {
		List<String> resultList = new ArrayList<>();
		List<RedisSaasRoleTO> roleList = getRedisSaasRoleList();
		if(roleList != null) {
			for(RedisSaasRoleTO role : roleList) {
				if(StringUtils.isNotBlank(role.getEnname()) && !resultList.contains(role.getEnname())) {
					resultList.add(role.getEnname());
				}
			}
		}
		return resultList;
	}
	
	public static boolean isAssignedRole(String roleName) {
		List<RedisSaasRoleTO> roleList = getRedisSaasRoleList();
		if(roleList != null) {
			for(RedisSaasRoleTO role : roleList) {
				if(StringUtils.equals(role.getEnname(), roleName)) {
					return true;
				}
			}
		}
		
		return false;
	}

	
	public static List<RedisSaasMenuTO> getRedisSaasMenuList(){

		List<RedisSaasMenuTO> menuList = (List<RedisSaasMenuTO>)getUserSession(CACHE_MENU_LIST);
		if (menuList == null || menuList.isEmpty()){
			User user = getUser();
				/*if (isSysAdmin()){
					menuList = menuDao.findAllList(new Menu());
				}else{*/
				Menu m = new Menu();
				m.setUserId(user.getId());
				List<Menu> menuDbList = userService.findMenuByUserId(m);
				menuList = userService.convertMenuList2Redis(menuDbList);
				//}
				setUserSession(CACHE_MENU_LIST, menuList);
		}
		return menuList;

	}
	
	
	// ============== User Cache ==============
	
	public static Object getUserSession(String key) {
		return getUserSession(key, null);
	}
	
	public static Object getUserSession(String key, Object defaultValue) {
//		Object obj = getCacheMap().get(key);
		Object obj = null;
		if("webapi".equals(Global.getApplicationType())) {
			try {
				obj = redisUserSessionService.getAttribute(key, Object.class);
			} catch (MessageException e) {
			
			}
		} else {
			logger.error(Global.getApplicationType() + " not support for getUserSession");
		} /*else {
			obj = getSession().getAttribute(key);
		}*/
		return obj==null?defaultValue:obj;
	}

	public static void setUserSession(String key, Object value) {
//		getCacheMap().put(key, value);
		if("webapi".equals(Global.getApplicationType()) || "job".equals(Global.getApplicationType())) {
			try {
				redisUserSessionService.setAttribute(key, value);
			} catch (Exception e) {
			
			}
		} else {
			logger.error(Global.getApplicationType() + " not support for setUserSession");
		}  /*else {
			getSession().setAttribute(key, value);
		}*/
		
	}

	public static void removeUserSession(String key) {
//		getCacheMap().remove(key);
		if("webapi".equals(Global.getApplicationType())) {
			redisUserSessionService.removeAttribute(key);
		} else {
			logger.error(Global.getApplicationType() + " not support for removeUserSession");
		} /*else {
			getSession().removeAttribute(key);
		}*/
	}

//	public static void userCheck() throws MessageException {
//		if (StringUtils.isBlank(getUserCompanyId())) {
//			throw new MessageException("warn.login.status.timeout");
//		}
//		if (getUser().getCompany() == null || StringUtils.isBlank(getUser().getCompany().getProKey()) || StringUtils.isBlank(getUser().getCompany().getProSecretKey())) {
//			throw new MessageException("warn.login.status.timeout");
//		}
//	}

	/**
	 *
	 *
	 * 公共方法：判断当前用户是否是超级管理员
	 * 后续有改名，需要在此处修改
	 */
	public static boolean isSuperManager(){
		User user = getUser();
		//updated for v1.3.7 KNZT-2018 通过用户权限判断，移除固定的账号判断
		return user.getCompany() != null && isAssignedRole("sysadmin");
	}

	// added for v1.8.2 KNZT-3680
	public static boolean hideCredit() {
		String showCredit = Optional.of(getUser()).map(User::getCompany).map(Company::getShowCredit).orElse(null);
		return !UserUtils.isCompanyMainUser() && CompShowCreditEnum.N.getCode().equals(showCredit);
	}

	// 是否是C端客户 added for v1.9.4 KNZT-4381
	public static boolean isSelf() {
		Integer compType = Optional.of(getUser()).map(User::getCompany).map(Company::getType).orElse(null);
		return compType != null && CompTypeEnum.isSelfPay(compType);
	}

	// 是否是银行客户
	public static boolean isBank() {
		String category = Optional.of(getUser()).map(User::getCompany).map(Company::getCategory).orElse(null);
		return CompCategoryEnum.BANK.getCode().equals(category);
	}

	public static boolean isBank(String userId) {
		User user = get(userId);
		String category = Optional.ofNullable(user).map(User::getCompany).map(Company::getCategory).orElse(null);
		return CompCategoryEnum.BANK.getCode().equals(category);
	}
}
