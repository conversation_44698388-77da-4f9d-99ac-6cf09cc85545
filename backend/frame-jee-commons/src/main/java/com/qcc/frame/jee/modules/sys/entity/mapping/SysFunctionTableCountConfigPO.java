package com.qcc.frame.jee.modules.sys.entity.mapping;

import java.io.Serializable;

public class SysFunctionTableCountConfigPO implements Serializable {

	private String functionTableId;
	private String supportCountStd;
	private String functionName;


	public String getFunctionTableId() {
		return functionTableId;
	}

	public void setFunctionTableId(String functionTableId) {
		this.functionTableId = functionTableId;
	}

	public String getSupportCountStd() {
		return supportCountStd;
	}

	public void setSupportCountStd(String supportCountStd) {
		this.supportCountStd = supportCountStd;
	}

	public String getFunctionName() {
		return functionName;
	}

	public void setFunctionName(String functionName) {
		this.functionName = functionName;
	}
}
