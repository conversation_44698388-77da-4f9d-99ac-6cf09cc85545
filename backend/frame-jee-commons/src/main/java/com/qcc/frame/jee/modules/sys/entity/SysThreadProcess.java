package com.qcc.frame.jee.modules.sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.util.Date;

public class SysThreadProcess extends DataEntity<SysThreadProcess> {
	
	private String threadPoolName;
	private String processAppType;
	private String processType;
	private String userId;
	private Short supportRetrigger;
	private String retriggerRunnableClass;
	private String paramJson;
	private String functionCd;
	private Date beginDatetime;
	private Date endDatetime;
	private String processStatus;
	private String processException;
	private String lockJson;
	private String remarks;
	private String proLoginType;
	
	
	private String companyName;
	private String userName;
	private String loginName;
	private String processAppTypeDesc;
	private String processTypeDesc;
	private String supportRetriggerDesc;
	private String processStatusDesc;
	private String functionCdDesc;
	private String threadProcessFileIds;

	public String getLockJson() {
		return lockJson;
	}
	public void setLockJson(String lockJson) {
		this.lockJson = lockJson;
	}
	public String getProLoginType() {
		return proLoginType;
	}
	public void setProLoginType(String proLoginType) {
		this.proLoginType = proLoginType;
	}
	public String getThreadPoolName() {
		return threadPoolName;
	}
	public void setThreadPoolName(String threadPoolName) {
		this.threadPoolName = threadPoolName;
	}
	public String getProcessAppType() {
		return processAppType;
	}
	public void setProcessAppType(String processAppType) {
		this.processAppType = processAppType;
	}
	public String getProcessType() {
		return processType;
	}
	public void setProcessType(String processType) {
		this.processType = processType;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public Short getSupportRetrigger() {
		return supportRetrigger;
	}
	public void setSupportRetrigger(Short supportRetrigger) {
		this.supportRetrigger = supportRetrigger;
	}
	public String getRetriggerRunnableClass() {
		return retriggerRunnableClass;
	}
	public void setRetriggerRunnableClass(String retriggerRunnableClass) {
		this.retriggerRunnableClass = retriggerRunnableClass;
	}
	public String getParamJson() {
		return paramJson;
	}
	public void setParamJson(String paramJson) {
		this.paramJson = paramJson;
	}
	public String getFunctionCd() {
		return functionCd;
	}
	public void setFunctionCd(String functionCd) {
		this.functionCd = functionCd;
	}
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getBeginDatetime() {
		return beginDatetime;
	}
	public void setBeginDatetime(Date beginDatetime) {
		this.beginDatetime = beginDatetime;
	}
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getEndDatetime() {
		return endDatetime;
	}
	public void setEndDatetime(Date endDatetime) {
		this.endDatetime = endDatetime;
	}
	public String getProcessStatus() {
		return processStatus;
	}
	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}
	public String getProcessException() {
		return processException;
	}
	public void setProcessException(String processException) {
		this.processException = processException;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getProcessAppTypeDesc() {
		return processAppTypeDesc;
	}
	public void setProcessAppTypeDesc(String processAppTypeDesc) {
		this.processAppTypeDesc = processAppTypeDesc;
	}
	public String getProcessTypeDesc() {
		return processTypeDesc;
	}
	public void setProcessTypeDesc(String processTypeDesc) {
		this.processTypeDesc = processTypeDesc;
	}
	public String getSupportRetriggerDesc() {
		return supportRetriggerDesc;
	}
	public void setSupportRetriggerDesc(String supportRetriggerDesc) {
		this.supportRetriggerDesc = supportRetriggerDesc;
	}
	public String getProcessStatusDesc() {
		return processStatusDesc;
	}
	public void setProcessStatusDesc(String processStatusDesc) {
		this.processStatusDesc = processStatusDesc;
	}
	public String getFunctionCdDesc() {
		return functionCdDesc;
	}
	public void setFunctionCdDesc(String functionCdDesc) {
		this.functionCdDesc = functionCdDesc;
	}
	public String getThreadProcessFileIds() {
		return threadProcessFileIds;
	}
	public void setThreadProcessFileIds(String threadProcessFileIds) {
		this.threadProcessFileIds = threadProcessFileIds;
	}
	
	
}
