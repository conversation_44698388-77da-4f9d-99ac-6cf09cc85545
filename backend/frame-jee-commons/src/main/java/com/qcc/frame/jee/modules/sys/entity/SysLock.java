package com.qcc.frame.jee.modules.sys.entity;

import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.util.Date;

public class SysLock extends DataEntity<SysLock> {
	
	private String lockType;
	private String lockCd;
	private String functionCd;
	private String logicStatus;
	private Date lockDatetime;
	private Integer timeout;

	public String getLockType() {
		return lockType;
	}
	public void setLockType(String lockType) {
		this.lockType = lockType;
	}
	public String getLockCd() {
		return lockCd;
	}
	public void setLockCd(String lockCd) {
		this.lockCd = lockCd;
	}
	public String getFunctionCd() {
		return functionCd;
	}
	public void setFunctionCd(String functionCd) {
		this.functionCd = functionCd;
	}
	public String getLogicStatus() {
		return logicStatus;
	}
	public void setLogicStatus(String logicStatus) {
		this.logicStatus = logicStatus;
	}
	public Date getLockDatetime() {
		return lockDatetime;
	}
	public void setLockDatetime(Date lockDatetime) {
		this.lockDatetime = lockDatetime;
	}
	public Integer getTimeout() {
		return timeout;
	}
	public void setTimeout(Integer timeout) {
		this.timeout = timeout;
	}
}
