package com.qcc.frame.jee.modules.sys.model.saas;

public class RedisSaasRoleTO implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6283559760273860136L;
	private String id;
	private String name; 	// 角色名称
	private String enname;	// 英文名称
	private String roleType;// 权限类型
	private String dataScope;// 数据范围
	private String module;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getEnname() {
		return enname;
	}
	public void setEnname(String enname) {
		this.enname = enname;
	}
	public String getRoleType() {
		return roleType;
	}
	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}
	public String getDataScope() {
		return dataScope;
	}
	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}
	public String getModule() {
		return module;
	}
	public void setModule(String module) {
		this.module = module;
	}


}
