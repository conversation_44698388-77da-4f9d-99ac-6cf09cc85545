package com.qcc.frame.jee.modules.sys.model;

// added for v2.1.9 chenbl KNZT-6952
public class UserSimpleTO {
    private String id;
    private String name;
    private String lastName;
    private String loginName;
    private String compRoleId;
    private String compRoleName;
    private String compRoleDescription;
    private String compGroupId;
    private String compGroupName;
    private String status;
    private String dataScope;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getCompRoleId() {
        return compRoleId;
    }

    public void setCompRoleId(String compRoleId) {
        this.compRoleId = compRoleId;
    }

    public String getCompRoleName() {
        return compRoleName;
    }

    public void setCompRoleName(String compRoleName) {
        this.compRoleName = compRoleName;
    }

    public String getCompRoleDescription() {
        return compRoleDescription;
    }

    public void setCompRoleDescription(String compRoleDescription) {
        this.compRoleDescription = compRoleDescription;
    }

    public String getCompGroupId() {
        return compGroupId;
    }

    public void setCompGroupId(String compGroupId) {
        this.compGroupId = compGroupId;
    }

    public String getCompGroupName() {
        return compGroupName;
    }

    public void setCompGroupName(String compGroupName) {
        this.compGroupName = compGroupName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }
}
