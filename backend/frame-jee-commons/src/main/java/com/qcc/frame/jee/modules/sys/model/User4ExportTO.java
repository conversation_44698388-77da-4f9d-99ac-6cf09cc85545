package com.qcc.frame.jee.modules.sys.model;

import com.qcc.frame.commons.ienum.UserStatusEnum;
import com.qcc.frame.jee.commons.excel.ExcelField;
import com.qcc.frame.jee.commons.utils.DateUtils;

import java.util.Locale;

public class User4ExportTO {

    @ExcelField(title = "Username", sort = 10, fieldType = String.class, width = 6000)
    private String name;
    @ExcelField(title = "Email", sort = 20, fieldType = String.class, width = 6000)
    private String loginName;
    @ExcelField(title = "Last login time", sort = 30, fieldType = String.class, width = 6000)
    private String lastLoginTime;
    @ExcelField(title = "Last login IP", sort = 40, fieldType = String.class, width = 6000)
    private String lastLoginIp;
    @ExcelField(title = "Group", sort = 50, fieldType = String.class, width = 6000)
    private String groupNames;
    @ExcelField(title = "Role", sort = 60, fieldType = String.class, width = 6000)
    private String compRoleDescriptions;
    @ExcelField(title = "Status", sort = 25, fieldType = String.class, width = 6000)
    private String status;

    public static User4ExportTO buildFrom(UserTO userTO) {
        User4ExportTO user4ExportTO = new User4ExportTO();
        user4ExportTO.setName(userTO.getName());
        user4ExportTO.setLoginName(userTO.getLoginName());
        user4ExportTO.setLastLoginTime(userTO.getLastLoginTime() == null ? "" : DateUtils.formatDateForSg(userTO.getLastLoginTime(), DateUtils.DATE_FORMAT_DDMMMYYYYHHmmss, Locale.ENGLISH));
        user4ExportTO.setLastLoginIp(userTO.getLastLoginIp());
        user4ExportTO.setGroupNames(userTO.getGroupNames());
        user4ExportTO.setCompRoleDescriptions(userTO.getCompRoleDescriptions());
        user4ExportTO.setStatus(mappingStatus4PageExport(userTO.getAccountStatus()));
        return user4ExportTO;
    }

    /**
     * added for v2.3.2 fengsw KNZT-7930
     * 只在导出的地方处理状态映射字段和前端页面一致
     *
     * @param accountStatus
     * @return
     */
    private static String mappingStatus4PageExport(String accountStatus) {
        if (UserStatusEnum.ACTIVE.getKey().equals(accountStatus)) {
            return "Active";
        } else if (UserStatusEnum.PASSWORD_EXCEPTION.getKey().equals(accountStatus)) {
            return "Locked";
        } else if (UserStatusEnum.INACTIVE.getKey().equals(accountStatus)) {
            return "Invitation sent";
        }
        return accountStatus;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    public String getGroupNames() {
        return groupNames;
    }

    public void setGroupNames(String groupNames) {
        this.groupNames = groupNames;
    }

    public String getCompRoleDescriptions() {
        return compRoleDescriptions;
    }

    public void setCompRoleDescriptions(String compRoleDescriptions) {
        this.compRoleDescriptions = compRoleDescriptions;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
