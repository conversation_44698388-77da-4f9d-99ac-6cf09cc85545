package com.qcc.frame.jee.modules.sys.entity;

import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.util.Date;

public class SysMobileMsgHis extends DataEntity<SysMobileMsgHis> {
	
	private String mobilePrefix;
	private String mobile;
	private Short sendBy;
	private Short messageType;
	private String verifyCode;
	private String messageContent;
	private String messageResponse="";
	private Date sendDate;
	private String remarks;

	public String getMobilePrefix() {
		return mobilePrefix;
	}
	public void setMobilePrefix(String mobilePrefix) {
		this.mobilePrefix = mobilePrefix;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public Short getSendBy() {
		return sendBy;
	}
	public void setSendBy(Short sendBy) {
		this.sendBy = sendBy;
	}
	public Short getMessageType() {
		return messageType;
	}
	public void setMessageType(Short messageType) {
		this.messageType = messageType;
	}
	public String getVerifyCode() {
		return verifyCode;
	}
	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}
	public String getMessageContent() {
		return messageContent;
	}
	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}
	public Date getSendDate() {
		return sendDate;
	}
	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getMessageResponse() {
		return messageResponse;
	}
	public void setMessageResponse(String messageResponse) {
		this.messageResponse = messageResponse;
	}
	
	
}
