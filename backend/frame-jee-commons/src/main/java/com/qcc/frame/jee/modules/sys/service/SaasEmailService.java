package com.qcc.frame.jee.modules.sys.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.util.EncryptUtil;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.model.RedisEmailAttemptTO;
import com.qcc.frame.jee.modules.sys.model.saas.SaasRedisEmailCodeTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;

/**
 * KNZT-254
 */
@Service
public class SaasEmailService {
	private static Logger logger = LoggerFactory.getLogger(SaasEmailService.class);
	@Autowired
	private QccMailSenderService qccMailSenderService;
	@Autowired
	private RedisService redisService;
	
	private String getRedisIdByEmail(String email) {
		return EncryptUtil.encodeMd5(email);
	}

	public void deleteEmailCode(String email) throws MessageException {
		String redisId = getRedisIdByEmail(email);
		redisService.deleteById(Constants.RedisKey.WEBAPI_SAAS_EMAIL_CODE, redisId);
	}
	
	public void addFailCount4EmailCode(String email, int maxAtmoCount) throws MessageException {
		try {
			String redisId = getRedisIdByEmail(email);
			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, redisId);
			RedisEmailAttemptTO emailAtmpTO = null;
			if(StringUtils.isNotBlank(jsonStr)) {
				JSONObject jsonObj = JsonUtils.parseObject(jsonStr);
				if(jsonObj != null) {
					emailAtmpTO = jsonObj.toJavaObject(RedisEmailAttemptTO.class);
				}
			}
			if(emailAtmpTO == null) {
				emailAtmpTO = new RedisEmailAttemptTO();
			}

			boolean resendVerifyCode = false;
			if(emailAtmpTO.getEmailCodeAtmpCount() < maxAtmoCount) {
				emailAtmpTO.setEmailCodeAtmpCount(emailAtmpTO.getEmailCodeAtmpCount() + 1);
			} else {
				emailAtmpTO.setEmailCodeAtmpCount(0);
				redisService.deleteById(Constants.RedisKey.WEBAPI_SAAS_EMAIL_CODE, redisId);
				resendVerifyCode = true;
			}
			redisService.saveString(Constants.RedisKey.LOGIN_ATTEMPT, redisId, JSON.toJSONString(emailAtmpTO));
			if(resendVerifyCode) {
				throw new MessageException("err.email_code.wrong.max.times");
			}
		} catch(MessageException e) {
			throw e;
		} catch(Exception e) {
			logger.error("", e);
		}
	}

	public void resetFailCount4EmailCode(String email) throws MessageException {
		try {
			if(StringUtils.isBlank(email)) {
				return;
			}
			String redisId = getRedisIdByEmail(email);
			String jsonStr = redisService.getStringById(Constants.RedisKey.LOGIN_ATTEMPT, redisId);
			RedisEmailAttemptTO emailAtmpTO = null;
			if(StringUtils.isNotBlank(jsonStr)) {
				JSONObject jsonObj = JsonUtils.parseObject(jsonStr);
				if(jsonObj != null) {
					emailAtmpTO = jsonObj.toJavaObject(RedisEmailAttemptTO.class);
				}
			}
			if(emailAtmpTO == null) {
				return;
			}

			emailAtmpTO.setEmailCodeAtmpCount(0);
			redisService.saveString(Constants.RedisKey.LOGIN_ATTEMPT, redisId, JSON.toJSONString(emailAtmpTO));
		} catch(MessageException e) {
			throw e;
		} catch(Exception e) {
			logger.error("", e);
		}
	}
	
	public void validateEmailCode(String email, String verifyCode) throws MessageException {
		if(StringUtils.isBlank(email)) {
			throw new MessageException("err.email.reqiured");
		}
		if(StringUtils.isBlank(verifyCode)) {
			throw new MessageException("err.email_code.reqiured");
		}
		if(!StringUtils.validateEmail(email)) {
			throw new MessageException("err.email.invalid");
		}
		SaasRedisEmailCodeTO emailCodeTO = getEmailCodeByEmail(email);
		if(emailCodeTO != null && !StringUtils.equals(emailCodeTO.getVerifyCode(), verifyCode)) {
			addFailCount4EmailCode(email, 10);
			throw new MessageException("err.email_code.wrong");
		}
	}
	
	public SaasRedisEmailCodeTO getEmailCodeByEmail(String email) throws MessageException {
		String redisId = getRedisIdByEmail(email);
		SaasRedisEmailCodeTO emailCodeTO = null;
		try {
			emailCodeTO = redisService.getObjectById(Constants.RedisKey.WEBAPI_SAAS_EMAIL_CODE, redisId, SaasRedisEmailCodeTO.class);
		} catch (MessageException e) {
			
		}
		
		if(emailCodeTO == null || StringUtils.isBlank(emailCodeTO.getVerifyCode())) {
			throw new MessageException("err.system.email.verify.code.required");
		}
		
		long interval = Long.parseLong(Global.getConfig("email.security.code.expiry.time.interval")) * 1000;
		long remain = System.currentTimeMillis() - emailCodeTO.getTimeMillis();
		if(interval < remain) {
			throw new MessageException("err.system.email.verify.code.required");
		}
		
		return emailCodeTO;
	}
	
	public void sendEmailCode(String email, String verificationCode, String emailSubject, String emailContent) throws MessageException {
//		if(StringUtils.isBlank(email)) {
//			throw new MessageException("err.system.account.mobile.required");
//		}
		
		if(!StringUtils.validateEmail(email)) {
			throw new MessageException("err.email.invalid");
		}
		
		String redisId = getRedisIdByEmail(email);
		SaasRedisEmailCodeTO emailCodeTO = null;
		try {
			emailCodeTO = redisService.getObjectById(Constants.RedisKey.WEBAPI_SAAS_EMAIL_CODE, redisId, SaasRedisEmailCodeTO.class);
		} catch (MessageException e) {
			
		}
		long curTimeMillis = Calendar.getInstance().getTimeInMillis();
		int intervalSec = Integer.parseInt(Global.getConfig("email.security.code.expiry.time.interval"));
		long interval = intervalSec * 1000;
		if (emailCodeTO == null || StringUtils.isBlank(emailCodeTO.getVerifyCode()) || ((curTimeMillis - emailCodeTO.getTimeMillis()) >= interval)) {
//			if(Global.isProjectEnv(Constants.ProjectEnv.DEV)) {
//				System.out.println(email + " code=" + verificationCode);
//			} else {
				try {
					qccMailSenderService.send(email, emailSubject, emailContent);
				} catch(Exception e) {
					logger.error("", e);
					if(StringUtils.contains(e.getMessage(), "559 Invalid rcptto")) {
						throw new MessageException("err.email.invalid");
					} else {
						throw new MessageException("err.access");
					}
					
				}
//			}
			emailCodeTO = new SaasRedisEmailCodeTO();
			emailCodeTO.setEmail(email);
			emailCodeTO.setVerifyCode(verificationCode);
			redisService.saveObject(Constants.RedisKey.WEBAPI_SAAS_EMAIL_CODE, redisId, emailCodeTO, intervalSec);
		} else {
			throw new MessageException("err.system.email.auth.code.retry", getMaxValidMin()+"");
		}
	}
	
	public int getMaxValidMin() {
		int intervalSec = Integer.parseInt(Global.getConfig("email.security.code.expiry.time.interval"));
		int intervalMin = (intervalSec / 60 <= 0 ? 1 : intervalSec / 60);
		return intervalMin;
	}
}
