package com.qcc.frame.jee.modules.sys.model.saas;

/**
 * KNZT-254
 */
public class SaasRedisEmailCodeTO implements java.io.Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7541638076821599584L;

	public SaasRedisEmailCodeTO() {
		timeMillis = System.currentTimeMillis();
	}
	
	private String email;
	private String verifyCode;
	private long timeMillis;

	public String getVerifyCode() {
		return verifyCode;
	}
	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}
	public long getTimeMillis() {
		return timeMillis;
	}
	public void setTimeMillis(long timeMillis) {
		this.timeMillis = timeMillis;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}

}
