package com.qcc.frame.jee.modules.sys.utils;

import com.google.common.collect.Lists;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.modules.sys.dao.DictDao;
import com.qcc.frame.jee.modules.sys.entity.Dict;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典工具类
 */
public class DictUtils {
	private static Logger logger = LoggerFactory.getLogger(DictUtils.class);
	
	private static DictDao dictDao = SpringContextHolder.getBean(DictDao.class);
	private static RedisService redisServie = SpringContextHolder.getBean(RedisService.class);

	public static final String CACHE_DICT_MAP = "dictMap";

	
	public static String getDictLabel(String value, String type, String defaultValue){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && value.equals(dict.getValue())){
					return dict.getLabel();
				}
			}
		}
		return defaultValue;
	}
	public static String getDictDesc(String value, String type, String defaultValue){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && value.equals(dict.getValue())){
					return dict.getDescription();
				}
			}
		}
		return defaultValue;
	}
	
	public static String getDictLabels(String values, String type, String defaultValue){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(values)){
			List<String> valueList = Lists.newArrayList();
			for (String value : StringUtils.split(values, ",")){
				valueList.add(getDictLabel(value, type, defaultValue));
			}
			return StringUtils.join(valueList, ",");
		}
		return defaultValue;
	}

	public static Dict getDictByTypeAndDesc(String desc, String type){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(desc)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && desc.equals(dict.getDescription())){
					return dict;
				}
			}
		}
		return null;
	}
	
	public static Dict getDictByTypeAndLabel(String type, String label){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(label)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && label.equals(dict.getLabel())){
					return dict;
				}
			}
		}
		return null;
	}

	public static String getDictValue(String label, String type, String defaultLabel){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(label)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && label.equals(dict.getLabel())){
					return dict.getValue();
				}
			}
		}
		return defaultLabel;
	}

	public static String getDictRemark(String value, String type, String defaultValue){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && value.equals(dict.getValue())){
					return dict.getRemarks();
				}
			}
		}
		return defaultValue;
	}

	public static String getDictLabelByRemark(String type, String remarks, String defaultValue){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(remarks)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && remarks.equals(dict.getRemarks())){
					return dict.getLabel();
				}
			}
		}
		return defaultValue;
	}
	
	/**
	 * 直接从数据库读取
	 * @param label
	 * @param type
	 * @param defaultLabel
	 * @return
	 */
	public static String getDictValueByTypeLabel(String label, String type, String defaultLabel){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(label)){
			List<Dict> list = dictDao.findListByTypeAndLabel(type, label);
			if(list != null && list.size() > 0) {
				return list.get(0).getValue();
			}
		}
		return defaultLabel;
	}
	
	/**
	 * 直接从数据库读取
	 * @param value
	 * @param type
	 * @return
	 */
	public static Dict getDictByTypeValue(String value, String type){
		return dictDao.getByTypeAndValue(type, value);
	}
	
	public static List<String> getDictValueList(String type, String label) {
		List<String> list = Lists.newArrayList();
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(label)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && label.equals(dict.getLabel())){
					list.add(dict.getValue());
				}
			}
		}
		return list;
	}
	
	public static List<String> listDictValueByType(String type) {
		List<String> resultList = new ArrayList<>();
		if(StringUtils.isNotBlank(type)) {
			for (Dict dict : getDictList(type)){
				resultList.add(dict.getValue());
			}
		}
		return resultList;
	}
	
	public static Dict getDict(String value, String type){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && value.equals(dict.getValue())){
					return dict;
				}
			}
		}
		return null;
	}

	public static List<Dict> getDictByTypeAndValue(String value, String type){
		List<Dict> dictList = new ArrayList<>();
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)){
			for (Dict dict : getDictList(type)){
				if (type.equals(dict.getType()) && value.equals(dict.getValue())){
					dictList.add(dict);
				}
			}
		}
		return dictList;
	}
	
	public static List<Dict> getDictList(String type, String label){
		Dict dict = new Dict();
		dict.setType(type);
		dict.setLabel(label);
		return dictDao.findListByType(dict);
	}
	
	public static List<Dict> getDictList(String type){
		/*@SuppressWarnings("unchecked")
		Map<String, List<Dict>> dictMap = null;//(Map<String, List<Dict>>)CacheUtils.get(CACHE_DICT_MAP);
		if (dictMap==null){
			dictMap = Maps.newHashMap();
			for (Dict dict : dictDao.findAllList(new Dict())){
				List<Dict> dictList = dictMap.get(dict.getType());
				if (dictList != null){
					dictList.add(dict);
				}else{
					dictMap.put(dict.getType(), Lists.newArrayList(dict));
				}
			}
			//CacheUtils.put(CACHE_DICT_MAP, dictMap);
		}
		List<Dict> dictList = dictMap.get(type);
		if (dictList == null){
			dictList = Lists.newArrayList();
		}
		return dictList;*/
		Dict dict = new Dict();
		dict.setType(type);
		return dictDao.findListByType(dict);
	}
	
	public static List<Dict> getDictListOrderBySortAndLabel(String type){
		return dictDao.findListByTypeOrderBySortAndLabel(type);
	}
	
	
	public static String getDictDesc(List<Dict> dictList, String value, String defaultValue) {
		if(dictList != null) {
			for(Dict dict : dictList) {
				if(StringUtils.equalsIgnoreCase(dict.getValue(), value)) {
					return dict.getLabel();
				}
			}
		}
		return defaultValue;
	}
	
	public static Dict getParentDict(String value, List<Dict> dictList, List<Dict> parentDictList){
		if(dictList != null && dictList.size() > 0 && parentDictList != null && parentDictList.size() > 0) {
			for(Dict dict : dictList) {
				if(StringUtils.equals(dict.getValue(), value)) {
					if(StringUtils.isNotBlank(dict.getParentId())) {
						for(Dict parentDict : parentDictList) {
							if(StringUtils.equals(dict.getParentId(), parentDict.getId())) {
								return parentDict;
							}
						}
					} else {
						return null;
					}
				}
			}
		}
		return null;
	}

	public static List<Dict> getDictListOrderByParentIdAndSort(String type){
		Dict dict = new Dict();
		dict.setType(type);
		return dictDao.findListByTypeOrderByParentIdAndSort(dict);
	}


	public static String getDictValueAsString(String type) {
		return dictDao.getDictValueAsString(type);
	}


	/**
	 * 获取字典map
	 * added for lvcy v2.1.1 KNZT-5973
	 * @param type
	 * @return
	 */
	public static Map<String, String> getDictMap(String type) {
		Map<String, String> dictMap = new HashMap<>();
		for (Dict dict : getDictList(type)) {
			dictMap.put(dict.getLabel(), dict.getValue());
		}
		return dictMap;

	}
}
