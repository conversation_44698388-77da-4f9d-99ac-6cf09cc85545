package com.qcc.frame.jee.modules.sys.model;

import java.io.Serializable;

// added for v2.0.7 chenbl KNZT-5773
public class RedisEmailAttemptTO implements Serializable {

    private static final long serialVersionUID = 7024547377869252079L;

    private int emailCodeAtmpCount;
    private long emailCodeAtmpTs;

    public int getEmailCodeAtmpCount() {
        return emailCodeAtmpCount;
    }

    public void setEmailCodeAtmpCount(int emailCodeAtmpCount) {
        this.emailCodeAtmpCount = emailCodeAtmpCount;
    }

    public long getEmailCodeAtmpTs() {
        return emailCodeAtmpTs;
    }

    public void setEmailCodeAtmpTs(long emailCodeAtmpTs) {
        this.emailCodeAtmpTs = emailCodeAtmpTs;
    }
}
