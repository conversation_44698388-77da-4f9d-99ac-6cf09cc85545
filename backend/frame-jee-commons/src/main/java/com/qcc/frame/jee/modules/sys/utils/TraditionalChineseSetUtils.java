package com.qcc.frame.jee.modules.sys.utils;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.util.*;

public class TraditionalChineseSetUtils {
    private static final Logger log = LoggerFactory.getLogger(TraditionalChineseSetUtils.class);
    private static final HashSet<Character> hashSet = new HashSet<>();
    // added for v1.9.4 KNZT-4503
    private static volatile boolean initStatus = false;// 通过该状态来判断是否完成中文繁体字符集是否组装完成
    private static final String ROOT_PATH = "/traditionchinese/";
    private static final String TRADITION_CHINESE_FILE_NAME = "traditionchinese.txt"; // 项目中繁体中文字符集文件名称
    private static final Object lock = new Object();

    private static void init() {
        synchronized (lock) {
            if (!initStatus) {
                InputStream is = null;
                try {
                    log.info("--------start assemble tradition chinese--------");
                    ClassPathResource resource = new ClassPathResource(ROOT_PATH + TRADITION_CHINESE_FILE_NAME);
                    is = resource.getInputStream();
                    if (is != null) {
                        List<String> list = IOUtils.readLines(is, "UTF-8");
                        for (String str : list) {
                            if (StringUtils.isNotBlank(str)) {
                                char[] chars = str.toCharArray();
                                for (char c : chars) {
                                    hashSet.add(c);
                                }
                            }
                        }
                    }
                    log.info("--------assemble tradition chinese end--------");
                    initStatus = true;
                } catch (Exception e) {
                    log.error("failed to assemble traditional Chinese character set", e);
                } finally {
                    IOUtils.closeQuietly(is);
                }
            }
        }
    }

    /**
     * 通过文件名获取HashSet, /traditionchinese/下的文件
     *
     * @return
     */
    public static HashSet<Character> getTraditionChineseCharacterSet() {
        if (!initStatus) {
            init();
        }
        return hashSet;
    }
}
