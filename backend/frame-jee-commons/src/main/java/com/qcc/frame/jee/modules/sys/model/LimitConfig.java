package com.qcc.frame.jee.modules.sys.model;

import com.qcc.frame.jee.modules.sys.service.RateLimitUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 限流参数类
 * @see RateLimitUtils 使用处
 * <AUTHOR>
 * @datetime 12/6/2025 11:13 上午
 */
public class LimitConfig {
    private String checkKey;           // 校验key
    private String limitKey;           // 限流key（统一前缀）
    private int dailyLimit = 0;        // 每日限制次数，0表示不限制
    private boolean autoBlockOnWindowLimit = false;   // 超出窗口限制时是否自动block
    private long blockSeconds = 300;   // block时间（秒），默认5分钟
    private boolean recordAccess = true; // 是否记录访问
    private List<WindowLimit> windowLimits; // 滑动窗口限制列表

    // 构造方法
    public LimitConfig(String checkKey) {
        this.checkKey = checkKey;
    }

    private LimitConfig(String checkKey, String limitKey) {
        this.checkKey = checkKey;
        this.limitKey = limitKey;
    }

    // ============== 便捷构建方法 ==============

    /**
     * 创建基础配置
     * @param checkKey 校验key（如IP、用户ID等）
     * @param limitKey 限流key
     * @return LimitConfig
     */
    public static LimitConfig create(String checkKey, String limitKey) {
        return new LimitConfig(checkKey, limitKey);
    }

    /**
     * 创建IP限频配置
     * @param ip IP地址
     * @return LimitConfig
     */
    public static LimitConfig forIp(String ip) {
        return new LimitConfig(ip, "ip_limit");
    }

    /**
     * 创建用户限频配置
     * @param userId 用户ID
     * @return LimitConfig
     */
    public static LimitConfig forUser(String userId) {
        return new LimitConfig(userId, "user_limit");
    }

    /**
     * 创建API限频配置
     * @param apiKey API标识
     * @param identifier 标识符（如IP、用户ID等）
     * @return LimitConfig
     */
    public static LimitConfig forApi(String apiKey, String identifier) {
        return new LimitConfig(identifier, "api_" + apiKey);
    }


    // ============== 链式配置方法 ==============

    public LimitConfig setLimitKey(String limitKey) {
        this.limitKey = limitKey;
        return this;
    }

    public LimitConfig setDailyLimit(int dailyLimit) {
        this.dailyLimit = dailyLimit;
        return this;
    }

    public LimitConfig setAutoBlockOnWindowLimit(boolean autoBlockOnWindowLimit) {
        this.autoBlockOnWindowLimit = autoBlockOnWindowLimit;
        return this;
    }

    public LimitConfig setBlockSeconds(long blockSeconds) {
        this.blockSeconds = blockSeconds;
        return this;
    }

    public LimitConfig setRecordAccess(boolean recordAccess) {
        this.recordAccess = recordAccess;
        return this;
    }

    public LimitConfig setWindowLimits(List<WindowLimit> windowLimits) {
        this.windowLimits = windowLimits;
        return this;
    }

    /**
     * 添加单个窗口限制
     */
    public LimitConfig addWindowLimit(String windowName, long windowSeconds, int maxCount) {
        if (this.windowLimits == null) {
            this.windowLimits = new java.util.ArrayList<>();
        }
        this.windowLimits.add(new WindowLimit(windowName, windowSeconds, maxCount));
        return this;
    }

    /**
     * 添加多个窗口限制
     */
    public LimitConfig addWindowLimits(WindowLimit... windowLimits) {
        if (this.windowLimits == null) {
            this.windowLimits = new java.util.ArrayList<>();
        }
        this.windowLimits.addAll(Arrays.asList(windowLimits));
        return this;
    }

    // ============== Getter方法 ==============

    public String getCheckKey() { return checkKey; }
    public String getLimitKey() { return limitKey; }
    public int getDailyLimit() { return dailyLimit; }
    public boolean isAutoBlockOnWindowLimit() { return autoBlockOnWindowLimit; }
    public long getBlockSeconds() { return blockSeconds; }
    public boolean isRecordAccess() { return recordAccess; }
    public List<WindowLimit> getWindowLimits() { return windowLimits; }


    /**
     * 滑动窗口限制配置
     */
    public static class WindowLimit {
        private String windowName;         // 窗口名称
        private long windowSeconds;        // 窗口时间长度（秒）
        private int maxCount;              // 最大允许次数

        public WindowLimit(String windowName, long windowSeconds, int maxCount) {
            this.windowName = windowName;
            this.windowSeconds = windowSeconds;
            this.maxCount = maxCount;
        }

        // ============== 便捷构建方法 ==============

        public static WindowLimit of(String windowName, long windowSeconds, int maxCount) {
            return new WindowLimit(windowName, windowSeconds, maxCount);
        }

        public static WindowLimit perMinute(int maxCount) {
            return new WindowLimit("1min", 60, maxCount);
        }

        public static WindowLimit perXMinute(int x, int maxCount) {
            return new WindowLimit(x + "min", x * 60L, maxCount);
        }

        public static WindowLimit perHour(int maxCount) {
            return new WindowLimit("1hour", 3600, maxCount);
        }

        public static WindowLimit perDay(int maxCount) {
            return new WindowLimit("1day", 86400, maxCount);
        }

        // ============== Getter/Setter方法 ==============

        public String getWindowName() { return windowName; }
        public void setWindowName(String windowName) { this.windowName = windowName; }

        public long getWindowSeconds() { return windowSeconds; }
        public void setWindowSeconds(long windowSeconds) { this.windowSeconds = windowSeconds; }

        public int getMaxCount() { return maxCount; }
        public void setMaxCount(int maxCount) { this.maxCount = maxCount; }
    }
}