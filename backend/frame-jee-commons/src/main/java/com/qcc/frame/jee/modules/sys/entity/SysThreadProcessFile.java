package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.io.File;

public class SysThreadProcessFile extends DataEntity<SysThreadProcessFile> {
	
	private String threadProcessId;
	private String userId;
	private byte[] exportPath;
	private String exportFileName;
	private Long exportFileSize;
	
	
	//非数据库
	private File exportFile;

	public Long getExportFileSize() {
		return exportFileSize;
	}
	public void setExportFileSize(Long exportFileSize) {
		this.exportFileSize = exportFileSize;
	}
	public String getThreadProcessId() {
		return threadProcessId;
	}
	public void setThreadProcessId(String threadProcessId) {
		this.threadProcessId = threadProcessId;
	}

	public byte[] getExportPath() {
		return exportPath;
	}
	public void setExportPath(byte[] exportPath) {
		this.exportPath = exportPath;
	}
	public String getExportFileName() {
		return exportFileName;
	}
	public void setExportFileName(String exportFileName) {
		this.exportFileName = exportFileName;
	}
	public File getExportFile() {
		return exportFile;
	}
	public void setExportFile(File exportFile) {
		this.exportFile = exportFile;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	
}
