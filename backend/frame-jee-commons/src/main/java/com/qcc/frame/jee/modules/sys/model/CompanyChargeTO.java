package com.qcc.frame.jee.modules.sys.model;

import java.math.BigDecimal;

// added for v.2.2.7 fengsw KNZT-7785 账号充值信息
public class CompanyChargeTO {
    private BigDecimal chargeLimitAmount;// 充值限额
    private BigDecimal currentChargedAmount;// 当前已充值金额

    public CompanyChargeTO() {
    }

    public CompanyChargeTO(BigDecimal chargeAmount, BigDecimal chargeLimitAmount) {
        this.currentChargedAmount = chargeAmount;
        this.chargeLimitAmount = chargeLimitAmount;
    }

    public BigDecimal getChargeLimitAmount() {
        return chargeLimitAmount;
    }

    public void setChargeLimitAmount(BigDecimal chargeLimitAmount) {
        this.chargeLimitAmount = chargeLimitAmount;
    }

    public BigDecimal getCurrentChargedAmount() {
        return currentChargedAmount;
    }

    public void setCurrentChargedAmount(BigDecimal currentChargedAmount) {
        this.currentChargedAmount = currentChargedAmount;
    }
}
