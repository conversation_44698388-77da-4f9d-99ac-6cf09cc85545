package com.qcc.frame.jee.modules.sys.model;

// added for v1.8.0 KNZT-3617
public class GetEnglishTextBatchParam {

    private String key;

    private String value;

    private String saveKeyNo;

    private String saveName;

    private String functionCd;

    public GetEnglishTextBatchParam(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public GetEnglishTextBatchParam(String key, String value, String saveKeyNo, String saveName, String functionCd) {
        this.key = key;
        this.value = value;
        this.saveKeyNo = saveKeyNo;
        this.saveName = saveName;
        this.functionCd = functionCd;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getSaveKeyNo() {
        return saveKeyNo;
    }

    public void setSaveKeyNo(String saveKeyNo) {
        this.saveKeyNo = saveKeyNo;
    }

    public String getSaveName() {
        return saveName;
    }

    public void setSaveName(String saveName) {
        this.saveName = saveName;
    }

    public String getFunctionCd() {
        return functionCd;
    }

    public void setFunctionCd(String functionCd) {
        this.functionCd = functionCd;
    }
}
