/**
 * Copyright &copy; 2012-2013 <a href="httparamMap://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.modules.sys.dao.LogDao;
import com.qcc.frame.jee.modules.sys.entity.Log;
import com.qcc.frame.jee.modules.sys.entity.mapping.MonitorLogTO;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 日志Service
 * @version 2014-05-16
 */
@Service
//@Transactional(readOnly = true)
public class LogService extends CrudService<LogDao, Log> {

	public Page<Log> findPage(Page<Log> page, Log log) {
		
		// 设置默认时间范围，默认当前月
		if (log.getBeginDate() == null){
			log.setBeginDate(DateUtils.setDays(DateUtils.parseDate(DateUtils.getDate()), 1));
		}
		if (log.getEndDate() == null){
			log.setEndDate(DateUtils.addMonths(log.getBeginDate(), 1));
		}
		
		return super.findPage(page, log);
		
	}
	
	public void insertLog(Log log) {
		log.setId(IdGenUtil.uuid());
		log.setCreateDate(new Date());
		dao.insert(log);
	}
	
	public void physicallyDeleteBeforeDate(String dateStr) {
		dao.physicallyDeleteBeforeDate(dateStr);
	}
	
	public List<MonitorLogTO> listMonitoLog4Login(String beginTime, String endTime) {
		return dao.listMonitoLog4Login(beginTime, endTime);
	}
	
	public List<MonitorLogTO> listMonitoLog4ApiIp(String beginTime, String endTime) {
		return dao.listMonitoLog4ApiIp(beginTime, endTime);
	}
}
