package com.qcc.frame.jee.modules.sys.model.saas;

import com.qcc.frame.jee.modules.sys.entity.User;

public class SaasLoginCallbackResult {
	private User user;
	private String pluginAuthUrl;
	private String pluginAuthIsEnabled;
	private boolean demoPluginFlag;

	public boolean isDemoPluginFlag() {
		return demoPluginFlag;
	}

	public void setDemoPluginFlag(boolean demoPluginFlag) {
		this.demoPluginFlag = demoPluginFlag;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public String getPluginAuthUrl() {
		return pluginAuthUrl;
	}

	public void setPluginAuthUrl(String pluginAuthUrl) {
		this.pluginAuthUrl = pluginAuthUrl;
	}

	public String getPluginAuthIsEnabled() {
		return pluginAuthIsEnabled;
	}

	public void setPluginAuthIsEnabled(String pluginAuthIsEnabled) {
		this.pluginAuthIsEnabled = pluginAuthIsEnabled;
	}
	
	
}
