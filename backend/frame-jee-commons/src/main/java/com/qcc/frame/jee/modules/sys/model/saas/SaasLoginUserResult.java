package com.qcc.frame.jee.modules.sys.model.saas;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

public class SaasLoginUserResult implements java.io.Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5320433326525010190L;

	// added for v1.6.9 KNZT-3009
	@JSONField(serialize=false)
	@JsonIgnore
	private String userId;
	@JSONField(serialize=false)
	@JsonIgnore
	private String companyId;
	private String loginName;
	private String userName; // 用户名
	private String userLastName; // 用户姓 added for v2.0.7 chenbl KNZT-6595
	// added for v1.6.9 KNZT-3009
	private String groupId;
	private String groupName;
	// added for v2.0.7 chenbl KNZT-5787
	private String compRoleIds;
	private String compRoleDescList;
	@ApiModelProperty(value = "访问Token - 调用受保护的接口时, 需要加到header里(参数名:pro-access-token)", example = "")
	private String accessToken;
	@ApiModelProperty(value = "刷新Token - 调用刷新Access Token接口时, 需要加到请求参数", example = "")
	private String refreshToken;//预留字段
	@ApiModelProperty(value = "标识每隔几秒调用刷新Access Token接口", example = "")
	private int refreshIntervalSec;//刷新间隔(秒)
//	@ApiModelProperty(value = "记住登录Token - 用于Access Token过期且有记住登录时传给记住登录接口", example = "")
//	private String keepToken;
	@ApiModelProperty(value = "访问安全密钥 - 调用受保护的接口时, 需要加到header里(参数名:pro-access-secret-key) ", example = "")
	private String accessSecretKey;//loginName+Password+timestamp加密
	
//	@ApiModelProperty(value = "登录用户能访问的菜单列表", example = "")
//	private List<SaasMenuTO> menuList;
	
	@ApiModelProperty(value = "登录后首页菜单列表(页头/功能/高级应用)", example = "")
	private SaasMenuHome menuHome;

	@ApiModelProperty(value = "登录用户的权限标识(有该权限标识才能访问)", example = "")
	private List<String> permissionList;
	@ApiModelProperty(value = "登录用户的权限标识(有该权限标识不能能访问)", example = "")
	private List<String> reversalPermissionList;
	
	@ApiModelProperty(value = "登录用户的数据权限", example = "")
	private String dataScope;
	@ApiModelProperty(value = "登录用户的签约类型", example = "")
	private String acctType;
	@ApiModelProperty(value = "登录用户的是否是主账号", example = "")
	private boolean mainUserFlag;

	
	private String module;//前端会根据这个判断显示不同值
//	private String chargeMode; // added for v1.5.8 KNZT-2616 updated for v1.8.2 KNZT-3441
	private String showCredit; // added for v1.8.2 KNZT-3680
	private String canUpgrade; // added for v1.8.2 KNZT-3700
	private Integer companyType; // added for v1.9.3 KNZT-4193
	private Boolean enableApi; // added for v1.9.7 KNZT-4708
	private String payType; // added for lvcy v2.0.6 KNZT-5499

	private List<String> corpNationList; // added for v1.8.2 KNZT-3722 企业国家 权限列表
	private List<String> serviceCorpPersAuthList; // added for v1.8.2 KNZT-3722 服务企业人员类型 权限列表

	private String loginId;// added for v1.9.5 KNZT-4515 登录用户唯一标识
	private String picId; // added for lvcy v2.0.0 KNZT-5142 指纹id
	// added for v2.0.4 chenbl KNZT-5371
	private boolean enableMfa; // MFA
	private String mfaSecret; // MFA Secret
	private String mfaSecretQrUrl; // MFA Secret
	private String mfaToken; // MFA Token
	private Integer daysBeforeExpiry; // 密码即将失效天数 added for v2.0.4 chenbl KNZT-5442
	private String securityPolicy; // 安全策略 added for v2.0.4 chenbl KNZT-5442
	private boolean isLogOut; // 是否是登出 added for lvcy v2.1.2 KNZT-6149
	private boolean beginningGuide = true; // 新手引导 added for v2.1.5 fengsw KNZT-6454
	// added for v2.1.7 chenbl KNZT-6477
	private Boolean canSwitchUi; // UI版本切换开关
	private String uiVersion; // UI版本
	private boolean duplicateOrderRemind; // 是否开启重复订单提醒 added for v2.1.8 fengsw KNZT-6879
	//added for V2.3.2 KNZT-8218 返回userId，用于BI埋点分析
	private String bizLinkId;
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getAcctType() {
		return acctType;
	}

	public void setAcctType(String acctType) {
		this.acctType = acctType;
	}

	public List<String> getReversalPermissionList() {
		return reversalPermissionList;
	}
	public void setReversalPermissionList(List<String> reversalPermissionList) {
		this.reversalPermissionList = reversalPermissionList;
	}
	public String getModule() {
		return module;
	}
	public void setModule(String module) {
		this.module = module;
	}
	public List<String> getPermissionList() {
		return permissionList;
	}
	public void setPermissionList(List<String> permissionList) {
		this.permissionList = permissionList;
	}
	public String getDataScope() {
		return dataScope;
	}
	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}
	public boolean isMainUserFlag() {
		return mainUserFlag;
	}
	public void setMainUserFlag(boolean mainUserFlag) {
		this.mainUserFlag = mainUserFlag;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserLastName() {
		return userLastName;
	}

	public void setUserLastName(String userLastName) {
		this.userLastName = userLastName;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getCompRoleIds() {
		return compRoleIds;
	}

	public void setCompRoleIds(String compRoleIds) {
		this.compRoleIds = compRoleIds;
	}

	public String getCompRoleDescList() {
		return compRoleDescList;
	}

	public void setCompRoleDescList(String compRoleDescList) {
		this.compRoleDescList = compRoleDescList;
	}

	public int getRefreshIntervalSec() {
		return refreshIntervalSec;
	}
	public void setRefreshIntervalSec(int refreshIntervalSec) {
		this.refreshIntervalSec = refreshIntervalSec;
	}

//	public List<SaasMenuTO> getMenuList() {
//		return menuList;
//	}
//	public void setMenuList(List<SaasMenuTO> menuList) {
//		this.menuList = menuList;
//	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	public String getRefreshToken() {
		return refreshToken;
	}
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}
//	public String getKeepToken() {
//		return keepToken;
//	}
//	public void setKeepToken(String keepToken) {
//		this.keepToken = keepToken;
//	}
	public String getAccessSecretKey() {
		return accessSecretKey;
	}
	public void setAccessSecretKey(String accessSecretKey) {
		this.accessSecretKey = accessSecretKey;
	}
	public SaasMenuHome getMenuHome() {
		return menuHome;
	}
	public void setMenuHome(SaasMenuHome menuHome) {
		this.menuHome = menuHome;
	}

	// updated for v1.8.2 KNZT-3441
//	public String getChargeMode() {
//		return chargeMode;
//	}
//
//	public void setChargeMode(String chargeMode) {
//		this.chargeMode = chargeMode;
//	}

	public String getShowCredit() {
		return showCredit;
	}

	public void setShowCredit(String showCredit) {
		this.showCredit = showCredit;
	}

	public String getCanUpgrade() {
		return canUpgrade;
	}

	public void setCanUpgrade(String canUpgrade) {
		this.canUpgrade = canUpgrade;
	}

	public List<String> getCorpNationList() {
		return corpNationList;
	}

	public void setCorpNationList(List<String> corpNationList) {
		this.corpNationList = corpNationList;
	}

	public List<String> getServiceCorpPersAuthList() {
		return serviceCorpPersAuthList;
	}

	public void setServiceCorpPersAuthList(List<String> serviceCorpPersAuthList) {
		this.serviceCorpPersAuthList = serviceCorpPersAuthList;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public Integer getCompanyType() {
		return companyType;
	}

	public void setCompanyType(Integer companyType) {
		this.companyType = companyType;
	}

	public String getLoginId() {
		return loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	public Boolean getEnableApi() {
		return enableApi;
	}

	public void setEnableApi(Boolean enableApi) {
		this.enableApi = enableApi;
	}

	public String getPicId() {
		return picId;
	}

	public void setPicId(String picId) {
		this.picId = picId;
	}

	public boolean getEnableMfa() {
		return enableMfa;
	}

	public void setEnableMfa(boolean enableMfa) {
		this.enableMfa = enableMfa;
	}

	public String getMfaSecret() {
		return mfaSecret;
	}

	public void setMfaSecret(String mfaSecret) {
		this.mfaSecret = mfaSecret;
	}

	public String getMfaSecretQrUrl() {
		return mfaSecretQrUrl;
	}

	public void setMfaSecretQrUrl(String mfaSecretQrUrl) {
		this.mfaSecretQrUrl = mfaSecretQrUrl;
	}

	public String getMfaToken() {
		return mfaToken;
	}

	public void setMfaToken(String mfaToken) {
		this.mfaToken = mfaToken;
	}

	public Integer getDaysBeforeExpiry() {
		return daysBeforeExpiry;
	}

	public void setDaysBeforeExpiry(Integer daysBeforeExpiry) {
		this.daysBeforeExpiry = daysBeforeExpiry;
	}

	public String getSecurityPolicy() {
		return securityPolicy;
	}

	public void setSecurityPolicy(String securityPolicy) {
		this.securityPolicy = securityPolicy;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public boolean isEnableMfa() {
		return enableMfa;
	}

	public boolean getIsLogOut() {
		return isLogOut;
	}

	public void setIsLogOut(boolean isLogOut) {
		this.isLogOut = isLogOut;
	}

	public boolean isBeginningGuide() {
		return beginningGuide;
	}

	public void setBeginningGuide(boolean beginningGuide) {
		this.beginningGuide = beginningGuide;
	}

	public Boolean getCanSwitchUi() {
		return canSwitchUi;
	}

	public void setCanSwitchUi(Boolean canSwitchUi) {
		this.canSwitchUi = canSwitchUi;
	}

	public String getUiVersion() {
		return uiVersion;
	}

	public void setUiVersion(String uiVersion) {
		this.uiVersion = uiVersion;
	}

	public boolean getDuplicateOrderRemind() {
		return duplicateOrderRemind;
	}

	public void setDuplicateOrderRemind(boolean duplicateOrderRemind) {
		this.duplicateOrderRemind = duplicateOrderRemind;
	}

	public String getBizLinkId() {
		return bizLinkId;
	}

	public void setBizLinkId(String bizLinkId) {
		this.bizLinkId = bizLinkId;
	}
}
