package com.qcc.frame.jee.modules.sys.entity;

import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.util.Date;

public class SysEmailMsgHis extends DataEntity<SysEmailMsgHis> {
	
	private String email;
	private Integer sendBy;
	private String messageSubject;
	private Date sendDate;
	private String remarks;

	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public Integer getSendBy() {
		return sendBy;
	}
	public void setSendBy(Integer sendBy) {
		this.sendBy = sendBy;
	}
	public String getMessageSubject() {
		return messageSubject;
	}
	public void setMessageSubject(String messageSubject) {
		this.messageSubject = messageSubject;
	}
	public Date getSendDate() {
		return sendDate;
	}
	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
}
