package com.qcc.frame.jee.modules.sys.form;

import java.util.List;

// added for v2.0.7 chenbl KNZT-6595
public class CompanyAddressUpdateForm {

    private String companyId;
    private String country;
    private String province;
    private String city;
    private String addressLine1;
    private String addressLine2;
    private String zipCode;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public static CompanyAddressUpdateForm buildFrom(String companyId, UserProfileUpdateForm userProfileUpdateForm) {
        if (userProfileUpdateForm == null) {
            return null;
        }
        CompanyAddressUpdateForm companyAddressUpdateForm = new CompanyAddressUpdateForm();
        companyAddressUpdateForm.setCompanyId(companyId);
        companyAddressUpdateForm.setCountry(userProfileUpdateForm.getCompanyCountry());
        companyAddressUpdateForm.setProvince(userProfileUpdateForm.getCompanyProvince());
        companyAddressUpdateForm.setCity(userProfileUpdateForm.getCompanyCity());
        companyAddressUpdateForm.setAddressLine1(userProfileUpdateForm.getCompanyAddressLine1());
        companyAddressUpdateForm.setAddressLine2(userProfileUpdateForm.getCompanyAddressLine2());
        companyAddressUpdateForm.setZipCode(userProfileUpdateForm.getCompanyZipCode());
        return companyAddressUpdateForm;
    }
}
