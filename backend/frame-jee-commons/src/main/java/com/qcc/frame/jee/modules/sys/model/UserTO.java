package com.qcc.frame.jee.modules.sys.model;

import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.util.Date;
import java.util.List;

public class UserTO extends DataEntity<UserTO> {

	private String id;
	private String name;
	private String lastName;
	private String loginName;
	private String email;
	private String phone;
	private String country;
	private String msgReceivedType;
	private String msgReceivedPeriod;
	private String periodExtendValue;
	private String periodTime;
	private String dataScope;
	private String groupIds;
	private String groupNames;
	private String compRoleNames;
	private String compRoleDescriptions; // added for v2.0.7 chenbl KNZT-5787
	private String compRoleIds;
	private List<String> compRoleIdList;
	private List<String> compGroupIdList;
	private boolean showHandover;
	private String msgReceivedTypeRecovery; 
	private boolean systemAdminFlag;
	private String userType;
	private Date lastLoginTime;
	private String lastLoginIp;
	private String accountStatus;// added for v2.3.2 fengsw KNZT-7930 账号状态
	private String lockedReason; // added for v2.3.2 fengsw KNZT-7930 锁定原因 mfa,pwd
	
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public boolean isSystemAdminFlag() {
		return systemAdminFlag;
	}
	public void setSystemAdminFlag(boolean systemAdminFlag) {
		this.systemAdminFlag = systemAdminFlag;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getMsgReceivedType() {
		return msgReceivedType;
	}
	public void setMsgReceivedType(String msgReceivedType) {
		this.msgReceivedType = msgReceivedType;
	}
	public String getMsgReceivedPeriod() {
		return msgReceivedPeriod;
	}
	public void setMsgReceivedPeriod(String msgReceivedPeriod) {
		this.msgReceivedPeriod = msgReceivedPeriod;
	}
	public String getPeriodExtendValue() {
		return periodExtendValue;
	}
	public void setPeriodExtendValue(String periodExtendValue) {
		this.periodExtendValue = periodExtendValue;
	}
	public String getPeriodTime() {
		return periodTime;
	}
	public void setPeriodTime(String periodTime) {
		this.periodTime = periodTime;
	}
	public List<String> getCompRoleIdList() {
		return compRoleIdList;
	}
	public void setCompRoleIdList(List<String> compRoleIdList) {
		this.compRoleIdList = compRoleIdList;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public List<String> getCompGroupIdList() {
		return compGroupIdList;
	}
	public void setCompGroupIdList(List<String> compGroupIdList) {
		this.compGroupIdList = compGroupIdList;
	}

	public String getGroupIds() {
		return groupIds;
	}

	public void setGroupIds(String groupIds) {
		this.groupIds = groupIds;
	}

	public String getGroupNames() {
		return groupNames;
	}
	public void setGroupNames(String groupNames) {
		this.groupNames = groupNames;
	}
	public String getCompRoleNames() {
		return compRoleNames;
	}
	public void setCompRoleNames(String compRoleNames) {
		this.compRoleNames = compRoleNames;
	}

	public String getCompRoleDescriptions() {
		return compRoleDescriptions;
	}

	public void setCompRoleDescriptions(String compRoleDescriptions) {
		this.compRoleDescriptions = compRoleDescriptions;
	}

	public boolean isShowHandover() {
		return showHandover;
	}
	public void setShowHandover(boolean showHandover) {
		this.showHandover = showHandover;
	}
	public String getDataScope() {
		return dataScope;
	}
	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}
	public String getCompRoleIds() {
		return compRoleIds;
	}
	public void setCompRoleIds(String compRoleIds) {
		this.compRoleIds = compRoleIds;
	}
	public String getMsgReceivedTypeRecovery() {
		return msgReceivedTypeRecovery;
	}
	public void setMsgReceivedTypeRecovery(String msgReceivedTypeRecovery) {
		this.msgReceivedTypeRecovery = msgReceivedTypeRecovery;
	}

	public Date getLastLoginTime() {
		return lastLoginTime;
	}

	public void setLastLoginTime(Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public String getLastLoginIp() {
		return lastLoginIp;
	}

	public void setLastLoginIp(String lastLoginIp) {
		this.lastLoginIp = lastLoginIp;
	}

	public String getAccountStatus() {
		return accountStatus;
	}

	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

	public String getLockedReason() {
		return lockedReason;
	}

	public void setLockedReason(String lockedReason) {
		this.lockedReason = lockedReason;
	}
}
