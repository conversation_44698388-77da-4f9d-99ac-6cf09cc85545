package com.qcc.frame.jee.modules.sys.entity.mapping;

public class CompanyMainInfoTO {
	private String id;
	private String mainUserId;
	private String category;
	private int type;
	private short enableApi;
	private String menuHomeGroupId;

	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public String getMainUserId() {
		return mainUserId;
	}
	public void setMainUserId(String mainUserId) {
		this.mainUserId = mainUserId;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public short getEnableApi() {
		return enableApi;
	}
	public void setEnableApi(short enableApi) {
		this.enableApi = enableApi;
	}
	public String getMenuHomeGroupId() {
		if(menuHomeGroupId == null) {
			return "";
		}
		return menuHomeGroupId;
	}
	public void setMenuHomeGroupId(String menuHomeGroupId) {
		this.menuHomeGroupId = menuHomeGroupId;
	}

}
