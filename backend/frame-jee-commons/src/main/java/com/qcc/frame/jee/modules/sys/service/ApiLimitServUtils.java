package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.RequestTO;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.utils.WebContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApiLimitServUtils {
	protected Logger logger = LoggerFactory.getLogger(getClass());
	@Autowired
	private RedisServUtils redisServUtils;
	
	public void addAndCheckLimitCountByRedisLua(String key, String reqUUID, int waitCount, int redisTimeout) throws MessageException {
		long currentTimestampSec = System.currentTimeMillis() / 1000;
		long timeoutSecBegin = currentTimestampSec - redisTimeout;
		long timeoutSecEnd = currentTimestampSec + 20;
		boolean limitFlag = redisServUtils.addLimitApiByScript(Global.getApiLimitLuaScript(), key,
				reqUUID, waitCount, currentTimestampSec, timeoutSecBegin, timeoutSecEnd);
		if(limitFlag) {
			throw new MessageException("apilimit");
		}
	}
	
	public void deleteLimitCountForRedisLua(String redisKey, String reqUUID, String concurrencyId) throws MessageException {
		Long deleteCount = redisServUtils.deleteLimitApiForScript(redisKey, reqUUID);
		logger.info("deleteconcurrencyId=" + concurrencyId + " " + reqUUID + " deleteCount:" + deleteCount);
	}
}
