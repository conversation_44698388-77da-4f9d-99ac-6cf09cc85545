package com.qcc.frame.jee.modules.sys.model.saas;

import java.util.List;

public class RedisUserAccessTO implements java.io.Serializable, Cloneable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4160134227039029684L;
	
	private String userId;
	private String loginName;
	private String loginType;
	private String loginSource;
	private String clientId;
	private String accessToken;
	private String refreshToken;
	private String loginIp;
	private List<String> permissionList;
	private List<String> reversalPermissionList;
	private long lastAccessTime;//最后一次访问时间
	private long loginTime; 
	private boolean expiryFlag;//标识是否将过期, 用于刷新access token
	private long lastRefreshTime;//最后一次刷新时间
	private long lastRefreshPermissionTime;//最近一次刷新权限的时间
	private boolean keep;//该用户是否保持登录
	private String reserved1;
	private String reserved2;
	private String menuHomeGroupId;
	private String mfaSecret; // added for v2.0.4 chenbl KNZT-5371

	public String getMenuHomeGroupId() {
		return menuHomeGroupId;
	}
	public void setMenuHomeGroupId(String menuHomeGroupId) {
		this.menuHomeGroupId = menuHomeGroupId;
	}

	public String getLoginSource() {
		return loginSource;
	}
	public void setLoginSource(String loginSource) {
		this.loginSource = loginSource;
	}
	public String getLoginType() {
		return loginType;
	}
	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}
	public List<String> getReversalPermissionList() {
		return reversalPermissionList;
	}
	public void setReversalPermissionList(List<String> reversalPermissionList) {
		this.reversalPermissionList = reversalPermissionList;
	}
	public long getLastRefreshPermissionTime() {
		return lastRefreshPermissionTime;
	}
	public void setLastRefreshPermissionTime(long lastRefreshPermissionTime) {
		this.lastRefreshPermissionTime = lastRefreshPermissionTime;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getReserved1() {
		return reserved1;
	}
	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}
	public String getReserved2() {
		return reserved2;
	}
	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}
	public boolean isKeep() {
		return keep;
	}
	public void setKeep(boolean keep) {
		this.keep = keep;
	}
	public long getLoginTime() {
		return loginTime;
	}
	public void setLoginTime(long loginTime) {
		this.loginTime = loginTime;
	}
	public long getLastRefreshTime() {
		return lastRefreshTime;
	}
	public void setLastRefreshTime(long lastRefreshTime) {
		this.lastRefreshTime = lastRefreshTime;
	}
	public boolean isExpiryFlag() {
		return expiryFlag;
	}
	public void setExpiryFlag(boolean expiryFlag) {
		this.expiryFlag = expiryFlag;
	}
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getLoginIp() {
		return loginIp;
	}
	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}


	public long getLastAccessTime() {
		return lastAccessTime;
	}
	public void setLastAccessTime(long lastAccessTime) {
		this.lastAccessTime = lastAccessTime;
	}
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	public String getRefreshToken() {
		return refreshToken;
	}
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}
	public List<String> getPermissionList() {
		return permissionList;
	}
	public void setPermissionList(List<String> permissionList) {
		this.permissionList = permissionList;
	}

	public Object clone() throws CloneNotSupportedException {
		return super.clone();
    }

    public String getMfaSecret() {
        return mfaSecret;
    }

    public void setMfaSecret(String mfaSecret) {
        this.mfaSecret = mfaSecret;
    }
}
