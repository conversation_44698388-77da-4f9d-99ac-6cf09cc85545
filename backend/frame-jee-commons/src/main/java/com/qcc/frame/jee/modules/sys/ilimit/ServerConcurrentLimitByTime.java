package com.qcc.frame.jee.modules.sys.ilimit;

import com.qcc.frame.jee.commons.service.MessageException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 限制某些功能的并发次数
 * <AUTHOR>
 *
 */
public class ServerConcurrentLimitByTime {
	protected Logger logger = LoggerFactory.getLogger(getClass());

	private static final Map<String, AtomicInteger> limitMap = new ConcurrentHashMap<>();
	
	private static ServerConcurrentLimitByTime instance = new ServerConcurrentLimitByTime();
	private ServerConcurrentLimitByTime() {}
	
	public static ServerConcurrentLimitByTime getInstance() {
		return instance;
	}
	
	private synchronized void beforeExecute(String keyAndFunctionSign, int limitSize) throws MessageException {
		AtomicInteger time = limitMap.get(keyAndFunctionSign);
		if(time == null) {
			time = new AtomicInteger(0);
			limitMap.put(keyAndFunctionSign, time);
		}

		if(time.get() >= limitSize) {
			logger.info("[debugapilimit]" + keyAndFunctionSign + " apilimit_time " + time.get());
			throw new MessageException("apilimit");
		} else {
			time.getAndIncrement();
		}
	}

	public void execute(String key, String functionSign, int limitSize, ServerConcurrentLimitCallbackInterface callback) throws MessageException {
		beforeExecute(key + "|" + functionSign, limitSize);

		try {
			callback.process();
		} catch(MessageException e) {
			throw e;
		} catch(Exception e) {
			throw e;
		} finally {
			afterExecute(key + "|" + functionSign);
		}
		
	}
	
	private synchronized void afterExecute(String keyAndFunctionSign) {
		AtomicInteger time = limitMap.get(keyAndFunctionSign);
		if(time != null) {
			time.getAndDecrement();
		}
		
//		logger.info("[debugapilimit]" + key + "==" + value + " end synchronized");
	}

	public static Map<String, AtomicInteger> getLimitMap() {
		return limitMap;
	}
	
	
}
