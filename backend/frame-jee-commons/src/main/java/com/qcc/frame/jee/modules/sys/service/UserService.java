package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.PwdSecurityPolicyEnum;
import com.qcc.frame.commons.util.EncryptUtil;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.security.Digests;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.service.ServiceException;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.condition.CompUserListCondition;
import com.qcc.frame.jee.modules.sys.dao.MenuDao;
import com.qcc.frame.jee.modules.sys.dao.RoleDao;
import com.qcc.frame.jee.modules.sys.dao.UserDao;
import com.qcc.frame.jee.modules.sys.entity.*;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompUserListTO;
import com.qcc.frame.jee.modules.sys.model.saas.RedisSaasMenuTO;
import com.qcc.frame.jee.modules.sys.model.saas.RedisSaasRoleTO;
import com.qcc.frame.jee.modules.sys.model.saas.SaasMenuTO;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.qcc.pa.utils.RsaUtil;
import org.apache.shiro.session.UnknownSessionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * user Service
 */
@Service
//@Transactional(readOnly = true)
public class UserService extends CrudService<UserDao, User> {
	public static final String HASH_ALGORITHM = "SHA-1";
	public static final int HASH_INTERATIONS = 1024;
	public static final int SALT_SIZE = 8;
	private static final String DEFAULT_GROUP_ID = "default";
	
	@Autowired
	private RedisService redisService;
	@Autowired
	private SysCompInfoColExtService sysCompInfoColExtService;
	
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private MenuDao menuDao;
	@Autowired
	private CommSysUserPwdHistService userPwdHistService;

	public static final String RSA_PREFIX = "RSA#";
	private static final String FRONT_RSA_PRIVATE_KEY = Global.getConfig("front.rsa.private.key");

	public List<User> findUserByCompanyId4Select(String companyId) {
		User user = new User();
		user.setCompany(new Company(companyId));
		return dao.findUserByCompanyId4Select(user);
	}
	
	public int findUserCountByCompanyId(String companyId) {
		if(StringUtils.isBlank(companyId)) {
			return 0;
		}
		User user = new User();
		user.setCompany(new Company(companyId));
		long count = dao.findAllCount(user);
		return Integer.parseInt(count+"");
	}

	public void updateLoginInfo(User user) {
		dao.updateLoginInfo(user);
	}
	
	public User getByLoginName(String loginName) {
		return dao.getByLoginName(new User(null, loginName));
	}
	
	/**
	 * 用于判断登录名是否存在于companyId下
	 * @param companyId
	 * @param loginName
	 * @return
	 */
	public User getByLoginName(String companyId, String loginName) {
		return dao.getByCompanyIdAndLoginName(companyId, loginName);
	}
	
	public void physicallyDeleteUserRoleByUserId(String userId) {
		roleDao.physicallyDeleteUserRoleByUserId(userId);
	}
	
	/**
	 * 物理删除系统用户相关表和物理文件, 请放在最后调用
	 * @param userId
	 */
	public void physicallyDeleteSysUserAndUserRole(String userId) {
		roleDao.physicallyDeleteUserRoleByUserId(userId);
		dao.physicallyDelete(userId);
		
	}
	
	/**
	 * 通过公司id和用户id返回用户信息, 防止刷数据
	 * @param companyId
	 * @param userId
	 * @return
	 */
	public User getUserByCompanyIdAndUserId(String companyId, String userId) {
		User user = get(userId);
		if(user != null && StringUtils.equals(companyId, (user.getCompany() != null ? user.getCompany().getId() : ""))) {
			return user;
		}
		return null;
	}
	
	public List<Menu> listMenuByUserIdAndRoleTypeList(String userId, List<String> roleTypeList) {
		return menuDao.listMenuByUserIdAndRoleTypeList(userId, roleTypeList);
	}
	
	public List<Menu> listAllCompAssignMenu() {
		return menuDao.listAllCompAssignMenu(Constants.Sys.COMP_ASSIGN_PERMISSION_PREFIX);
	}
	
	/**
	 * 生成安全的密码，生成随机的16位salt并经过1024次 sha-1 hash
	 */
	public static String entryptPassword(String plainPassword) {
		String plain = Encodes.unescapeHtml(plainPassword);
		byte[] salt = Digests.generateSalt(SALT_SIZE);
		byte[] hashPassword = Digests.sha1(plain.getBytes(), salt, HASH_INTERATIONS);
		return Encodes.encodeHex(salt)+Encodes.encodeHex(hashPassword);
	}
	
	/**
	 * 验证密码
	 * @param plainPassword 明文密码
	 * @param password 密文密码
	 * @return 验证成功返回true
	 */
	public static boolean validatePassword(String plainPassword, String password) {
		String plain = Encodes.unescapeHtml(plainPassword);
		byte[] salt = Encodes.decodeHex(password.substring(0,16));
		byte[] hashPassword = Digests.sha1(plain.getBytes(), salt, HASH_INTERATIONS);
		return password.equals(Encodes.encodeHex(salt)+Encodes.encodeHex(hashPassword));
	}

	public void checkCompanyValid(Company company) throws MessageException {
		if(company == null) {
			throw new MessageException("err.system.account.company.not.in.date.range");
		}
		Date curDt = DateUtils.getCurDate();
		// added for v2.0.5 KNZT-5573 fengsw 禁用账号提示
		if(company.getEndDate() != null) {
			if(company.getEndDate().before(curDt)) {
				if (CompTypeEnum.isSelfPay(company.getType())) {
					throw new MessageException("err.system.account.company.forbidden.client");
				}
				throw new MessageException("err.system.account.company.not.in.date.range");
			}
		}
		if(company.getBeginDate() != null) {
			if(company.getBeginDate().after(curDt)) {
				throw new MessageException("err.system.account.company.not.in.date.range");
			}
		}
		
	}

	// updated for v2.0.7 chenbl KNZT-5773
	public void checkUserValid4Login(User user) throws MessageException {
		if(Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
			throw new MessageException("err.system.account.inactive");
		} else if(Constants.Sys.USER_STATUS_LOCKED.equals(user.getStatus())) {
			throw new MessageException("err.system.account.locked");
		} else if (Constants.Sys.USER_STATUS_PASSWORD_EXCEPTION.equals(user.getStatus())) {
			throw new MessageException(Constants.MessageExceptionKey.ERR_SYSTEM_ACCOUNT_PASSWORD_EXCEPTION);
		}
		
		if(user.getCompany() == null || !StringUtils.equals(user.getCompany().getDelFlag(), "0")) {
			logger.error("company is deleted");
			throw new MessageException("err.access");
		}

	}

	// added for v2.0.7 chenbl KNZT-5773
	public void checkUserValid4ResetPassword(User user) throws MessageException {
		if (Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
			throw new MessageException("err.system.account.inactive");
		} else if (Constants.Sys.USER_STATUS_LOCKED.equals(user.getStatus())) {
			throw new MessageException("err.system.account.locked");
		}

		if (user.getCompany() == null || !StringUtils.equals(user.getCompany().getDelFlag(), "0")) {
			logger.error("company is deleted");
			throw new MessageException("err.access");
		}

	}
	
	public boolean validateEmail(String email) {
		if (dao.checkEmailExists(email)==0){
			return true;
		}
		return false;
	}
	
	/**
	 * 获取用户
	 * @param id
	 * @return
	 */
	public User getUser(String id) {
		return UserUtils.get(id);
	}

	public List<Role> findUserRole(String userId, String roleType){
		Role role = new Role();
		role.setUser(new User(userId));
		List<Role> roleList = roleDao.findList(role);
		if(StringUtils.isBlank(roleType)) {
			return roleList;
		}
		
		List<Role> resultList = new ArrayList<>();
		if(roleList != null) {
			for(Role r : roleList) {
				if(StringUtils.equalsIgnoreCase(roleType, r.getRoleType())) {
					resultList.add(r);
				}
			}
		}
		return resultList;
	}

	
	public void deleteUserRole(String userId) {
		User user = new User(userId);
		dao.deleteUserRole(user);
	}
	
	public void insertUserRole(String userId, List<String> roleIdList) {
		User user = new User(userId);
		for(String roleId : roleIdList) {
			if(StringUtils.isNotBlank(roleId)) {
			Role role = new Role();
			role.setId(roleId);
			user.getRoleList().add(role);
			}
		}
		dao.insertUserRole(user);
	}

	// added for v2.1.8 chenbl KNZT-6899
	public void updateUserRole(String userId, Collection<String> roleIds) {
		if (StringUtils.isBlank(userId)) {
			return;
		}
		deleteUserRole(userId);
		if (CollectionUtils.isNotEmpty(roleIds)) {
			insertUserRole(userId, new ArrayList<>(roleIds));
		}
	}

	/**
	 * 根据登录名获取用户
	 * @param loginName
	 * @return
	 */
	public User getUserByLoginName(String loginName) {
		return UserUtils.getByLoginName(loginName);
	}
	
	public void saveUser(User user) {
		if (StringUtils.isBlank(user.getId())){
			user.preInsert();
			dao.insert(user);
		}else{
			// 清除原用户机构用户缓存
//			User oldUser = userDao.get(user.getId());
//			if (oldUser.getOffice() != null && oldUser.getOffice().getId() != null){
//				CacheUtils.remove(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + oldUser.getOffice().getId());
//			}
			// 更新用户数据
			user.preUpdate();
			dao.update(user);
		}
		if (StringUtils.isNotBlank(user.getId())){
			// 更新用户与角色关联
			dao.deleteUserRole(user);
			if (user.getRoleList() != null && user.getRoleList().size() > 0){
				dao.insertUserRole(user);
			}else{
				throw new ServiceException(user.getLoginName() + "没有设置角色！");
			}
		}
	}
	
	public boolean hasPrefixEngNameRole(List<String> roleIdList, String prefixEngName) {
		if(roleIdList == null || roleIdList.size() == 0) {
			return false;
		}
		int count = roleDao.countPrefixEngNameRole(roleIdList, prefixEngName);
		return count > 0;
	}
	
	public void updateUser(User user) {
		user.preUpdate();
		dao.update(user);
		// 清除用户缓存
//		UserUtils.clearCache(user);
//		// 清除权限缓存
//		systemRealm.clearAllCachedAuthorizationInfo();
	}
	
	public void deleteUser(User user) {
		user.preUpdate();
		dao.delete(user);
	}
	
	public List<Role> findRole(Role role){
		return roleDao.findList(role);
	}
	
	public List<RedisSaasRoleTO> convertRoleList2Redis(List<Role> list) {
		List<RedisSaasRoleTO> resultList = new ArrayList<>();
		if(list != null) {
			for(Role role : list) {
				resultList.add(convertRole2Redis(role));
			}
		}
		return resultList;
	}
	
	public RedisSaasRoleTO convertRole2Redis(Role role) {
		RedisSaasRoleTO result = new RedisSaasRoleTO();
		result.setDataScope(role.getDataScope());
		result.setEnname(role.getEnname());
		result.setId(role.getId());
		result.setModule(role.getModule());
		result.setName(role.getName());
		result.setRoleType(role.getRoleType());
		return result;
	}
	
	public boolean logicDeleteSession(String sessionId) {//TODO
		try {
//			Session session = getSessionDao().readSession(sessionId);
//			session.setAttribute(Constants.SESSION_KEY_LOGIN_MULTIPLE_FLAG, "true");
//			getSessionDao().update(session);
			return true;
		} catch(UnknownSessionException e) {
			//ignore this exception
			logger.info("[LOGINLOG] UnknownSessionException:[" + sessionId + "]");
			return true;
		} catch(Exception e) {
			logger.error("[LOGINLOG] [" + sessionId + "]", e);
		}
		return false;
	}
	
	public void saveUserInfo(User user) {
		if (StringUtils.isBlank(user.getId())){
			user.preInsert();
			dao.insert(user);
		}else{
			// 清除原用户机构用户缓存
//			User oldUser = userDao.get(user.getId());
//			if (oldUser.getOffice() != null && oldUser.getOffice().getId() != null){
//				CacheUtils.remove(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + oldUser.getOffice().getId());
//			}
			// 更新用户数据
			user.preUpdate();
			dao.update(user);
		}
	}
	
	
	public String getCompGroupIdByUserId(String userId) {
		return dao.getCompGroupIdByUserId(userId);
	}
	
	public User getByLoginName(User user) {
		return dao.getByLoginName(user);
	}
	
	public List<Menu> findAllMenuList(Menu menu) {
		return menuDao.findAllList(menu);
	}

	
	public List<Menu> findMenuByUserId(Menu menu) {
		User user = UserUtils.getUser();
		List<String> roleIdList = new ArrayList<>();
		return menuDao.findByUserId(menu);
	}
	
	public List<RedisSaasMenuTO> convertMenuList2Redis(List<Menu> menuList) {
		List<RedisSaasMenuTO> resultList = new ArrayList<>();
		if(menuList != null) {
			for(Menu menu : menuList) {
				resultList.add(convertMenu2Redis(menu));
			}
		}
		return resultList;
	}
	
	public RedisSaasMenuTO convertMenu2Redis(Menu menu) {
		RedisSaasMenuTO result = new RedisSaasMenuTO();
		result.setHref(menu.getHref());
		result.setIcon(menu.getIcon());
		result.setId(menu.getId());
		result.setIsShow(menu.getIsShow());
		result.setName(menu.getName());
		result.setParentId(menu.getParent() != null ? menu.getParent().getId() : null);
		result.setParentIds(menu.getParentIds());
		result.setPermission(menu.getPermission());
		result.setSort(menu.getSort());
		result.setTarget(menu.getTarget());
		if(menu.getRemarks() != null && menu.getRemarks().startsWith("home_module_remark:")) {
			result.setHomeModuleRemark(menu.getRemarks().substring("home_module_remark:".length()));
		}
		
		return result;
	}

	
	public List<SaasMenuTO> regroupMenu2ParentChild(List<RedisSaasMenuTO> menuList) {
		String rootMenuId = Global.getConfig("qcc.menu.root");
//		System.out.println("1:"+JSON.toJSONString(menuList));
		List<SaasMenuTO> mainMenuList = new ArrayList<>();
		if(menuList != null) {
			Iterator<RedisSaasMenuTO> it = menuList.iterator();
			RedisSaasMenuTO tmenu = null;
			List<String> idAndParentList = new ArrayList<>();
			String idAndParent = null;
			while(it.hasNext()) {//移除会导致死循环的数据
				tmenu = it.next();
				if(StringUtils.isBlank(tmenu.getHref())) {
					it.remove();
				} else {
					if(StringUtils.isNotBlank(tmenu.getParentId())) {
						if(StringUtils.equals(tmenu.getId(), tmenu.getParentId())) {
							logger.error("remove menuDeedLoop 1 for " + tmenu.getId());
							it.remove();
						} else {
							idAndParent = tmenu.getId() + "_" + tmenu.getParentId();
							if(idAndParentList.contains(idAndParent)) {
								logger.error("remove menuDeedLoop 2 for " + tmenu.getId());
								it.remove();
							} else {
								idAndParentList.add(idAndParent);
								idAndParent = tmenu.getParentId() + "_" + tmenu.getId();
								if(idAndParentList.contains(idAndParent)) {
									logger.error("remove menuDeedLoop 3 for " + tmenu.getId());
									it.remove();
								} else {
									idAndParentList.add(idAndParent);
								}
							}
						}
					}
				}
			}
			
//			System.out.println("2:"+JSON.toJSONString(menuList));
			for(RedisSaasMenuTO menu : menuList) {
				if(StringUtils.equals(rootMenuId, menu.getParentId())) {
					mainMenuList.add(convertMenu2Saas(menu));
				}
			}
			
			for(SaasMenuTO mainMenu : mainMenuList) {
				addSubMenu2Main(mainMenu, menuList);
			}
		}
		return mainMenuList;
	}
	
	private SaasMenuTO convertMenu2Saas(RedisSaasMenuTO menu) {
		SaasMenuTO saasMenu = new SaasMenuTO();
		saasMenu.setHref(menu.getHref());
		saasMenu.setId(menu.getId());
		saasMenu.setIsShow(menu.getIsShow());
		saasMenu.setPermission(menu.getPermission());
		saasMenu.setTitle(menu.getName());
		return saasMenu;
	}
	
	private void addSubMenu2Main(SaasMenuTO mainMenu, List<RedisSaasMenuTO> menuList) {
		for(RedisSaasMenuTO menu : menuList) {
			if(StringUtils.equals(menu.getParentId(), mainMenu.getId())) {
				SaasMenuTO subMenu = convertMenu2Saas(menu);
				addSubMenu2Main(subMenu, menuList);
				mainMenu.getSubList().add(subMenu);
			}
		}
	}
	
	public void updatePasswordById(String id, String loginName, String newPassword) {
		User user = new User(id);
		user.setPassword(entryptPassword(newPassword));
		dao.updatePasswordById(user);
		// 清除用户缓存
		user.setLoginName(loginName);
	}

    
    public Page<User> findUser(Page<User> page, User user) {
        user.setPage(page);
        // 执行分页查询
        page.setList(dao.findList(user));
        return page;
    }
    
    public User getMainUserByCompanyId(String companyId) {
    	return dao.getMainUserByCompanyId(companyId);
    }
    
    public String getModuleByUserId(String userId) {
    	return roleDao.getModuleByUserId(userId);
    }

	public void saveEmail(String userId, String email) throws MessageException {
		User user = this.get(userId);
		//邮箱保存
		if (StringUtils.isNotBlank(email)) {
			if (!StringUtils.validateEmail(email)) {
				throw new MessageException("msg:Incorrect email format.");
			}
			if (!this.validateEmail(email) && !StringUtils.equals(email, user.getEmail())) {
				throw new MessageException("msg:Email occupied.");
			}
			user.setEmail(email);
			this.save(user);
		}
	}
	
	public List<MenuHome> listMenuHome() {
		return menuDao.listMenuHome();
	}

	// added for v2.0.6 ZS-452 【角色管理】角色信息编辑与保存
	public List<String> getAllUserIdByCompanyId(String companyId) {
		return dao.getAllUserIdByCompanyId(companyId);
	}

	// added for lvcy v2.0.7 KNZT-5739 查询所有激活的用户
	public List<String> getUserIdByCompanyIdWithoutInactive(String companyId) {
		return dao.getUserIdByCompanyIdWithoutInactive(companyId);
	}

	// added for ZS-473 【权限校验】后端增加权限校验
	public boolean checkPermissionForUserWithPermissionStr(String userId, String permission){
		if(StringUtils.isAnyBlank(userId, permission)){
			return false;
		}
		// 从数据库count值判断该用户是否存在该权限 1表示存在，0表示不存在
		int count = roleDao.countPermissionForUserWithPermissionStr(userId, permission);
		return count > 0;
	}

	/**
	 * added for v1.5.6 KNZT-2561
	 */
	public List<CompUserListTO> listCompUser(CompUserListCondition condition) {
		return dao.listCompUser(condition);
	}

	// added for v1.6.9 KNZT-3009
	public boolean existUserWithCompRoleInGroup( String groupId, String compRoleId, String currentUserId){
		return dao.countUserWithCompRoleInGroup(groupId, compRoleId, currentUserId) > 0;
	}

	// added for v1.6.9 KNZT-3009
	public List<Role> findRolesByRoleType(String roleType){
		return roleDao.findListByRoleType(roleType);
	}

	/**
	 * Rsa密码解密，失败时返回原始密码
	 *
	 * @param pwd        密码
	 * @return
	 */
	public String decryptRSAForFront(String pwd) {
		if(StringUtils.isBlank(pwd)) {
			return "";
		}
		String password = pwd;
		try {
			if(pwd.startsWith(RSA_PREFIX)) {
				password = RsaUtil.decrypt(pwd.substring(RSA_PREFIX.length()), FRONT_RSA_PRIVATE_KEY);
			} else if(pwd.length() > 50) {
				logger.info("use plainpwd:" + pwd);
				return password;
			} else {
				logger.info(" RSAnoprefix :" + pwd);
				password = RsaUtil.decrypt(pwd, FRONT_RSA_PRIVATE_KEY);
			}
		} catch (Exception e) {
			logger.error("RsaDecrypt failed: " + pwd + "=" + (e != null ? e.getMessage() : null));

			// 临时兼容
			try {
				String tempPrivateKey = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAhZcjNOH0z3TwqLK73/pBAeQyf7vIm7TviRnfL9jfnn1edheHAI8ZoQA0hJldLvqVCYuQvj2VKR/aMu3i9k3xKwIDAQABAkAUZWz0qAMOoZYUy6Uvny/JsCOdqPgJ/egMkKyoy33o3pZEWYiB1DRsn0eXErwZEnJy65yl/CJfR8wrNZYHAjBRAiEA1NF0WR+mjZULFH+QCUrqPSpKB1wxf+CoqnEaRCnvJ88CIQCgsk4C9r1Ncgw/h+tQAcd9jIOBQeSSQONfXrfNwaub5QIgT7zGthnYwpWEWELPSdwK1a4BnK5bUYlwSKQl7kIz/BsCIQCSzvLoUETXmQbJj8IR4N5Au/t0Ph+04eUXvu/K4VGHZQIgWKldDq0pJx15jb/YD8vvISzuN48JMA7DlCQsyhuqOUc=";
				if(pwd.startsWith(RSA_PREFIX)) {
					password = RsaUtil.decrypt(pwd.substring(RSA_PREFIX.length()), tempPrivateKey);
				} else if(pwd.length() > 50) {
					logger.info("Old use plainpwd:" + pwd);
					return password;
				} else {
					logger.info("Old RSAnoprefix :" + pwd);
					password = RsaUtil.decrypt(pwd, tempPrivateKey);
				}
				logger.info("Old RsaDecrypt success");
			} catch (Exception e2) {
				logger.error("Old RsaDecrypt failed: " + pwd + "=" + (e != null ? e.getMessage() : null));
			}
		}
		return password;
	}


	public static String encryptWithMd5(String plainText) {
		if (StringUtils.isBlank(plainText)) {
			plainText = IdGenUtil.uuid();
		}
		String passwordTmp = EncryptUtil.encodeMd5(plainText).toLowerCase();
		return 'p' + EncryptUtil.encodeMd5((passwordTmp + "6D5E7B12-8DF3-11EE-A225-FA163E618C8D" + plainText)).substring(1, 8).toLowerCase();
	}

	public static String generateUserIdToken(String userId) {
		if (StringUtils.isBlank(userId)) {
			return "";
		}
		String token = StringUtils.getSha256Token4Validate(userId);
		return userId + Constants.TokenSplit.SPLIT + token;
	}

	public static String getAndValidUserIdToken(String str) {
		if (StringUtils.isBlank(str)) {
			return "";
		}
		String[] splits = StringUtils.split(str, Constants.TokenSplit.SPLIT);
		if (splits == null || splits.length != 2) {
			return "";
		}
		boolean validated = StringUtils.validateSha256Token(splits[0], splits[1]);
		return validated ? splits[0] : "";
	}

	/**
	 * added for v1.9.7 KNZT-4708
	 *
	 * @param company
	 * @return boolean
	 */
	public boolean checkCompanyValidRtnBoolean(Company company) {
		try {
			checkCompanyValid(company);
			return true;
		} catch (MessageException e) {
			return false;
		}
	}

	/**
	 * 更新加密用户id
	 * added for lvcy v2.0.0 KNZT-5142
	 *
	 * @param userId
	 * @param encryptUserId
	 * @return
	 */
	public void updateEncryptUserId(String userId, String encryptUserId) {
		User user = new User();
		user.setId(userId);
		user.setEncryptUserId(encryptUserId);
		user.preUpdate();
		dao.updateEncryptUserId(user);
	}

	/**
	 * added for v2.0.4 chenbl KNZT-5371
	 * 更新用户MFA Secret
	 *
	 * @param userId
	 * @param mfaSecret
	 * @return
	 */
	public void updateMfaSecret(String userId, String mfaSecret, String updateByUserId) {
		User user = new User();
		user.setId(userId);
		user.setMfaSecret(mfaSecret);
		user.setUpdateBy(new User(updateByUserId));
		user.setUpdateDate(new Date());
		dao.updateMfaSecret(user);
	}

	// added for v2.0.4 chenbl KNZT-5371
	public boolean checkNeedMfa(String loginName) throws MessageException {
		User user = getByLoginName(loginName);
		MsgExceptionUtils.checkIsNull(user);
		MsgExceptionUtils.checkIsNull(user.getCompanyId());
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(user.getCompanyId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ENABLE_MFA);
		return Constants.YES.equals(value);
	}

	// added for v2.0.4 chenbl KNZT-5371
	// updated for v2.3.2 fengsw KNZT-7930 去除不必要的MessageException
	public boolean checkNeedMfaByCompanyId(String companyId) {
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ENABLE_MFA);
		return Constants.YES.equals(value);
	}

	// added for v2.0.4 chenbl KNZT-5442
	public PwdSecurityPolicyEnum getCompPwdSecurityPolicy(String companyId){
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_SECURITY_POLICY);
		PwdSecurityPolicyEnum policyEnum = PwdSecurityPolicyEnum.getByCode(value);
		return policyEnum == null ? PwdSecurityPolicyEnum.BAS : policyEnum;
	}

	public void checkPwd4Security(PwdSecurityPolicyEnum policyEnum, String userId, String loginName, String oldPwd, String newPlainPwd) throws MessageException {
		if (policyEnum == PwdSecurityPolicyEnum.ADV) {
			// 密码正则
			String pwdRegex = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityADV.CONFIG_TYPE, Constants.SysConfig.SecurityADV.CONFIG_KEY_PWD_REGEX, "");
			boolean matches = Pattern.matches(pwdRegex, newPlainPwd);
			MsgExceptionUtils.failBuild(!matches, Constants.MessageExceptionKey.ERR_PWD_REGEX);

			// 密码与用户名字字符相同长度限制
			String pwdUsernameSameCharLimitStr = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityADV.CONFIG_TYPE, Constants.SysConfig.SecurityADV.CONFIG_KEY_PWD_USERNAME_SAME_CHAR_LIMIT, "4");
			Integer pwdUsernameSameCharLimit = StringUtils.toInteger(pwdUsernameSameCharLimitStr);
			boolean similar = StringUtils.isSimilar(loginName, newPlainPwd, pwdUsernameSameCharLimit);
			MsgExceptionUtils.failBuild(similar, Constants.MessageExceptionKey.ERR_PWD_USERNAME_SIMILAR);

			// 历史密码不能重复个数
			if (StringUtils.isNotBlank(oldPwd)) {
				String pwdRecentUniqueLimitStr = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityADV.CONFIG_TYPE, Constants.SysConfig.SecurityADV.CONFIG_KEY_PWD_RECENT_UNIQUE_LIMIT, "9");
				Integer pwdRecentUniqueLimit = StringUtils.toInteger(pwdRecentUniqueLimitStr);
				MsgExceptionUtils.failBuild(validatePassword(newPlainPwd, oldPwd), Constants.MessageExceptionKey.ERR_PWD_RECENT_REPEAT, pwdRecentUniqueLimitStr);
				List<SysUserPwdHist> recentList = userPwdHistService.getRecentList(userId, pwdRecentUniqueLimit);
				boolean anyMatch = recentList.stream().anyMatch(sysUserPwdHist -> validatePassword(newPlainPwd, sysUserPwdHist.getPassword()));
				MsgExceptionUtils.failBuild(anyMatch, Constants.MessageExceptionKey.ERR_PWD_RECENT_REPEAT, pwdRecentUniqueLimitStr);
			}
		} else {
			boolean matches = Pattern.matches("^(?![^a-zA-Z]+$)(?!\\D+$).{6,}$", newPlainPwd);
			MsgExceptionUtils.failBuild(!matches, Constants.MessageExceptionKey.ERR_PWD_REGEX_BAS);
		}
	}

	public static boolean setResult4checkPwd4Security(MessageException e, JsonResult jsonResult) {
		if (Constants.MessageExceptionKey.ERR_PWD_REGEX.equals(e.getMessage())
				|| Constants.MessageExceptionKey.ERR_PWD_USERNAME_SIMILAR.equals(e.getMessage())
				|| Constants.MessageExceptionKey.ERR_PWD_RECENT_REPEAT.equals(e.getMessage())
				|| Constants.MessageExceptionKey.ERR_PWD_REGEX_BAS.equals(e.getMessage())) {
			jsonResult.setStatus(Constants.Result.PWD_NOT_RULE);
			jsonResult.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
			return true;
		} else {
			return false;
		}
	}

	// added for v2.0.4 chenbl KNZT-5442
	public void savePwdHist4Security(PwdSecurityPolicyEnum policyEnum, String companyId, String userId, String newPlainPwd) throws MessageException {
		if (policyEnum == PwdSecurityPolicyEnum.ADV) {
			userPwdHistService.save4NoLogin(companyId, userId, newPlainPwd);
		}
	}

	private String getCompanyIdByLoginName(String loginName) {
		if (StringUtils.isNotBlank(loginName)) {
			User user = getByLoginName(loginName);
			if (user != null) {
				return user.getCompanyId();
			}
		}
		return null;
	}

	/**
	 * added for v2.0.4 chenbl KNZT-5442
	 * MFA验证，登录成功后用户Token的失效时间
	 */
	public int getLoginMfaTokenExpirySec() throws MessageException {
		String value = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityCOMM.CONFIG_TYPE, Constants.SysConfig.SecurityCOMM.CONFIG_KEY_LOGIN_MFA_TOKEN_EXPIRY_SEC, "600");
		return StringUtils.toInteger(value);
	}

	/**
	 * added for v2.0.4 chenbl KNZT-5442
	 * 登录成功后用户Access Token的失效时间(前提是没有被Refresh)
	 */
	public int getLoginAccessTokenExpirySec(String companyId) {
		String value = null;
		if (StringUtils.isNotBlank(companyId)) {
			PwdSecurityPolicyEnum policyEnum = getCompPwdSecurityPolicy(companyId);
			if (policyEnum == PwdSecurityPolicyEnum.ADV) {
				value = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityADV.CONFIG_TYPE, Constants.SysConfig.SecurityADV.CONFIG_KEY_LOGIN_ACCESS_TOKEN_EXPIRY_SEC, "900");
			}
		}
		if (StringUtils.isBlank(value)) {
			value = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityBAS.CONFIG_TYPE, Constants.SysConfig.SecurityBAS.CONFIG_KEY_LOGIN_ACCESS_TOKEN_EXPIRY_SEC, "604800");
		}
		return StringUtils.toInteger(value);
	}

	/**
	 * added for v2.0.4 chenbl KNZT-5442
	 * 登录成功后用户Access Token的失效时间(前提是没有被Refresh)
	 */
	public int getLoginAccessTokenExpirySecByLoginName(String loginName) {
		String companyId = getCompanyIdByLoginName(loginName);
		return getLoginAccessTokenExpirySec(companyId);
	}

	public int getLoginAccessTokenRefreshIntervalSec(String companyId) {
		String value = null;
		if (StringUtils.isNotBlank(companyId)) {
			PwdSecurityPolicyEnum policyEnum = getCompPwdSecurityPolicy(companyId);
			if (policyEnum == PwdSecurityPolicyEnum.ADV) {
				value = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityADV.CONFIG_TYPE, Constants.SysConfig.SecurityADV.CONFIG_KEY_ACCESS_TOKEN_REFRESH_INTERVAL_SEC, "3600");
			}
		}
		if (StringUtils.isBlank(value)) {
			value = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfig.SecurityBAS.CONFIG_TYPE, Constants.SysConfig.SecurityBAS.CONFIG_KEY_ACCESS_TOKEN_REFRESH_INTERVAL_SEC, "3600");
		}
		return StringUtils.toInteger(value);
	}

	/**
	 * added for v2.0.4 chenbl KNZT-5442
	 * 前端调用刷新Access Token的间隔时间, 每隔一段时间要刷新一次Access Token
	 */
	public int getLoginAccessTokenRefreshIntervalSecByLoginName(String loginName) {
		String companyId = getCompanyIdByLoginName(loginName);
		return getLoginAccessTokenRefreshIntervalSec(companyId);
	}

	// 无companyId条件，仅用于后台 added for v2.0.5 chenbl KNZT-5491
	public List<User> findUserByLoginName4Select(String loginName, int limitCount) {
		return dao.findUserByLoginName4Select(loginName, limitCount);
	}

	// added for v2.0.7 chenbl KNZT-5773
	public void updateUserStatus(String userId, String userStatus, String updateByUserId) {
		User user = new User();
		user.setId(userId);
		user.setStatus(userStatus);
		user.setUpdateBy(new User(updateByUserId));
		user.setUpdateDate(new Date());
		dao.updateUserStatus(user);
	}

	// added for v2.1.9 chenbl KNZT-6952
	public void batchUpdateUserDataScope(List<String> userIds, String dataScope, String updateByUserId) {
		dao.batchUpdateUserDataScope(userIds, dataScope, updateByUserId);
	}

	public List<User> listMainUserByCompanyId(List<String> companyIdList) {
		return dao.listMainUserByCompanyId(companyIdList);
	}

	// added for v2.1.8 chenbl KNZT-6662
	public List<String> listByLoginNames(Collection<String> loginNames){
		return dao.listByLoginNames(loginNames);
	}

    public List<User> listMonitorNotifyUserInfo() {
		return dao.listMonitorNotifyUserInfo();
    }

	public List<User> listByIds(Collection<String> userIds) {
		return dao.listByIds(userIds);
	}

	// added for v2.3.0 fengsw KNZT-7929
    public boolean checkExistsUserWithLoginNameAndCompType(String loginName, List<Integer> compTypeList) {
		return dao.countUserByLoginNameAndCompType(loginName, compTypeList) > 0;
    }
	
	public User getCompanyMainUserByLoginNameWithCompType(String loginName, List<Integer> compTypeList) {
		return dao.getCompanyMainUserByLoginNameAndCompType(loginName, compTypeList);
	}

	public List<User> listAllUnactivatedUserInfo4Notify() {
		return dao.listAllUnactivatedUserInfo4Notify();
	}

	public List<User> listCompanyAdminUser(String companyId) {
		return dao.listCompanyAdminUser(companyId);
	}
}
