package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysScheduleTaskUpdate extends DataEntity<SysScheduleTaskUpdate> {
	
	private String updateValue;
	private String updateValueExtra1;
	private String remark;

	public String getUpdateValue() {
		return updateValue;
	}
	public void setUpdateValue(String updateValue) {
		this.updateValue = updateValue;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getUpdateValueExtra1() {
		return updateValueExtra1;
	}
	public void setUpdateValueExtra1(String updateValueExtra1) {
		this.updateValueExtra1 = updateValueExtra1;
	}
	
	
}
