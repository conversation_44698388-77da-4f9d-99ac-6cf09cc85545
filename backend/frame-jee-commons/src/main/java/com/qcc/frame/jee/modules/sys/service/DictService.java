package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.dao.DictDao;
import com.qcc.frame.jee.modules.sys.entity.Dict;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典Service
 */
@Service
//@Transactional(readOnly = true)
public class DictService extends CrudService<DictDao, Dict> {
	
	/**
	 * 查询字段类型列表
	 * @return
	 */
	public List<String> findTypeList(){
		return dao.findTypeList(new Dict());
	}

	//@Transactional(readOnly = false)
	public void save(Dict dict) {
		super.save(dict);
//		CacheUtils.remove(DictUtils.CACHE_DICT_MAP);
	}

	// added for lvcy v2.0.7 KNZT-5739
	public void save(String type, String label, String value) {
		Dict dict = new Dict();
		dict.setType(type);
		dict.setLabel(label);
		dict.setValue(value);
		dict.setDescription("");
		dict.setSort(0);
		super.save(dict);
	}

	// 根据type和label找出唯一dict, 保存或者更新 added for v1.9.8 KNZT-4538
	public void saveOrUpdateValue(String type, String label, String value) throws MessageException {
		List<Dict> list = dao.findListByTypeAndLabel(type, label);
		if (list.size() > 1) {
			logger.error(String.format("字典项重复, type: %s, label: %s", type, label));
			throw new MessageException("err.access");
		} else if (list.isEmpty()) {
			Dict dict = new Dict();
			dict.setType(type);
			dict.setLabel(label);
			dict.setValue(value);
			dict.setDescription("");
			dict.setSort(0);
			super.save(dict);
		} else {
			Dict dict = list.get(0);
			dict.setValue(value);
			super.save(dict);
		}
	}

	//@Transactional(readOnly = false)
	public void delete(Dict dict) {
		super.delete(dict);
//		CacheUtils.remove(DictUtils.CACHE_DICT_MAP);
	}

	public String getDictValueByTypeLabel(String label, String type, String defaultLabel){
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(label)){
			List<Dict> list = dao.findListByTypeAndLabel(type, label);
			if(list != null && list.size() > 0) {
				return list.get(0).getValue();
			}
		}
		return defaultLabel;
	}
	
	public List<Dict> getDictList(String type){
		Dict dict = new Dict();
		dict.setType(type);
		return dao.findListByType(dict);
	}

	// added for v2.0.2 chenbl KNZT-5271
	public Map<String, String> getDictValue2LabelMap(String type) {
		Dict dict = new Dict();
		dict.setType(type);
		return dao.findListByType(dict).stream().filter(d -> StringUtils.isNotBlank(d.getLabel()) && StringUtils.isNotBlank(d.getValue()))
				.collect(Collectors.toMap(Dict::getValue, Dict::getLabel, (k1, k2) -> k1));
	}
	
	public List<Dict> getDictListByTypeList(String ... typeList){
		if(typeList == null || typeList.length == 0) {
			return new ArrayList<>(0);
		}
		return dao.getDictListByTypeList(typeList);
	}
	
	public List<String> listValueByType(String type){
		return dao.listValueByType(type);
	}

	/**
	 * added for v1.6.4 KNZT-2806
	 * 根据type和value 查询字典项
	 * @param type
	 * @param value
	 * @return
	 */
	public Dict getByTypeAndValue(String type, String value){
		return dao.getByTypeAndValue(type, value);
	}
}
