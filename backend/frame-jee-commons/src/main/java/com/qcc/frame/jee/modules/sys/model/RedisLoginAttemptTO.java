package com.qcc.frame.jee.modules.sys.model;

public class RedisLoginAttemptTO implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3889014122977389166L;
	
	private int acctLoginAtmpCount;//账号登录的尝试次数
	private long acctLoginAtmpTs;//账号登录的尝试最后时间戳
	
	private int mobileLoginAtmpCount;//手机登录的尝试次数
	private long mobileLoginAtmpTs;//手机登录的尝试最后时间戳
	
	private int resetPwdAtmpCount;//重置密码的尝试次数

	private int mfaLoginAtmpCount;// MFA登录的尝试次数
	private long mfaLoginAtmpTs; // MFA登录的尝试最后时间戳

	public int getResetPwdAtmpCount() {
		return resetPwdAtmpCount;
	}
	public void setResetPwdAtmpCount(int resetPwdAtmpCount) {
		this.resetPwdAtmpCount = resetPwdAtmpCount;
	}
	public long getAcctLoginAtmpTs() {
		return acctLoginAtmpTs;
	}
	public void setAcctLoginAtmpTs(long acctLoginAtmpTs) {
		this.acctLoginAtmpTs = acctLoginAtmpTs;
	}

	public long getMobileLoginAtmpTs() {
		return mobileLoginAtmpTs;
	}
	public void setMobileLoginAtmpTs(long mobileLoginAtmpTs) {
		this.mobileLoginAtmpTs = mobileLoginAtmpTs;
	}
	public int getAcctLoginAtmpCount() {
		return acctLoginAtmpCount;
	}
	public void setAcctLoginAtmpCount(int acctLoginAtmpCount) {
		this.acctLoginAtmpCount = acctLoginAtmpCount;
	}
	public int getMobileLoginAtmpCount() {
		return mobileLoginAtmpCount;
	}
	public void setMobileLoginAtmpCount(int mobileLoginAtmpCount) {
		this.mobileLoginAtmpCount = mobileLoginAtmpCount;
	}

	public int getMfaLoginAtmpCount() {
		return mfaLoginAtmpCount;
	}

	public void setMfaLoginAtmpCount(int mfaLoginAtmpCount) {
		this.mfaLoginAtmpCount = mfaLoginAtmpCount;
	}

	public long getMfaLoginAtmpTs() {
		return mfaLoginAtmpTs;
	}

	public void setMfaLoginAtmpTs(long mfaLoginAtmpTs) {
		this.mfaLoginAtmpTs = mfaLoginAtmpTs;
	}
}
