package com.qcc.frame.jee.modules.sys.enums;

/**
 * 注: 一个文件对应一个枚举, 将文件添加到/proj-common/src/main/resources/translation下
 * 使用TranslationMapUtils获取文件Map
 */
public enum TranslationMapEnum {

    POSITION_MAP("position_map.txt", ""),
    POSITION_SEQMAP("position_seqmap.txt", "LinkedHashMap"),
    NOT_EXIST("position_seqmap11.txt", "LinkedHashMap"),
    STATE_ORGANS_4_EQUALS_MAP("state_organs_4_equals_map.txt", "LinkedHashMap"), // 国家机关完全匹配映射关系 added for v1.8.7 KNZT-3940
    LITIGATION_CAUSE_CN_2_EN_MAP("litigation_cause_cn_2_en_map.txt", "LinkedHashMap"), // 司法案件案由翻译映射 added for v1.9.0 KNZT-4145
    LITIGATION_PROCEEDING_CN_2_EN_MAP("litigation_proceeding_cn_2_en_map.txt", "LinkedHashMap"), // 司法案件进程翻译映射 added for v1.9.0 KNZT-4145
    HK_SHARE_CLASS_MAP("hk_share_class_map.txt", "LinkedHashMap"), // 香港股东持股类型映射 added for v1.9.0 KNZT-4164
    SPECIAL_FONT_MAP("special_font_map.txt", "LinkedHashMap"), // 特殊字符映射 added for v1.9.3 KNZT-4312 来源https://www.unicode.org/charts/normalization/
    SPECIAL_PERSON_PINYIN_MAP("special_person_surname_pinyin_map.txt", "LinkedHashMap"), // 人名姓氏拼音特殊映射 added for v2.0.2 chenbl KNZT-5245
    SPECIAL_STOCK_TYPE_MAP("special_stock_type_map.txt", "LinkedHashMap"), // 特殊股东类型映射 added for v2.0.5 chenbl KNZT-4527
    ;

    private final String fileName;
    /**
     * map类 - 空(HashMap) 或 LinkedHashMap(用于保证文件中的key顺序)
     */
    private final String mapClass;

    private TranslationMapEnum(String fileName, String mapClass) {
        this.fileName = fileName;
        this.mapClass = mapClass;
    }

    public String getFileName() {
        return fileName;
    }

    public String getMapClass() {
        return mapClass;
    }
}
