package com.qcc.frame.commons.ienum;

// added for v2.0.2 chenbl KNZT-5353
// 调整展示顺序 updated for v2.0.3 chenbl KNZT-5408
public enum ShellLabelEnum {
    // 治理结构特征-该企业与关联企业的成立日期、主要人员等基础信息重叠
    LABEL_11(11, "Risk", "Governance Structure",
            "Overlapped attributes with other affiliated companies (company name, company registered address, key persons, date of incorporation, business scope, etc).",
            "The company has overlapped attributes (legal representative, shareholders, key persons etc.) with %s other companies, while the dates of incorporations are close to each other.",
            "The company has overlapped attributes (legal representative, shareholders, key persons etc.) with another company, while the dates of incorporations are close to each other.",
            10),

    // 治理结构特征-该企业与关联企业的成立日期、主要人员等基础信息较为相似
    LABEL_12(12, "Risk", "Governance Structure",
            "Similar attributes with other affiliated companies (company name, company registered address, key persons, date of incorporation, business scope, etc).",
            "The company has overlapped attributes (legal representative, shareholders, key persons etc.) with %s other companies, while the dates of incorporations are close to each other.",
            "The company has overlapped attributes (legal representative, shareholders, key persons etc.) with another company, while the dates of incorporations are close to each other.",
            30),

    // 企业注册地址特征-工商经营异常信息显示，无法联系该企业
    LABEL_31(31, "Risk", "Dormancy",
            "The company cannot be reached according to the last spot check by State Administration for Market Regulation.",
            "The registered address of this company appears to be invalid. Please check the address information carefully.",
            "",
            60),

    // 关联方特征-关联企业被列入相关高风险客户名单
    LABEL_51(51, "Risk", "Legal Risk",
            "Associated parties have been listed as high-risk entities in China.",
            "The company's associated parties have been listed as high-risk entities in China. Please verify the company information carefully.",
            "",
            100),

    // 严重违法失信-被列入严重违法失信企业名单且尚未移出
    LABEL_61(61, "Risk", "Legal Risk",
            "The company is listed as a low credit entity or illegal entity in China and has not yet been removed from the list.",
            "The company is listed as a low credit entity or illegal entity in China on %s due to %s.",
            "",
            90),

    // 关联方特征-关联企业被列入相关高风险客户名单
    LABEL_52(52, "Risk", "Legal Risk",
            "Associated parties have been listed as high-risk entities in China.",
            "The company's associated parties have been listed as high-risk entities in China. Please verify the company information carefully.",
            "",
            100),

    // 法定代表人特征-该企业法定代表人疑似对外担任法定代表人较多
    LABEL_13(13, "Caution", "Governance Structure",
            "Legal representative outlier patterns are observed based on counts for legal representative.",
            "The legal representative %s of this company also currently hold as the legal representative position for %s other companies in which he/she has Zero shareholding. Some of these companies have similar incorporation date, registered address, and business scope.",
            "The legal representative of this company currently also serves as the legal representative for another company in which he/she has no shareholding. Both companies share similar incorporation dates, registered addresses, and business scopes.",
            20),

    // 企业注册地址特征-注册地址重复
    LABEL_21(21, "Caution", "Mass Registration Pattern",
            "Duplicated registered address with other companies.",
            "The registered address of this company matches the registered address of %s other companies.",
            "The registered address of this company matches the registered address of another company.",
            130),

    // 企业经营状态特征-该企业未在规定的期限公示年度报告信息
    LABEL_32(32, "Caution", "Dormancy",
            "The company did not file the annual report before the deadline to the State Administration for Market Regulation.",
            "The company is listed under abnormal business status.",
            "",
            70),

    // 企业注册地址特征-注册地址中包含托管
    LABEL_41(41, "Caution", "Mass Registration Pattern",
            "Registered address contains the word \"hosted\" or \"bulk registration\".",
            "Registered address of the company contains the word \"hosted\" or \"bulk registration\". Please verify the address information carefully.",
            "",
            120),

    // 法定代表人特征-法定代表人基础信息异常
    LABEL_14(14, "Caution", "Governance Structure",
            "Outlier age of the legal representative (significantly young or old).",
            "The basic information of the legal representative of the company appears as an outlier based on our database, please verify the details of the legal representative.",
            "",
            50),

    // 经营活动-反应企业经营活动的外部数据较少
    LABEL_72(72, "Caution", "Dormancy",
            "The company has limited records reflecting its business activities.",
            "The company has limited records reflecting its business activities since the incorporation date %s. Please verify its business status carefully.",
            "",
            80),

    // 疑似异常变更-企业相关自然人重大变更时间较为集中
    LABEL_81(81, "Caution", "Governance Structure",
            "Change of key persons within a short period of time.",
            "The change of key persons has been concentrated within a short period of time. Please verify the continuity and authenticity of the business operation.",
            "The change of key persons has been concentrated within a short period of time. Please verify the continuity and authenticity of the business operation.",
            40),

    // 企业名称异常-包含生僻字
    LABEL_82(82, "Caution", "Mass Registration Pattern",
            "The company name contains rare Chinese characters.",
            "The company name contains rare Chinese characters, which is a sign of bulk registration.",
            "The company name contains rare Chinese characters, which is a sign of bulk registration.",
            110),

    // 企业名称异常-包含异常词
    LABEL_83(83, "Caution", "Mass Registration Pattern",
            "The company name contains unusual Chinese phrases.",
            "The company name or business scope contains unusual Chinese phrases, which is a sign of bulk registration.",
            "The company name or business scope contains unusual Chinese phrases, which is a sign of bulk registration.",
            140),

    ;

    private final Integer code;
    private final String level;
    private final String title;
    private final String name;
    private final String descriptionTemplate;
    private final String descriptionTemplateSingle;
    private final int sort;

    ShellLabelEnum(Integer code, String level, String title, String name, String descriptionTemplate, String descriptionTemplateSingle, int sort) {
        this.code = code;
        this.level = level;
        this.title = title;
        this.name = name;
        this.descriptionTemplate = descriptionTemplate;
        this.descriptionTemplateSingle = descriptionTemplateSingle;
        this.sort = sort;
    }

    public Integer getCode() {
        return code;
    }

    public String getLevel() {
        return level;
    }

    public String getTitle() {
        return title;
    }

    public String getName() {
        return name;
    }

    public String getDescriptionTemplate() {
        return descriptionTemplate;
    }

    public String getDescriptionTemplateSingle() {
        return descriptionTemplateSingle;
    }

    public int getSort() {
        return sort;
    }

    public static ShellLabelEnum getShellLabelEnumByCode(Integer code) {
        if (code != null) {
            for (ShellLabelEnum enumItem : values()) {
                if (enumItem.getCode().equals(code)) {
                    return enumItem;
                }
            }
        }
        return null;
    }
}