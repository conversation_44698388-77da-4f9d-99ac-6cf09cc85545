package com.qcc.frame.jee.modules.sys.form;

// added for v2.1.9 chenbl KNZT-6952
public class UserSimpleCondition {
    private String companyId;
    private String searchKey;

    private String compGroupId4Self; // 仅筛选本分组（不关联子级）sys_comp_group.id
    private String groupId; // sys_comp_group.group_id

    private Boolean suggest; // 是否建议列表(true时，只展示未分组的并且非Owner和Admin的用户)

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public String getCompGroupId4Self() {
        return compGroupId4Self;
    }

    public void setCompGroupId4Self(String compGroupId4Self) {
        this.compGroupId4Self = compGroupId4Self;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Boolean getSuggest() {
        return suggest;
    }

    public void setSuggest(Boolean suggest) {
        this.suggest = suggest;
    }
}
