package com.qcc.frame.commons.ienum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;

/**
 * 权益类型
 * <AUTHOR>
 * @datetime 8/5/2025 7:09 下午
 */
public enum BenefitTypeEnum {
    BASE("BASE", "Basic Monitor", "KYC043", 10),
    NEWS("NEWS", "News Monitor", "KYC033", 20),
    // AML("ADV", "AML反洗钱监控", null, null),
    // ADV("AML", "高级版监控",null, null),
    MAP_CN_ORG("MAP_CN_ORG", "CN-Organization Chart", null, 30),
    MAP_CN_NETWORK("MAP_CN_NETWORK", "CN-Network Map", null, 40),
    
    MAP_CN_REL_3ENTITY("MAP_CN_REL_3ENTITY", "CN-Find Relationships (3 Entities)", null, 50),
    ;

    private String code;
    private String name;
    private String kzzProductCode;
    private Integer sort;

    BenefitTypeEnum(String code, String name, String kzzProductCode, Integer sort) {
        this.code = code;
        this.name = name;
        this.kzzProductCode = kzzProductCode;
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public String getCode() {
        return code;
    }


    public String getKzzProductCode() {
        return kzzProductCode;
    }

    public String getName() {
        return name;
    }

    public static BenefitTypeEnum getByCode(String code) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getCode().equals(code)) {
                return benefitTypeEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getCode().equals(code)) {
                return benefitTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getSortByCode(String code) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getCode().equals(code)) {
                return benefitTypeEnum.getSort();
            }
        }
        return 9999;
    }

    public static BenefitTypeEnum getByKzzProductCode(String kzzProductCode) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getKzzProductCode().equals(kzzProductCode)) {
                return benefitTypeEnum;
            }
        }
        return null;
    }

    public static List<String> getMonitorBenefitTypeList() {
        return Lists.newArrayList(BASE.getCode(), NEWS.getCode());
    }

    public static List<String> withNews() {
        return Lists.newArrayList(NEWS.getCode());
    }

    public static List<String> getMapBenefitTypeList() {
        return Lists.newArrayList(MAP_CN_ORG.getCode(), MAP_CN_NETWORK.getCode(), MAP_CN_REL_3ENTITY.getCode());
    }


    private static final Map<String, String> REPORT_TYPE_BENEFIT = new HashMap<String, String>(10) {
        {
            put(ReportTypeEnum.MAP_OWNERSHIP.getCode(), MAP_CN_ORG.getCode());
            put(ReportTypeEnum.MAP_NETWORK.getCode(), MAP_CN_NETWORK.getCode());
            put(ReportTypeEnum.MAP_RELATION_3.getCode(), MAP_CN_REL_3ENTITY.getCode());
        }
    };

    public static String getBenefitByReportType(String reportType) {
        return REPORT_TYPE_BENEFIT.get(reportType);
    }

 
    public static String getReportTypeByBenefit(String benefitType) {
        for (Map.Entry<String, String> entry : REPORT_TYPE_BENEFIT.entrySet()) {
            if (entry.getValue().equals(benefitType)) {
                return entry.getKey();
            }
        }
        return null;
    }

}