package com.qcc.frame.jee.modules.sys.excel.model;

import java.util.List;
import java.util.Objects;

public class ExportExcelSheetTO {

	private String sheetName;
	private String title;
	private Class<?> exportClazz;
	private List<String> headerList;
	private List dataList;

	public List<String> getHeaderList() {
		return headerList;
	}
	public void setHeaderList(List<String> headerList) {
		this.headerList = headerList;
	}
	public String getSheetName() {
		return sheetName;
	}
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public Class<?> getExportClazz() {
		return exportClazz;
	}
	public void setExportClazz(Class<?> exportClazz) {
		this.exportClazz = exportClazz;
	}
	public List getDataList() {
		return dataList;
	}
	public void setDataList(List dataList) {
		this.dataList = dataList;
	}
	@Override
	public int hashCode() {
		return Objects.hash(sheetName);
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ExportExcelSheetTO other = (ExportExcelSheetTO) obj;
		return Objects.equals(sheetName, other.sheetName);
	}
	
	
}
