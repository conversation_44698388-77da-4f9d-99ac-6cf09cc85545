package com.qcc.frame.jee.commons.utils;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

	private static Logger logger = LoggerFactory.getLogger(DateUtils.class);
	public static String DATE_FORMAT = "yyyy-MM-dd";
	public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	public static final String DATETIME_FORMAT_2 = "yyyy/MM/dd HH:mm:ss";
	public static String DATETIME_HH_FORMAT = "yyyy-MM-dd HH";
	public static String DATE_HHMM_FORMAT = "yyyy-MM-dd HH:mm";
	public static String DATE_FORMAT_YMD = "yyyyMMdd";
	public static String DATE_FORMAT_YMDHMS = "yyyyMMddHHmmss";
	public static String DATE_FORMAT_YMDHMSSSS = "yyyyMMddHHmmsssss";
	public static String DATE_YEAR = "yyyy";

	public static String DATE_FORMAT_YYMMDD = "yyMMdd";
	public static String DATE_FORMAT_HHMMSS = "HHmmss";
	public static String DATE_FORMAT_YYYY_MM = "yyyy-MM";
	public static String DATE_FORMAT_YYYYMM = "yyyyMM";
	public static String DATE_FORMAT_YYYY_MM2 = "yyyy/MM";
	public static String DATE_FORMAT_DDMMMYYYY = "dd MMM yyyy";
	public static String DATE_FORMAT_DDMMMYYYYHHmmss = "dd MMM yyyy HH:mm:ss"; // added for v1.9.9 KNZT-5016
	public static String DATE_FORMATMMMYYYY = "MMM yyyy";

	public static final String DEFAULT_TIME_ZONE = "GMT+8";

	private static String[] parsePatterns = {
			"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
			"yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
			"yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

	public static String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

	/**
	 * 返回日期带时间
	 * @return
	 */
	public static Date getCurrentDate() {
		return new Date();
	}

	/**
	 * 只返回日期
	 * @return
	 */
	public static Date getCurDate() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}

	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd）
	 */
	public static String getDate() {
		return getDate("yyyy-MM-dd");
	}

	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String getDate(String pattern) {
		return DateFormatUtils.format(new Date(), pattern);
	}

	/**
	 * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String formatDate(Date date, String pattern) {
		if(date == null) {
			return "";
		}
		String formatDate = null;
		formatDate = DateFormatUtils.format(date, pattern);
		return formatDate;
	}

	/**
	 * 得到日期字符串 格式（dd-MMM-yyyy）
	 */
	public static String formatDateForSg(Date date, String pattern, Locale locale) {
		if(date == null) {
			return "";
		}
		String formatDate = null;
		formatDate = DateFormatUtils.format(date, pattern,locale);
		return formatDate;
	}

	public static String formatDate(Date date) {
		return formatDate(date, "yyyy-MM-dd");
	}

	/**
	 * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String formatDateTime(Date date) {
		return formatDate(date, "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 将整型日期转换为字符串日期
	 * @param dateInt
	 * @return
	 */
	public static String formatDateInt(Integer dateInt) {
		Date date = parseDateInt(dateInt);
		if (Objects.isNull(date)) {
			return "";
		}
		return formatDate(date, "yyyy-MM-dd");
	}

	/**
	 * 将整型日期转换为字符串日期
	 * @param dateInt
	 * @param pattern
	 * @return
	 */
	public static String formatDateInt(Integer dateInt, String pattern) {
		Date date = parseDate(dateInt);
		if (Objects.isNull(date)) {
			return "";
		}
		return formatDate(date, pattern);
	}

	/**
	 * 得到当前时间字符串 格式（HH:mm:ss）
	 */
	public static String getTime() {
		return formatDate(new Date(), "HH:mm:ss");
	}

	/**
	 * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String getDateTime() {
		return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前年份字符串 格式（yyyy）
	 */
	public static String getYear() {
		return formatDate(new Date(), "yyyy");
	}

	public static String getNextYear(int n) {
//		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar c = Calendar.getInstance();
		c.setTime(new Date());
		c.add(Calendar.YEAR, n);
		Date y = c.getTime();
		return formatDate(y, "yyyy");
	}

	public static String getYear(Long startDate) {
		Date date = toDate(startDate);
		return formatDate(date, "yyyy");
	}

	/**
	 * 得到当前月份字符串 格式（MM）
	 */
	public static String getMonth() {
		return formatDate(new Date(), "MM");
	}

	/**
	 * 得到当天字符串 格式（dd）
	 */
	public static String getDay() {
		return formatDate(new Date(), "dd");
	}

	/**
	 * 得到当前星期字符串 格式（E）星期几
	 */
	public static String getWeek() {
		return formatDate(new Date(), "E");
	}

	/**
	 * 日期型字符串转化为日期 格式
	 * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
	 *   "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm",
	 *   "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
	 */
	public static Date parseDate(Object str) {
		if (str == null){
			return null;
		}
		try {
			return parseDate(str.toString(), parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 日期型数字转换
	 * @param dateInt
	 * @return
	 */
	public static Date parseDateInt(Integer dateInt) {
		if (Objects.isNull(dateInt)) {
			return null;
		}
		try {
			return parseDate(dateInt.toString(), DATE_FORMAT_YMD);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 获取过去的天数
	 * @param date
	 * @return
	 */
	public static long pastDays(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(24*60*60*1000);
	}

	/**
	 * 获取过去的小时
	 * @param date
	 * @return
	 */
	public static long pastHour(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(60*60*1000);
	}

	/**
	 * 获取过去的分钟
	 * @param date
	 * @return
	 */
	public static long pastMinutes(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(60*1000);
	}

	/**
	 * 获取过去的秒
	 * @param date
	 * @return
	 */
	public static long pastSeconds(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/1000;
	}

	/**
	 * 转换为时间（天,时:分:秒.毫秒）
	 * @param timeMillis
	 * @return
	 */
	public static String formatDateTime(long timeMillis){
		long day = timeMillis/(24*60*60*1000);
		long hour = (timeMillis/(60*60*1000)-day*24);
		long min = ((timeMillis/(60*1000))-day*24*60-hour*60);
		long s = (timeMillis/1000-day*24*60*60-hour*60*60-min*60);
		long sss = (timeMillis-day*24*60*60*1000-hour*60*60*1000-min*60*1000-s*1000);
		return (day>0?day+",":"")+hour+":"+min+":"+s+"."+sss;
	}

	/**
	 * 获取两个日期之间的天数
	 *
	 * @param before
	 * @param after
	 * @return
	 */
	public static double getDistanceOfTwoDate(Date before, Date after) {
		long beforeTime = before.getTime();
		long afterTime = after.getTime();
		return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
	}
	/**
	 *
	 * @param time - time will multiply 1000
	 * @param format
	 * @return
	 */
	public static String toDateStr(Long time, String format) {
		if (time != null && time != 0l) {
			time = time*1000;
			if (StringUtils.isBlank(format))
				format = DATE_FORMAT;
			SimpleDateFormat sf = new SimpleDateFormat(format);
			Date date = new Date(time);
			return sf.format(date);
		}
		return "";
	}

	public static Date toDate(Long time) {
		if (time != null && time != 0l) {
			time = time*1000;
			Date date = new Date(time);
			return date;
		}
		return null;
	}

	/**
	 *
	 * @param time - time will multiply 1000
	 * @return
	 */
	public static String toDateStr(Long time) {
		return toDateStr(time, null);
	}

	/**
	 *
	 * @param timeStr - time not multiply 1000
	 * @return
	 */
	public static String toDateStr(String timeStr) {
		return toDateStr(timeStr, null);
	}

	/**
	 *
	 * @param timeStr - time not multiply 1000
	 * @param format
	 * @return
	 */
	public static String toDateStr(String timeStr, String format) {
		if (StringUtils.isNotBlank(timeStr)) {
			Long time = NumberUtils.toLong(timeStr);
			if(time > 0L) {
				if (StringUtils.isBlank(format))
					format = DATE_FORMAT;
				SimpleDateFormat sf = new SimpleDateFormat(format);
				Date date = new Date(time);
				return sf.format(date);
			}
		}
		return "";
	}

	public static String toDateStrBase(String timeStr, String format) {//专门为企业搜索使用
//		if(timeStr.equals("0")){//0这个值本身也是有意义的，但是现在应该没有意义了
//			return "";
//		}
		if (StringUtils.isNotBlank(timeStr)) {
			Long time = NumberUtils.toLong(timeStr);
			if (StringUtils.isBlank(format))
				format = DATE_FORMAT;
			SimpleDateFormat sf = new SimpleDateFormat(format);
			Date date = new Date(time);
			return sf.format(date);
		}
		return "";
	}

	public static String getMonth(Date date) {
		return formatDate(date, "MM");
	}

	public static Date getNextDate(Date initDate, int afterNum) {
		if(initDate != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(initDate);
			SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
			calendar.add(Calendar.DATE, afterNum);
			String days_after = sdf.format(calendar.getTime());
			return parseDate(days_after, DATE_FORMAT);
		}
		return null;
	}

	public static Date getNextMonthWithDay(Date date, int day) {

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int month = calendar.get(Calendar.MONTH);
		calendar.set(Calendar.MONTH, month + 1);
		calendar.set(Calendar.DAY_OF_MONTH, day);

		if(calendar.get(Calendar.MONTH) == month + 2) {
			calendar.set(Calendar.DAY_OF_MONTH, 0);
		}

		SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
		String days_after = sdf.format(calendar.getTime());
		return parseDate(days_after, DATE_FORMAT);
	}


	public static int getWeekDay(Date date) {
		if(date != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			return calendar.get(Calendar.DAY_OF_WEEK);
		}
		return 0;
	}

	public static String getHour(Date date) {
		if(date != null) {
			return Integer.toString(date.getHours());
		}
		return null;
	}

	public static int getHourNum(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.HOUR_OF_DAY);
	}

	public static Date getNextMonth(Date date) {
		if(date != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);

			SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
			String days_after = sdf.format(calendar.getTime());
			return parseDate(days_after, DATE_FORMAT);
		} else {
			return null;
		}
	}

	public static Date getPrevMonth(Date date) {
		if(date != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);

			SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
			String days_after = sdf.format(calendar.getTime());
			return parseDate(days_after, DATE_FORMAT);
		} else {
			return null;
		}
	}

	public static boolean between(Date target, Date from, Date end) {
		if(target != null && from != null && end != null) {
			if(target.after(from) && target.before(end)) {
				return true;
			}
		}
		if(target != null && from == null && end == null) {
			return true;
		}
		if(target != null && from != null && end == null) {
			if(target.after(from)) {
				return true;
			}
		}
		if(target != null && from == null && end != null) {
			if(target.before(end)) {
				return true;
			}
		}
		if(target == null) {
			return true;
		}
		return false;
	}

	public static Date parseDate(String date, String format) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			if (date != null) {
				return sdf.parse(date);
			} else {
				return null;
			}
		} catch (ParseException e) {
			return null;
		}
	}

	public static String getTokenInvalidTime() {
		Date date = DateUtils.addMinutes(new Date(), 118);
		return formatDate(date, DATETIME_FORMAT);
	}

	/**
	 * 将日期转换成秒
	 * @param dateStr
	 * @return
	 */
	public static long toDateSecond(String dateStr) {
		if(StringUtils.isNotBlank(dateStr)) {
			Date date = parseDate(dateStr, DATETIME_FORMAT);
			if(date != null) {
				return date.getTime() / 1000;
			}
		}
		return 0;
	}

	/**
	 * 将日期转换成秒
	 * added for v1.9.0 KNZT-4145
	 */
	public static long toDateSecond(Date date) {
		if (date != null) {
			return date.getTime() / 1000;
		}
		return 0;
	}

	/**
	 * 将日期转换成秒
	 * @param dateStr
	 * @param format
	 * @return
	 */
	public static long toFormatDateSecond(String dateStr, String format) {
		if(StringUtils.isNotBlank(dateStr)) {
			if (StringUtils.isBlank(format))
				format = DATETIME_FORMAT;
			Date date = parseDate(dateStr, format);
			if(date != null) {
				return date.getTime() / 1000;
			}
		}
		return 0;
	}

	/**
	 * 将日期转换成ms
	 *
	 * @param dateStr
	 * @param format
	 * @return
	 */
	public static Long toFormatDateMs(String dateStr, String format) {
		if (StringUtils.isNotBlank(dateStr)) {
			if (StringUtils.isBlank(format)) {
				format = DATETIME_FORMAT;
			}
			Date date = parseDate(dateStr, format);
			if (date != null) {
				return date.getTime();
			}
		}
		return null;
	}


	/**
	 * 日期间隔 (date1 - date 2)
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static int subtractDate(Date date1, Date date2) {
		int a = (int) ((date1.getTime() - date2.getTime()) / (1000*3600*24));
		return a;
	}

	public static int subtractDate2Minute(Date date1, Date date2) {
		int a = (int) ((date1.getTime() - date2.getTime()) / (1000*60));
		return a;
	}

	public static int subtractDate2Second(Date date1, Date date2) {
		int a = (int) ((date1.getTime() - date2.getTime()) / (1000));
		return a;
	}

	public static List<String> getBetweenDateStrList(Date begin, Date end) {
		List<String> resultList = new ArrayList<String>();
		Calendar tempStart = Calendar.getInstance();
		tempStart.setTime(begin);

		while(begin.getTime()<=end.getTime()){
			resultList.add(formatDate(tempStart.getTime()));
			tempStart.add(Calendar.DAY_OF_YEAR, 1);
			begin = tempStart.getTime();
		}
		return resultList;
	}

	public static String getTimeFromLong(long diff) {
		final String DAYS = "t";
		final String HOURS = "h";
		final String MINUTES = "min";
		//final String SECONDS = "sec";

		final long MS_IN_A_DAY = 1000 * 60 * 60 * 24;
		final long MS_IN_AN_HOUR = 1000 * 60 * 60;
		final long MS_IN_A_MINUTE = 1000 * 60;
		final long MS_IN_A_SECOND = 1000;
		//Date currentTime = new Date();
		long numDays = diff / MS_IN_A_DAY;
		diff = diff % MS_IN_A_DAY;
		long numHours = diff / MS_IN_AN_HOUR;
		diff = diff % MS_IN_AN_HOUR;
		long numMinutes = diff / MS_IN_A_MINUTE;
		diff = diff % MS_IN_A_MINUTE;
		//long numSeconds = diff / MS_IN_A_SECOND;
		diff = diff % MS_IN_A_SECOND;
		//long numMilliseconds = diff;

		StringBuffer buf = new StringBuffer();
		if(numDays > 0) {
			buf.append(numDays + " " + DAYS + ", ");
		}
		if (numHours > 0) {
			buf.append(numHours + " " + HOURS + ", ");
		}

		if (numMinutes > 0) {
			buf.append(numMinutes + " " + MINUTES);
		}

		//buf.append(numSeconds + " " + SECONDS);

		String result = buf.toString();

		if (numMinutes < 1) {
			result = "< 1 minute";
		}

		return result;
	}


	public static String getDiffDay(Date d1, Date d2) {
		StringBuffer buf = new StringBuffer();
		if (d1 != null && d2 != null) {
			long diff = d1.getTime() - d2.getTime();
			long days = diff / (1000 * 60 * 60 * 24);
			buf.append(days).append("天");
		}
		return buf.toString();
	}

	public static String  getDiff(Date d1, Date d2) {
		StringBuffer buf = new StringBuffer();
		if(d1 != null && d2 != null) {
			long diff = d1.getTime() - d2.getTime();//这样得到的差值是微秒级别
			long days = diff / (1000 * 60 * 60 * 24);

			long hours = (diff-days*(1000 * 60 * 60 * 24))/(1000* 60 * 60);
			long minutes = (diff-days*(1000 * 60 * 60 * 24)-hours*(1000* 60 * 60))/(1000* 60);

			if(days > 0) {
				buf.append(days + "天");
			} else {
				if (hours > 0) {
					buf.append(hours + "时");
				}

				if (minutes > 0) {
					buf.append(minutes + "分");
				}
			}
		}

		if(buf.length() > 0) {
			return buf.toString() + "前";
		} else {
			return "刚刚";
		}
	}

	/**
	 * 当前日期是否在有效期内
	 * @param beginDate
	 * @param endDate
	 * isExpired(null, null) = false
	 * isExpired(null, date>=当前日期) = false
	 * isExpired(date<=当前日期, null) = false
	 * isExpired(date<=当前日期, date>=当前日期) = false
	 * @return
	 */
	public static boolean isInValidityPeriod(Date beginDate, Date endDate) {
		if(beginDate == null && endDate == null) {
			return true;
		}
		Date curDate = getCurDate();
		if(beginDate == null) {
			return !endDate.before(curDate);
		} else if(endDate == null) {
			return !beginDate.after(curDate);
		} else {
			if(!(beginDate.after(curDate) || endDate.before(curDate))) {
				return true;
			}
		}
		return false;
	}

	public static String getDateFromTs(long ts) {
		if(ts > 0) {
			Timestamp tstamp = new Timestamp(ts);
			return formatDate(tstamp);
		}
		return null;
	}

	public static String getTimeDiff(Date d1, Date d2) {
		StringBuffer buf = new StringBuffer();
		if(d1 != null && d2 != null) {
			long nd = 1000 * 24 * 60 * 60;//每天毫秒数
			long nh = 1000 * 60 * 60;//每小时毫秒数
			long nm = 1000 * 60;//每分钟毫秒数
			long diff = d2.getTime() - d1.getTime();
			long day = diff / nd;   // 计算差多少天
			long hour = diff % nd / nh; // 计算差多少小时
			long min = diff % nd % nh / nm;  // 计算差多少分钟

			if(day < 1) {
				if(hour < 1) {
					buf.append(min + "分钟前");
				} else {
					buf.append(hour + "小时前");
				}
			}
		}

		if(buf.length() > 0) {
			return buf.toString();
		} else {
			return "";
		}
	}

	/**
	 * 套餐延期
	 * @param compEndDate
	 * @param num
	 * @return
	 */
	public static Date postponeSet(Date compEndDate, int year) {
		if(compEndDate != null) {
			Date curDate = getCurDate();
			if(compEndDate.before(curDate)) {//如果结束日期在当前日期前, 使用当前日期计算
				compEndDate = curDate;
			}
			return DateUtils.addYears(compEndDate, year);
		}
		return null;
	}

	public static boolean isDate(String dateStr) {
		Date date = parseDate(dateStr, DATE_FORMAT);
		if(date != null) {
			if(StringUtils.equals(formatDate(date), dateStr)) {
				return true;
			}
		}
		return false;
	}

	public static Date resetTime(Date dateTime, int hour, int minute, int second) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(dateTime);
		calendar.set(Calendar.HOUR_OF_DAY, hour);
		calendar.set(Calendar.MINUTE, minute);
		calendar.set(Calendar.SECOND, second);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 判断当前时间是否在时间段内
	 * @param beginTime - 例如00:30:00
	 * @param endTime - 例如19:00:00
	 * @return
	 */
	public static boolean betweenTimeRange(String beginTime, String endTime) {
		String dateStr = getDate();
		try {
			Date beginDateTime = DateUtils.parseDate(dateStr + " " + beginTime, DATETIME_FORMAT);
			Date endDateTime = DateUtils.parseDate(dateStr + " " + endTime, DATETIME_FORMAT);
			Date curDateTime = new Date();
			if((beginDateTime == null || beginDateTime.before(curDateTime)) && endDateTime != null && endDateTime.after(curDateTime)) {
				return true;
			}
		} catch(Exception e) {
			logger.warn(beginTime + " " + endTime, e);
		}
		return false;
	}
	/**
	 * 判断当前天数是周几
	 * @param date - 必须yyyy-MM-dd
	 * @return
	 */
	public static String getWeekday(String date){
//		linux环境会返回英文
//		SimpleDateFormat sdw = new SimpleDateFormat("E");
//		Date d = parseDate(date, DATE_FORMAT);
//		if (d == null){
//			return "";
//		}
//		return sdw.format(d);
		Date d = parseDate(date, DATE_FORMAT);
		if (d == null) {
			return weekDays[0];
		}
		Calendar cal = Calendar.getInstance();
		cal.setTime(d);
		int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (w < 0){
			w = 0;
		}
		return weekDays[w];
	}

	/**
	 * 获取上周周一时间
	 * @param date
	 * @return
	 */
	public static Date getLastWeekMonday(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(getThisWeekMonday(date));
		cal.add(Calendar.DATE, -7);
		return cal.getTime();
	}

	/**
	 * 获取本周一时间
	 * @param date
	 * @return
	 */
	public static Date getThisWeekMonday(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		// 获得当前日期是一个星期的第几天
		int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
		if (1 == dayWeek) {
			cal.add(Calendar.DAY_OF_MONTH, -1);
		}
		// 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
		cal.setFirstDayOfWeek(Calendar.MONDAY);
		// 获得当前日期是一个星期的第几天
		int day = cal.get(Calendar.DAY_OF_WEEK);
		// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
		cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
		return cal.getTime();
	}


	/**
	 * 获取上月第一天时间
	 * @return
	 */
	public static Date getLastMonthFirstDay() {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, -1);
		cal.set(Calendar.DAY_OF_MONTH,1);
		return cal.getTime();
	}


	/**
	 * 获取上月最后一天时间
	 * @return
	 */
	public static Date getLastMonthEndDay() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DAY_OF_MONTH, 0);
		return cal.getTime();
	}

	/**
	 * 计算两个日期之间相差的天数
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static int daysBetween(Date date1,Date date2){
		Calendar cal = Calendar.getInstance();
		cal.setTime(date1);
		long time1 = cal.getTimeInMillis();
		cal.setTime(date2);
		long time2 = cal.getTimeInMillis();
		long between_days=(time2-time1)/(1000*3600*24);
		return Integer.parseInt(String.valueOf(between_days));
	}

	public static Long getTimespanFromDateTime(String dateTime) {
		Date date = DateUtils.parseDate(dateTime, DATETIME_FORMAT);
		if(date != null) {
			return date.getTime();
		}
		return null;
	}

	public static boolean isDateTime(String dateTime) {
		try {
			if(StringUtils.isBlank(dateTime)) {
				return false;
			}
			Date date = DateUtils.parseDate(dateTime, DATETIME_FORMAT);
			if(date != null) {
				String timeCompare = DateUtils.formatDateTime(date);
				return StringUtils.equals(dateTime, timeCompare);
			}
		} catch(Exception e) {
			return false;
		}
		return false;
	}


	/**
	 * @param args
	 * @throws ParseException
	 */
	public static void main(String[] args) throws ParseException {
//		System.out.println(getWeekday("2021-03-30"));
//		System.out.println(formatDate(parseDate("2010/3/6")));
//		System.out.println(getDate("yyyy年MM月dd日 E"));
//		long time = new Date().getTime()-parseDate("2012-11-19").getTime();
//		System.out.println(time/(24*60*60*1000));

//		System.out.println(getNextDate(new Date(), -1));
//		System.out.println(getNextMonth(new Date()));
//		System.out.println(toDateStr(1459353600l, DATE_FORMAT));
//		System.out.println(toDateStr(1506700800l, DATE_FORMAT));
//		System.out.println(toDateStr(, DATE_FORMAT));
//		System.out.println(toDateSecond("2018-08-17 00:00:00"));
//		System.out.println(toDateSecond("2018-08-17 23:59:59"));//1534435200
//		System.out.println(getNextIndicatedDay(parseDate("2010/3/6"), "M", 32));
//		System.out.println(getNextMonthWithDay(parseDate("2010/3/6"), 31));
//		System.out.println(getHour(new Date()));
//		System.out.println(getTimeFromLong(new Date().getTime() - (1539598522*1000)));
//		System.out.println(DateUtils.toDateStr(1542945170l, DateUtils.DATETIME_FORMAT));
//		System.out.println(getWeekDay(parseDate("2010/3/6")));


//		System.out.println((new Date()).getTime() - parseDate("2019-11-27 16:00:00", DATETIME_FORMAT).getTime());
//		Date dt = addDays(new Date(), 2);
//		
//		System.out.println(DateUtils.getHourNum(parseDate("2019-11-27 01:03:00", DATETIME_FORMAT)));
//		System.out.println(subtractDate2Minute(parseDate("2019-11-27 00:29:59", DATETIME_FORMAT), parseDate("2019-11-26 23:30:00", DATETIME_FORMAT)));

//		System.out.println(isDate("2020-04-22"));
//		System.out.println(isDate("2020-04-32"));
//		System.out.println(isDate("2020-04/32"));
//		DateUtils.
//		Date resetDate = DateUtils.addDays(new Date(), 1);
//		Date date = resetTime(resetDate, 0, 0, 0);
//		System.out.println(formatDate(date, DATETIME_FORMAT));

		//System.out.println(betweenTimeRange("18:09", "19:00"));
		System.out.println(formatDateForSg(new Date(),"dd-MMM-yyyy",Locale.ENGLISH));

	}

	/**
	 * updated for v1.8.9 KNZT-4100 获取当前日期的前后X天时间
	 * 
	 * @param days 为负数代表当前时间之前，正代表当前时间之后
	 * @return
	 */
	public static Date getSpecialDateWithCurDate(int days) {
		Calendar cal = Calendar.getInstance();
		Date currentDate = DateUtils.getCurrentDate();
		cal.setTime(currentDate);
		cal.add(Calendar.DAY_OF_MONTH, days);
		return cal.getTime();
	}

	/**
	 * added for v1.8.8 KNZT-3324
	 * 计算日期之间的月份
	 *
	 * @param startDate
	 * @param endDate
	 * @return int
	 */
	public static int getPeriodsOfMonth(Date startDate, Date endDate) {
		long days = getDifferenceDays(startDate, endDate);
		BigDecimal month = new BigDecimal(days).multiply(new BigDecimal("12")).divide(new BigDecimal("365"), RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP);
		return month.intValue();
	}

	/**
	 * added for v1.8.8 KNZT-3324
	 * 计算日期之间的天数
	 * @param date1
	 * @param date2
	 * @return long
	 */
	public static long getDifferenceDays(Date date1, Date date2) {
		long diff = date2.getTime() - date1.getTime();
		return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
	}

	public static int getDateInt() {
		return getDateInt(new Date());
	}

	public static int getDateInt(Date date) {
		String dateStr = DateFormatUtils.format(date, DateUtils.DATE_FORMAT_YMD);
		return Integer.parseInt(dateStr);
	}

	public static int getDateInt(LocalDate date) {
		String dateStr = date.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YMD));
		return Integer.parseInt(dateStr);
	}

	public static LocalDate toLocalDate(Date date) {
		return date == null ? null : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	}

	public static LocalDate toLocalDate(int dateInt) {
		return LocalDate.parse(dateInt + "", DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YMD));
	}

	public static LocalDateTime toLocalDateTime(Date date) {
		return date == null ? null : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
	}

	public static Date toDate(LocalDate localDate) {
		return localDate == null ? null : Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
	}

	public static Date toDate(LocalDateTime localDateTime) {
		return localDateTime == null ? null : Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}

	public static boolean checkInDateRange(LocalDate date, LocalDate beginDate, LocalDate endDate) {
		if (beginDate == null && endDate == null) {
			return true;
		}
		if (beginDate == null) {
			return !date.isAfter(endDate);
		}
		if (endDate == null) {
			return !date.isBefore(beginDate);
		}
		return !date.isBefore(beginDate) && !date.isAfter(endDate);
	}

	public static String formatDate(LocalDate date, String pattern) {
		return date.format(DateTimeFormatter.ofPattern(pattern));
	}

	public static String formatDateEn(LocalDate date, String pattern) {
		return date.format(DateTimeFormatter.ofPattern(pattern, Locale.ENGLISH));
	}

	public static String formatDate(LocalDate date) {
		return date.format(DateTimeFormatter.ofPattern(DATE_FORMAT));
	}

	public static Integer formatDateToInt(LocalDate date) {
		String formatStr = date.format(DateTimeFormatter.ofPattern(DATE_FORMAT_YMD));
		return Integer.valueOf(formatStr);
	}


	public static boolean isMonthInRange(LocalDate beginDate, LocalDate endDate, YearMonth targetYM) {
		YearMonth currentYM = YearMonth.from(beginDate);
		YearMonth endYM = YearMonth.from(endDate);
		while (!currentYM.isAfter(endYM)) {
			if (currentYM.equals(targetYM)) {
				return true;
			}
			currentYM = currentYM.plusMonths(1L);
		}
		return false;
	}

	public static String formatYearMonth(YearMonth ym) {
		if (Objects.isNull(ym)) {
			return "";
		}
		return ym.format(DateTimeFormatter.ofPattern(DATE_FORMAT_YYYY_MM));
	}

	public static String formatYearMonth(YearMonth ym, String pattern) {
		if (Objects.isNull(ym)) {
			return "";
		}
		return ym.format(DateTimeFormatter.ofPattern(pattern));
	}

	public static String formatYearMonthEn(Integer ymInt, String pattern) {
		if (Objects.isNull(ymInt)) {
			return "";
		}
		YearMonth ym = YearMonth.parse(ymInt.toString(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
		return DateUtils.formatYearMonthEn(ym, pattern);
	}

	public static String formatYearMonthEn(YearMonth ym, String pattern) {
		if (Objects.isNull(ym)) {
			return "";
		}
		return ym.format(DateTimeFormatter.ofPattern(pattern, Locale.ENGLISH));
	}

	public static Integer formatYearMonthInt(YearMonth ym) {
		String formatStr = formatYearMonth(ym, DATE_FORMAT_YYYYMM);
		return Integer.valueOf(formatStr);
	}

	/**
	 * added for v2.0.7 fengsw KNZT-5809
	 * 获取当前时间的上一年
	 */
	public static Date getLastYear() {
		LocalDateTime now = LocalDateTime.now();
		// 减去一年
		LocalDateTime lastYear = now.minusYears(1);
		return toDate(lastYear);
	}

	/**
	 * added for lvcy v2.1.2 KNZT-4565
	 * 获取两个日期之间的天数
	 * @param beginDate 开始日期
	 * @param endDate 结束日期
	 * @return 天数
	 */
    public static int getDaysBetween(Integer beginDate, Integer endDate) {
        LocalDate begin = toLocalDate(beginDate);
        LocalDate end = toLocalDate(endDate);
        return getDaysBetween(begin, end);
    }

	public static int getDaysBetween(Date beginDate, Date endDate) {
		LocalDate begin = toLocalDate(beginDate);
		LocalDate end = toLocalDate(endDate);
		return getDaysBetween(begin, end);
	}

	/**
	 * added for lvcy v2.1.2 KNZT-4565
	 * 获取两个日期之间的天数
	 * @param beginDate 开始日期
	 * @param endDate 结束日期
	 * @return 天数
	 */
	public static int getDaysBetween(LocalDate beginDate, LocalDate endDate) {
		return (int) ChronoUnit.DAYS.between(beginDate, endDate);
	}

	/**
	 * added for External API date conversion
	 * 将Unix时间戳转换为API标准日期格式 yyyy-MM-dd
	 * Convert Unix timestamp to API standard date format yyyy-MM-dd
	 * 
	 * @param timestamp Unix时间戳（秒）
	 * @return 格式化的日期字符串，失败时返回null
	 */
	public static String toApiDateStr(Long timestamp) {
		if (timestamp == null || timestamp <= 0) {
			return null;
		}
		try {
			return toDateStr(timestamp, DATE_FORMAT);
		} catch (Exception e) {
			logger.warn("Date conversion failed for timestamp: " + timestamp, e);
			return null;
		}
	}

	/**
	 * added for External API date conversion
	 * 将Integer型Unix时间戳转换为API标准日期格式 yyyy-MM-dd
	 * Convert Integer Unix timestamp to API standard date format yyyy-MM-dd
	 * 
	 * @param timestamp Unix时间戳（秒）
	 * @return 格式化的日期字符串，失败时返回null
	 */
	public static String toApiDateStr(Integer timestamp) {
		if (timestamp == null || timestamp <= 0) {
			return null;
		}
		return toApiDateStr(timestamp.longValue());
	}
}
