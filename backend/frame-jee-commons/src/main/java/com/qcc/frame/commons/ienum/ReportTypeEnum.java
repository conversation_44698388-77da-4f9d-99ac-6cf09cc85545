package com.qcc.frame.commons.ienum;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qcc.frame.jee.commons.utils.CollectionUtils;

import java.util.*;

/**
 * added for v1.7.0 KNZT-3065
 * 
 *
 * <AUTHOR>
 * @datetime 2024/4/19 21:12
 */
public enum ReportTypeEnum {

    BASIC("KYC_BASIC", "KYC Basic", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Shareholding Structure", "Key Person", "Sector Information"), "Instant delivery"),
    UBO("UBO", "UBO", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Shareholding Structure", "Key Person", "Sector Information", "Family Tree", "UBO"), "Instant Delivery"),
    ADVANCED("KYC_ADVANCED", "KYC Advanced", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Shareholding Structure", "Key Person", "Sector Information", "Family Tree", "UBO", "Change History", "Litigations", "Legal Alerts", "Operational Alerts"), "Instant Delivery"),
    LEGAL("LEGAL_DOCUMENT", "Original Document", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Source: National Enterprise Credit Information Publicity System"), "24-48 hours"), // updated for v1.8.9 4095 updated for v2.2.2 KNZT-7302
    CORP_360("CORP_360", "360° Profile", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Registration Information", "Operational Alerts", "Legal Alerts", "Operation Information", "Financial Information", "Honour & Certificate", "Public News & Opinions", "Change History", "Listing Information", "Data Map"), "Instant Delivery"),
    HK_BAS("CORP_HK_BAS_DTL", "KYC Basic", UnitGroupEnum.HK_UNIT, Lists.newArrayList("Firmographics", "Ownership", "Directors", "Company Secretaries"), "Instant delivery"),
    HK_BAS_AR("CORP_HK_BAS_AR", "KYC Basic + Annual Return/Incorporation Form", UnitGroupEnum.HK_UNIT, Lists.newArrayList("Firmographics", "Ownership", "Directors", "Company Secretaries", "Name History", "Annual Return/Incorporation Form"), "10-15 mins"),
    HK_DOCUMENT("CORP_HK_DOCUMENT", "Official Document", UnitGroupEnum.HK_UNIT, Lists.newArrayList("Various types of official documents, such as Annual Return, Directors and Company Secretary, Incorporation Form, Application for Registration, Association, Certificate of Incorporation / Registration, Change of Address of Registered Office / Name, Return of Allotment, Winding Up, Mortgage & Charges etc."), "Instant delivery"),
    HK_BR_EXTRACT("HK_BR_EXTRACT", "Business Register Extract", UnitGroupEnum.HK_UNIT, Lists.newArrayList("Business Information", "Owner Information (Only Available for Sole Proprietors or Partnerships)", "Original Document"), "10-15 mins"),
    HK_DUP_CERT("HK_DUP_CERT", "Duplicate of Registration Certificate", UnitGroupEnum.HK_UNIT, Lists.newArrayList("Registration Certificate Details", "Original Document"), "10-15 mins"),
    SG_BAS("CORP_SG_BAS_DTL", "KYC Basic", UnitGroupEnum.SG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "30-60 mins"),// updated for v2.3.3 fengsw KNZT-8350
    PERS_BASIC("PERS_BASIC", "Executive Snapshot", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Legal Representative", "Capital Investment", "Current Positions"), "Instant Delivery"),
    PERS_360("PERS_360", "Executive 360°", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Legal Representative", "Capital Investment", "Current Positions", "Controlled Companies", "Business Risks", "Historical Changes", "Radial Map"), "Instant Delivery"),
    FIN_TAX("FIN_TAX", "Financial & Tax", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Tax Return & Collection", "Suppliers & Customers", "Balance Sheet", "Income Statement", "Financial Metrics", "Tax Violations & Infractions Consent Required from the Target Company"), "0.5-1 hour"),
    SCAN("SCAN", "Sanctions & Watchlist Scan", null, null, "Instant Delivery"),
    MERCHANT("MERCHANT", "Merchant Onboarding", UnitGroupEnum.CN_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Shareholding Structure", "Key Person", "Sector Information", "Family Tree", "UBO", "Tax Compliance", "E-commerce Information", "Intellectual Property & Public Recognition"), "Instant delivery"), // added for lvcy v2.1.1 KNZT-5973
    MY_BASIC("CORP_MY_BAS_DTL", "KYC Basic", UnitGroupEnum.MY_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers", "Probable Associates"), "Instant delivery"),
    NZ_BASIC("CORP_NZ_BAS_DTL", "KYC Basic", UnitGroupEnum.NZ_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Shareholding Structure", "Officers", "Ultimate Holding Company"), "Instant delivery"),
    TW_BASIC("CORP_TW_BAS_DTL", "KYC Basic", UnitGroupEnum.TW_UNIT, Lists.newArrayList("Firmographics", "Contact Information", "Shareholding Structure", "Officers"), "Instant delivery"),
    // 核验
    VERIFY_CORP("VERIFY_CORP", "Identity Verification (Three-Field Verification)", UnitGroupEnum.CN_UNIT, null, null),
    VERIFY("VERIFY", "Identity Verification (Two-Field Verification)", UnitGroupEnum.CN_UNIT, null, null),
    // 图谱
    MAP_OWNERSHIP("M_OWNERSHIP", "Organization Chart", UnitGroupEnum.CN_UNIT, null, null),
    MAP_NETWORK("M_NETWORK", "Network Map", UnitGroupEnum.CN_UNIT, null, null),
    MAP_RELATION("M_RELATION", "Find Relationships (10 Entities)", UnitGroupEnum.CN_UNIT, null, null),
    MAP_RELATION_3("M_RELATION_3", "Find Relationships (3 Entities)", UnitGroupEnum.CN_UNIT, null, null),
    MAP_OWNERSHIP_HK("M_OWNERSHIP_HK", "Organization Chart-HK", UnitGroupEnum.HK_UNIT, null, null),
    MAP_NETWORK_HK("M_NETWORK_HK", "Network Map-HK", UnitGroupEnum.HK_UNIT, null, null),

    /*
    // 中东 KYC Basic
    PK_BASIC("KYC_BASIC_PK", "KYC Basic", UnitGroupEnum.PK_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    JO_BASIC("KYC_BASIC_JO", "KYC Basic", UnitGroupEnum.JO_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    KW_BASIC("KYC_BASIC_KW", "KYC Basic", UnitGroupEnum.KW_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    OM_BASIC("KYC_BASIC_OM", "KYC Basic", UnitGroupEnum.OM_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SA_BASIC("KYC_BASIC_SA", "KYC Basic", UnitGroupEnum.SA_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    AE_BASIC("KYC_BASIC_AE", "KYC Basic", UnitGroupEnum.AE_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    IR_BASIC("KYC_BASIC_IR", "KYC Basic", UnitGroupEnum.IR_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    LB_BASIC("KYC_BASIC_LB", "KYC Basic", UnitGroupEnum.LB_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    QA_BASIC("KYC_BASIC_QA", "KYC Basic", UnitGroupEnum.QA_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    PS_BASIC("KYC_BASIC_PS", "KYC Basic", UnitGroupEnum.PS_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SY_BASIC("KYC_BASIC_SY", "KYC Basic", UnitGroupEnum.SY_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    TR_BASIC("KYC_BASIC_TR", "KYC Basic", UnitGroupEnum.TR_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    YE_BASIC("KYC_BASIC_YE", "KYC Basic", UnitGroupEnum.YE_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    BH_BASIC("KYC_BASIC_BH", "KYC Basic", UnitGroupEnum.BH_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    IQ_BASIC("KYC_BASIC_IQ", "KYC Basic", UnitGroupEnum.IQ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CY_BASIC("KYC_BASIC_CY", "KYC Basic", UnitGroupEnum.CY_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    MA_BASIC("KYC_BASIC_MA", "KYC Basic", UnitGroupEnum.MA_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    EG_BASIC("KYC_BASIC_EG", "KYC Basic", UnitGroupEnum.EG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    LY_BASIC("KYC_BASIC_LY", "KYC Basic", UnitGroupEnum.LY_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    TN_BASIC("KYC_BASIC_TN", "KYC Basic", UnitGroupEnum.TN_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    DZ_BASIC("KYC_BASIC_DZ", "KYC Basic", UnitGroupEnum.DZ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SD_BASIC("KYC_BASIC_SD", "KYC Basic", UnitGroupEnum.SD_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    MU_BASIC("KYC_BASIC_MU", "KYC Basic", UnitGroupEnum.MU_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    RW_BASIC("KYC_BASIC_RW", "KYC Basic", UnitGroupEnum.RW_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SC_BASIC("KYC_BASIC_SC", "KYC Basic", UnitGroupEnum.SC_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    KM_BASIC("KYC_BASIC_KM", "KYC Basic", UnitGroupEnum.KM_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    DJ_BASIC("KYC_BASIC_DJ", "KYC Basic", UnitGroupEnum.DJ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ER_BASIC("KYC_BASIC_ER", "KYC Basic", UnitGroupEnum.ER_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ET_BASIC("KYC_BASIC_ET", "KYC Basic", UnitGroupEnum.ET_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    KE_BASIC("KYC_BASIC_KE", "KYC Basic", UnitGroupEnum.KE_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    MG_BASIC("KYC_BASIC_MG", "KYC Basic", UnitGroupEnum.MG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SO_BASIC("KYC_BASIC_SO", "KYC Basic", UnitGroupEnum.SO_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SS_BASIC("KYC_BASIC_SS", "KYC Basic", UnitGroupEnum.SS_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    TZ_BASIC("KYC_BASIC_TZ", "KYC Basic", UnitGroupEnum.TZ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    UG_BASIC("KYC_BASIC_UG", "KYC Basic", UnitGroupEnum.UG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    BI_BASIC("KYC_BASIC_BI", "KYC Basic", UnitGroupEnum.BI_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CM_BASIC("KYC_BASIC_CM", "KYC Basic", UnitGroupEnum.CM_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CF_BASIC("KYC_BASIC_CF", "KYC Basic", UnitGroupEnum.CF_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    TD_BASIC("KYC_BASIC_TD", "KYC Basic", UnitGroupEnum.TD_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CG_BASIC("KYC_BASIC_CG", "KYC Basic", UnitGroupEnum.CG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CD_BASIC("KYC_BASIC_CD", "KYC Basic", UnitGroupEnum.CD_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    GQ_BASIC("KYC_BASIC_GQ", "KYC Basic", UnitGroupEnum.GQ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    GA_BASIC("KYC_BASIC_GA", "KYC Basic", UnitGroupEnum.GA_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ST_BASIC("KYC_BASIC_ST", "KYC Basic", UnitGroupEnum.ST_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    BJ_BASIC("KYC_BASIC_BJ", "KYC Basic", UnitGroupEnum.BJ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    BF_BASIC("KYC_BASIC_BF", "KYC Basic", UnitGroupEnum.BF_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CV_BASIC("KYC_BASIC_CV", "KYC Basic", UnitGroupEnum.CV_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    CI_BASIC("KYC_BASIC_CI", "KYC Basic", UnitGroupEnum.CI_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    GM_BASIC("KYC_BASIC_GM", "KYC Basic", UnitGroupEnum.GM_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    GH_BASIC("KYC_BASIC_GH", "KYC Basic", UnitGroupEnum.GH_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    GN_BASIC("KYC_BASIC_GN", "KYC Basic", UnitGroupEnum.GN_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    GW_BASIC("KYC_BASIC_GW", "KYC Basic", UnitGroupEnum.GW_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    LR_BASIC("KYC_BASIC_LR", "KYC Basic", UnitGroupEnum.LR_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ML_BASIC("KYC_BASIC_ML", "KYC Basic", UnitGroupEnum.ML_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    MR_BASIC("KYC_BASIC_MR", "KYC Basic", UnitGroupEnum.MR_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    NE_BASIC("KYC_BASIC_NE", "KYC Basic", UnitGroupEnum.NE_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    NG_BASIC("KYC_BASIC_NG", "KYC Basic", UnitGroupEnum.NG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SN_BASIC("KYC_BASIC_SN", "KYC Basic", UnitGroupEnum.SN_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SL_BASIC("KYC_BASIC_SL", "KYC Basic", UnitGroupEnum.SL_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    TG_BASIC("KYC_BASIC_TG", "KYC Basic", UnitGroupEnum.TG_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ZA_BASIC("KYC_BASIC_ZA", "KYC Basic", UnitGroupEnum.ZA_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    AO_BASIC("KYC_BASIC_AO", "KYC Basic", UnitGroupEnum.AO_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    BW_BASIC("KYC_BASIC_BW", "KYC Basic", UnitGroupEnum.BW_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    SZ_BASIC("KYC_BASIC_SZ", "KYC Basic", UnitGroupEnum.SZ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    LS_BASIC("KYC_BASIC_LS", "KYC Basic", UnitGroupEnum.LS_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    NA_BASIC("KYC_BASIC_NA", "KYC Basic", UnitGroupEnum.NA_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    MW_BASIC("KYC_BASIC_MW", "KYC Basic", UnitGroupEnum.MW_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    MZ_BASIC("KYC_BASIC_MZ", "KYC Basic", UnitGroupEnum.MZ_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ZM_BASIC("KYC_BASIC_ZM", "KYC Basic", UnitGroupEnum.ZM_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
    ZW_BASIC("KYC_BASIC_ZW", "KYC Basic", UnitGroupEnum.ZW_UNIT, Lists.newArrayList("Firmographics", "Shareholding Structure", "Officers"), "Instant delivery"),
*/

    // 废弃
    LITE("KYC_LITE", "KYC Lite", UnitGroupEnum.CN_UNIT, null, "Instant Delivery"),// added for v2.0.5 KNZT-5597
    OFFICIAL_WATERMARK("OFFICIAL_WATERMARK", "Snapshot with official watermark", UnitGroupEnum.CN_UNIT, null, "1 Working Days"), // updated for v1.8.9 4095
    ;


    private final String code;
    private final String codeDesc;
    private final UnitGroupEnum unitGroupEnum;
    private final List<String> infoList;
    private final String genDays;
    public String getCode() {
        return code;
    }


    public String getDesc() {
        return codeDesc;
    }

    public List<String> getInfoList() {
        return infoList;
    }

    public UnitGroupEnum getUnitGroupEnum() {   
        return unitGroupEnum;
    }

    public String getGenDays() {
        return genDays;
    }

    ReportTypeEnum(String code, String codeDesc, UnitGroupEnum unitGroupEnum, List<String> infoList, String genDays) {
        this.code = code;
        this.codeDesc = codeDesc;
        this.infoList = infoList;
        this.unitGroupEnum = unitGroupEnum;
        this.genDays = genDays;
    }


    public static ReportTypeEnum getEnumByCode(String code){
        for (ReportTypeEnum enumItem : values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (ReportTypeEnum modeEnum : ReportTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getDesc();
            }
        }
        return "";
    }

    public static List<String> getInfoList(String code) {
        for (ReportTypeEnum modeEnum : ReportTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getInfoList();
            }
        }
        return Collections.emptyList();
    }

    public static UnitGroupEnum getUnitGroupEnum(String code) {
        for (ReportTypeEnum modeEnum : ReportTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getUnitGroupEnum();
            }
        }
        return null;
    }

    public static String getGenDays(String code) {
        for (ReportTypeEnum modeEnum : ReportTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getGenDays();
            }
        }
        return "Instant delivery";
    }


    // updated for v2.0.2 chenbl KNZT-5271
    public static List<String> withBasicList() {
        return Lists.newArrayList(BASIC.getCode(), UBO.getCode(), ADVANCED.getCode(), FIN_TAX.getCode(), MERCHANT.getCode());
    }

    public static List<String> withUBOList() {
        return Lists.newArrayList(UBO.getCode(), ADVANCED.getCode(), FIN_TAX.getCode(), MERCHANT.getCode());
    }

    public static List<String> withAdvancedList() {
        return Lists.newArrayList(ADVANCED.getCode(), FIN_TAX.getCode());
    }

    public static List<String> witFinTaxList() {
        return Lists.newArrayList(FIN_TAX.getCode());
    }

    public static List<String> withMerchantList() {
        return Lists.newArrayList(MERCHANT.getCode());
    }

    public static List<String> immediateReportList() {
        return Lists.newArrayList(BASIC.getCode(), UBO.getCode(), ADVANCED.getCode(), CORP_360.getCode(), PERS_BASIC.getCode(),
                PERS_360.getCode(), LEGAL.getCode(), VERIFY.getCode(), VERIFY_CORP.getCode(), OFFICIAL_WATERMARK.getCode());
    }

    /**
     * added for v2.0.6 chenbl KNZT-5506
     * 大陆需要浏览器打印的报告类型
     * @return
     */
    public static Set<String> localChromeReportList() {
        return Sets.newHashSet(LITE.getCode(), BASIC.getCode(), UBO.getCode(), ADVANCED.getCode(), PERS_BASIC.getCode(), FIN_TAX.getCode(), MERCHANT.getCode());
    }

    /**
     * updated for v2.0.6 chenbl KNZT-5506
     * updated for v1.8.0 KNZT-3613
     * 需要浏览器打印的报告类型
     * @return
     */
    public static Set<String> chromeReportList() {
        Set<String> list = localChromeReportList();
        list.addAll(sgCorpList());
        list.add(SCAN.getCode());// added for v2.0.9 chenbl KNZT-5362
        list.addAll(hkReportList());//added for v2.0.9 fengsw KNZT-5608 香港企业需要浏览器打印的报告类型
        list.addAll(getVerifyList());
        list.addAll(myCorpList());
        list.addAll(nzCorpList());
        list.addAll(twCorpList());
        return list;
    }

    // 需要加密的报告列表 added for v2.0.2 chenbl KNZT-5292
    public static List<String> needEncryptReportDocList() {
        return Lists.newArrayList(FIN_TAX.getCode());
    }

    // updated for v1.8.0 KNZT-3613
    public static List<String> local360ReportList() {
        return Lists.newArrayList(CORP_360.getCode(), PERS_360.getCode());
    }

    // added for v1.9.0 KNZT-4095
    public static List<String> spiderReportList() {
        return Lists.newArrayList(LEGAL.getCode()); // updated for v2.0.9 chenbl KNZT-5883
    }

    /**
     * 需要生成pdf的报告类型
     * @return
     */
    public static List<String> jobCheckList() { // updated for v1.9.6 KNZT-4762
        List<String> res = Lists.newArrayList();
        res.addAll(chromeReportList());
        res.addAll(local360ReportList());
        res.addAll(spiderReportList());
        // 去除verify 列表
        res.removeAll(getVerifyList());
        return res;
    }



    /**
     * updated for v2.1.2 chenbl KNZT-6169
     * 在ExecuteAfterSubmitRunnable中跑数据后，才能创建报告的类型
     * added for lvcy v2.1.1 KNZT-5973
     * @return
     */
    public static List<String> needRunDataInSubmitRunnable() {
        return Lists.newArrayList(MERCHANT.getCode(), SCAN.getCode());
    }

    public static List<String> getScanReportList() {
        return Lists.newArrayList(SCAN.getCode());
    }
    /**
     * update for v1.7.8
     * @return
     */
    public static List<String> corpList() {
        ArrayList<String> typeList = Lists.newArrayList(SG_BAS.getCode());
        typeList.addAll(localCorpList()); // updated for v1.8.5 KNZT-3802
        typeList.addAll(hkCorpList());
        typeList.addAll(myCorpList());
        typeList.addAll(nzCorpList());
        typeList.addAll(twCorpList());
        return typeList;
    }

    // added for v1.8.5 KNZT-3802
    // 大陆企业
    public static List<String> localCorpList() {
        return Lists.newArrayList(LITE.getCode(), BASIC.getCode(), UBO.getCode(), ADVANCED.getCode(), LEGAL.getCode(), CORP_360.getCode(), OFFICIAL_WATERMARK.getCode(), FIN_TAX.getCode(), MERCHANT.getCode());
    }

    // 大陆报告 added for v1.9.3 KNZT-4382
    public static List<String> localList() {
        ArrayList<String> localList = Lists.newArrayList(localCorpList());
        localList.addAll(persTypeList());
        return localList;
    }

    // api订单类型, 香港新加坡需要底层购买爬取的 added for v2.0.0 chenbl KNZT-5055
    public static List<String> apiOrderTypeList() {
        return Lists.newArrayList(
                HK_BAS.getCode(), HK_BAS_AR.getCode(), HK_BR_EXTRACT.getCode(), HK_DUP_CERT.getCode(), SG_BAS.getCode(), MY_BASIC.getCode(), MAP_NETWORK_HK.getCode(),
                MAP_OWNERSHIP_HK.getCode(), NZ_BASIC.getCode(), HK_DOCUMENT.getCode(), TW_BASIC.getCode());
    }

    public static List<String> apiOrderTypeWithoutViewList() {
        return Lists.newArrayList(MAP_NETWORK_HK.getCode(), MAP_OWNERSHIP_HK.getCode(), HK_DOCUMENT.getCode());
    }

    public static List<String> hkCorpList() {
        List<String> list = hkIcrisCorpList();
        list.addAll(hkIrdCorpList());
        return list;
    }

    public static List<String> hkIcrisCorpList() {
        return Lists.newArrayList(HK_BAS.getCode(), HK_BAS_AR.getCode(), HK_DOCUMENT.getCode(), MAP_NETWORK_HK.getCode(), MAP_OWNERSHIP_HK.getCode());
    }

    public static List<String> hkIrdCorpList() {
        return Lists.newArrayList(HK_BR_EXTRACT.getCode(), HK_DUP_CERT.getCode());
    }

    public static List<String> hkReportList() {
        return Lists.newArrayList(HK_BAS.getCode(), HK_BAS_AR.getCode(), HK_BR_EXTRACT.getCode(), HK_DUP_CERT.getCode());
    }

    public static List<String> myCorpList() {
        return Lists.newArrayList(MY_BASIC.getCode());
    }

    // added for v1.8.6 KNZT-3873
    public static List<String> sgCorpList() {
        return Lists.newArrayList(SG_BAS.getCode());
    }

    public static List<String> nzCorpList() {
        return Lists.newArrayList(NZ_BASIC.getCode());
    }

    public static List<String> twCorpList() {
        return Lists.newArrayList(TW_BASIC.getCode());
    }

    // 有实际成本的订单类型列表 added for v2.0.2 chenbl KNZT-5294
    public static List<String> actualCostTypeList(){
        ArrayList<String> list = Lists.newArrayList(apiOrderTypeList());
        list.add(FIN_TAX.getCode());
        list.add(HK_DOCUMENT.getCode());
        return list;
    }

    // added for v1.8.0 KNZT-3524
    private static final Map<ReportTypeEnum, List<ReportTypeEnum>> UPGRADE_MAP = new HashMap<>();

    static {
        UPGRADE_MAP.put(ReportTypeEnum.LITE, Lists.newArrayList(ReportTypeEnum.BASIC, ReportTypeEnum.UBO, ReportTypeEnum.ADVANCED, ReportTypeEnum.CORP_360));
        UPGRADE_MAP.put(ReportTypeEnum.BASIC, Lists.newArrayList(ReportTypeEnum.UBO, ReportTypeEnum.ADVANCED, ReportTypeEnum.CORP_360));
        UPGRADE_MAP.put(ReportTypeEnum.UBO, Lists.newArrayList(ReportTypeEnum.ADVANCED, ReportTypeEnum.CORP_360));
        UPGRADE_MAP.put(ReportTypeEnum.ADVANCED, Lists.newArrayList(ReportTypeEnum.CORP_360));
    }


    public static boolean canUpgrade(String fromCode, String toCode) {
        ReportTypeEnum from = getEnumByCode(fromCode);
        ReportTypeEnum to = getEnumByCode(toCode);
        if (Objects.isNull(from) || Objects.isNull(to)) {
            return false;
        }
        List<ReportTypeEnum> reportTypeEnums = UPGRADE_MAP.get(from);
        return CollectionUtils.isNotEmpty(reportTypeEnums) && reportTypeEnums.contains(to);
    }

    public static List<String> persTypeList() {
        return Lists.newArrayList(PERS_BASIC.getCode(), PERS_360.getCode());
    }

    public static List<String> outboundAllowList() {
        // updated for v2.0.5 KNZT-5597 KYC Lite 需要记录法定代表人数据出境
        return Lists.newArrayList(LITE.getCode(), BASIC.getCode(), UBO.getCode(), ADVANCED.getCode(), PERS_BASIC.getCode(), MERCHANT.getCode(), CORP_360.getCode());
    }

    public static List<String> subOrderReportTypeList() {
        return Lists.newArrayList(LEGAL.getCode());
    }

    // 报告状态超时配置 added for v1.9.6 KNZT-4646
    private static final Map<String, Integer> REPORT_TYPE_2_STATUS_CHECK_DELAYED_SEC_MAP = new HashMap<>();

    public static Map<String, Integer> getReportType2StatusCheckDelayedSecMap() {
        if (REPORT_TYPE_2_STATUS_CHECK_DELAYED_SEC_MAP.isEmpty()) {
            for (String reportType : apiOrderTypeList()) {
                if (HK_BAS_AR.getCode().equals(reportType) || hkIrdCorpList().contains(reportType)) {
                    REPORT_TYPE_2_STATUS_CHECK_DELAYED_SEC_MAP.put(HK_BAS_AR.getCode(), 10 * 60);
                } else if (sgCorpList().contains(reportType)) {
                    REPORT_TYPE_2_STATUS_CHECK_DELAYED_SEC_MAP.put(reportType, 30 * 60);
                } else {
                    REPORT_TYPE_2_STATUS_CHECK_DELAYED_SEC_MAP.put(reportType, 2 * 60);
                }
            }
        }
        return REPORT_TYPE_2_STATUS_CHECK_DELAYED_SEC_MAP;
    }

    // KYC 对应的产品类型 added for lvcy v2.1.4 KNZT-6254
    public static List<String> getKYCReportList() {
        return Lists.newArrayList(
                BASIC.getCode(), MERCHANT.getCode(), CORP_360.getCode(), UBO.getCode(), ADVANCED.getCode(),
                FIN_TAX.getCode(), LEGAL.getCode(), HK_BAS_AR.getCode(), HK_BAS.getCode(), HK_DOCUMENT.getCode(), HK_BR_EXTRACT.getCode(), HK_DUP_CERT.getCode(),
                SG_BAS.getCode(), PERS_BASIC.getCode(), PERS_360.getCode(), MY_BASIC.getCode(), NZ_BASIC.getCode(), TW_BASIC.getCode());
    }

    public static List<String> getKYCList() {
        List<String> res = new ArrayList<>();
        res.addAll(getKYCReportList());
        res.addAll(getVerifyList());
        return res;
    }

    public static List<String> getKnowYourCustomerInKYC() {
        return Lists.newArrayList(
                BASIC.getCode(), MERCHANT.getCode(), CORP_360.getCode(), PERS_BASIC.getCode(), PERS_360.getCode(),
                HK_BAS.getCode(), HK_BAS_AR.getCode(), SG_BAS.getCode(), MY_BASIC.getCode(), NZ_BASIC.getCode(), TW_BASIC.getCode());
    }

    // 图谱list added for lvcy v2.1.5 KNZT-6384
    public static List<String> getMapList() {
        return Lists.newArrayList(MAP_OWNERSHIP.getCode(), MAP_NETWORK.getCode(), MAP_RELATION.getCode(), MAP_RELATION_3.getCode(), MAP_OWNERSHIP_HK.getCode(), MAP_NETWORK_HK.getCode());
    }

    public static List<String> getVerifyList() {
        return Lists.newArrayList(VERIFY.getCode(), VERIFY_CORP.getCode());
    }

    public static boolean isRelationMap(String reportType) {
        return MAP_RELATION.getCode().equals(reportType) || MAP_RELATION_3.getCode().equals(reportType);
    }

}
