package com.qcc.frame.jee.modules.sys.model;


import io.swagger.annotations.ApiModelProperty;

public class CompCorpFunctionTO implements java.io.Serializable {
	
	/**
	 *
	 */
	private static final long serialVersionUID = 5892793661816578732L;
    
    @ApiModelProperty(value = "功能Id", example = "")
	private String functionId;
	@ApiModelProperty(value = "功能名称", example = "")
	private String functionName;
    @ApiModelProperty(value = "是否为主功能", example = "")
	private Boolean mainFunctionFlag;
    @ApiModelProperty(value = "计数分类，P:自然人;C:企业;F:基金产品;N:非穿透且不分表 ", example = "")
	private String countCategory;

	@ApiModelProperty(value = "是否已经存在", example = "")
	private boolean isExist;
	
	public String getFunctionId() {
		return functionId;
	}
	public void setFunctionId(String functionId) {
		this.functionId = functionId;
	}
	
	public String getFunctionName() {
		return functionName;
	}
	public void setFunctionName(String functionName) {
		this.functionName = functionName;
	}
	public Boolean getMainFunctionFlag() {
		return mainFunctionFlag;
	}
	public void setMainFunctionFlag(Boolean mainFunctionFlag) {
		this.mainFunctionFlag = mainFunctionFlag;
	}
	public String getCountCategory() {
		return countCategory;
	}
	public void setCountCategory(String countCategory) {
		this.countCategory = countCategory;
	}

	public boolean isExist() {
		return isExist;
	}

	public void setExist(boolean exist) {
		isExist = exist;
	}
}
