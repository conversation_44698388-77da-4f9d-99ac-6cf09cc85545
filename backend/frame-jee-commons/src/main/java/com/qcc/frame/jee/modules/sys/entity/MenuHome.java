package com.qcc.frame.jee.modules.sys.entity;

import com.qcc.frame.jee.modules.sys.model.saas.SaasMenuTO;

import java.util.ArrayList;
import java.util.List;

public class MenuHome implements java.io.Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -9044436953743587154L;
	private String areaInd;
	private String menuHref;
	private String moduleName;
	private String moduleRemarks;
	private String sign;
	private String groupId;
	private List<SaasMenuTO> menuList = new ArrayList<>(1);

	public MenuHome() {
		super();
	}
	public MenuHome(String areaInd, String menuHref, String moduleName, String moduleRemarks) {
		super();
		this.areaInd = areaInd;
		this.menuHref = menuHref;
		this.moduleName = moduleName;
		this.moduleRemarks = moduleRemarks;
	}

	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
	public List<SaasMenuTO> getMenuList() {
		return menuList;
	}
	public void setMenuList(List<SaasMenuTO> menuList) {
		this.menuList = menuList;
	}
	public String getAreaInd() {
		return areaInd;
	}
	public void setAreaInd(String areaInd) {
		this.areaInd = areaInd;
	}
	public String getMenuHref() {
		return menuHref;
	}
	public void setMenuHref(String menuHref) {
		this.menuHref = menuHref;
	}
	public String getModuleName() {
		return moduleName;
	}
	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}
	public String getModuleRemarks() {
		return moduleRemarks;
	}
	public void setModuleRemarks(String moduleRemarks) {
		this.moduleRemarks = moduleRemarks;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	
}
