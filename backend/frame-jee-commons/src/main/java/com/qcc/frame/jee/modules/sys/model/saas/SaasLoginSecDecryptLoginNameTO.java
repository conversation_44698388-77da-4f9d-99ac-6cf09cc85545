package com.qcc.frame.jee.modules.sys.model.saas;

public class SaasLoginSecDecryptLoginNameTO {
	private String mobile;
	private String mobileAuth;
	private String setId;
	private int duration;
	private short genMode;
	private String postponeSeq;
	private String macAddr;
	
	private String signUserType;//S:子账号标识
	private String adminLoginName;//如果signUserType为S, adminLoginName就是对应的管理员登录名
	private Integer subUserCount;
	private String areaId;
	private String provinceCd;
	
//	private int count;

	public String getPostponeSeq() {
		return postponeSeq;
	}
	public String getProvinceCd() {
		return provinceCd;
	}
	public void setProvinceCd(String provinceCd) {
		this.provinceCd = provinceCd;
	}
	public String getAreaId() {
		return areaId;
	}
	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}
	public Integer getSubUserCount() {
		return subUserCount;
	}
	public void setSubUserCount(Integer subUserCount) {
		this.subUserCount = subUserCount;
	}
	public String getAdminLoginName() {
		return adminLoginName;
	}
	public void setAdminLoginName(String adminLoginName) {
		this.adminLoginName = adminLoginName;
	}
	public String getSignUserType() {
		return signUserType;
	}
	public void setSignUserType(String signUserType) {
		this.signUserType = signUserType;
	}
	public String getMacAddr() {
		return macAddr;
	}
	public void setMacAddr(String macAddr) {
		this.macAddr = macAddr;
	}
	public void setPostponeSeq(String postponeSeq) {
		this.postponeSeq = postponeSeq;
	}
	public String getMobile() {
		return mobile;
	}

	public short getGenMode() {
		return genMode;
	}
	public void setGenMode(short genMode) {
		this.genMode = genMode;
	}
	public int getDuration() {
		return duration;
	}
	public void setDuration(int duration) {
		this.duration = duration;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	public String getSetId() {
		return setId;
	}
	public void setSetId(String setId) {
		this.setId = setId;
	}
//	public int getCount() {
//		return count;
//	}
//	public void setCount(int count) {
//		this.count = count;
//	}
	public String getMobileAuth() {
		return mobileAuth;
	}
	public void setMobileAuth(String mobileAuth) {
		this.mobileAuth = mobileAuth;
	}

}
