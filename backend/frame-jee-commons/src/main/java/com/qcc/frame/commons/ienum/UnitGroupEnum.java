package com.qcc.frame.commons.ienum;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

// updated for v1.9.6 KNZT-4642 增加中文
public enum UnitGroupEnum {
    CN_UNIT(GlobalAreaEnum.CN),
    HK_UNIT(GlobalAreaEnum.HK),
    TW_UNIT(GlobalAreaEnum.TW),
    MO_UNIT(GlobalAreaEnum.MO),
    JP_UNIT(GlobalAreaEnum.JP),
    KR_UNIT(GlobalAreaEnum.KR),
    SG_UNIT(GlobalAreaEnum.SG),
    VN_UNIT(GlobalAreaEnum.VN),
    ID_UNIT(GlobalAreaEnum.ID),
    TH_UNIT(GlobalAreaEnum.TH),
    PH_UNIT(GlobalAreaEnum.PH),
    MY_UNIT(GlobalAreaEnum.MY),
    KH_UNIT(GlobalAreaEnum.KH),
    MM_UNIT(GlobalAreaEnum.MM),
    BN_UNIT(GlobalAreaEnum.BN),
    IN_UNIT(GlobalAreaEnum.IN),
    PK_UNIT(GlobalAreaEnum.PK),
    IL_UNIT(GlobalAreaEnum.IL),
    JO_UNIT(GlobalAreaEnum.JO),
    KW_UNIT(GlobalAreaEnum.KW),
    OM_UNIT(GlobalAreaEnum.OM),
    SA_UNIT(GlobalAreaEnum.SA),
    AE_UNIT(GlobalAreaEnum.AE),
    CY_UNIT(GlobalAreaEnum.CY),
    MA_UNIT(GlobalAreaEnum.MA),
    MU_UNIT(GlobalAreaEnum.MU),
    RW_UNIT(GlobalAreaEnum.RW),
    SC_UNIT(GlobalAreaEnum.SC),
    ZA_UNIT(GlobalAreaEnum.ZA),
    RU_UNIT(GlobalAreaEnum.RU),
    CZ_UNIT(GlobalAreaEnum.CZ),
    SK_UNIT(GlobalAreaEnum.SK),
    HU_UNIT(GlobalAreaEnum.HU),
    PL_UNIT(GlobalAreaEnum.PL),
    RO_UNIT(GlobalAreaEnum.RO),
    EE_UNIT(GlobalAreaEnum.EE),
    LV_UNIT(GlobalAreaEnum.LV),
    LT_UNIT(GlobalAreaEnum.LT),
    DK_UNIT(GlobalAreaEnum.DK),
    FI_UNIT(GlobalAreaEnum.FI),
    NO_UNIT(GlobalAreaEnum.NO),
    SE_UNIT(GlobalAreaEnum.SE),
    GB_UNIT(GlobalAreaEnum.GB),
    FR_UNIT(GlobalAreaEnum.FR),
    DE_UNIT(GlobalAreaEnum.DE),
    NL_UNIT(GlobalAreaEnum.NL),
    BE_UNIT(GlobalAreaEnum.BE),
    LU_UNIT(GlobalAreaEnum.LU),
    CH_UNIT(GlobalAreaEnum.CH),
    AT_UNIT(GlobalAreaEnum.AT),
    IE_UNIT(GlobalAreaEnum.IE),
    IT_UNIT(GlobalAreaEnum.IT),
    ES_UNIT(GlobalAreaEnum.ES),
    GR_UNIT(GlobalAreaEnum.GR),
    MT_UNIT(GlobalAreaEnum.MT),
    SI_UNIT(GlobalAreaEnum.SI),
    HR_UNIT(GlobalAreaEnum.HR),
    AU_UNIT(GlobalAreaEnum.AU),
    NZ_UNIT(GlobalAreaEnum.NZ),
    MH_UNIT(GlobalAreaEnum.MH),
    WS_UNIT(GlobalAreaEnum.WS),
    US_AL_UNIT("US-AL", GlobalAreaEnum.US, "AL", "Alabama", "阿拉巴马州"),
    US_AK_UNIT("US-AK", GlobalAreaEnum.US, "AK", "Alaska", "阿拉斯加州"),
    US_AZ_UNIT("US-AZ", GlobalAreaEnum.US, "AZ", "Arizona", "亚利桑那州"),
    US_CA_UNIT("US-CA", GlobalAreaEnum.US, "CA", "California", "加利福尼亚州"),
    US_CO_UNIT("US-CO", GlobalAreaEnum.US, "CO", "Colorado", "科罗拉多州"),
    US_CT_UNIT("US-CT", GlobalAreaEnum.US, "CT", "Connecticut", "康涅狄格州"),
    US_DE_UNIT("US-DE", GlobalAreaEnum.US, "DE", "Delaware", "特拉华州"),
    US_FL_UNIT("US-FL", GlobalAreaEnum.US, "FL", "Florida", "佛罗里达州"),
    US_HI_UNIT("US-HI", GlobalAreaEnum.US, "HI", "Hawaii", "夏威夷州"),
    US_ID_UNIT("US-ID", GlobalAreaEnum.US, "ID", "Idaho", "爱达荷州"),
    US_KS_UNIT("US-KS", GlobalAreaEnum.US, "KS", "Kansas", "堪萨斯州"),
    US_KY_UNIT("US-KY", GlobalAreaEnum.US, "KY", "Kentucky", "肯塔基州"),
    US_LA_UNIT("US-LA", GlobalAreaEnum.US, "LA", "Louisiana", "路易斯安那州"),
    US_ME_UNIT("US-ME", GlobalAreaEnum.US, "ME", "Maine", "缅因州"),
    US_MI_UNIT("US-MI", GlobalAreaEnum.US, "MI", "Michigan", "密歇根州"),
    US_MN_UNIT("US-MN", GlobalAreaEnum.US, "MN", "Minnesota", "明尼苏达州"),
    US_MS_UNIT("US-MS", GlobalAreaEnum.US, "MS", "Mississippi", "密西西比州"),
    US_NJ_UNIT("US-NJ", GlobalAreaEnum.US, "NJ", "New Jersey", "新泽西州"),
    US_NY_UNIT("US-NY", GlobalAreaEnum.US, "NY", "New York", "纽约州"),
    US_NC_UNIT("US-NC", GlobalAreaEnum.US, "NC", "North Carolina", "北卡罗来纳州"),
    US_ND_UNIT("US-ND", GlobalAreaEnum.US, "ND", "North Dakota", "北达科他州"),
    US_RI_UNIT("US-RI", GlobalAreaEnum.US, "RI", "Rhode Island", "罗德岛州"),
    US_UT_UNIT("US-UT", GlobalAreaEnum.US, "UT", "Utah", "犹他州"),
    US_WA_UNIT("US-WA", GlobalAreaEnum.US, "WA", "Washington", "华盛顿州"),
    US_WV_UNIT("US-WV", GlobalAreaEnum.US, "WV", "West Virginia", "西弗吉尼亚州"),
    US_WY_UNIT("US-WY", GlobalAreaEnum.US, "WY", "Wyoming", "怀俄明州"),
    CA_UNIT(GlobalAreaEnum.CA),
    BM_UNIT(GlobalAreaEnum.BM),
    JM_UNIT(GlobalAreaEnum.JM),
    PR_UNIT(GlobalAreaEnum.PR),
    KY_UNIT(GlobalAreaEnum.KY),
    VG_UNIT(GlobalAreaEnum.VG),
    BZ_UNIT(GlobalAreaEnum.BZ),
    BR_UNIT(GlobalAreaEnum.BR),
    AR_UNIT(GlobalAreaEnum.AR),

    /*
    // 中东地区
    IR_UNIT(GlobalAreaEnum.IR),     // 伊朗
    LB_UNIT(GlobalAreaEnum.LB),     // 黎巴嫩
    QA_UNIT(GlobalAreaEnum.QA),     // 卡塔尔
    PS_UNIT(GlobalAreaEnum.PS),     // 巴勒斯坦
    SY_UNIT(GlobalAreaEnum.SY),     // 叙利亚
    TR_UNIT(GlobalAreaEnum.TR),     // 土耳其
    YE_UNIT(GlobalAreaEnum.YE),     // 也门
    BH_UNIT(GlobalAreaEnum.BH),     // 巴林
    IQ_UNIT(GlobalAreaEnum.IQ),     // 伊拉克
    EG_UNIT(GlobalAreaEnum.EG),     // 埃及
    LY_UNIT(GlobalAreaEnum.LY),     // 利比亚
    TN_UNIT(GlobalAreaEnum.TN),     // 突尼斯
    DZ_UNIT(GlobalAreaEnum.DZ),     // 阿尔及利亚
    SD_UNIT(GlobalAreaEnum.SD),     // 苏丹

    // 非洲地区
    KM_UNIT(GlobalAreaEnum.KM),     // 科摩罗
    DJ_UNIT(GlobalAreaEnum.DJ),     // 吉布提
    ER_UNIT(GlobalAreaEnum.ER),     // 厄立特里亚
    ET_UNIT(GlobalAreaEnum.ET),     // 埃塞俄比亚
    KE_UNIT(GlobalAreaEnum.KE),     // 肯尼亚
    MG_UNIT(GlobalAreaEnum.MG),     // 马达加斯加
    SO_UNIT(GlobalAreaEnum.SO),     // 索马里
    SS_UNIT(GlobalAreaEnum.SS),     // 南苏丹
    TZ_UNIT(GlobalAreaEnum.TZ),     // 坦桑尼亚
    UG_UNIT(GlobalAreaEnum.UG),     // 乌干达
    BI_UNIT(GlobalAreaEnum.BI),     // 布隆迪
    CM_UNIT(GlobalAreaEnum.CM),     // 喀麦隆
    CF_UNIT(GlobalAreaEnum.CF),     // 中非共和国
    TD_UNIT(GlobalAreaEnum.TD),     // 乍得
    CG_UNIT(GlobalAreaEnum.CG),     // 刚果（布）
    CD_UNIT(GlobalAreaEnum.CD),     // 刚果（金）
    GQ_UNIT(GlobalAreaEnum.GQ),     // 赤道几内亚
    GA_UNIT(GlobalAreaEnum.GA),     // 加蓬
    ST_UNIT(GlobalAreaEnum.ST),     // 圣多美和普林西比
    BJ_UNIT(GlobalAreaEnum.BJ),     // 贝宁
    BF_UNIT(GlobalAreaEnum.BF),     // 布基纳法索
    CV_UNIT(GlobalAreaEnum.CV),     // 佛得角
    CI_UNIT(GlobalAreaEnum.CI),     // 科特迪瓦
    GM_UNIT(GlobalAreaEnum.GM),     // 冈比亚
    GH_UNIT(GlobalAreaEnum.GH),     // 加纳
    GN_UNIT(GlobalAreaEnum.GN),     // 几内亚
    GW_UNIT(GlobalAreaEnum.GW),     // 几内亚比绍
    LR_UNIT(GlobalAreaEnum.LR),     // 利比里亚
    ML_UNIT(GlobalAreaEnum.ML),     // 马里
    MR_UNIT(GlobalAreaEnum.MR),     // 毛里塔尼亚
    NE_UNIT(GlobalAreaEnum.NE),     // 尼日尔
    NG_UNIT(GlobalAreaEnum.NG),     // 尼日利亚
    SN_UNIT(GlobalAreaEnum.SN),     // 塞内加尔
    SL_UNIT(GlobalAreaEnum.SL),     // 塞拉利昂
    TG_UNIT(GlobalAreaEnum.TG),     // 多哥
    AO_UNIT(GlobalAreaEnum.AO),     // 安哥拉
    BW_UNIT(GlobalAreaEnum.BW),     // 博茨瓦纳
    SZ_UNIT(GlobalAreaEnum.SZ),     // 埃斯瓦蒂尼
    LS_UNIT(GlobalAreaEnum.LS),     // 莱索托
    NA_UNIT(GlobalAreaEnum.NA),     // 纳米比亚
    MW_UNIT(GlobalAreaEnum.MW),     // 马拉维
    MZ_UNIT(GlobalAreaEnum.MZ),     // 莫桑比克
    ZM_UNIT(GlobalAreaEnum.ZM),     // 赞比亚
    ZW_UNIT(GlobalAreaEnum.ZW),     // 津巴布韦
    */

    ;

    private final String group; // 唯一编码
    private final String name; // 英文名称
    private final String nameCn; // 中文名称
    private final GlobalAreaEnum globalAreaEnum; // Nation枚举
    private final String regPlaceCode; // 注册地(编码)
    private final String regPlace; // 注册地(英文)
    private final String regPlaceCn; // 注册地(中文)

    /**
     * 和GlobalAreaEnum一一对应情况的构造方法
     *
     * @param globalAreaEnum
     */
    UnitGroupEnum(GlobalAreaEnum globalAreaEnum) {
        this.group = globalAreaEnum.getNameCode();
        this.name = globalAreaEnum.getShortNameEn();
        this.nameCn = globalAreaEnum.getShortNameCn();
        this.globalAreaEnum = globalAreaEnum;
        this.regPlaceCode = null;
        this.regPlace = null;
        this.regPlaceCn = null;
    }

    /**
     * 美国等一个GlobalAreaEnum对应多个UnitGroupEnum的情况的构造方法
     *
     * @param group
     * @param globalAreaEnum
     * @param regPlaceCode
     * @param regPlace
     * @param regPlaceCn
     */
    UnitGroupEnum(String group, GlobalAreaEnum globalAreaEnum, String regPlaceCode, String regPlace, String regPlaceCn) {
        this.group = group;
        this.name = regPlace;
        this.nameCn = regPlaceCn;
        this.globalAreaEnum = globalAreaEnum;
        this.regPlaceCode = regPlaceCode;
        this.regPlace = regPlace;
        this.regPlaceCn = regPlaceCn;
    }

    public String getGroup() {
        return group;
    }

    public String getName() {
        return name;
    }

    public String getNameCn() {
        return nameCn;
    }

    public GlobalAreaEnum getGlobalAreaEnum() {
        return globalAreaEnum;
    }

    public String getRegPlaceCode() {
        return regPlaceCode;
    }

    public String getRegPlace() {
        return regPlace;
    }

    public String getRegPlaceCn() {
        return regPlaceCn;
    }

    public String getNationCode() {
        return globalAreaEnum != null ? globalAreaEnum.getNameCode() : null;
    }

    public static UnitGroupEnum getByGroup(String group) {
        for(UnitGroupEnum f: UnitGroupEnum.values()) {
            if(StringUtils.equals(f.getGroup(), group)) {
                return f;
            }
        }
        return null;
    }

    public static String getNameByUnitGroup(String group) {
        for(UnitGroupEnum f: UnitGroupEnum.values()) {
            if(StringUtils.equals(f.getGroup(), group)) {
                return f.getName();
            }
        }
        return "";
    }

    public static String getNameCnByUnitGroup(String group) {
        for (UnitGroupEnum f : UnitGroupEnum.values()) {
            if (StringUtils.equals(f.getGroup(), group)) {
                return f.getNameCn();
            }
        }
        return "";
    }

    public static GlobalAreaEnum getGlobalAreaEnumByUnitGroup(String group) {
        for (UnitGroupEnum f : UnitGroupEnum.values()) {
            if (StringUtils.equals(f.getGroup(), group)) {
                return f.getGlobalAreaEnum();
            }
        }
        return null;
    }

    public static String getGroupByName(String name) {
        for(UnitGroupEnum f: UnitGroupEnum.values()) {
            if(StringUtils.equals(f.getName(), name)) {
                return f.getGroup();
            }
        }
        return "";
    }

    public static UnitGroupEnum getByRegionCode(String nationCode, String regPlaceCode) {
        for (UnitGroupEnum enumItem : UnitGroupEnum.values()) {
            if (StringUtils.equals(enumItem.getNationCode(), nationCode)
                    && (StringUtils.isBlank(enumItem.getRegPlaceCode()) || StringUtils.equals(enumItem.getRegPlaceCode(), regPlaceCode))) {
                return enumItem;
            }
        }
        return null;
    }

    public static List<String> cnFunctionTableUnitGroup() {
        return Lists.newArrayList(UnitGroupEnum.CN_UNIT.getGroup());
    }

    public static List<String> globalFunctionTableUnitGroup() {
        return Lists.newArrayList(UnitGroupEnum.HK_UNIT.getGroup(), UnitGroupEnum.SG_UNIT.getGroup());
    }

    /**
     * 获取是海外并且基础数据是内部的列表
     *
     * @return
     */
    public static List<UnitGroupEnum> getBasicOverseaEnumList() {
        return Lists.newArrayList(HK_UNIT, SG_UNIT, MY_UNIT, TH_UNIT, PH_UNIT, GB_UNIT, AU_UNIT, NZ_UNIT);
    }


    public static List<UnitGroupEnum> getAsiaList() {
        return Lists.newArrayList(
                CN_UNIT, HK_UNIT, TW_UNIT, MO_UNIT, JP_UNIT, KR_UNIT, SG_UNIT, VN_UNIT,
                ID_UNIT, TH_UNIT, MY_UNIT, PH_UNIT, KH_UNIT, MM_UNIT, BN_UNIT, IN_UNIT, PK_UNIT
        );
    }

    public static List<UnitGroupEnum> getMiddleEastList() {
        return Lists.newArrayList(
                IL_UNIT, JO_UNIT, KW_UNIT, OM_UNIT, SA_UNIT, AE_UNIT,
                CY_UNIT, MA_UNIT
        );
    }

    public static List<UnitGroupEnum> getAfricaList() {
        return Lists.newArrayList(
                MU_UNIT, RW_UNIT, SC_UNIT, ZA_UNIT
        );
    }

    public static List<UnitGroupEnum> getEuropeList() {
        return Lists.newArrayList(
                GB_UNIT, FR_UNIT, DE_UNIT, NL_UNIT, BE_UNIT,
                LU_UNIT, CH_UNIT, AT_UNIT, IE_UNIT, RU_UNIT,
                CZ_UNIT, SK_UNIT, HU_UNIT, PL_UNIT, RO_UNIT,
                EE_UNIT, LV_UNIT, LT_UNIT, DK_UNIT, FI_UNIT,
                NO_UNIT, SE_UNIT, IT_UNIT, ES_UNIT, GR_UNIT,
                MT_UNIT, SI_UNIT, HR_UNIT
        );
    }

    public static List<UnitGroupEnum> getOceaniaList() {
        return Lists.newArrayList(
                AU_UNIT, NZ_UNIT, MH_UNIT, WS_UNIT
        );
    }

    public static List<UnitGroupEnum> getNorthAmericaList() {
        return Lists.newArrayList(
                US_AL_UNIT, US_AK_UNIT, US_AZ_UNIT, US_CA_UNIT,
                US_CO_UNIT, US_CT_UNIT, US_DE_UNIT, US_FL_UNIT, US_HI_UNIT,
                US_ID_UNIT, US_KS_UNIT, US_KY_UNIT, US_LA_UNIT,
                US_ME_UNIT, US_MI_UNIT, US_MN_UNIT, US_MS_UNIT,
                US_NJ_UNIT, US_NY_UNIT, US_NC_UNIT, US_ND_UNIT,
                US_RI_UNIT, US_UT_UNIT, US_WA_UNIT, US_WV_UNIT,
                US_WY_UNIT, CA_UNIT, BM_UNIT
        );
    }

    public static List<UnitGroupEnum> getLatinAmericaAndTheCaribbeanList() {
        return Lists.newArrayList(
                JM_UNIT, PR_UNIT, KY_UNIT, VG_UNIT, BZ_UNIT, BR_UNIT, AR_UNIT
        );
    }
}
