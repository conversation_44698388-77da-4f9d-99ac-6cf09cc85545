package com.qcc.frame.jee.modules.sys.utils;

import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Log;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.ThreadPoolService;
import com.qcc.frame.jee.modules.sys.thread.AccessLogRunnable;

import javax.servlet.http.HttpServletRequest;

/**
 * 字典工具类
 */
public class LogUtils {
	
	public static final String CACHE_MENU_NAME_PATH_MAP = "menuNamePathMap";
	
//	private static LogDao logDao = SpringContextHolder.getBean(LogDao.class);
//	private static MenuDao menuDao = SpringContextHolder.getBean(MenuDao.class);
	private static ThreadPoolService threadPoolService = SpringContextHolder.getBean(ThreadPoolService.class);
	
	/**
	 * 保存日志
	 */
	public static void saveLog(HttpServletRequest request, String title, String createBy){
		saveLog(request, null, title, createBy);
	}
	
	public static void saveApiIpLog(String ip, String title, String key){

			Log log = new Log();
			log.setTitle(title);
			log.setType(Log.TYPE_API_IP);
			log.setRemoteAddr(ip);
			log.setUserAgent("");
			log.setRequestUri("");
			log.setParams("");
			log.setMethod("");
			log.setCreateBy(new User(key));

			// 异步保存日志
			AccessLogRunnable logRunnable = new AccessLogRunnable();
			logRunnable.setLog(log);
			threadPoolService.execute("pool.access.log", logRunnable);
//			new SaveLogThread(log, handler, ex).start();

	}
	
	public static void saveLog(HttpServletRequest request, Exception ex, String title, String createBy){
		saveLog(request, ex, title, createBy, Log.TYPE_ACCESS);
	}
	
	/**
	 * 保存日志
	 */
	public static void saveLog(HttpServletRequest request, Exception ex, String title, String createBy, String logType){
		User user = UserUtils.getUser();
		if (user != null && user.getId() != null || StringUtils.isNotBlank(createBy)){
			Log log = new Log();
			log.setTitle(title);
			log.setType(logType);
			log.setRemoteAddr(StringUtils.getRemoteAddr(request));
			log.setUserAgent(StringUtils.left(request.getHeader("user-agent"), 200));
			log.setRequestUri(request.getRequestURI());
			log.setParams(request.getParameterMap());
			log.setMethod(request.getMethod());
			if(StringUtils.isNotBlank(createBy)) {
				log.setCreateBy(new User(createBy));
			} else {
				log.setCreateBy(user);
			}
			// 异步保存日志
			AccessLogRunnable logRunnable = new AccessLogRunnable();
			logRunnable.setLog(log);
			
			threadPoolService.execute("pool.access.log", logRunnable);
//			new SaveLogThread(log, handler, ex).start();
		}
	}

	/**
	 * 保存日志线程
	 */
//	public static class SaveLogThread extends Thread{
//		
//		private Log log;
//		private Object handler;
//		private Exception ex;
//		
//		public SaveLogThread(Log log, Object handler, Exception ex){
//			super(SaveLogThread.class.getSimpleName());
//			this.log = log;
//			this.handler = handler;
//			this.ex = ex;
//		}
//		
//		@Override
//		public void run() {
//			// 获取日志标题
//			if (StringUtils.isBlank(log.getTitle())){
//				String permission = "";
//				if (handler instanceof HandlerMethod){
//					Method m = ((HandlerMethod)handler).getMethod();
//					RequiresPermissions rp = m.getAnnotation(RequiresPermissions.class);
//					permission = (rp != null ? StringUtils.join(rp.value(), ",") : "");
//				}
//				log.setTitle(getMenuNamePath(log.getRequestUri(), permission));
//			}
//			// 如果有异常，设置异常信息
//			log.setException(Exceptions.getStackTraceAsString(ex));
//			// 如果无标题并无异常日志，则不保存信息
//			if (StringUtils.isBlank(log.getTitle()) && StringUtils.isBlank(log.getException())){
//				return;
//			}
//			// 保存日志信息
//			log.preInsert();
//			logDao.insert(log);
//		}
//	}
//
//	/**
//	 * 获取菜单名称路径（如：系统设置-机构用户-用户管理-编辑）
//	 */
//	public static String getMenuNamePath(String requestUri, String permission){
//		String href = StringUtils.substringAfter(requestUri, Global.getAdminPath());
//		@SuppressWarnings("unchecked")
//		Map<String, String> menuMap = null;//(Map<String, String>)CacheUtils.get(CACHE_MENU_NAME_PATH_MAP);
//		if (menuMap == null){
//			menuMap = Maps.newHashMap();
//			List<Menu> menuList = menuDao.findAllList(new Menu());
//			for (Menu menu : menuList){
//				// 获取菜单名称路径（如：系统设置-机构用户-用户管理-编辑）
//				String namePath = "";
//				if (menu.getParentIds() != null){
//					List<String> namePathList = Lists.newArrayList();
//					for (String id : StringUtils.split(menu.getParentIds(), ",")){
//						if (Menu.getRootId().equals(id)){
//							continue; // 过滤跟节点
//						}
//						for (Menu m : menuList){
//							if (m.getId().equals(id)){
//								namePathList.add(m.getName());
//								break;
//							}
//						}
//					}
//					namePathList.add(menu.getName());
//					namePath = StringUtils.join(namePathList, "-");
//				}
//				// 设置菜单名称路径
//				if (StringUtils.isNotBlank(menu.getHref())){
//					menuMap.put(menu.getHref(), namePath);
//				}else if (StringUtils.isNotBlank(menu.getPermission())){
//					for (String p : StringUtils.split(menu.getPermission())){
//						menuMap.put(p, namePath);
//					}
//				}
//				
//			}
////			CacheUtils.put(CACHE_MENU_NAME_PATH_MAP, menuMap);
//		}
//		String menuNamePath = menuMap.get(href);
//		if (menuNamePath == null){
//			for (String p : StringUtils.split(permission)){
//				menuNamePath = menuMap.get(p);
//				if (StringUtils.isNotBlank(menuNamePath)){
//					break;
//				}
//			}
//			if (menuNamePath == null){
//				return "";
//			}
//		}
//		return menuNamePath;
//	}

	
}
