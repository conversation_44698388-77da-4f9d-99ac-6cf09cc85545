package com.qcc.frame.jee.commons.utils;

import com.alibaba.fastjson.JSON;
import com.qcc.frame.jee.commons.model.LogAccessTO;
import com.qcc.frame.jee.commons.model.RequestTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class LogMonitorUtil {
	
	private static Logger logger = LoggerFactory.getLogger("MONITOR");
	
	private static List<String> monitorUrlList = new ArrayList<>();
	private static List<String> monitorUrlList4WebApi = new ArrayList<>();
	private static List<String> ignoreMonitorUrlList = new ArrayList<>();//忽略某些URL的记录
	static {
		monitorUrlList.add("/webapi/saas/");
		monitorUrlList.add("//webapi/saas/");
		monitorUrlList.add("/webapi/open/");
        monitorUrlList.add("/webapi/plugin/");
		monitorUrlList.add("/api/");

        monitorUrlList4WebApi.add("/webapi/a/corp/");
		
		ignoreMonitorUrlList.add("/webapi/saas/corp/common/businessInfo/refreshCorp/finish");
	}
	
	private static boolean isMonitorUrl(String url) {
		if(url == null) {
			return false;
		}
		if(ignoreMonitorUrlList.contains(url)) {
			return false;
		}
		for(String monitorUrl : monitorUrlList) {
			if(url.startsWith(monitorUrl)) {
				return true;
			}
		}
		
		return false;
	}
	
	private static boolean isMonitorUrl4WebApi(String url) {
		if(url == null) {
			return false;
		}
		for(String monitorUrl : monitorUrlList4WebApi) {
			if(url.startsWith(monitorUrl)) {
				return true;
			}
		}
		return false;
	}
	/**
	 * 只记录配置好的指定URL - 限webapi项目
	 * @param url
	 * @param key
	 * @param customerType
	 * @param searchKey
	 * @param took
	 */
	public static void accessLogLimitWebApi(String url, String key, String customerType, String remark, long took, String clientIp, String userAgent) {
		if(isMonitorUrl4WebApi(url)) {
			accessLogAll(url, key, customerType, remark, took, "webapp", clientIp, userAgent);
		}
	}
	
	/**
	 *  只记录配置好的指定URL - 限webapp项目
	 * @param url
	 * @param key
	 * @param customerType
	 * @param searchKey
	 * @param took
	 */
	public static void accessLogLimit(String url, String key, String customerType, String remark, long took, String appType, String clientIp, String userAgent) {
		if(isMonitorUrl(url)) {
			accessLogAll(url, key, customerType, remark, took, appType, clientIp, userAgent);
		}
	}
	
	public static void accessLogThread(String url, String key, String customerType, String remark, long took, String appType, String clientIp, String userAgent) {
		if(StringUtils.isNotBlank(key)) {
			LogAccessTO accessTO = new LogAccessTO();
			accessTO.setCustomerType(customerType);
			accessTO.setTimestamp(System.currentTimeMillis()/1000);
			accessTO.setKey(key);
			accessTO.setLogType("acs_thread");
			accessTO.setRemark(remark);
			accessTO.setUrl(url);
			accessTO.setTook(took);
			accessTO.setAppType(appType);
			accessTO.setClientIp(clientIp);
			accessTO.setUserAgent(userAgent);
			if(WebContextHolder.getRequest() != null && StringUtils.isNotBlank(WebContextHolder.getRequest().getRequestUUID())) {
				accessTO.setReqUuid(WebContextHolder.getRequest().getRequestUUID());
			}
			logger.info(JSON.toJSONString(accessTO));
		}
	}
	
	/**
	 * 记录所有URL, 只要key不为空
	 * @param url
	 * @param key
	 * @param customerType
	 * @param remark
	 * @param took
	 */
	public static void accessLogAll(String url, String key, String customerType, String remark, long took, String appType, String clientIp, String userAgent) {
		if(StringUtils.isNotBlank(key)) {
			LogAccessTO accessTO = new LogAccessTO();
			accessTO.setCustomerType(customerType);
			accessTO.setTimestamp(System.currentTimeMillis()/1000);
			accessTO.setKey(key);
			accessTO.setLogType("acs");
			accessTO.setRemark(remark);
			accessTO.setUrl(url);
			accessTO.setTook(took);
			accessTO.setAppType(appType);
			accessTO.setClientIp(clientIp);
			accessTO.setUserAgent(userAgent);
			RequestTO req = WebContextHolder.getRequest();
			if(req != null && StringUtils.isNotBlank(req.getRequestUUID())) {
				accessTO.setReqUuid(req.getRequestUUID());
				accessTO.setReqP(req.getReqParam());//added for songld KNZT-8347
			}
			logger.info(JSON.toJSONString(accessTO));
		}
	}
}
