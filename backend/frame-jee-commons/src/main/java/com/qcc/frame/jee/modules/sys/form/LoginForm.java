package com.qcc.frame.jee.modules.sys.form;

// updated for v2.0.4 chenbl KNZT-5371
public class LoginForm {
	private String clientId;
	private String loginType;
	private String loginName;
	private String pwd;
	private String keep;
	private String keepToken;
	private String accessToken;
	private String accessSecretKey;
	private String refreshToken;
	private String refreshAutoLogin;
	private String thirdToken;
	private String source;
	private String hash;
	private String verifyCode;
	private String mfaToken;

	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getHash() {
		return hash;
	}
	public void setHash(String hash) {
		this.hash = hash;
	}
	public String getThirdToken() {
		return thirdToken;
	}
	public void setThirdToken(String thirdToken) {
		this.thirdToken = thirdToken;
	}
	public String getRefreshAutoLogin() {
		return refreshAutoLogin;
	}
	public void setRefreshAutoLogin(String refreshAutoLogin) {
		this.refreshAutoLogin = refreshAutoLogin;
	}
	public String getRefreshToken() {
		return refreshToken;
	}
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	public String getAccessSecretKey() {
		return accessSecretKey;
	}
	public void setAccessSecretKey(String accessSecretKey) {
		this.accessSecretKey = accessSecretKey;
	}
	public String getKeepToken() {
		return keepToken;
	}
	public void setKeepToken(String keepToken) {
		this.keepToken = keepToken;
	}
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getLoginType() {
		return loginType;
	}
	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getPwd() {
		return pwd;
	}
	public void setPwd(String pwd) {
		this.pwd = pwd;
	}
	public String getKeep() {
		return keep;
	}
	public void setKeep(String keep) {
		this.keep = keep;
	}

	public String getVerifyCode() {
		return verifyCode;
	}

	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}

	public String getMfaToken() {
		return mfaToken;
	}

	public void setMfaToken(String mfaToken) {
		this.mfaToken = mfaToken;
	}
}
