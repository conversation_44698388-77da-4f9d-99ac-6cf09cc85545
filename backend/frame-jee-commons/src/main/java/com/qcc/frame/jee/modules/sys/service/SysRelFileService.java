package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import com.qcc.frame.jee.modules.sys.entity.SysRelFile;
import com.qcc.frame.jee.modules.sys.dao.SysRelFileDao;

import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;

import java.util.List;

@Service
public class SysRelFileService extends CrudService<SysRelFileDao, SysRelFile> {

    public void saveFileList(String relId, String relType, String companyId, List<SysRelFile> fileList) {
        dao.deleteByRelIdAndType(relId, relType);
        for (SysRelFile sysRelFile : fileList) {
            sysRelFile.setRelId(relId);
            sysRelFile.setRelType(relType);
            sysRelFile.setCompanyId(StringUtils.isBlank(companyId) ? Constants.DEFAULT_COMPANY_ID : companyId);
        }
        batchInsert(fileList);
    }

    public void saveFileList(String relId, String relType, List<SysRelFile> fileList) {
        saveFileList(relId, relType, null, fileList);
    }

    public List<SysRelFile> getByRelIdAndType(String relId, String relType) {
        return dao.getByRelIdAndType(relId, relType);
    }

}
