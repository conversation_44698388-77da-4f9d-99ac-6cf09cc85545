package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.modules.sys.dao.SysConfigDao;
import com.qcc.frame.jee.modules.sys.entity.SysConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SysConfigService extends CrudService<SysConfigDao, SysConfig> {
	
	public void saveConfig(SysConfig config) throws MessageException {
		if(config != null && StringUtils.startsWith(config.getId(), "allowupdate")) {
			this.save(config);
		} else {
			throw new MessageException("err.access");
		}
	}
	
	public SysConfig getConfigByTypeAndKey(String configType, String configKey) {
		return dao.getConfigByTypeAndKey(configType, configKey);
	}
	
	public List<SysConfig> listConfigByType(String configType) {
		return dao.listConfigByType(configType);
	}
	
	public List<String> listValueByTypeAndKey(String configType, String configKey) {
		return dao.listValueByTypeAndKey(configType, configKey);
	}

	public String getValueByTypeAndKey(String configType, String configKey) {
		List<String> list = dao.listValueByTypeAndKey(configType, configKey);
		if(list != null && !list.isEmpty()) {
			return list.get(0);
		}
		return "";
	}

	public List<SysConfig> listConfigByTypeAndKey(String configType, String configKey) {
		return dao.listConfigByType(configType);
	}

	public static List<String> listValueByKeyFromConfigList(List<SysConfig> configList, String configKey) {
		List<String> resultList = new ArrayList<>();
		if(configList != null) {
			for(SysConfig config : configList) {
				if(StringUtils.equalsIgnoreCase(config.getConfigKey(), configKey)) {
					resultList.add(config.getConfigValue());
				}
			}
		}
		return resultList;
	}

	public static String getValueByKeyFromConfigList(List<SysConfig> configList, String configKey) {
		return getValueByKeyFromConfigList(configList, configKey, "");
	}

	public static String getValueByKeyFromConfigList(List<SysConfig> configList, String configKey, String defaultValue) {
		if(configList != null) {
			for(SysConfig config : configList) {
				if(StringUtils.equalsIgnoreCase(config.getConfigKey(), configKey)) {
					return config.getConfigValue();
				}
			}
		}
		return defaultValue;
	}
}
