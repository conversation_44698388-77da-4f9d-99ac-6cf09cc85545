package com.qcc.frame.jee.modules.sys.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.excel.ExcelField;
import com.qcc.frame.jee.commons.persistence.DataEntity;

public class Company extends DataEntity<Company> {

	/**
	 *
	 */
	private static final long serialVersionUID = 5678319267329505753L;

	public Company() {
		super();
	}
	public Company(String id) {
		super(id);
	}
	@ExcelField(title = "公司名称", sort = 10, fieldType = String.class)
	private String name;
	@ExcelField(title = "公司简称", sort = 11, fieldType = String.class)
	private String shortName;
	private String keyNo;
	private String email;
	private String phone;
	private String contactName;
	@ExcelField(title = "购买用户数", sort = 40, fieldType = Integer.class)
	private Integer purchaseAccountNum;
	private Integer monitorEnterpriseNum;

	private Integer monitorPersonNum = 0;

	@ExcelField(title = "开通日期", sort = 60, fieldType = Integer.class)
	private Date beginDate;

	private Date contractBeginDate;
	@ExcelField(title = "到期日期", sort = 70, fieldType = Integer.class)
	private Date endDate;
	private String mainUserId;
	//private Short assignGroupAction;//

	private User mainUser;
	@ExcelField(title = "主账号姓名", sort = 20, fieldType = String.class)
	private String mainUserName;
	@ExcelField(title = "主账号", sort = 30, fieldType = String.class)
	private String mainUserLoginName;

	private Integer remainPurchaseAccountNum;
	private Integer remainMonitorEnterpriseNum;
	private Integer remainMonitorPersonNum = 0;
	private String countStd;

	private Date nextJobRunDate;
	private Date prevJobRunDate;
	private String jobPeriod;
	private String menuHomeGroupId;

	private Integer type;
	private String currentSeller;
	@ExcelField(title = "合同总金额", sort = 80, fieldType = BigDecimal.class)
	private BigDecimal totalContractAmt;
	private Integer customerId;
	private Integer purchaseFinanceNum;
	private String sendMsg;
	private String sendEmail;
	private String secretKey;
	private String saleId;
	//定义数据接口的变量
	private Boolean enableApi;
	private Integer enableExport;
	private String compType;
	//穿透企业数量
	@ExcelField(title = "穿透企业数量", sort = 50, fieldType = Integer.class)
	private Integer beneCompanyCount = 0;
	//监控企业数量
	@ExcelField(title = "监控企业数", sort = 50, fieldType = Integer.class)
	private Integer radarCompanyCount = 0;

	private String setId;
	private String category;
	private Integer consumedPurchaseAccountNum;

	private String proMainLoginName;
	private String proSecretKey;
	private String proKey;

	private String yunjuKey;
	private String yunjuSecretKey;
	private String openApiUserId;
	private Boolean enableOpenApi;//added for v1.3.7 KNZT-1420
	private String businessScenario; // added for v1.3.9 KNZT-2131 应用场景
	// private String chargeMode; // added  for v1.3.9 KNZT-2101 计费标准 updated for v1.8.2 KNZT-3441
	private String dataSave; // added  for v1.5.8 KNZT-2611 数据保存
	private String showCredit; // 子账号展示credit added for v1.8.2 KNZT-3680
	private String canUpgrade; // 数据保存 added for v1.8.2 KNZT-3700
	private String payType; // 支付方式 added for lvcy v2.0.6 KNZT-5499

	public String getYunjuKey() {
		return yunjuKey;
	}
	public void setYunjuKey(String yunjuKey) {
		this.yunjuKey = yunjuKey;
	}
	public String getYunjuSecretKey() {
		return yunjuSecretKey;
	}
	public void setYunjuSecretKey(String yunjuSecretKey) {
		this.yunjuSecretKey = yunjuSecretKey;
	}
	public Integer getConsumedPurchaseAccountNum() {
		return consumedPurchaseAccountNum;
	}
	public void setConsumedPurchaseAccountNum(Integer consumedPurchaseAccountNum) {
		this.consumedPurchaseAccountNum = consumedPurchaseAccountNum;
	}
	public String getSetId() {
		return setId;
	}

	public void setSetId(String setId) {
		this.setId = setId;
	}

	public String getSendEmail() {
		return sendEmail;
	}
	public void setSendEmail(String sendEmail) {
		this.sendEmail = sendEmail;
	}
	public String getCountStd() {
		return countStd;
	}
	public void setCountStd(String countStd) {
		this.countStd = countStd;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getKeyNo() {
		return keyNo;
	}
	public void setKeyNo(String keyNo) {
		this.keyNo = keyNo;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getContactName() {
		return contactName;
	}
	public void setContactName(String contactName) {
		this.contactName = contactName;
	}
	public Integer getPurchaseAccountNum() {
		return purchaseAccountNum;
	}
	public void setPurchaseAccountNum(Integer purchaseAccountNum) {
		this.purchaseAccountNum = purchaseAccountNum;
	}
	public Integer getMonitorEnterpriseNum() {
		return monitorEnterpriseNum;
	}
	public void setMonitorEnterpriseNum(Integer monitorEnterpriseNum) {
		this.monitorEnterpriseNum = monitorEnterpriseNum;
	}
	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}

	public Date getContractBeginDate() {
		return contractBeginDate;
	}

	public void setContractBeginDate(Date contractBeginDate) {
		this.contractBeginDate = contractBeginDate;
	}

	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getMainUserId() {
		return mainUserId;
	}
	public void setMainUserId(String mainUserId) {
		this.mainUserId = mainUserId;
	}
	public User getMainUser() {
		return mainUser;
	}
	public void setMainUser(User mainUser) {
		this.mainUser = mainUser;
	}

	public String getMainUserName() {
		return mainUserName;
	}

	public void setMainUserName(String mainUserName) {
		this.mainUserName = mainUserName;
	}

	public String getMainUserLoginName() {
		return mainUserLoginName;
	}

	public void setMainUserLoginName(String mainUserLoginName) {
		this.mainUserLoginName = mainUserLoginName;
	}

	public Integer getRemainPurchaseAccountNum() {
		return remainPurchaseAccountNum;
	}
	public void setRemainPurchaseAccountNum(Integer remainPurchaseAccountNum) {
		this.remainPurchaseAccountNum = remainPurchaseAccountNum;
	}
	public Integer getRemainMonitorEnterpriseNum() {
		return remainMonitorEnterpriseNum;
	}
	public void setRemainMonitorEnterpriseNum(Integer remainMonitorEnterpriseNum) {
		this.remainMonitorEnterpriseNum = remainMonitorEnterpriseNum;
	}
	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getNextJobRunDate() {
		return nextJobRunDate;
	}
	public void setNextJobRunDate(Date nextJobRunDate) {
		this.nextJobRunDate = nextJobRunDate;
	}
	public String getJobPeriod() {
		return jobPeriod;
	}
	public void setJobPeriod(String jobPeriod) {
		this.jobPeriod = jobPeriod;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getCurrentSeller() {
		return currentSeller;
	}

	public void setCurrentSeller(String currentSeller) {
		this.currentSeller = currentSeller;
	}

	public BigDecimal getTotalContractAmt() {
		return totalContractAmt;
	}

	public void setTotalContractAmt(BigDecimal totalContractAmt) {
		this.totalContractAmt = totalContractAmt;
	}
	public Integer getCustomerId() {
		return customerId;
	}
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	public Integer getMonitorPersonNum() {
		return monitorPersonNum;
	}
	public void setMonitorPersonNum(Integer monitorPersonNum) {
		this.monitorPersonNum = monitorPersonNum;
	}
	public Integer getRemainMonitorPersonNum() {
		return remainMonitorPersonNum;
	}
	public void setRemainMonitorPersonNum(Integer remainMonitorPersonNum) {
		this.remainMonitorPersonNum = remainMonitorPersonNum;
	}

	public Date getPrevJobRunDate() {
		return prevJobRunDate;
	}
	public void setPrevJobRunDate(Date prevJobRunDate) {
		this.prevJobRunDate = prevJobRunDate;
	}
	public Integer getPurchaseFinanceNum() {
		return purchaseFinanceNum;
	}
	public void setPurchaseFinanceNum(Integer purchaseFinanceNum) {
		this.purchaseFinanceNum = purchaseFinanceNum;
	}
	public String getSendMsg() {
		return sendMsg;
	}
	public void setSendMsg(String sendMsg) {
		this.sendMsg = sendMsg;
	}
	public String getSecretKey() {
		return secretKey;
	}
	public void setSecretKey(String secretKey) {
		this.secretKey = secretKey;
	}

	public String getSaleId() {
		return saleId;
	}

	public void setSaleId(String saleId) {
		this.saleId = saleId;
	}


	//数据变量的get,set方法
	public Boolean getEnableApi() {
		return enableApi;
	}
	public void setEnableApi(Boolean enableApi) {
		this.enableApi = enableApi;
	}

	public Integer getBeneCompanyCount() {
		return beneCompanyCount;
	}

	public void setBeneCompanyCount(Integer beneCompanyCount) {
		this.beneCompanyCount = beneCompanyCount;
	}

	public Integer getRadarCompanyCount() {
		return radarCompanyCount;
	}

	public void setRadarCompanyCount(Integer radarCompanyCount) {
		this.radarCompanyCount = radarCompanyCount;
	}
	public String getCompType() {
		return compType;
	}
	public void setCompType(String compType) {
		this.compType = compType;
	}

	public String getMenuHomeGroupId() {
		return menuHomeGroupId;
	}
	public void setMenuHomeGroupId(String menuHomeGroupId) {
		this.menuHomeGroupId = menuHomeGroupId;
	}
	public String getCategory() {return category;}
	public void setCategory(String category) {this.category = category;}
	public Integer getEnableExport() {
		return enableExport;
	}
	public void setEnableExport(Integer enableExport) {
		this.enableExport = enableExport;
	}

	public String getProMainLoginName() { return proMainLoginName; }
	public void setProMainLoginName(String proMainLoginName) { this.proMainLoginName = proMainLoginName; }

	public String getProSecretKey() { return proSecretKey; }
	public void setProSecretKey(String proSecretKey) { this.proSecretKey = proSecretKey; }

	public String getProKey() { return proKey; }
	public void setProKey(String proKey) { this.proKey = proKey; }

	public String getOpenApiUserId() {
		return openApiUserId;
	}

	public void setOpenApiUserId(String openApiUserId) {
		this.openApiUserId = openApiUserId;
	}

	public Boolean getEnableOpenApi() {
		return enableOpenApi;
	}

	public void setEnableOpenApi(Boolean enableOpenApi) {
		this.enableOpenApi = enableOpenApi;
	}

	public String getBusinessScenario() {
		return businessScenario;
	}

	public void setBusinessScenario(String businessScenario) {
		this.businessScenario = businessScenario;
	}

	// updated for v1.8.2 KNZT-3441
//	public String getChargeMode() {
//		return chargeMode;
//	}
//
//	public void setChargeMode(String chargeMode) {
//		this.chargeMode = chargeMode;
//	}

	public String getDataSave() {
		return dataSave;
	}

	public void setDataSave(String dataSave) {
		this.dataSave = dataSave;
	}

	public String getShowCredit() {
		return showCredit;
	}

	public void setShowCredit(String showCredit) {
		this.showCredit = showCredit;
	}

	public String getCanUpgrade() {
		return canUpgrade;
	}

	public void setCanUpgrade(String canUpgrade) {
		this.canUpgrade = canUpgrade;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}
}
