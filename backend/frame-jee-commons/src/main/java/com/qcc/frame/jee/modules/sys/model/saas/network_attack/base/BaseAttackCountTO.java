package com.qcc.frame.jee.modules.sys.model.saas.network_attack.base;

public abstract class BaseAttackCountTO implements java.io.Serializable {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = -860515702079235914L;
	private int attackCount;
	private String functionCd;
	private String redisKey;//用户标识(用户ID或者对于未登录的用户为IP)
	private String lastClientIp;
	private String userId;
	private String loginName;
	/**
	 * 攻击等级, 攻击次数达到一定程度, 需要提醒还是直接锁住账号
	 * W: 邮件提醒内部人员有攻击行为,  L: 邮件提醒, 用户账号被锁住
	 */
	private String attackLevel;
	private long createTimestamp;
	private long updateTimestamp;

	public long getUpdateTimestamp() {
		return updateTimestamp;
	}
	public void setUpdateTimestamp(long updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}
	public long getCreateTimestamp() {
		return createTimestamp;
	}
	public void setCreateTimestamp(long createTimestamp) {
		this.createTimestamp = createTimestamp;
	}
	public final int getAttackCount() {
		return attackCount;
	}
	public final void setAttackCount(int attackCount) {
		this.attackCount = attackCount;
	}
	public final String getFunctionCd() {
		return functionCd;
	}
	public final void setFunctionCd(String functionCd) {
		this.functionCd = functionCd;
	}
	public final String getRedisKey() {
		return redisKey;
	}
	public final void setRedisKey(String redisKey) {
		this.redisKey = redisKey;
	}
	public final String getLastClientIp() {
		return lastClientIp;
	}
	public final void setLastClientIp(String lastClientIp) {
		this.lastClientIp = lastClientIp;
	}
	public final String getUserId() {
		return userId;
	}
	public final void setUserId(String userId) {
		this.userId = userId;
	}
	public final String getLoginName() {
		return loginName;
	}
	public final void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public final String getAttackLevel() {
		return attackLevel;
	}
	public final void setAttackLevel(String attackLevel) {
		this.attackLevel = attackLevel;
	}

}
