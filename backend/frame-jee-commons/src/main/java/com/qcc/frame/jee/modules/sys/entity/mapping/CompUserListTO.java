package com.qcc.frame.jee.modules.sys.entity.mapping;

import java.io.Serializable;
import java.util.Date;

/**
 * added for v1.5.6 KNZT-2561
 */
public class CompUserListTO implements Serializable {
    private String id;
    private String name;
    private String lastName;
    private String loginName;
    private String email;
    private String phone;
    private String country;
    private Date createDate;
    private boolean mainUserFlag; // 是否主账号 added for v2.0.8 chenbl KNZT-5815

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public boolean getMainUserFlag() {
        return mainUserFlag;
    }

    public void setMainUserFlag(boolean mainUserFlag) {
        this.mainUserFlag = mainUserFlag;
    }
}
