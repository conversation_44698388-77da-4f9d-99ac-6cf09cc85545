package com.qcc.frame.jee.modules.sys.thread;

import com.qcc.frame.jee.commons.thread.BaseRunnable;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Log;
import com.qcc.frame.jee.modules.sys.service.LogService;

public class AccessLogRunnable extends BaseRunnable {
	
	private static LogService logService = SpringContextHolder.getBean(LogService.class);

	private Log log;
	private String companyId;
	private String userId;

	@Override
	public void process() {
		if(log != null && StringUtils.isNotBlank(log.getTitle())) {
			logService.insertLog(log);
		}
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Log getLog() {
		return log;
	}

	public void setLog(Log log) {
		this.log = log;
	}


	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	
	
}
