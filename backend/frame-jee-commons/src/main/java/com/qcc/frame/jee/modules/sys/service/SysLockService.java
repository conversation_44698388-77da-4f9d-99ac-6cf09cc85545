package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.dao.SysLockDao;
import com.qcc.frame.jee.modules.sys.entity.SysLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class SysLockService extends CrudService<SysLockDao, SysLock> {
	@Autowired
	private SysLockNewTranService sysLockNewTranService;
	
	/**
	 * 判断用户功能锁
	 * @param companyId
	 * @param userId
	 * @param functionCd
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public boolean isLogicLockCompFuncByUserId(String companyId, String userId, String functionCd, int timeout) throws MessageException {
		if(StringUtils.isNotBlank(userId)) {
			companyId = companyId + "#" + userId;
		}
		SysLock lock = dao.getLock(SysConstants.Lock.LOCK_TYPE_COMP_FUNC, SysConstants.Lock.LOCK_CD_LOGIC, companyId, functionCd);
		if(lock != null && SysConstants.Lock.LOGIC_STATUS_LOCK.equals(lock.getLogicStatus())) {
			int sec = DateUtils.subtractDate2Second(new Date(), lock.getLockDatetime());
			if(timeout > sec) {
				logger.warn("[syslock]check " + companyId + " " + userId + " " + functionCd + " double submit");
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 判断公司功能锁
	 * @param companyId
	 * @param functionCd
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public boolean isLogicLockCompFunc(String companyId, String functionCd, int timeout) throws MessageException {
		return this.isLogicLockCompFuncByUserId(companyId, null, functionCd, timeout);
	}

	/**
	 * 用户功能锁, 请跟logicUnLockCompFunc配套使用
	 * String lockId = null;
	 * try {
	 *    lockId = logicLockCompFunc(companyId, userId, functionCd, timeout);
	 *    ...
	 * } catch(MessageException e) {
	 * 	  ...
	 * } finally {
	 *    logicUnLockCompFunc(lockId);
	 * }
	 * 
	 * @param companyId
	 * @param functionCd
	 * @param timeout -- 超时时间(秒), 请估算这个功能最多需要多长时间, 以防服务器重启时导致任务一直被锁住
	 * @throws MessageException
	 */
	public String logicLockCompFuncByUserId(String companyId, String userId, String functionCd, int timeout) throws MessageException {
		if(StringUtils.isNotBlank(userId)) {
			companyId = companyId + "#" + userId;
		}
		SysLock lock = dao.getLock(SysConstants.Lock.LOCK_TYPE_COMP_FUNC, SysConstants.Lock.LOCK_CD_LOGIC, companyId, functionCd);
		if(lock == null) {
			lock = new SysLock();
			lock.setCompanyId(companyId);
			lock.setFunctionCd(functionCd);
			lock.setLockCd(SysConstants.Lock.LOCK_CD_LOGIC);
			lock.setLockType(SysConstants.Lock.LOCK_TYPE_COMP_FUNC);
			lock.setLockDatetime(new Date());
			lock.setTimeout(timeout);
			lock.setLogicStatus(SysConstants.Lock.LOGIC_STATUS_LOCK);
			sysLockNewTranService.newTranSave(lock);
			return lock.getId();
		}
		
		if(SysConstants.Lock.LOGIC_STATUS_LOCK.equals(lock.getLogicStatus())) {
			int sec = DateUtils.subtractDate2Second(new Date(), lock.getLockDatetime());
			if(timeout > sec) {
				logger.warn("[syslock]" + companyId + " " + userId + " " + functionCd + " double submit");
				throw new MessageException("msg:Ongoing task. Avoid duplication.");
			}
		}
		
		lock.setLockDatetime(new Date());
		lock.setTimeout(timeout);
		lock.setLogicStatus(SysConstants.Lock.LOGIC_STATUS_LOCK);
		sysLockNewTranService.newTranSave(lock);
		return lock.getId();
	}
	
	/**
	 * 公司功能锁, 请跟logicUnLockCompFunc配套使用
	 * String lockId = null;
	 * try {
	 *    lockId = logicLockCompFunc(companyId, functionCd, timeout);
	 *    ...
	 * } catch(MessageException e) {
	 * 	  ...
	 * } finally {
	 *    logicUnLockCompFunc(lockId);
	 * }
	 * 
	 * @param companyId
	 * @param functionCd
	 * @param timeout -- 超时时间(秒), 请估算这个功能最多需要多长时间, 以防服务器重启时导致任务一直被锁住
	 * @throws MessageException
	 */
	public String logicLockCompFunc(String companyId, String functionCd, int timeout) throws MessageException {
		return logicLockCompFuncByUserId(companyId, null, functionCd, timeout);
	}
	
	/**
	 * 公司功能解锁, 请跟logicLockCompFunc配套使用
	 * @param companyId
	 * @param functionCd
	 * @throws MessageException
	 */
	public void logicUnLockCompFunc(String lockId) {
		if(StringUtils.isNotBlank(lockId)) {
			try {
				SysLock lock = this.get(lockId);
				if(lock != null) {
					lock.setLogicStatus(SysConstants.Lock.LOGIC_STATUS_UNLOCK);
					sysLockNewTranService.newTranSave(lock);
				}
			} catch(Exception e) {
				logger.error("", e);
			}
		}
	}
}
