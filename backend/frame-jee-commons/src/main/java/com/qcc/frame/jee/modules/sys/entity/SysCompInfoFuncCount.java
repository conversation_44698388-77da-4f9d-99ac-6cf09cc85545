package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

import java.math.BigDecimal;

public class SysCompInfoFuncCount extends DataEntity<SysCompInfoFuncCount> {

	private String functionTableId;
	private String countStd;
	private BigDecimal totalCount;
	private BigDecimal consumedCount;
	private Short mainFuncFlag;

	public BigDecimal calRemainCount() {
		if (null == totalCount || null == consumedCount) {
			return BigDecimal.ZERO;
		}
		return totalCount.subtract(consumedCount);
	}

	public String getFunctionTableId() {
		return functionTableId;
	}
	public void setFunctionTableId(String functionTableId) {
		this.functionTableId = functionTableId;
	}
	public String getCountStd() {
		return countStd;
	}
	public void setCountStd(String countStd) {
		this.countStd = countStd;
	}
	public BigDecimal getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(BigDecimal totalCount) {
		this.totalCount = totalCount;
	}
	public BigDecimal getConsumedCount() {
		return consumedCount;
	}
	public void setConsumedCount(BigDecimal consumedCount) {
		this.consumedCount = consumedCount;
	}
	public Short getMainFuncFlag() {
		return mainFuncFlag;
	}
	public void setMainFuncFlag(Short mainFuncFlag) {
		this.mainFuncFlag = mainFuncFlag;
	}
}
