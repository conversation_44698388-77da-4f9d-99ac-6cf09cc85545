package com.qcc.frame.commons.ienum;

/**
 * added for v1.7.6 KNZT-3306
 * 钉钉告警模版
 *
 * <AUTHOR>
 * @datetime 2024/5/9 9:45
 */
public enum AlarmTypeEnum {
    CORP_REPORT_FAIL("报告生成失败，请关注数据是否正常", "orderNo:%s, reportType:%s"),
    CORP_REPORT_COMPENSATE("报告生成失败，已进行补偿，请持续关注数据是否正常", "orderNo:%s, reportType:%s"),
    SPIDER_REPORT_FAIL("报告生成失败，已退款，请确认爬虫侧是否正常", "orderNo:%s, reportType:%s"),
    REPORT_JOB_FAIL("SyncAndCheckReportJob 执行失败", "orderNo:%s, reportType:%s"),
    WEB_CONVERTER_FAIL("web-converter 服务健康检查未通过", null),
    OUTBOUND_TASK_FAIL("额度异步任务 执行失败", "taskId:%s"),
    MONITOR_CORP_FAIL("企业监控逻辑 处理失败", "corpKeyNo:%s"),
    EXECUTE_AFTER_SUBMIT_ORDER_FAIL("订单异步处理逻辑失败", "orderId:%s,reportType:%s"),
    EXECUTE_AFTER_BATCH_IMPORT_USER_FAIL("导入用户异步处理逻辑失败", "companyId : %s"), // added for v2.1.8 chenbl KNZT-6662

    OUTBOUND_DATA_TOO_MUCH("出境数据过多", "keyNo: %s, corpName/persName: %s, reportType: %s, dataType: %s, count: %s"), // added for v1.8.5 KNZT-3802
    CONTRACT_DELIVERY_JOB_ERROR("ContractDeliveryLifecycleJob 执行失败", "contractDeliveryId：%s"), // added for v1.8.5 KNZT-3802
    BENEFIT_LIFECYCLE_JOB_ERROR("BenefitLifecycleJob 执行失败", "benefitDeliveryId：%s"),
    BENEFIT_RECONCILIATION_ERROR("权益对账失败", "benefitDeliveryId：%s"),

    INCR_CORP_DATA_SEND_FAIL("新增企业信息数据发送失败", "fileName:%s"),
    INCR_CORP_DATA_COUNT_CHECK_ERROR("新增企业数量低于阈值", "日期：%s，新增企业数据量：%s家，低于系统阈值%s，请检查数据"),// added v2.0.3 KNZT-5446
    CONTRACT_NEAR_EXPIRY("合同即将过期", "合同编号：%s，合同过期日期：%s，客户名称：%s"), // added for v1.8.5 KNZT-3802
    CONTRACT_NEAR_CONSUMED("合同额度即将耗尽", "合同编号：%s，额度单元：%s，客户名称：%s"), // added for v1.8.5 KNZT-3802
    BILL_DAILY_ERROR("日账计算 执行失败", "prodAccId:%s, date:%s"), // added for v1.8.5 KNZT-3802
    BILL_MONTHLY_ERROR("月账计算 执行失败", "prodAccId:%s, date:%s"), // added for v1.8.5 KNZT-3802
    FINANCE_CONFIRM_ERROR("财务确认收入计算 执行失败", ""), // added for v1.8.5 KNZT-3802
    FINANCE_CONFIRM_DATA_ERROR("财务确认收入缺少必要数据，执行失败，请检查数据", "contractDeliveryId:%s, prodAccId:%s, yearMonth:%s"), // added for v1.8.5 KNZT-3802
    RECONCILIATION_FAILURE("财务对账校验失败", "checkTaskName: %s, errorItemsSize: %s"), // added for v1.9.2 KNZT-3894
    PUSH_HK_CORP_JOB_EXPIRE("推送香港新增企业任务过期", "taskId:%s"), // added for v1.9.3 KNZT-4366
    STRIPE_WEBHOOK_ERROR("Stripe webhook error", ""), // added for v1.9.2 KNZT-3894
    STRIPE_WEBHOOK_HANDLE_ERROR("Stripe webhook error", "eventType:%s,paymentIntentId:%s"), // added for v1.9.2 KNZT-3894
    STRIPE_REFUND("Stripe 退款，检查数据", "paymentIntentId : %s"), // added for v1.9.2 KNZT-3894
    ORDER_CANCEL_ERROR("订单取消失败", "payRelId : %s"), // added for v1.9.2 KNZT-3894
    RECAPTCHA_ERROR("谷歌人机验证失败", "errorCode : %s"), // added for v1.9.3 KNZT-4193
    ORDER_OVER_100("自助账户下单超过100笔。请及时联系客户。", "companyName : %s"), // added for v1.9.3 KNZT-4193
    GENERATE_STATEMENT_ERROR("月度报表生成失败", "companyId : %s"), // added for v1.9.3 KNZT-4193
    COMM_DELAYED_TASK_ERROR("通用延时任务处理失败", "taskType: %s, taskId: %s"), // added for v1.9.6 KNZT-4646
    SPIDER_ORDER_TIMEOUT_ERROR("爬虫订单处理超时", "订单号: %s, 数据订单号: %s"), // added for v1.9.6 KNZT-4646 已有重复字段显示 updated for v2.0.0 chenbl KNZT-5055
    SPIDER_ORDER_DATA_SUCCESS_AFTER_TIMEOUT("爬虫订单数据处理成功(超时订单)", "orderNo: %s, apiOrderNo: %s"), // added for v2.1.7 chenbl KNZT-6506
    SEARCH_COUNT_OUT_LIMIT("模糊搜索使用数量超出限制", "companyId : %s, companyName : %s, 当前数量: %s, 告警数量: %s"), // added for v1.9.6 KNZT-4646
    FIN_TAX_DATA_ERROR("财税报告订单授权完成后, 超1h未成功, 请跟进原因", "orderNo : %s，loginName : %s"), // added for v1.9.2 KNZT-3894
    FIN_TAX_REFUND_ERROR("财税报告订单授权完成后, 超24h未成功, 订单已完成退款, 请见检查原因", "orderNo : %s，loginName : %s"), // added for v1.9.2 KNZT-3894
    FIN_TAX_DATA_CALLBACK_ERROR("财税报告订单数据回调失败", "orderNo : %s"), // added for v1.9.2 KNZT-3894
    FIN_TAX_ITEM_EN_NO_MAPPING("财税报告项目英文未映射", "keyNo : %s"), // added for v2.0.2 chenbl KNZT-5271
    PUSH_KZZ_CRATE_CONTRACT_ERROR("客找找收入确认合同推送失败", "contractNo: %s, yearMonth: %s, pushDate: %s"), // added for v2.0.8 fengsw KNZT-5806
    SHOP_PLATFORM_EN_NOT_MAPPING("店铺平台英文未映射", "shopId: %s, platformId: %s"), // added for lvcy v2.1.1 KNZT-5973
    LEGAL_DOCUMENT_WEBSITE_STATUS_CHANGE("工商报告网站状态发生变化", "websiteStatus: %s"), // added for v2.1.1 lvcy KNZT-5988

    PUSH_KZZ_CRATE_CONTRACT_NO_HOLIDAY_LIST_ERROR("客找找收入确认合同推送失败", "No chinese mainland holiday (%s) found in the system."), // added for v2.1.1 fengsw KNZT-6123
    ORDER_SUBMIT_ERROR("订单提交失败", null),
    CREDITS_INSUFFICIENT_ERROR("用户额度不足", null),
    CONTRACT_LIFECYCLE_ERROR("合同生命周期处理失败", "contractDeliveryId:%s"),
    PUSH_AFTER_KZZ_CRATE_CONTRACT_ERROR("后付费账单收入确认合同推送失败", "invoiceNo: %s, pushDate: %s"),
    STRIPE_CHARGE_REMINDER("Stripe充值订单金额提醒", "客户: %s, 充值金额: %s"), // added for v2.1.8 KNZT-6875

    MERCHANT_ITEM_EN_NO_MAPPING("电商报告项目英文未映射", "keyNo : %s"),
    QUOTA_MONTHLY_SNAPSHOT_ERROR("月度额度快照处理失败", "companyId : %s"),
    SPIDER_ORDER_DATA_FAILED("爬虫订单数据处理失败，订单进行退款", "orderNo: %s, apiOrderNo: %s, reportType: %s"),
    API_DATA_PROCESSING_ERROR("API订单数据处理失败，订单进行退款", "orderNo: %s"),
    API_CALLBACK_ERROR("API订单结果回调通知客户失败", "orderNo : %s"), // added for v2.3.1 KNZT-7904
    API_ORDER_REFOUND_ERROR("API订单退款失败", "orderNo : %s"),
    ;
    private final String title;
    private final String argTemplate;

    AlarmTypeEnum(String title, String argTemplate) {
        this.title = title;
        this.argTemplate = argTemplate;
    }

    public String getTitle() {
        return title;
    }

    public String getArgTemplate() {
        return argTemplate;
    }
}
