package com.qcc.frame.jee.modules.sys.utils;

import com.qcc.frame.jee.modules.sys.enums.TranslationMapEnum;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

public class TranslationMapUtils {
    private static Logger log = LoggerFactory.getLogger(TranslationMapUtils.class);
    private static Map<String, Map<String, String>> allMap = new HashMap<>();

    private static final String ROOT_PATH = "/translation/";

    private static Object lock = new Object();

    private static void init(TranslationMapEnum fileEnum) {
        synchronized(lock) {
            String fileName = fileEnum.getFileName();
            if(allMap.get(fileName) == null) {
                InputStream is = null;
                try {
                    ClassPathResource resource = new ClassPathResource(ROOT_PATH + fileName);
                    is = resource.getInputStream();
//                    resource = TranslationMapUtils.class.getClassLoader().getResourceAsStream(ROOT_PATH + fileName);
                    if(is != null) {
                        List<String> list = IOUtils.readLines(is, "UTF-8");
                        Map<String, String> list2Map = null;
                        if("LinkedHashMap".equalsIgnoreCase(fileEnum.getMapClass())) {
                            list2Map = new LinkedHashMap<>();
                        } else {
                            list2Map = new HashMap<>();
                        }
                        if (list != null) {
                            String key = null;
                            String value = null;
                            for (String str : list) {
                                if (StringUtils.isNotBlank(str)) {
                                    key = StringUtils.substringBefore(str, "=");
                                    value = StringUtils.substringAfter(str, "=");
                                    if (list2Map.containsKey(key)) {
                                        log.error(fileName + " translation doublekey 有重复key:" + key);
                                    } else {
                                        list2Map.put(key, value);
                                    }
                                }
                            }
                        }
                        allMap.put(fileName, Collections.unmodifiableMap(list2Map));
                    } else {
                        log.error(ROOT_PATH + fileName + " not exist");
                    }
                } catch (Exception e) {
                    log.error(fileName, e);
                } finally {
                    IOUtils.closeQuietly(is);
                }
            }
        }
    }

    /**
     * 通过文件名获取Map, /translation/下的文件
     * @param fileEnum
     * @return
     */
    public static Map<String, String> getMap(TranslationMapEnum fileEnum) {
        if(allMap.get(fileEnum.getFileName()) == null) {
            init(fileEnum);
        }
        return allMap.get(fileEnum.getFileName());
    }

    /**
     *
     * @param fileEnum
     * @param key
     * @return
     */
    public static String getValueByKey(TranslationMapEnum fileEnum, String key) {
        Map<String, String> map = getMap(fileEnum);
        if(map != null) {
            return map.get(key);
        }
        return null;
    }
}
