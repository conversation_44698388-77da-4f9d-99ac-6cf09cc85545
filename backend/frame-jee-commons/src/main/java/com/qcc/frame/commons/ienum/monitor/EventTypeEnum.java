package com.qcc.frame.commons.ienum.monitor;

import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum EventTypeEnum {
    NEWS("NEWS", "News", Lists.newArrayList(BenefitTypeEnum.NEWS, BenefitTypeEnum.BASE_AND_NEWS)),
    COMPANY_INFO("COMPANY_INFO", "Company info", Lists.newArrayList(BenefitTypeEnum.BASE, BenefitTypeEnum.BASE_AND_NEWS)),
    ;


    private final String code;
    private final String desc;
    private final List<BenefitTypeEnum> benefitTypeEnums;

    EventTypeEnum(String code, String desc, List<BenefitTypeEnum> benefitTypeEnums) {
        this.code = code;
        this.desc = desc;
        this.benefitTypeEnums = benefitTypeEnums;
    }

    public static EventTypeEnum getByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (EventTypeEnum item : values()) {
                if (StringUtils.equals(code, item.getCode())) {
                    return item;
                }
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        EventTypeEnum item = getByCode(code);
        return item == null ? null : item.getDesc();
    }

    public static List<EventTypeEnum> getByBenefitTypeEnum(BenefitTypeEnum benefitTypeEnum) {
        List<EventTypeEnum> resultList = new ArrayList<>();
        for (EventTypeEnum enumItem : values()) {
            if (enumItem.getBenefitTypeEnums().contains(benefitTypeEnum)) {
                resultList.add(enumItem);
            }
        }
        return resultList;
    }
}
