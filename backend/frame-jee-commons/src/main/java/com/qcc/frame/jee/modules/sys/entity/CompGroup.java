package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class CompGroup extends DataEntity<CompGroup> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3287098722021551693L;
	private String groupId;
	private String parentGroupId;
	private String parentGroupIds;
	private Integer sequence;
	private String officeName;
	private String addressCode;
	private String detailAddress;
	private String groupAddress;
	private String groupLng;
	private String groupLat;
	private int quota;

	public int getQuota() {
		return quota;
	}
	public void setQuota(int quota) {
		this.quota = quota;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	public String getParentGroupId() {
		return parentGroupId;
	}
	public void setParentGroupId(String parentGroupId) {
		this.parentGroupId = parentGroupId;
	}
	public String getParentGroupIds() {
		return parentGroupIds;
	}
	public void setParentGroupIds(String parentGroupIds) {
		this.parentGroupIds = parentGroupIds;
	}
	public Integer getSequence() {
		return sequence;
	}
	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}
	public String getOfficeName() {
		return officeName;
	}
	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getAddressCode() {
		return addressCode;
	}

	public void setAddressCode(String addressCode) {
		this.addressCode = addressCode;
	}

	public String getDetailAddress() {
		return detailAddress;
	}

	public void setDetailAddress(String detailAddress) {
		this.detailAddress = detailAddress;
	}

	public String getGroupAddress() {
		return groupAddress;
	}

	public void setGroupAddress(String groupAddress) {
		this.groupAddress = groupAddress;
	}

	public String getGroupLng() {
		return groupLng;
	}

	public void setGroupLng(String groupLng) {
		this.groupLng = groupLng;
	}

	public String getGroupLat() {
		return groupLat;
	}

	public void setGroupLat(String groupLat) {
		this.groupLat = groupLat;
	}
}
