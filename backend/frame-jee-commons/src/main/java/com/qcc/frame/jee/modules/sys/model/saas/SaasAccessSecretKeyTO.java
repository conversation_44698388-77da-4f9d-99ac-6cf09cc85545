package com.qcc.frame.jee.modules.sys.model.saas;

public class SaasAccessSecretKeyTO {
	private String loginName;
	private String loginType;
	private String source = "";
	private String extraLoginCd;
	private long loginTime;
	private String accessToken;

	public String getExtraLoginCd() {
		return extraLoginCd;
	}
	public void setExtraLoginCd(String extraLoginCd) {
		this.extraLoginCd = extraLoginCd;
	}
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getLoginType() {
		return loginType;
	}
	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}
	public long getLoginTime() {
		return loginTime;
	}
	public void setLoginTime(long loginTime) {
		this.loginTime = loginTime;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	
}
