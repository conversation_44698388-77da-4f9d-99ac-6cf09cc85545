package com.qcc.frame.jee.modules.sys.entity;

import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysDingMsg extends DataEntity<SysDingMsg> {
    private String referenceNo;
    private String msgTitle;
    private String msgText;
    private String msgLevel;
    private String accessToken;
    private String msgAtMobiles;
    private String msgType;
    private String btnsTitle;
    private String actionUrl;
    private Integer status;
    private Integer retryCount;
    private String createDt;
    private String sendTime;
    private String response;
    private String mobileSplitType;

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgText() {
        return msgText;
    }

    public void setMsgText(String msgText) {
        this.msgText = msgText;
    }

    public String getMsgLevel() {
        return msgLevel;
    }

    public void setMsgLevel(String msgLevel) {
        this.msgLevel = msgLevel;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getMsgAtMobiles() {
        return msgAtMobiles;
    }

    public void setMsgAtMobiles(String msgAtMobiles) {
        this.msgAtMobiles = msgAtMobiles;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getBtnsTitle() {
        return btnsTitle;
    }

    public void setBtnsTitle(String btnsTitle) {
        this.btnsTitle = btnsTitle;
    }

    public String getActionUrl() {
        return actionUrl;
    }

    public void setActionUrl(String actionUrl) {
        this.actionUrl = actionUrl;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getCreateDt() {
        return createDt;
    }

    public void setCreateDt(String createDt) {
        this.createDt = createDt;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getMobileSplitType() {
        return mobileSplitType;
    }

    public void setMobileSplitType(String mobileSplitType) {
        this.mobileSplitType = mobileSplitType;
    }
}
