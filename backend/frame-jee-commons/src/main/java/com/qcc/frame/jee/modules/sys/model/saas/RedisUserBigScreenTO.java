package com.qcc.frame.jee.modules.sys.model.saas;

public class RedisUserBigScreenTO implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4713770729845478478L;
	private String companyId;
	private String userId;
	private String source;
	private String lastAccessTime;
	private boolean hasCloudChartRole;
	private long checkCloudChartRoleTime;
	private String specUserInd;

	public String getSpecUserInd() {
		return specUserInd;
	}
	public void setSpecUserInd(String specUserInd) {
		this.specUserInd = specUserInd;
	}
	public boolean isHasCloudChartRole() {
		return hasCloudChartRole;
	}
	public void setHasCloudChartRole(boolean hasCloudChartRole) {
		this.hasCloudChartRole = hasCloudChartRole;
	}
	public long getCheckCloudChartRoleTime() {
		return checkCloudChartRoleTime;
	}
	public void setCheckCloudChartRoleTime(long checkCloudChartRoleTime) {
		this.checkCloudChartRoleTime = checkCloudChartRoleTime;
	}
	public String getLastAccessTime() {
		return lastAccessTime;
	}
	public void setLastAccessTime(String lastAccessTime) {
		this.lastAccessTime = lastAccessTime;
	}
	public String getCompanyId() {
		return companyId;
	}
	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	
}
