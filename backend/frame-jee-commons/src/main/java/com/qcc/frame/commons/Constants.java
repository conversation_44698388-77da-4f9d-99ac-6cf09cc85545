package com.qcc.frame.commons;

import com.qcc.frame.commons.ienum.GlobalAreaEnum;

import java.math.BigDecimal;

public interface Constants {

	public String YES = "Y";
	public String NO = "N";
	public String INVEST = "->";// added for v1.7.8 KNZT-3359 
	public String CORP_NAME_REGEX = "@%@";// added for v1.7.8 KNZT-3359 企业名称分隔符
	String DEFAULT_COMPANY_ID = "comm";
	BigDecimal MAX_UNIT = BigDecimal.valueOf(*********);

	public interface Result{
		int SUCCESS = 200;
		int FALSE = 500;
		String SUCCESS_STR = "200";
		String WARNING_STR = "203";
		String TOO_MANY_RESULTS = "215";
		String CONFIRM_STR = "228";
		String FALSE_STR = "500";
		String BLOCK_EVIL = "600";
		String EXCEED_MAX = "112";
		String EXCEED_DAILY_MAX = "110";
		String EXCEED_GROUP_MAX = "114";
		String RECAPTCHA_ERROR = "506";
		String TOKEN_EXPIRED = "507";

		/**
		 * 前台黄色提示
		 */
		String FRONT_YELLOW_WARNING = "115";

		String IGNORE_RESP = "99999";

		String FORBIDDEN_403 = "403";
		String MULI_LOGIN_1000 = "1000";
		String UNLOGIN_1001 = "1001";
		String SESSION_TIMEOUT_1002 = "1002";
		String PWD_NOT_RULE = "1003"; // 密码不符合规范 added for v2.0.4 chenbl KNZT-5442
		String PASSWORD_EXCEPTION = "1004"; // 密码异常，需要重置密码 added for v2.0.7 chenbl KNZT-5773
		String REPORT_SHARE_NO_PERMISSION = "1005"; // 共享报告无权限 added for v2.1.2 chenbl KNZT-6158

		String SERVICE_EXPIRED = "2000";
		String ORDER_HK_SEARCHER_INFO_MISSING = "2001"; // 香港查册人信息缺失 added for v2.0.0 chenbl KNZT-5094
		String FIN_TAX_DONE = "2002";
		String MONITOR_SERVICE_NOT_ENABLED = "2003";
		String RECAPTCHA_INVALID = "2004"; // 人机验证不通过
		String API_LIMIT = "2005";
		String DUPLICATE_RULE_NAME = "2006";
		String HK_IRD_MAINTAIN = "2007";
        String MAP_TRIAL_BENEFIT_AUTH_ERROR = "2008";

		String MSG_PREFIX_SUCCESS = "success";


    }

	public interface ErrorCode{
		String CODE_KEY_INVALID = "101";
		String CODE_QUOTAS_REACHED = "112";
		String CODE_QUERY_NO_RESULT = "201";
		String CODE_PARA_ERROR = "202";
		String CODE_EXCEPTION = "1003";
		String CODE_EXCEED_MAX = "207";
	}

	String OSS_URL_PREFIX = "Oss";

	public String DEFAULT_TIMEZONE = "GMT+8";

	public int TRUE = 1;
	public int FALSE = 0;
	public int TEMP = 2;

	public final static String SESSION_KEY_LOGIN_MULTIPLE_FLAG = "session.login.multiple.flag";
	public final static String SESSION_KEY_SIMPLE_LOGIN_INFO = "session.simple.login.info";
	public final static String SESSION_KEY_SIMPLE_LOGIN_TMP_MSG = "session.simple.login.tmp.msg";

	String CLIENT_PAGE = "CLIENT_PAGE"; // 返给前端标记, 需要前端进行分页

	public interface MsgReceivedType {
		String EMAIL = "E";
		String MESSAGE = "M";
		String WECHAT = "W";
		String EMAIL_MESSAGE = "B";
		String NOT_RECEIVE = "N";
	}

	public interface MsgReceivedTypeZh {
		String EMAIL = "邮箱";
		String MESSAGE = "短信";
		String EMAIL_MESSAGE = "短信邮箱都接收";
		String NOT_RECEIVE = "不接收";
	}

	public interface RichInformationType {
		String NEWS = "N";
		String REVERSION = "R";
	}

	public interface Job {
		String RUN_STATUS_RUNING = "R";
		String RUN_STATUS_WAIT = "W";
	}

	public interface JobPeriod {
		String EVERY_DAY = "D";
		String EVERY_WEEK = "W";
		String EVERY_MONTH = "M";
		String NOT_MONITOR = "N";
		String REALTIME = "R";
		/**
		 * 新闻推送 - 按小时
		 */
		String NEWS_HOUR = "H";
	}

	public interface RequestType {
		int FORM_SUBMIT = 0;
		int AJAX_SUBMIT = 1;
	}

	public interface ProjectEnv {
		String PRD = "prd";
		String SIT = "sit";
		String DEV = "dev";
	}

    public interface UserRelationType{
        String NOT_DELETE = "X";
        String DELETE = "1";
    }

	public String EMAIL_NAME = "QCC";

	// added for v1.5.0 KNZT-2253
	String NOTIFICATION_EMAIL = "<EMAIL>";

	public interface CompType {
		String AML = "A";
		String NORMAL = "N";
	}

	public interface RedisKey {
		String TODO_CORP_LIST = "todo_corp_list";
		String WEBAPI_SAAS_MOBILE_CODE = "saas:mobilecd";
		String LOGIN_ATTEMPT = "login:attempt";
		String LOGIN_SAAS_USER_ACCESSTOKEN = "login:userat";//登录的用户信息accesstoken
		String LOGIN_SAAS_USER_MFA_TOKEN = "login:mfa"; // 登录的用户信息mfaToken added for v2.0.4 chenbl KNZT-5371
		String LOGIN_SAAS_USER_BIGSCREEN_TOKEN = "login:bigscrn";//登录用户的大屏token
		String LOGIN_SAAS_USER_REFRESHTOKEN = "login:userrt";//登录的用户信息accesstoken
		String LOGOUT_SAAS_USER_ACCESSTOKEN = "logout:userat";//登出的用户 accesstoken
		String LOGIN_SAAS_USER_SESSION_INFO = "login:usersess";//登录的用户的session需要保存的数据, 使用loginName
		String LOGIN_SAAS_SESSION_INFO = "login:session";//未登录Session

		String WEBAPI_SAAS_EMAIL_CODE = "saas:emailcd";
		String ORDER_PAGE_TIME = "order_page_time";
		String PAYMENT_INTENT_INFO = "payment_intent_info"; // 支付意向缓存信息 added for v2.1.8 chenbl KNZT-6936
		String USER_FRONT_CONFIG = "user_front_config"; // 用户前端配置

		String VERIFY_REQ = "verify:req";
		String VERIFY_RESP = "verify:resp";
		String HK_ANNOUNCEMENT_LIST_REFRESH = "hk_announcement_list_refresh"; // 香港文档列表刷新
		String DATA_CALLBACK = "data_callback";
		String HANDSHAKES = "handshakes";
		String HANDSHAKES_TOKEN = "token";

		// 延时队列 added for v1.9.6 KNZT-4646
		interface DelayedTaskQueue {
			String QUEUE_NAME_COMMON = "comm"; // updated for v2.1.0 chenbl KNZT-5830
		}
	}

	// added for v1.9.9 chenbl KNZT-4974
	interface DelayedTask {
		int CLIENT_ACTIVATE_CHECK_DELAYED_SEC = 3 * 24 * 60 * 60; // C端用户激活检查延时秒数
		int PAYMENT_INTENT_CANCEL_DELAYED_SEC = 30 * 60; // 支付超时取消延时秒数 added for v2.1.8 chenbl KNZT-6936
	}

	public interface OfflineAction {
		String STATUS_PENDING = "P";
		String STATUS_SUCCESS = "S";
		String STAUTS_FAILURE = "F";
	}

	public interface LoginType {
		int ACCOUNT = 1;
		String PARAM_ACCOUNT = "NORMAL";
		int MOBILE = 2;
		String PARAM_MOBILE = "MOBILE";

	}


	public interface messageType {
		String MESSAGE_TYPE_SYSTEM = "1";
		String MESSAGE_TYPE_BACKGROUND = "2";
	}

	public interface messageCategory {
		String TASK_REMINDER = "10";
		String MONITOR_NOTICE_DOWNLOAD = "4";
		String BACKGROUND = "11";
	}

	public interface messageRefSign {
		String PROCESS_THREAD_FILE = "process_thread_file";
		String SYS_PROCESS_THREAD = "sys_thread_process";
		String REVERSION = "tbl_information";
		String TASK_REMINDER = "tbl_offline_action_record";
		String IMPORT_FAIL_FILE = "tbl_comp_import_fail_file";
	}

	public interface messageIconName {
		String MONITOR = "reminder";
		String FINANCE = "finance";
		String DOWNLOAD = "download";
		String REVERSION = "reversion";
		String TASK = "task";
	}

	public interface Message {
		String HIGHLIGHT_WORD_SPLIT = "#A#";
		String HIGHLIGHT_TEXT_SPLIT = "#%#";

		String DOWNLOAD_TYPE_1 = "1";
		String DOWNLOAD_TYPE_REMINDER = "reminder";

	}

	public interface RiskMonitor {
		short READ_STATUS_UNREAD = 0;
		short READ_STATUS_READ = 1;
	}

	interface MessageLinkedUrl {

	}

	public interface MessageMode {
		String INVOICE = "IVC";
		String DATA_TERMINAL = "DT";
	}

	public interface MonitorChangeWay {
		int UPDATE = 0;
		int ADD = 1;
		int DELETE = 2;
	}

	public interface DictLabel {
		String MOBILE_FORMAT = "mobile_format";
		String SYS_COMP_INFO_ID = "sys_comp_info_id";// added for v1.6.4 KNZT-2806
		String MSG_INFO_TYPE = "msg_info_type";
	}

	public interface ThreadPool {
		String POOL_NAME_TEST = "test";
		String POOL_NAME_JOB_TRIGGER = "pool.job.trigger";
		String POOL_NAME_SEND_INFO_EMAIL = "pool.send.info.email";
		String POOL_NAME_EXPORT_DATA = "pool.export.data";
		String POOL_NAME_IMPORT_DATA = "pool.import.data";
		String POOL_NAME_COMMON_POOL = "pool.common";

	}

	public interface Sys {
		String APPLICATION_TYPE_ADMIN_BACKEND = "admin-backend";

		String ROLE_TYPE_QCC_PLATFORM = "1";
		String ROLE_TYPE_USER_ADMIN = "10";
		// added for v1.6.9 KNZT-3009
		// 主管
		String ROLE_TYPE_MANAGER = "15";
		String ROLE_TYPE_USER = "20";

		String MODULE_QCC_PLATFORM = "1";

		String USER_STATUS_ACTIVE = "A";
		String USER_STATUS_INACTIVE = "I";
		String USER_STATUS_LOCKED = "L";
		String USER_STATUS_PASSWORD_EXCEPTION = "E"; // added for v2.0.7 chenbl KNZT-5773

		String USER_LANG_CN = "zh_CN";

		String JOB_STATUS_ACTIVE = "A";
		String JOB_STATUS_INACTIVE = "I";

		String TEMPLATE_TYPE_EMAIL = "E";
		String TEMPLATE_TYPE_MESSAGE = "M";

		String PROCESS_STAUTS_PENDING = "P";
		String PROCESS_STAUTS_RUNNING = "R";
		String PROCESS_STAUTS_FAILURE = "F";
		String PROCESS_STAUTS_SUCCESS = "S";

		String PROCESS_TYPE_EXPORT = "export";
		String PROCESS_TYPE_BUSINESS = "business";
		String PROCESS_TYPE_BUSINESS_CONCURRENT = "business_concurrent";
		String PROCESS_TYPE_BUSINESS_CONCURRENT_NO_USER = "business_concurrent_no_user";
		String PROCESS_TYPE_EXPORT_COMP = "export_comp";
		String PROCESS_TYPE_BUSINESS_COMP = "business_comp";

		/**
		 * 同步业务操作, 用于重新触发使用
		 */
		String PROCESS_TYPE_SYNC_BUSINESS = "sync_business";

		String EXPORT_FOLDER = "export";

		short SUPPORT_RETRIGGER_YES = 1;

		/**
		 * 分配角色-系统管理员-1
		 */
		String COMP_ROLE_ID_SYS_ADMIN = "1";
		/**
		 * 分配角色-业务管理员-2
		 */
		String COMP_ROLE_ID_BUS_ADMIN = "2";
		/**
		 * 分配角色-主管-3
		 */
		String COMP_ROLE_ID_MANAGER = "3";
		/**
		 * 分配角色-员工-4
		 */
		String COMP_ROLE_ID_PERSONNEL = "4";

		/**
		 * 企业可分配权限前缀
		 */
		String COMP_ASSIGN_PERMISSION_PREFIX = "comp_asgn:";

		String UPLOAD_FILE_TYPE_UEDITOR = "EDITOR";

		String UPLOAD_FILE_SOURCE_DISK = "DISK";
		String UPLOAD_FILE_SOURCE_OSS = "OSS";

		String PLUGIN_ROLE_ADMIN = "admin";
		String PLUGIN_ROLE_STAFF = "staff";

		String PERMISSION_LOGIC_AND = "and";
		String PERMISSION_LOGIC_OR = "or";
		String PERMISSION_LOGIC_REVERSE = "rvrs";
	}

	public interface Excel {
		int STYLE_TYPE_HYPERLINK = 10;
	}

	public interface Comp {
		short ASSIGN_GROUP_ACTION_NOTHING = 0;
		short ASSIGN_GROUP_ACTION_TO_MAIN_USER = 1;
		short ASSIGN_GROUP_ACTION_TO_ALL_USER = 2;
		short ASSIGN_GROUP_ACTION_TO_GROUP = 3;

		String CHANGE_MAIN_USER_OP_FROM_EXIST_USER = "1";
		String CHANGE_MAIN_USER_OP_FROM_NEW_USER = "2";

		Integer Type_Try = 1;//试用
		Integer Type_sign = 2;//签约

		String CATEGORY_NORMAL = "N";//正常用户
	}


	public interface DictType {

		String SYS_FUNCTION_NEW_THREAD_EXCEED_SIZE = "sys_function_new_thread_exceed_size";
		String MOBILE_CHECK = "mobile_check";
		String INNER_ACCOUNT = "inner_account";// added for v1.6.4 KNZT-2806
		String OVERSEAS_ORDER_LIMIT = "overseas_order_limit"; // 境外订单次数限制 added for v1.9.8 KNZT-4538
		String HK_SEARCHER_INFO_ISSUING_PLACE = "hk_searcher_info_issuing_place"; // 香港查册人ISSUING_PLACE字典信息, 来源：https://www.e-services.cr.gov.hk/ICRIS3EP/system/countryList.do?csrt=6022561505670861299 added for v2.0.0 chenbl KNZT-5094
		String FINANCIAL_BALANCE_SHEET_ANALYSIS_ITEM = "financial_balance_sheet_analysis_item"; // 财税资产负债表项目映射 added for v2.0.2 chenbl KNZT-5271
		String FINANCIAL_PROFIT_STATEMENT_ANALYSIS_ITEM = "financial_profit_statement_analysis_item"; // 财税利润表项目映射 added for v2.0.2 chenbl KNZT-5271
		String MSG_INFO_DIY = "msg_info_diy"; // 消息自定义类型 added for lvcy v2.0.7 KNZT-5739
		String TOP_UP_AMOUNT_LIMIT = "top_up_amount_limit"; // 充值金额上限 added for v2.1.8 KNZT-6875
	}

	// 香港查册人字段sys_comp_info_col_ext类型、key added for v2.0.0 chenbl KNZT-5094
	interface CompInfoColExt {
		String ADDRESS = "ADDRESS"; // added for lvcy v2.1.1 KNZT-5973
		String NATIONALITY = "NATIONALITY"; // added for lvcy v2.1.1 KNZT-5973
		// added for v2.0.7 chenbl KNZT-6595
		String COUNTRY = "COUNTRY";
		String PROVINCE = "PROVINCE";
		String CITY = "CITY";
		String ADDRESS_LINE1 = "ADDRESS_LINE1";
		String ADDRESS_LINE2 = "ADDRESS_LINE2";
		String ZIP_CODE = "ZIP_CODE";
		String API_MOCK_CONFIG = "api_mock_config";

		// added for v2.0.4 chenbl KNZT-5371
		interface CommExt {
			String EXT_TYPE = "comm_ext";

			String COL_NAME_ENABLE_MFA = "enable_mfa"; // 是否启用MFA
			String COL_NAME_SECURITY_POLICY = "security_policy"; // 密码安全策略 added for v2.0.4 chenbl KNZT-5442
			// added for v2.1.7 chenbl KNZT-6477
			String COL_NAME_UI_DEFAULT_VERSION = "ui_default_version"; // ui默认版本(v1:旧版本,v2:新版本)
			String COL_NAME_UI_VERSION = "ui_version"; // ui版本(v1:旧版本,v2:新版本)
			String COL_DUPLICATE_ORDER_REMIND = "duplicate_order_remind"; // 重复订单提醒 added for v2.1.8 fengsw KNZT-6879
			String COL_MONITOR_TRANSLATION_MODE = "monitor_translation_mode"; // 监控翻译模式 AI、MT
			// ui版本
			String COL_VALUE_UI_VERSION_V1 = "v1";
			String COL_VALUE_UI_VERSION_V2 = "v2";
			String TOP_UP_AMOUNT_LIMIT = "TOP_UP_AMOUNT_LIMIT";// added for v.2.2.7 fengsw KNZT-7785
			String COL_NAME_NOT_ACTIVATE_USER_NOTIFY = "not_activate_user_notify"; // 未激活用户通知 added for v2.3.2 fengsw KNZT-7930
			String COL_NAME_API_LITE_CONTACT = "corp_lite_contact"; // API_LITE联系信息维度是否补充 added for v2.3.4 fegnsw KNZT-8340
			String COL_NAME_NOT_ACTIVATE_USER_NOTIFY_COUNT = "not_activate_user_notify_count"; // 未激活用户通知次数 added for v2.3.4 fengsw KNZT-8416
			String COL_NAME_API_CALLBACK_CONFIG = "api_callback_config"; // API回调配置 added for v2.3.4 fengsw KNZT-8416
			String COL_NAME_ANONYMOUS_ORDER = "anonymous_order"; // 匿名订单配置：Y=匿名（不显示用户信息），N=非匿名（显示用户信息）
		}

		interface HkSearcherInfo {
			String EXT_TYPE = "HkSearcherInfo";

			String COL_NAME_IGNORE= "ignore"; // 特殊列，表明不需要录入查册人信息，走底层默认值
			String COL_NAME_ENGLISH_SURNAME = "englishSurname";
			String COL_NAME_ENGLISH_OTHER_NAMES = "englishOtherNames";
			String COL_NAME_DOC_TYPE = "docType";
			String COL_NAME_ISSUING_PLACE = "issuingPlace";
			String COL_NAME_ISSUING_AUTHORITY = "issuingAuthority";
			String COL_NAME_ID_DOC_NUM = "idDocNum";
			String COL_NAME_ACCEPT_LIST = "acceptList";
		}

		interface Monitor {
			String EXT_TYPE = "MONITOR";

			String COL_NAME_MONITOR_TYPE= "MONITOR_TYPE"; // 监控套餐类型
			String COL_NAME_MONITOR_START_TIME= "MONITOR_START_TIME"; // 监控有效期开始时间, yyyyMMdd
			String COL_NAME_MONITOR_END_TIME= "MONITOR_END_TIME"; // 监控有效期结束时间, yyyyMMdd
			String COL_NAME_MONITOR_ENTITY_COUNT= "MONITOR_ENTITY_COUNT"; // 监控池额度

			String COL_VALUE_MONITOR_TYPE_NEWS = "NEWS"; // 监控套餐类型-新闻舆情
		}
	}

	public interface Role {
		String ROLE_SUB_COMMON = "role_sub_common"; // added for v1.9.8 KNZT-4538
		String ID_ROLE_SUB_HK_GLOBAL_SERVICE = "role_sub_hk_global_service";
		String ID_ROLE_SUB_GLOBAL_SERVICE = "role_sub_global_service";
		String ID_ROLE_PERS_VERIFY = "role_pers_verify";

		// added for v1.5.0 KNZT-2253
		String EN_NAME_ROLE_HK_GLOBAL_SERVICE = "role_hk_global_service";
		String EN_NAME_ROLE_GLOBAL_SERVICE = "role_global_service";
		String EN_NAME_PERS_VERIFY = "pers_verify";
	}

	String COMP_GROUP_ROOT_GROUP_ID = "000001";
	String COMP_GROUP_ROOT_PARENT_GROUP_ID = "0";
	String COMP_GROUP_ROOT_PARENT_GROUP_IDS = "0";

	String PARAM_LOG_EXCEPTION = "log_excepion";





	public interface DataScope {
		//管理员
		String USER_ADMIN = "0";
		//主管
		String MANAGER = "10";
		//员工
		String PERSONNEL = "20";
	}

	public interface AreaLevel{
		//省
		String PROVINCE = "P";
		//市
		String CITY = "C";
		//县
		String AREA = "A";
	}

	public interface CorpTypePrefix {
		String TAIWAN_CORPORATE_PREFIX = "t";
		String HONGKONG_CORPORATE_PREFIX = "h";
		String FOREIGN_CORPORATE_PREFIX = "z";
		String US_STOCKS = "l";
		String PERSON_PREFIX = "p";
	}


	interface FuzzyMatchImport {
		String MATCH_MODE_FUZZY = "FZY"; //模糊匹配
		String MATCH_MODE_ACCURATE = "ACRT";//精准匹配
		String MATCH_MODE_PREVIOUS_NAME = "PREVNM";//曾用名匹配

		String IMPORT_MODE_FUZZY = "fuzzy";

	}

	interface Report {
		@Deprecated
		String REPORT_GROUP_CORP_G = "CORP_G";
		@Deprecated
		String REPORT_GROUP_KYB_HK = "CORP_G_HK";
		@Deprecated
		String REPORT_GROUP_SG = "CORP_G_SG";//added for v1.3.7 KNZT-1423
		@Deprecated
		String REPORT_GROUP_PERS_G = "PERS_G";
		@Deprecated
		String REPORT_GROUP_PERS_NOT_RPT = "PERS_NOT_RPT";

		/**
		 * 人员 - 核验
		 */
		@Deprecated
		String REPORT_TYPE_PERS_NOT_RPT = "PER_VRFY";
		/**
		 * 人员 - 公司高管报告
		 */
		@Deprecated
		String REPORT_TYPE_PERS_GLOBAL_NPTN = "GLOBAL_NPTN";
		/**
		 * 人员 - 本地语言详情
		 */
		@Deprecated
		String REPORT_TYPE_PERS_LOCAL_LANGUAGE = "PERS_LCL_LANG";

		/**
		 * 国内企业详情 - 合并解锁模式
		 */
		@Deprecated
		String REPORT_TYPE_CORP_PROFILE_360 = "CORP_PRFL_360";
		/**
		 * 企业报告 - Advanced KYC Report
		 */
		@Deprecated
		String REPORT_TYPE_CORP_GLOBAL_CUSDD = "GLOBAL_CUSDD";
		/**
		 * 企业报告 - Basic KYC Report
		 */
		@Deprecated
		String REPORT_TYPE_CORP_GLOBAL_PROFILE = "GLOBAL_PROFILE";
		/**
		 * 企业报告 - KYC Report - Hong Kong
		 */
		@Deprecated
		String REPORT_TYPE_CORP_GLOBAL_HK = "GLOBAL_HK";

		/**
		 * added for v1.7.6 KNZT-3348 My Services；company：Services 中，香港报告部分，列名产品细项明细（计费明细）
		 * 企业详情 - 香港企业基本信息
		 */
		@Deprecated
		String REPORT_TYPE_CORP_HK_BAS_DTL = "CORP_HK_BAS_DTL";


		/**
		 * 企业详情 - 香港企业AR
		 */
		@Deprecated
		String REPORT_TYPE_CORP_HK_AR = "CORP_HK_AR";

		/**
		 * 人员详情 - 人员360
		 */
		@Deprecated
		String REPORT_TYPE_PERS_360 = "PERS_360";

		/**
		 * 企业详情 - 新加坡数据
		 */
		@Deprecated
		String REPORT_TYPE_CORP_SG_BAS_DTL = "CORP_SG_BAS_DTL";

		// added for v1.7.0 KNZT-3065
		@Deprecated
		String REPORT_TYPE_BASIC = "KYC_BASIC";
		@Deprecated
		String REPORT_TYPE_UBO = "UBO";
		@Deprecated
		String REPORT_TYPE_ADVANCED = "KYC_ADVANCED";
		@Deprecated
		String REPORT_TYPE_LITE = "KYC_LITE"; // added for v2.0.5 KNZT-5597
		@Deprecated
		String REPORT_TYPE_MERCHANT = "MERCHANT"; // added for lvcy v2.1.1 KNZT-5973

		/**
		 * 企业详情订单
		 */
		String KEY_NO_TYPE_CORP = "C";
		String KEY_NO_TYPE_PERS = "P";

		String PAGE_REPORT_TAB_CORP_VIEW = "tab_corp_view";
		String PAGE_REPORT_TAB_PERS_VIEW = "tab_pers_view";

	}

	//added for v6.7.1 KNZT-37 新增国际版服务，替代原有的征信报告，移除尽调，监控，页面嵌入服务
	interface FunctionTable {
		String ID_REPORT_ID = "global_service_id";
		String ID_HK_SERVICE_ID = "hk_service_id";
	}

	//added v1.0.1 KNZT-38 海外报告下单
	public interface ReportType {
		String CUSDD = "CUSDD";
	}
	
	interface HttpType{
		String HTTP = "http";
		String HTTPS = "https";
	}

	// updated for v2.1.4 chenbl KNZT-6378
	interface JURISDICTION { // added for v6.7.2 KNZT-84 // updated for v1.0.1 KNZT-163
		String CHINA_MAINLAND = "China mainland";
		String HK = "Hong Kong";
		String SG = "Singapore";//added for v1.3.7 KNZT-1423
		String MY = "Malaysia";
		String NZ = GlobalAreaEnum.NZ.getShortNameEn();
		String TW = GlobalAreaEnum.TW.getShortNameEn();
	}

	// added for v2.0.4 chenbl KNZT-5442
	interface SysConfig {
		// 安全策略公用
		interface SecurityCOMM {
			String CONFIG_TYPE = "security_comm";

			String CONFIG_KEY_LOGIN_MFA_TOKEN_EXPIRY_SEC= "login_mfa_token_expiry_sec"; // MFA验证, 登录成功后用户Token的失效时间(秒)
		}

		// 普通安全策略
		interface SecurityBAS {
			String CONFIG_TYPE = "security_bas";

			String CONFIG_KEY_LOGIN_ACCESS_TOKEN_EXPIRY_SEC = "login_access_token_expiry_sec"; // 长时间未操作退出时效(秒)
			String CONFIG_KEY_ACCESS_TOKEN_REFRESH_INTERVAL_SEC = "access_token_refresh_interval_sec"; // 前端调用刷新Access Token的间隔时间, 每隔一段时间要刷新一次Access Token(秒)
		}

		// 增强安全策略
		interface SecurityADV {
			String CONFIG_TYPE = "security_adv";

			String CONFIG_KEY_PWD_REGEX = "pwd_regex"; // 密码正则
			String CONFIG_KEY_PWD_RECENT_UNIQUE_LIMIT= "pwd_recent_unique_limit"; // 历史密码不能重复个数
			String CONFIG_KEY_PWD_USERNAME_SAME_CHAR_LIMIT = "pwd_username_same_char_limit"; // 密码与用户名字字符相同长度限制
			String CONFIG_KEY_PWD_EXPIRY_PRE_REMIND_SEC = "pwd_expiry_pre_remind_sec"; // 密码失效提前提醒时效(秒)
			String CONFIG_KEY_PWD_EXPIRY_SEC = "pwd_expiry_sec"; // 过期强制修改密码时效(秒)
			String CONFIG_KEY_LOGIN_ACCESS_TOKEN_EXPIRY_SEC = "login_access_token_expiry_sec"; // 长时间未操作退出时效(秒)
			String CONFIG_KEY_ACCESS_TOKEN_REFRESH_INTERVAL_SEC = "access_token_refresh_interval_sec"; // 前端调用刷新Access Token的间隔时间, 每隔一段时间要刷新一次Access Token(秒)
		}

		// 钉钉提醒@配置
		interface DingMsgAt {
			String CONFIG_TYPE = "DING_AT";

			String CONFIG_KEY_SG_BUY = "SG_BUY"; // 新加坡购买
		}
	}

	interface SysConfigType{
		String CREATE_TRIAL_ACCOUNT = "create_trial_account";
		String SERVICE_ACTIVE = "service_active"; // added for v1.7.9 KNZT-3515
	}

	interface SysConfigKey{
		String CREATE_TRIAL_ACCOUNT_DEFAULT_PARAM = "create_trial_account_default_param";
	}

	/**
	 * 订单信息来源
	 * API-开放平台API
	 * GLOBAL-国际版内部用户
	 */
	interface OrderDataResource{
		String GLOBAL = "GLOBAL";
		String API = "API";
	}

	interface Order {
		String EXTRA_INFO1_SG_V2 = "v2"; // 新加坡数据v2
	}

	// added for v1.5.8 KNZT-2608
	interface ChargeUnit {
		// 默认单价的companyId
		String DEFAULT_COMPANY_ID = "comm";
	}

	// added for v1.5.8 KNZT-2614
	interface Permission {
		// 公司不可以查询记录
		String COMP_RECORD_HIDDEN = "comp_record:hidden";
	}
	
	// added for v1.5.8 KNZT-2587
	interface OpenApiInterfaceType{
		String HK = "HK";
		String SG = "SG";
	}

	/**
	 * added for v1.5.8 KNZT-2626
	 * 生成token的分隔符，注意GRAPH和DATA不能一样，不然切割会有问题
	 */
	interface TokenSplit {
		String GRAPH = "_";
		String DATA = "-";
		String SPLIT = "_";
	}

	// added for v1.6.2 KNZT-2463
	interface ExportFunction {
		/**
		 * 用户列表导出
		 */
		String USER_MANAGEMENT_LIST_EXPORT = "user_management_list";
	}

	// added for v1.7.6-2 KNZT-3361
	interface HkAnnouncement {
		// 周年申报表
		String ANNUAL_TYPE = "annualreturn";
		// added for v1.8.6 KNZT-3916 法团成立表
		String APPLICATION = "application";
		String APPLICATION_F = "application_f"; // 注册非香港公司的注册申请书 added for v1.8.8 KNZT-3979
		String ANNUAL_RETURN_F = "annualreturn_f"; // 注册非香港公司周年申报表 added for v1.8.8 KNZT-3979

		interface status {
			String AVAILABLE = "可供查阅"; // 可供查阅 added for v1.8.8 KNZT-3979
		}
	}

	interface CorpUser {
		String OP_USER_SYSTEM = "System";
	}

	interface Delivery {
		String NO_CONTRACT_DELIVERY_ID = "NO_CONTRACT";
		String SELF_ACCOUNT_CONTRACT_DELIVERY_ID  = "SELF_ACCOUNT_CONTRACT_DELIVERY_ID";
		String SELF_ACCOUNT_PROD_ACC_ID  = "SELF_ACCOUNT_PROD_ACC_ID";


		// event
		String EVENT_PAYMENT_INTENT_SUCCEEDED = "payment_intent.succeeded";
		String EVENT_PAYMENT_INTENT_CANCELED = "payment_intent.canceled";
		String EVENT_CHARGE_REFUNDED = "charge.refunded";
	}

	interface StripePaymentIntent {
		String STATUS_SUCCEED = "succeeded";
		String STATUS_CANCELED = "canceled";
	}

	// added for v1.9.0 KNZT-4145
	interface CourtCase {

		String OVER_LIMIT_SUFFIX_CN = "及其他当事人";
		String OVER_LIMIT_SUFFIX_EN = ", AND OTHERS";

		int CASE_ROLE_LIMIT = 3;

		String ONLY_ONE_PREFIX_EN = "RE " ; // 相关人只有一方时前缀
	}

	interface MessageExceptionKey {
		String HK_SEARCHER_INFO_MISSING = "err.hk.searcher.info.missing";
		// added for v2.0.4 chenbl KNZT-5442
		String ERR_PWD_EXPIRED = "err.pwd.expired";
		String ERR_PWD_REGEX = "err.pwd.regex";
		String ERR_PWD_USERNAME_SIMILAR = "err.pwd.username.similar";
		String ERR_PWD_RECENT_REPEAT = "err.pwd.recent.repeat";
		String ERR_PWD_REGEX_BAS = "err.pwd.regex.bas";
		String ERR_SYSTEM_ACCOUNT_PASSWORD_EXCEPTION = "err.system.account.password.exception"; // added for v2.0.7 chenbl KNZT-5773
		String WARN_REPORT_SHARE_NO_PERMISSION ="warn.report.share.no.permission"; // added for v2.1.2 chenbl KNZT-6158
		String WARN_SEARCH_KEY_INVALID ="warn.search.key.invalid"; // added for v2.1.5 chenbl KNZT-6475
	}

	// added for lvcy v2.1.1 KNZT-5973
	interface MirrorContentType {
		String MERCHANT_CORP_SHOP = "MERCHANT_CORP_SHOP";
		String MERCHANT_SHOP = "MERCHANT_SHOP";
		String MERCHANT_SHOP_SALE = "MERCHANT_SHOP_SALE";
	}

	// added for v2.1.5 fengsw KNZT-6454
	interface UserExtType {
		String NEW_VERSION_GUIDE = "NEW_VERSION_GUIDE";
		String DATA_MAP_FUZZY_SEARCH_GUIDE = "DATA_MAP_FUZZY_SEARCH_GUIDE";// 图谱搜索指引
		String DATA_MAP_OWNERSHIP_GUIDE = "DATA_MAP_OWNERSHIP_GUIDE";// 图谱股权结构功能指引
		String DATA_MAP_NETWORK_GUIDE = "DATA_MAP_NETWORK_GUIDE";// 图谱关联关系功能指引

		String SILENT_NOTIFY = "SILENT_NOTIFY";
		// added for v2.1.9 chenbl KNZT-6952
		String FRONT_LIST_CONFIG_PREFIX = "FRONT_LIST_CONFIG_"; // 前端列表配置，后面需要拼接具体的列表类型type
		String FRONT_LIST_CONFIG_VERSION_PREFIX = "FRONT_LIST_CONFIG_VERSION_"; // 前端列表配置版本，后面需要拼接具体的列表类型type
		String MONITOR_NOTIFY_SWITCH = "MONITOR_NOTIFY_SWITCH"; // 监控推送开关
		String MONITOR_NOTIFY_TIME = "MONITOR_NOTIFY_TIME";// 监控推送时间
		String MONITOR_NOTIFY_EMAIL = "MONITOR_NOTIFY_EMAIL";//监控推送邮箱
		String MONITOR_NOTIFY_EMAIL_CONTENT = "MONITOR_NOTIFY_EMAIL_CONTENT";//监控推送邮件内容
	}

	interface StripeParam {
		String COMPANY_ID = "companyId";
		String USER_ID = "userId";
		String PROD_ACC_TYPE = "prodAccType";
		String CREDIT_COUNT = "creditCount";
		String YEARS = "years";
		String TOP_UP_NO = "topUpNo";

	}

	//added for v2.2.8 KNZT-7846 线索来源：平台申请，线上自助等
	interface LeadSource{
		String PLATFORM_APPLY = "平台申请";
		String OnlineSelfService = "线上自助";
	}
}
