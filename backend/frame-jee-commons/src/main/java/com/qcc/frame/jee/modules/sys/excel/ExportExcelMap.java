package com.qcc.frame.jee.modules.sys.excel;

import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.excel.fieldtype.ExcelMapField;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

public class ExportExcelMap {
	private static Logger log = LoggerFactory.getLogger(ExportExcelMap.class);
	/**
	 * 工作薄对象
	 */
	private SXSSFWorkbook wb;
	
	/**
	 * 工作表对象
	 */
	private Sheet sheet;
	
	/**
	 * 样式列表
	 */
	private Map<String, CellStyle> styles;
	
	public static final String FIELD_INDEX = "_index";//序号字段
	
	/**
	 * 当前行号
	 */
	private int rownum;
	
	private LinkedHashMap<String, ExcelMapField> mapField;
	
	public ExportExcelMap(String sheetName, String title, LinkedHashMap<String, ExcelMapField> mapField) {
		this.wb = new SXSSFWorkbook(500);
		this.sheet = wb.createSheet(sheetName);
		this.styles = createStyles(wb);
		this.mapField = mapField;
		
		if (StringUtils.isNotBlank(title)){
			Row titleRow = sheet.createRow(rownum++);
			titleRow.setHeightInPoints(30);
			Cell titleCell = titleRow.createCell(0);
			titleCell.setCellStyle(styles.get("title"));
			titleCell.setCellValue(title);
			sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(),
					titleRow.getRowNum(), titleRow.getRowNum(), mapField.keySet().size()-1));
		}
		
		Row headerRow = sheet.createRow(rownum++);
		headerRow.setHeightInPoints(16);
		
		int cellIndex = 0;
		for (Entry<String, ExcelMapField> entry : mapField.entrySet()) {
		    Cell cell = headerRow.createCell(cellIndex);
			cell.setCellStyle(styles.get("header"));
			cell.setCellValue(entry.getValue().getFieldName());
			sheet.setColumnWidth(cellIndex, entry.getValue().getFieldWidth());  
			cellIndex++;
		}
	}
	
	public ExportExcelMap setDataList(List<Map<String, Object>> list){
		int rowIndex = 1;
		for(Map<String, Object> map : list) {
			int column = 0;
			Row row = this.addRow();
			for (Entry<String, ExcelMapField> entry : mapField.entrySet()) {
				if(FIELD_INDEX.equals(entry.getKey())) {
					this.addCell(row, column, rowIndex, entry.getValue().getAlign());
				} else {
					this.addCell(row, column, map.get(entry.getKey()), entry.getValue().getAlign());
				}
				column++;
			}
			rowIndex++;
		}
		return this;
	}
	
	public ExportExcelMap write(OutputStream os) throws IOException{
		wb.write(os);
		return this;
	}
	
	/**
	 * 清理临时文件
	 */
	public ExportExcelMap dispose(){
		wb.dispose();
		return this;
	}
	
	public SXSSFWorkbook getWookbook() {
		return wb;
	}
	
	/**
	 * 输出到客户端
	 * @param fileName 输出文件名
	 */
	public ExportExcelMap write(HttpServletResponse response, String fileName) throws IOException{
		response.reset();
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename="+ StringUtils.encodeDownloadFileName(fileName));
		write(response.getOutputStream());
		response.getOutputStream().flush();
		return this;
	}
	
	public Row addRow(){
		return sheet.createRow(rownum++);
	}
	
	public Cell addCell(Row row, int column, Object val, int align){
		Cell cell = row.createCell(column);
		CellStyle style = styles.get("data"+(align>=1&&align<=3?align:""));
		try {
			
			if (val == null){
				cell.setCellValue("");
			} else if (val instanceof String) {
				cell.setCellValue((String) val);
			} else if (val instanceof Integer) {
				cell.setCellValue((Integer) val);
			} else if (val instanceof Long) {
				cell.setCellValue((Long) val);
			} else if (val instanceof Double) {
				cell.setCellValue((Double) val);
			} else if (val instanceof Float) {
				cell.setCellValue((Float) val);
			}else if (val instanceof BigDecimal) {
				cell.setCellValue(((BigDecimal) val).doubleValue());
			} else if (val instanceof Date) {
				DataFormat format = wb.createDataFormat();
				CellStyle styleDt = wb.createCellStyle();
				styleDt.cloneStyleFrom(style);
				styleDt.setDataFormat(format.getFormat("yyyy-MM-dd"));
				style = styleDt;
				cell.setCellValue((Date) val);
			} else {
				cell.setCellValue(val.toString());
			}
		} catch (Exception ex) {
			log.info("Set cell value ["+row.getRowNum()+","+column+"] error: " + ex.toString());
			cell.setCellValue(val != null ? val.toString() : "");
		}

		cell.setCellStyle(style);
		return cell;
	}
	
	private Map<String, CellStyle> createStyles(Workbook wb) {
		Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
		
		CellStyle style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
		Font titleFont = wb.createFont();
		titleFont.setFontName("Arial");
		titleFont.setFontHeightInPoints((short) 16);
		titleFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
		style.setFont(titleFont);
		styles.put("title", style);

		style = wb.createCellStyle();
		style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		Font dataFont = wb.createFont();
		dataFont.setFontName("Arial");
		dataFont.setFontHeightInPoints((short) 10);
		style.setFont(dataFont);
		styles.put("data", style);
		
		style = wb.createCellStyle();
		style.cloneStyleFrom(styles.get("data"));
		style.setAlignment(CellStyle.ALIGN_LEFT);
		styles.put("data1", style);

		style = wb.createCellStyle();
		style.cloneStyleFrom(styles.get("data"));
		style.setAlignment(CellStyle.ALIGN_CENTER);
		styles.put("data2", style);

		style = wb.createCellStyle();
		style.cloneStyleFrom(styles.get("data"));
		style.setAlignment(CellStyle.ALIGN_RIGHT);
		styles.put("data3", style);

		style = wb.createCellStyle();
		style.cloneStyleFrom(styles.get("data"));
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		Font headerFont = wb.createFont();
		headerFont.setFontName("Arial");
		headerFont.setFontHeightInPoints((short) 10);
		headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
		headerFont.setColor(IndexedColors.WHITE.getIndex());
		style.setFont(headerFont);
		styles.put("header", style);
		
		CellStyle hlink_style = wb.createCellStyle();  
	    Font hlink_font = wb.createFont();  
	    hlink_font.setUnderline(Font.U_SINGLE);  
	    hlink_font.setColor(IndexedColors.BLUE.getIndex());
	    hlink_style.setFont(hlink_font);
	    styles.put("hyperlink", hlink_style);
		return styles;
	}
}
