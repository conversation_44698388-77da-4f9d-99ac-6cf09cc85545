package com.qcc.frame.jee.modules.sys.utils;

import com.qcc.frame.jee.commons.persistence.interceptor.SQLHelper;
import org.apache.ibatis.mapping.BoundSql;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * 来源pagehelper5.1.11
 * com.github.pagehelper.util.ExecutorUtil
 */
public class ExecutorUtil {

    protected static Logger logger = LoggerFactory.getLogger(ExecutorUtil.class);

    private static Field additionalParametersField;

    static {
        try {
            additionalParametersField = BoundSql.class.getDeclaredField("additionalParameters");
            additionalParametersField.setAccessible(true);
        } catch (NoSuchFieldException e) {
            logger.error("获取 BoundSql 属性 additionalParameters 失败: ", e);
            throw new RuntimeException("获取 BoundSql 属性 additionalParameters 失败: " + e, e);
        }
    }

    public static Map<String, Object> getAdditionalParameter(BoundSql boundSql) {
        try {
            return (Map<String, Object>) additionalParametersField.get(boundSql);
        } catch (IllegalAccessException e) {
            logger.error("getAdditionalParameter 获取 BoundSql 属性 additionalParameters 失败: ", e);
            throw new RuntimeException("获取 BoundSql 属性值 additionalParameters 失败: " + e, e);
        }
    }
}
