package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysCompInfoFuncCountHis extends DataEntity<SysCompInfoFuncCountHis> {

	private String functionTableId;
	private String countStd;
	private Integer totalCount;
	private Integer consumedCount;
	private Short mainFuncFlag;

	public String getFunctionTableId() {
		return functionTableId;
	}
	public void setFunctionTableId(String functionTableId) {
		this.functionTableId = functionTableId;
	}
	public String getCountStd() {
		return countStd;
	}
	public void setCountStd(String countStd) {
		this.countStd = countStd;
	}
	public Integer getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	public Integer getConsumedCount() {
		return consumedCount;
	}
	public void setConsumedCount(Integer consumedCount) {
		this.consumedCount = consumedCount;
	}
	public Short getMainFuncFlag() {
		return mainFuncFlag;
	}
	public void setMainFuncFlag(Short mainFuncFlag) {
		this.mainFuncFlag = mainFuncFlag;
	}
}
