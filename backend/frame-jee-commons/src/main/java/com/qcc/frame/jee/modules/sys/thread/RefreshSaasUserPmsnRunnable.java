package com.qcc.frame.jee.modules.sys.thread;

import com.qcc.frame.jee.commons.thread.BaseRunnable;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.modules.sys.service.SaasLoginService;

public class RefreshSaasUserPmsnRunnable extends BaseRunnable {
	
	private static SaasLoginService saasLoginService = SpringContextHolder.getBean(SaasLoginService.class);
	
	private String userId;
	private String loginName;
	private String clientId;
	private String accessToken;
	private String loginType;

	@Override
	public void process() {
		logger.info("[refershuserpmsn]"+loginName + ":" + clientId + ":" + userId);
		saasLoginService.refreshUserPmsnAndMenu(userId, loginName, clientId, accessToken, loginType);
	}

	public String getLoginType() {
		return loginType;
	}

	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

}
