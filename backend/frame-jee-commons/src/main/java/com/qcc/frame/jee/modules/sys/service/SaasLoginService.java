package com.qcc.frame.jee.modules.sys.service;

import com.qcc.frame.commons.ienum.CompDataSaveEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.PwdSecurityPolicyEnum;
import com.qcc.frame.jee.commons.annotation.DingErrorMsg;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.qcc.frame.commons.Constants;

import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.web.Servlets;
import com.qcc.frame.jee.modules.sys.model.saas.AuthResult;
import com.qcc.frame.jee.modules.sys.model.saas.RedisSaasMenuTO;
import com.qcc.frame.jee.modules.sys.model.saas.RedisUserAccessTO;
import com.qcc.frame.jee.modules.sys.model.saas.SaasAccessSecretKeyTO;
import com.qcc.frame.jee.modules.sys.model.saas.SaasLoginUserResult;
import com.qcc.frame.jee.modules.sys.model.saas.SaasMenuAndHomeTO;
import com.qcc.frame.jee.modules.sys.model.saas.SaasMenuHome;
import com.qcc.frame.jee.modules.sys.model.saas.SaasMenuTO;
import com.qcc.frame.jee.modules.sys.model.saas.SaasUserPermissionTO;
import com.qcc.frame.jee.modules.sys.thread.RefreshSaasUserPmsnRunnable;
import com.qcc.frame.jee.modules.sys.utils.LogUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;

import java.util.*;
import com.qcc.pa.utils.AesUtil;

@Service
public class SaasLoginService {
	protected Logger logger = LoggerFactory.getLogger(getClass());

	private static final String LOGIN_NAME_AES_KEY = Global.getConfig("saas.login.name.aes.key");
	private static final String LOGIN_NAME_AES_IV = Global.getConfig("saas.login.name.aes.iv");
	private static final String LOGIN_NAME_SIGN_KEY = Global.getConfig("saas.login.name.sign.key");
	
//	private static final String LOGIN_KEEP_AES_KEY = Global.getConfig("saas.login.keep.aes.key");
//	private static final String LOGIN_KEEP_AES_IV = Global.getConfig("saas.login.keep.aes.iv");
//	private static final String LOGIN_KEEP_SIGN_KEY = Global.getConfig("saas.login.keep.sign.key");
		
	public static final String KEEP_TOKEN_SPLIT = "##";

	
	public static final String PRO_LOGIN_SOURCE_MOBILE = "mobile";
	public static final String PRO_LOGIN_SOURCE_PC = "pc";
	public static final String AES_KEY = "jIMSui6A6zsTu2m00qHGEQ==";
	public static final String ROLE_ADMIN = "admin";
	public static final String ROLE_STAFF = "staff";
	/**
	 * 会话超时时间
	 */
//	public static final long SESSION_TIMEOUT = Long.parseLong(Global.getConfigDefault("saas.login.session.timout", "1800")) * 1000;
	
	public static final int SESSION_TIMEOUT_SEC = Integer.parseInt(Global.getConfigDefault("saas.login.session.timout", "1800"));
	
	@Autowired
	private UserService userService;
	@Autowired
	private RedisService redisService;
	@Autowired
	private RedisUserSessionService redisUserSessionService;
	@Autowired
	private RedisUserTokenService redisUserTokenService;

	@Autowired
	private ThreadPoolService threadPoolService;
	@Autowired
	private RedisSessionService redisSessionService;

	@Autowired
	private SysConfigService sysConfigService;
	@Autowired
	private SaasEmailService saasEmailService;
	@Autowired
	private SysUserExtService sysUserExtService;


	public static final String MSG_KEY_MULTIPLE_LOGIN = "multiple.login";
	public static final String MSG_KEY_SESSION_TIMEOUT = "session.timeout";
	public static final String MSG_KEY_NO_PERMISSION = "no.permission";
	public static final String MSG_KEY_NOT_LOGIN = "not.login";

	
	private static final List<String> ignoreServiceExpiredUrlList = new ArrayList<>();
	private static final List<String> notIgnoreServiceExpiredUrlList = new ArrayList<>();
	
	public static final String MENU_HREF_SETTING_CENTER = "main/setting_ctr";
	
	public static final String MENU_HOME_SIGN_NOT_AVAILABLE = "NA";
	
	static {
		ignoreServiceExpiredUrlList.add("/webapi/saas/business/invoice/");
		ignoreServiceExpiredUrlList.add("/webapi/saas/business/order/");
		ignoreServiceExpiredUrlList.add("/webapi/saas/online/order/");
		ignoreServiceExpiredUrlList.add("/webapi/saas/sys/message/");
		ignoreServiceExpiredUrlList.add("/webapi/saas/common/");
		ignoreServiceExpiredUrlList.add("/webapi/saas/userinfo/getSellerInfo");
		ignoreServiceExpiredUrlList.add("/webapi/saas/comp/user/getUserRemainDays");
		ignoreServiceExpiredUrlList.add("/webapi/saas/todoCorp/list");
		ignoreServiceExpiredUrlList.add("/webapi/saas/corp/search/getCompCorpFunctionList");
		
		notIgnoreServiceExpiredUrlList.add("/webapi/saas/common/user/getUserMenuAndHome");

	}


	public boolean checkWeekKeep(String loginDateStr) {
		Date loginDate = DateUtils.parseDate(loginDateStr, DateUtils.DATE_FORMAT);
		if(loginDate != null) {
			Date addDate = DateUtils.addDays(loginDate, 6);
			Date curDate = DateUtils.getCurDate();
			if(curDate.after(addDate)) {
				return false;
			}
		}
		return true;
	}
	
	public RedisUserAccessTO checkLoginUserStatus(String clientId, String accessToken, String accessSecretKey, boolean isFromCheckLoginStatus, String thirdToken, SaasLoginCallbackInterface callback) throws MessageException {
		if(StringUtils.orBlank(clientId, accessToken, accessSecretKey)) {
			return null;
		}

		SaasAccessSecretKeyTO secretKeyTO = this.decryptLoginName(accessSecretKey);
		if(!isFromCheckLoginStatus) {
			logger.info(" track["+secretKeyTO.getLoginName() + "][" + secretKeyTO.getLoginType() + "][" + secretKeyTO.getSource() + "]");
		}

		String loginName = secretKeyTO.getLoginName();
		
		List<RedisUserAccessTO> userAccessList = redisUserTokenService.listLoginAccessToken(secretKeyTO.getLoginType(), loginName);
		String userId = null;
		RedisUserAccessTO currentAccToken = null;
		if(userAccessList != null && userAccessList.size() > 0) {
			for(RedisUserAccessTO userAccess : userAccessList) {
				if(StringUtils.equals(userAccess.getClientId(), clientId) && StringUtils.equals(userAccess.getAccessToken(), accessToken)) {//判断clientId和accessToken是否匹配, 如果匹配说明是登录用户
					userId = userAccess.getUserId();
				}
				if(!userAccess.isExpiryFlag()) {//判断是当前access token
					currentAccToken = userAccess;
				}
			}

			if(StringUtils.isBlank(userId)) {
				logger.info("MSG_KEY_MULTIPLE_LOGIN 1");
				try {
					if("/webapi/saas/common/user/getUserMenuAndHome".equals(Servlets.getRequest().getRequestURI())) {
						logger.info("accessSecretKey:"+accessSecretKey);
						logger.info("accessToken:"+accessToken);
						logger.info("clientId:"+clientId);
						logger.info("loginName:"+loginName + ";userId=" + userId + ";" + (currentAccToken == null));
					}
				} catch(Exception e) {

				}
				throw new MessageException(MSG_KEY_MULTIPLE_LOGIN);
			}
		}
		
		if(StringUtils.isBlank(userId) || currentAccToken == null) {//如果没有匹配的access token或者没有当前的access token, 表明acess token已经提出
			logger.warn("[checkLoginStatusWarn] notexisted [clientId=" + clientId + "][accessToken=" + accessToken + "][clientIp=" + StringUtils.getRemoteAddr(Servlets.getRequest()) + "]");
			try {
					if("/webapi/saas/common/user/getUserMenuAndHome".equals(Servlets.getRequest().getRequestURI())) {
						logger.info("accessSecretKey:"+accessSecretKey);
						logger.info("accessToken:"+accessToken);
						logger.info("clientId:"+clientId);
						logger.info("loginName:"+loginName + ";userId=" + userId + ";" + (currentAccToken == null));
					}
			} catch(Exception e) {
					
			}
			throw new MessageException(MSG_KEY_SESSION_TIMEOUT);
		}

		if((!currentAccToken.isKeep()) && (System.currentTimeMillis() - currentAccToken.getLastAccessTime() > userService.getLoginAccessTokenExpirySecByLoginName(loginName) * 1000L)) {
			logger.info("session timeout 1");
			throw new MessageException(MSG_KEY_SESSION_TIMEOUT);
		}


		String loginDateStr = DateUtils.getDateFromTs(currentAccToken.getLoginTime());
		if(!checkWeekKeep(loginDateStr)) {
			logger.info("session timeout 2");
			throw new MessageException(MSG_KEY_SESSION_TIMEOUT);
		}


		try {
			String checkDate = DateUtils.getDateFromTs(currentAccToken.getLastRefreshPermissionTime());
			String curDate = DateUtils.getDate();
			if((!StringUtils.equals(checkDate, curDate)) && (!isFromCheckLoginStatus)) {
				
				currentAccToken.setLastRefreshPermissionTime(System.currentTimeMillis());
				redisUserTokenService.saveLoginAccessToken(secretKeyTO.getLoginType(), loginName, currentAccToken);
				
				RefreshSaasUserPmsnRunnable refreshRunnable = new RefreshSaasUserPmsnRunnable();
				refreshRunnable.setUserId(userId);
				refreshRunnable.setLoginName(loginName);
				refreshRunnable.setClientId(clientId);
				refreshRunnable.setAccessToken(accessToken);
				refreshRunnable.setLoginType(secretKeyTO.getLoginType());
				threadPoolService.execute("pool.refresh.saas.user.pmsn", refreshRunnable);
				
				LogUtils.saveLog(Servlets.getRequest(), null, "系统登录", userId, Log.TYPE_REFRESH_LOGIN);
			}
			currentAccToken.setLoginSource(secretKeyTO.getSource());
			//不能设置LoginType, 会影响原有的登录账号
		} catch(Exception e) {
			logger.error("", e);
		}
		return currentAccToken;
	}
	
	
	public User checkLoginUser(String devLoginName, String clientId, String accessToken, String accessSecretKey, String [] permissionArr, String permissionLogic) throws MessageException {
		if(Global.isProjectEnv("dev")) {//如果是开发环境, 使用简单的方式登录
    		if(StringUtils.isNotBlank(devLoginName)) {
				// updated for V2.0.8 ZS-499 【产业招商云平台II】【WEB】-优化-用户登录与企业账号编辑时，增加账号有效期校验
				User user = userService.getByLoginName(devLoginName);
				userService.checkCompanyValid(user.getCompany());
				return user;
			}
    	}
		RedisUserAccessTO currentAccToken = null;

		currentAccToken = checkLoginUserStatus(clientId, accessToken, accessSecretKey, false, null, null);

		if(currentAccToken == null) {
			return null;
		}
		
		if(permissionArr != null && permissionArr.length > 0) {
			if(currentAccToken.getPermissionList() == null || currentAccToken.getPermissionList().isEmpty()) {
				throw new MessageException(MSG_KEY_NO_PERMISSION);
			}
			if(Constants.Sys.PERMISSION_LOGIC_OR.equals(permissionLogic)) {//如果该请求配置了多个权限标识, 判断用户是否要拥有该请求的所有权限
				boolean hasPerm = false;
				for(String perm : permissionArr) {
					if(currentAccToken.getPermissionList().contains(perm)) {
						hasPerm = true;
						break;
					}
				}
				if(!hasPerm) {
					throw new MessageException(MSG_KEY_NO_PERMISSION);
				}
			} else if(Constants.Sys.PERMISSION_LOGIC_REVERSE.equals(permissionLogic)) {//反向权限, 有该权限的是禁用功能
				for(String perm : permissionArr) {
					if(currentAccToken.getReversalPermissionList().contains(perm)) {
						throw new MessageException(MSG_KEY_NO_PERMISSION);
					}
				}
			} else {
				for(String perm : permissionArr) {
					if(!currentAccToken.getPermissionList().contains(perm)) {
						throw new MessageException(MSG_KEY_NO_PERMISSION);
					}
				}
			}

			logger.info("[hasperm]"+currentAccToken.getUserId() + "=" + Arrays.asList(permissionArr).toString() + "=permissionLogic=" + permissionLogic);
		}


		User user = UserUtils.get(currentAccToken.getUserId());
		if(user != null) {
			if(Constants.Sys.USER_STATUS_LOCKED.equalsIgnoreCase(user.getStatus())) {
				throw new MessageException("err.system.account.locked");
			}
			if(Constants.Sys.USER_STATUS_INACTIVE.equalsIgnoreCase(user.getStatus())) {
				throw new MessageException("err.system.account.inactive");
			}
			// added for V2.0.8 ZS-499 【产业招商云平台II】【WEB】-优化-用户登录与企业账号编辑时，增加账号有效期校验
			userService.checkCompanyValid(user.getCompany());

			boolean updateTime = true;
			// updated for v2.1.7 chenbl KNZT-6877
			if(WebContextHolder.getRequest() != null && WebContextHolder.getRequest().getRequestUrl() != null && "/webapi/saas/sys/message/countUnReadMessage".equalsIgnoreCase(WebContextHolder.getRequest().getRequestUrl())) {
				updateTime = false;
			}

			if(updateTime) {
				currentAccToken.setLastAccessTime(System.currentTimeMillis());
			}
			redisUserTokenService.saveLoginAccessToken(currentAccToken.getLoginType(), currentAccToken.getLoginName(), currentAccToken);
			WebContextHolder.setProLoginType(currentAccToken.getLoginType());
			WebContextHolder.setProLoginSource(currentAccToken.getLoginSource());
			return user;
		}
		clearLoginNameFromRedis(currentAccToken.getLoginType(), currentAccToken.getLoginName());
		return null;
	}

	/**
	 * 特殊账户代理用户查看订单逻辑
	 * added for lvcy v2.0.3 KNZT-5404
	 *
	 * @param clientId
	 * @param accessToken
	 * @param accessSecretKey
	 * @param proxyUserToken
	 * @return User
	 */
	public User checkProxyUser(String clientId, String accessToken, String accessSecretKey, String proxyUserToken) throws MessageException {
		RedisUserAccessTO currentAccToken = checkLoginUserStatus(clientId, accessToken, accessSecretKey, false, null, null);

		if(currentAccToken == null) {
			return null;
		}

		boolean hasProxyUserOrderPerm = currentAccToken.getPermissionList().stream().anyMatch(k -> k.equals("sys:proxy-user-order:view"));
		MsgExceptionUtils.failBuild(!hasProxyUserOrderPerm, MSG_KEY_NO_PERMISSION);
		MsgExceptionUtils.failBuild(StringUtils.isBlank(proxyUserToken), "err.param.invalid");
		int lastIndexOfProxyUserToken = proxyUserToken.lastIndexOf(Constants.TokenSplit.SPLIT);
		MsgExceptionUtils.failBuild(lastIndexOfProxyUserToken == -1, "err.param.invalid");
		String token = proxyUserToken.substring(lastIndexOfProxyUserToken + 1);
		String proxyUserId = proxyUserToken.substring(0, lastIndexOfProxyUserToken);
		boolean validate = TokenUtils.validateMd5WithTimeout(proxyUserId, token, 60L * 60L);
		MsgExceptionUtils.failBuild(!validate, "proxy.user.token.expired");
		User user = UserUtils.get(proxyUserId);
		if (Objects.nonNull(user)) {
			return user;
		}
		return null;
	}


	private boolean clearLoginNameFromRedis(String loginType, String loginName) throws MessageException {
		List<RedisUserAccessTO> accessTOList = redisUserTokenService.listLoginAccessToken(loginType, loginName);
		if (CollectionUtils.isEmpty(accessTOList)) {
			return false;
		}
		redisUserTokenService.deleteLoginAccessToken(loginType, loginName);
//		redisUserTokenService.deleteLoginRefreshToken(loginName);
		redisUserSessionService.clearAllAttribute(loginType, loginName);
		return true;
	}
	
	
	/**
	 * 如果过期时间是remainderSec秒后, 判断为过期
	 * @return
	 */
	public boolean isExpiryTime(long tokenTime, int redisTimeoutSec, int remainderSec) {
		if((redisTimeoutSec - (System.currentTimeMillis() - tokenTime)/1000) < remainderSec) {
			return true;
		}
		return false;
	}

	// 用AuthServiceContext替代 removed for v2.0.4 chenbl KNZT-5371
	// updated for v1.6.9 KNZT-2991【优化】国际版管理平台登录增加验证码校验
	/*public SaasLoginUserResult doLogin(String clientId, String loginType, String loginName, String pwd, String keep, String sessionId,String verifyCode, Map<String, Object> extraMap, SaasLoginCallbackInterface callback) throws MessageException {
		String source = extraMap != null ? StringUtils.nullToString((String)extraMap.get("login_source")) : "";
		String extraLoginType = extraMap != null ? StringUtils.nullToString((String)extraMap.get("extra_login_type")) : "";
		
		loginName = StringUtils.trim(loginName);
		if(clientId != null && (clientId.trim().length() > 50 || clientId.trim().length() < 30)) {
			logger.error("clientId parameter is error");
			throw new MessageException("err.access");
		}
		clientId = clientId.trim();
		long tm = System.currentTimeMillis();
		AuthResult authResult = null;

		if("NORMAL".equalsIgnoreCase(loginType)) {
			authResult = this.doLoginAccount(clientId, loginName, pwd, keep, loginType, source, verifyCode);
		} else{
			logger.error("Invalid login method");
			throw new MessageException("err.access");
		}
		logger.info("doLogin===1== took " + (System.currentTimeMillis() - tm));
		SaasLoginUserResult result = doAuthorization(clientId, keep, loginType, source, authResult);
		if(result != null) {
			userService.resetFailCount4Login(loginName);
		}
		return result;
	}*/

	private void changeMenuHomeName(List<MenuHome> menuHomeList, SysConfig config) {
		for(MenuHome menuHome : menuHomeList) {
			if(StringUtils.equals(menuHome.getMenuHref(), config.getConfigValue())) {
				menuHome.setModuleName(config.getConfigDesc());
			}
		}
	}
	
	private void changeMenuName(List<SaasMenuTO> menuList, SysConfig config) {
		if(menuList != null) {
			for(SaasMenuTO menuTO : menuList) {
				if(StringUtils.equals(menuTO.getHref(), config.getConfigValue())) {
					menuTO.setTitle(config.getConfigDesc());
				}
			}
		}
	}
	
	private void removeMenuHome(SaasMenuHome saasHome, List<SaasMenuTO> menuList, String companyId) {
		try {
			SysConfig hideConfig = sysConfigService.getConfigByTypeAndKey("menu_home_hide", companyId);
//			if(disableLoginName != null && disableLoginName.contains(loginName.trim())) {
//				String disableFunction = ConfigUtils.getConfigValueByTypeAndKey("menu_home_hide", "function", "");
				if(hideConfig != null && StringUtils.isNotBlank(hideConfig.getConfigValue())) {
					String disableFunction = hideConfig.getConfigValue();
					if(saasHome != null) {
						Iterator<MenuHome> it = saasHome.getHeaderList().iterator();
						while(it.hasNext()) {
							String menuHref = it.next().getMenuHref();
							if(disableFunction.contains("," + menuHref + ",")) {
								it.remove();
							}
						}
						it = saasHome.getModuleHomeList().iterator();
						while(it.hasNext()) {
							String menuHref = it.next().getMenuHref();
							if(disableFunction.contains("," + menuHref + ",")) {
								it.remove();
							}
						}
						it = saasHome.getAdvancedAppList().iterator();
						while(it.hasNext()) {
							String menuHref = it.next().getMenuHref();
							if(disableFunction.contains("," + menuHref + ",")) {
								it.remove();
							}
						}
						
						List<SysConfig> nameChgList = sysConfigService.listConfigByTypeAndKey("menu_home_name_chg", companyId);
						if(nameChgList != null && nameChgList.size() > 0) {
							for(SysConfig nameChg : nameChgList) {
								changeMenuHomeName(saasHome.getHeaderList(), nameChg);
								changeMenuHomeName(saasHome.getModuleHomeList(), nameChg);
								changeMenuHomeName(saasHome.getAdvancedAppList(), nameChg);
								changeMenuName(menuList, nameChg);
							}
						}
					}
				}
//			}
		} catch(Exception e) {
			logger.warn("", e);
		}
	}

	/**
	 * 授权
	 *
	 * @param clientId   不为空
	 * @param keepStr    可为空
	 * @param loginType  不为空
	 * @param source     不为空
	 * @param authResult 不为空
	 * @return
	 * @throws MessageException
	 */
	public SaasLoginUserResult doAuthorization(String clientId, String keepStr, String loginType, String source, AuthResult authResult) throws MessageException {
		if(authResult == null || authResult.getUser() == null) {
			return null;
		}
		logger.info("doAuthorization [userId=" + authResult.getUser().getId() + "][loginName=" + authResult.getUser().getLoginName() + "][" + loginType + "][" + source + "]");
		long tm = System.currentTimeMillis();
		boolean keep = "true".equalsIgnoreCase(keepStr) ? true : false;
		SaasLoginUserResult result = new SaasLoginUserResult();
		String accessToken = IdGenUtil.generateSaasAccessToken(authResult.getUser().getCompany().getType());
		String refreshToken = IdGenUtil.uuid();
		String loginIp = StringUtils.getRemoteAddr(Servlets.getRequest());
		String loginName = authResult.getUser().getLoginName();
		String companyId = authResult.getUser().getCompany().getId();
		
		User tempUser = new User();
		tempUser.setId(authResult.getUser().getId());
		tempUser.setLoginIp(loginIp);
		tempUser.setLoginDate(new Date());
		userService.updateLoginInfo(tempUser);
		logger.info("doAuthorization===1== took " + (System.currentTimeMillis() - tm));
		tm = System.currentTimeMillis();
		//将已经登录的用户信息踢出
		try {
			boolean isLogOut = clearLoginNameFromRedis(loginType, loginName);
			result.setIsLogOut(isLogOut);
		} catch (MessageException e) {
			throw e;
		}
		logger.info("doAuthorization===2== took " + (System.currentTimeMillis() - tm));
		tm = System.currentTimeMillis();
		//保存用户会话
		long timeLong = System.currentTimeMillis();
		RedisUserAccessTO userAccessTO = new RedisUserAccessTO();
		userAccessTO.setUserId(authResult.getUser().getId());
		userAccessTO.setAccessToken(accessToken);
		userAccessTO.setClientId(clientId);
		userAccessTO.setLastRefreshTime(timeLong);
		userAccessTO.setLastAccessTime(timeLong);
		userAccessTO.setLastRefreshPermissionTime(timeLong);
		userAccessTO.setLoginTime(timeLong);
		userAccessTO.setLoginIp(loginIp);
//		userAccessTO.setLoginIp("IP");
		userAccessTO.setLoginName(loginName);
		userAccessTO.setRefreshToken(refreshToken);
		userAccessTO.setKeep(keep);
		userAccessTO.setExpiryFlag(false);

		try {
			
			WebContextHolder.setCurrentUser(authResult.getUser());
			WebContextHolder.setProLoginType(loginType);
			
			tm = System.currentTimeMillis();
			List<RedisSaasMenuTO> menuList = UserUtils.getRedisSaasMenuList();
			logger.info("doAuthorization===2.1== took " + (System.currentTimeMillis() - tm));
			tm = System.currentTimeMillis();

			SaasUserPermissionTO permissionResult = getUserPermission(authResult.getUser(), loginType, menuList);
			
			List<String> permissionList = permissionResult.getPermissionList();
			List<String> reversalPermissionList = permissionResult.getReversalPermissionList();

			SaasMenuHome saasHome = new SaasMenuHome();

//			saasHome = getUserMenuHome(menuList, mainInfo.getMenuHomeGroupId());//首页菜单列表
			
//			removeMenuHome(saasHome, null, companyId);
			if(SysConstants.Menu.MENU_SHOW_MODE_TREE.equalsIgnoreCase(Global.getConfig("menu.show.mode"))) {
				List<SaasMenuTO> saasMenuList = userService.regroupMenu2ParentChild(menuList);
				Iterator<SaasMenuTO> it = saasMenuList.iterator();
				while(it.hasNext()) {
					if(MENU_HREF_SETTING_CENTER.equals(it.next().getHref())) {
						it.remove();
					}
				}
				saasHome.setMenuList(saasMenuList);
			} else {
				saasHome = getUserMenuHome(menuList);//首页菜单列表
				
				removeMenuHome(saasHome, null, companyId);
			}
			//added for v1.3.7 KNZT-2018 如果登陆后端管理平台，则校验登陆用户有管理员角色
			if(StringUtils.equals(source,"CRM") && CollectionUtils.isNotEmpty(permissionList)){
				String haveAdminPermission = permissionList.stream().filter(t->t.startsWith("sys:platform_mgnt")).findFirst().orElse(null);
				if(StringUtils.isBlank(haveAdminPermission)){
					logger.error("no admin permission [userId=" + authResult.getUser().getId() + "][loginName=" + authResult.getUser().getLoginName() + "][" + loginType + "][" + source + "]");
					throw new MessageException("msg:Access Permission Denied");
				}
			}
			logger.info("doAuthorization===4== took " + (System.currentTimeMillis() - tm));
			tm = System.currentTimeMillis();
			
			userAccessTO.setReversalPermissionList(reversalPermissionList);
			userAccessTO.setPermissionList(permissionList);
			userAccessTO.setLoginType(loginType);
			userAccessTO.setLoginSource(source);
			
			redisUserTokenService.saveLoginAccessToken(loginType, loginName, userAccessTO);
			logger.info("doAuthorization===5== took " + (System.currentTimeMillis() - tm));
			tm = System.currentTimeMillis();

			LogUtils.saveLog(Servlets.getRequest(), "系统登录", tempUser.getId());
			logger.info("doAuthorization===6== took " + (System.currentTimeMillis() - tm));
			result.setAccessToken(accessToken);
			result.setAccessSecretKey(encryptLoginName(loginName, accessToken, loginType, source, ""));

			result.setMenuHome(saasHome);
			result.setUserName(authResult.getUser().getName());
			result.setUserLastName(authResult.getUser().getLastName()); // added for v2.0.7 chenbl KNZT-6595
			// added for v1.6.9 KNZT-3009
			result.setUserId(authResult.getUser().getId());
			result.setLoginName(loginName);
			result.setRefreshToken(refreshToken);
//			result.setMenuList(saasMenuList);
			result.setRefreshIntervalSec(userService.getLoginAccessTokenRefreshIntervalSecByLoginName(loginName)); // updated for v2.0.4 chenbl KNZT-5442
			result.setPermissionList(permissionList);
			result.setReversalPermissionList(reversalPermissionList);
			result.setDataScope(authResult.getUser().getDataScope());
			result.setMainUserFlag(StringUtils.equals(authResult.getUser().getId(), authResult.getUser().getCompany().getMainUserId()));
			result.setModule(UserUtils.getUserModule());
			result.setAcctType(authResult.getUser().getCompany().getType()+"");
			// added for v1.9.5 KNZT-4515 登录用户唯一标识
			result.setLoginId(StringUtils.left(authResult.getUser().getCompany().getId(), 13) + "-" + StringUtils.left(authResult.getUser().getId(), 20));
			//added for V2.3.2 KNZT-8218 返回userId，用于BI埋点分析
			result.setBizLinkId(StringUtils.getRandomChar(5)+authResult.getUser().getId()+StringUtils.getRandomChar(3));
			// added for lvcy v2.0.0 KNZT-5142 增加指纹id
			String picId = StringUtils.substringAfter(authResult.getUser().getEncryptUserId(), ",");
			result.setPicId(picId);

			// added for v1.5.8 KNZT-2616 前端会缓存计费标准chargeMode，如果国际版CRM修改公司计费标准，前端不会及时更新。若有逻辑新依赖该字段，需要评估影响。
			if (Objects.nonNull(authResult.getUser().getCompany())) {
//				result.setChargeMode(authResult.getUser().getCompany().getChargeMode()); // updated for v.1.8.2 KNZT-3441 
				result.setShowCredit(authResult.getUser().getCompany().getShowCredit()); // added for v1.8.2 KNZT-3680
				result.setCanUpgrade(authResult.getUser().getCompany().getCanUpgrade()); // added for v1.8.2 KNZT-3700
				result.setCompanyId(authResult.getUser().getCompany().getId());
				result.setCompanyType(authResult.getUser().getCompany().getType());
				result.setEnableApi(authResult.getUser().getCompany().getEnableApi());
				result.setPayType(authResult.getUser().getCompany().getPayType());
				PwdSecurityPolicyEnum securityPolicyEnum = userService.getCompPwdSecurityPolicy(authResult.getUser().getCompany().getId());
				if (securityPolicyEnum != null) {
					result.setSecurityPolicy(securityPolicyEnum.getCode());
				}
			}
			// added for v2.1.5 fengsw KNZT-6454
			SysUserExt beginningGuide = sysUserExtService.getByUserIdAndType(result.getUserId(), Constants.UserExtType.NEW_VERSION_GUIDE);
			if (Objects.nonNull(beginningGuide) && Constants.YES.equals(beginningGuide.getValue())) {
				result.setBeginningGuide(false);
			}
			result.setEnableMfa(userService.checkNeedMfa(loginName));
			logger.info("[token] doAuthorization userid:" + userAccessTO.getUserId() + ";at:" + accessToken + ";rt:" + refreshToken);

			return result;
		} catch(Throwable e) {
			logger.error("loginerror", e);
			throw new MessageException("err.access");
		} finally {
			WebContextHolder.setCurrentUser(null);
			WebContextHolder.setProLoginType(null);
		}
	}
	
	
	public boolean refreshUserPmsnAndMenu(String userId, String loginName, String clientId, String accessToken, String loginType) {
		User user = UserUtils.get(userId);
		try {
			if(user != null) {
				WebContextHolder.setCurrentUser(user);
				WebContextHolder.setProLoginType(loginType);
				List<RedisSaasMenuTO> menuList = null;
				Menu m = new Menu();
				m.setUserId(userId);
				List<Menu> menuDbList = userService.findMenuByUserId(m);
				menuList = userService.convertMenuList2Redis(menuDbList);
				
				SaasUserPermissionTO permissionResult = getUserPermission(user, loginType, menuList);
				List<String> permissionList = permissionResult.getPermissionList();
				List<String> reversalPermissionList = permissionResult.getReversalPermissionList();
				
				RedisUserAccessTO userAccessTO = redisUserTokenService.getLoginAccessToken(loginType, loginName);
				if(userAccessTO != null) {
					if(StringUtils.equals(userAccessTO.getAccessToken(), accessToken)) {
						userAccessTO.setPermissionList(permissionList);
						userAccessTO.setReversalPermissionList(reversalPermissionList);

						redisUserTokenService.saveLoginAccessToken(loginType, loginName, userAccessTO);
						UserUtils.setUserSession(UserUtils.CACHE_MENU_LIST, menuList);

						logger.info(" RefreshUserPmsnAndMenu successfully. [" + loginName + "] menuList size=" + menuList.size() + ";pmsn size=" + permissionList.size());
						return true;
					}
				}
			}
		} catch(Throwable e) {
			logger.error("", e);
		} finally {
			WebContextHolder.setCurrentUser(null);
			WebContextHolder.setProLoginType(null);
		}
		return false;
	}


	public boolean saveLastAccessTime2UserAccessSession(String loginName, String loginType, String lastAccessTimeStr) {
		try {
			RedisUserAccessTO userAccessTO = redisUserTokenService.getLoginAccessToken(loginType, loginName);
			if(userAccessTO != null) {
				Long loginTime = DateUtils.getTimespanFromDateTime(lastAccessTimeStr);
				if(loginTime == null || loginTime.longValue() < 1000) {
					return false;
				}
				userAccessTO.setLastAccessTime(loginTime);
				redisUserTokenService.saveLoginAccessToken(loginType, loginName, userAccessTO);
				return true;
			}
		} catch(Exception e) {
			logger.error("", e);
			return false;
		}
		return true;
	}

	public boolean saveLoginTime2UserAccessSession(String loginName, String loginType, String loginTimeStr) {
		try {
			RedisUserAccessTO userAccessTO = redisUserTokenService.getLoginAccessToken(loginType, loginName);
			if(userAccessTO != null) {
				Long loginTime = DateUtils.getTimespanFromDateTime(loginTimeStr);
				if(loginTime == null || loginTime.longValue() < 1000) {
					return false;
				}
				userAccessTO.setLoginTime(loginTime);
				redisUserTokenService.saveLoginAccessToken(loginType, loginName, userAccessTO);
				return true;
			}
		} catch(Exception e) {
			logger.error("", e);
			return false;
		}
		return true;
	}

	
	public SaasUserPermissionTO getUserPermission4Front() {
		User user = UserUtils.getUser();
		String loginType = WebContextHolder.getProLoginType();
		Menu m = new Menu();
		m.setUserId(user.getId());
		List<Menu> menuDbList = userService.findMenuByUserId(m);
		List<RedisSaasMenuTO> menuList = userService.convertMenuList2Redis(menuDbList);
		return getUserPermission(user, loginType, menuList);
	}
	
	public SaasUserPermissionTO getUserPermission(User user, String loginType, List<RedisSaasMenuTO> menuList) {
		SaasUserPermissionTO result = new SaasUserPermissionTO();
		List<String> permissionList = new ArrayList<>();
		List<String> reversalPermissionList = new ArrayList<>();
		permissionList.add("user");
		// added for v1.5.8 KNZT-2614
		if(CompDataSaveEnum.NO.getCode().equals(user.getCompany().getDataSave())){
			permissionList.add(Constants.Permission.COMP_RECORD_HIDDEN);
		}

		String [] permissionArr = null;
		for (RedisSaasMenuTO menu : menuList){
			if (StringUtils.isNotBlank(menu.getPermission())){
				// 添加基于Permission的权限信息
				permissionArr = StringUtils.split(menu.getPermission(),",");
				for (String permission : permissionArr){
					if(StringUtils.startsWith(permission, "rvrs:")) {
						if(!reversalPermissionList.contains(permission)) {
							reversalPermissionList.add(permission);
						}
					} else {
						if(!permissionList.contains(permission)) {
							permissionList.add(permission);
						}
					}
				}
			}
		}
		
		result.setPermissionList(permissionList);

		result.setReversalPermissionList(reversalPermissionList);
		return result;
	}
	
	public SaasMenuAndHomeTO getUserMenuAndHomeByModule(String href) {
		SaasMenuAndHomeTO menuAndHome = new SaasMenuAndHomeTO();
		List<RedisSaasMenuTO> menuList = UserUtils.getRedisSaasMenuList();

		SaasMenuHome menuHome = getUserMenuHome(menuList);
		menuAndHome.setMenuHome(menuHome);

		//移除menuList中href为空的菜单,并且返回对象是重新组织父子菜单数据
		List<SaasMenuTO> saasMenuList = userService.regroupMenu2ParentChild(menuList);
		if(saasMenuList != null) {
			if(menuHome.getHeaderList() != null) {
				for(MenuHome mh : menuHome.getHeaderList()) {
					if(StringUtils.isNotBlank(mh.getMenuHref())) {
					for(SaasMenuTO saasMenu : saasMenuList) {
						if(StringUtils.equals(mh.getMenuHref(), saasMenu.getHref())) {
							mh.getMenuList().add(saasMenu);
							break;
						}
					}
					}
				}
			}
			if(menuHome.getModuleHomeList() != null) {
				for(MenuHome mh : menuHome.getModuleHomeList()) {
					if(mh.getMenuList().size() == 0 && StringUtils.isNotBlank(mh.getMenuHref())) {
						for(SaasMenuTO saasMenu : saasMenuList) {
							if(StringUtils.equals(mh.getMenuHref(), saasMenu.getHref())) {
								mh.getMenuList().add(saasMenu);
								break;
							}
						}
					}
				}
			}
			
			for(SaasMenuTO saasMenuTO : saasMenuList) {
				if("main/setting_ctr".equals(saasMenuTO.getHref())) {
					menuAndHome.getSettingMenuList().add(saasMenuTO);
					break;
				}
			}
		}
		
		
		if(StringUtils.isBlank(href)) {
			removeMenuHome(menuAndHome.getMenuHome(), null, UserUtils.getUserCompanyId());
			return menuAndHome;
		}
		//查找对应菜单后, 移除其他菜单
		Iterator<SaasMenuTO> it = saasMenuList.iterator();
		boolean find = false;
		SaasMenuTO menuTO = null;
		while(it.hasNext()) {
			menuTO = it.next();
			if(find) {
				it.remove();
				continue;
			}
			String jsonStr = JSON.toJSONString(menuTO);
			if(jsonStr.indexOf("\"" + href + "\"") != -1) {
				find = true;
			} else {
				it.remove();
			}
		}

		menuAndHome.setMenuList(saasMenuList);

		removeMenuHome(menuAndHome.getMenuHome(), saasMenuList, UserUtils.getUserCompanyId());
		return menuAndHome;
	}

	
	public SaasMenuHome getUserMenuHome(List<RedisSaasMenuTO> menuList) {
		boolean hasHomePage = true;
		if(menuList == null) {
			menuList = UserUtils.getRedisSaasMenuList();
		}
		List<MenuHome> menuHomeList = getMenuHomeCacheList();
		if(menuHomeList == null || menuHomeList.isEmpty()) {
			menuHomeList = userService.listMenuHome();
			logger.error("cache menuhome is empty");
		}
		SaasMenuHome saasHome = new SaasMenuHome();
		if(menuHomeList != null) {
			if(hasHomePage) {
				saasHome.getHeaderList().add(new MenuHome(null, "main/home", "首页", null));
			}
			MenuHome menuHome = null;
			for(MenuHome mh : menuHomeList) {

					if(("M".equals(mh.getAreaInd())) || "A".equals(mh.getAreaInd())) {
						if("nomenu/dataapi".equals(mh.getMenuHref())) {
							if(UserUtils.enableApi()) {
								menuHome = new MenuHome();
								menuHome.setMenuHref(mh.getMenuHref());
								menuHome.setModuleRemarks(mh.getModuleRemarks());
								menuHome.setModuleName(mh.getModuleName());
								menuHome.setSign(mh.getSign());
								menuHome.setGroupId(mh.getGroupId());
								addMenuHome2List(saasHome.getAdvancedAppList(), menuHome);
							}
						} else {
							for(RedisSaasMenuTO menu : menuList) {
								if(StringUtils.equals(menu.getHref(), mh.getMenuHref())) {
									menuHome = new MenuHome();
									menuHome.setMenuHref(mh.getMenuHref());
									menuHome.setModuleRemarks(mh.getModuleRemarks());
									menuHome.setSign(mh.getSign());
									/*if(StringUtils.isBlank(mh.getModuleName())) {
										menuHome.setModuleName(menu.getName());
									} else {*/
										menuHome.setModuleName(mh.getModuleName());
									//}
									if("M".equals(mh.getAreaInd())) {
										addMenuHome2List(saasHome.getHeaderList(), menuHome);
										addMenuHome2List(saasHome.getModuleHomeList(), menuHome);
									} else if("A".equals(mh.getAreaInd())) {
										addMenuHome2List(saasHome.getAdvancedAppList(), menuHome);
									}
									break;
								}
							}
						}
					}
//				}
			}
			
			for(MenuHome mh : menuHomeList) {
				if(("M".equals(mh.getAreaInd()))) {
					menuHome = new MenuHome();
					menuHome.setMenuHref(mh.getMenuHref());
					menuHome.setModuleRemarks(mh.getModuleRemarks());
					menuHome.setSign(mh.getSign());
					menuHome.setModuleName(mh.getModuleName());
					if(!containsInMenuHomeList(saasHome.getModuleHomeList(), menuHome)) {
						menuHome.setSign(MENU_HOME_SIGN_NOT_AVAILABLE);
						saasHome.getHeaderList().add(menuHome);
						saasHome.getModuleHomeList().add(menuHome);
					}
				}
			}
		}
		
		return saasHome;
	}
	
	private boolean containsInMenuHomeList(List<MenuHome> list, MenuHome menuHome) {
		for(MenuHome mh : list) {
			if(StringUtils.equals(mh.getAreaInd(), menuHome.getAreaInd()) && StringUtils.equals(mh.getMenuHref(), menuHome.getMenuHref())) {
				return true;
			}
		}
		return false;
	}
	
	private void addMenuHome2List(List<MenuHome> list, MenuHome menuHome) {
		boolean exist = false;
		for(MenuHome mh : list) {
			if(StringUtils.equals(mh.getAreaInd(), menuHome.getAreaInd()) && StringUtils.equals(mh.getMenuHref(), menuHome.getMenuHref())) {
				exist = true;
				break;
			}
		}
		if(!exist) {
			list.add(menuHome);
		}
	}
	
	public SaasMenuAndHomeTO getUserMenuAndHome(String href) {
		if(SysConstants.Menu.MENU_SHOW_MODE_TREE.equalsIgnoreCase(Global.getConfig("menu.show.mode"))) {
			return getUserMenuAndHomeByTree(href);
		} else {
			return getUserMenuAndHomeByModule(href);
		}
	}
	
	private SaasMenuAndHomeTO getUserMenuAndHomeByTree(String href) {
		SaasMenuAndHomeTO menuAndHome = new SaasMenuAndHomeTO();
		List<RedisSaasMenuTO> menuList = UserUtils.getRedisSaasMenuList();

		//移除menuList中href为空的菜单,并且返回对象是重新组织父子菜单数据
		List<SaasMenuTO> saasMenuList = userService.regroupMenu2ParentChild(menuList);
		if(saasMenuList != null) {			
			for(SaasMenuTO saasMenuTO : saasMenuList) {
				if(MENU_HREF_SETTING_CENTER.equals(saasMenuTO.getHref())) {
					menuAndHome.getSettingMenuList().add(saasMenuTO);
					break;
				}
			}
			
			Iterator<SaasMenuTO> it = saasMenuList.iterator();
			SaasMenuTO menuTO = null;
			while(it.hasNext()) {
				menuTO = it.next();
				if(MENU_HREF_SETTING_CENTER.equals(href)) {
					if(!MENU_HREF_SETTING_CENTER.equals(menuTO.getHref())) {
						it.remove();
					}
				} else if(MENU_HREF_SETTING_CENTER.equals(menuTO.getHref()))  {
					it.remove();
				}
			}
			
			menuAndHome.setMenuList(saasMenuList);
		}
		
		return menuAndHome;
	}
	

	/**
	 * 返回用户名##密码##时间戳
	 * @param keepToken
	 * @return
	 * @throws MessageException
	 */
//	public String decryptKeepToken(String keepToken) throws MessageException {
//		try {
//			String decrypt = AesUtil.decrypt(keepToken, LOGIN_KEEP_AES_KEY, LOGIN_KEEP_AES_IV);
//			if(StringUtils.isNotBlank(decrypt) && decrypt.indexOf(LOGIN_KEEP_SIGN_KEY) != -1) {
//				return decrypt.substring(0, decrypt.indexOf("@@"));
//			}
//			throw new MessageException("获取登录保持标识失败");
//		} catch (Exception e) {
//			logger.error(WebContextHolder.getClientIp() + ":keepToken=" + keepToken + ":" + e.getMessage());
//			throw new MessageException("获取登录保持标识失败");
//		}
//	}
	
	private static final String SPEC_SPLIT = "@!@";
	
	private String encryptLoginName(String loginName, String accessToken, String loginType, String source, String extraLoginCd) throws MessageException {
		try {
			StringBuilder result = new StringBuilder();
			result.append(loginName).append("#");
			result.append(System.currentTimeMillis()).append("#");
			result.append(loginType).append("#");
			result.append(accessToken).append("#");
			result.append(source).append("#");
			result.append(IdGenUtil.uuid()).append("#");
			result.append(LOGIN_NAME_SIGN_KEY);
			result.append(SPEC_SPLIT);
			result.append(extraLoginCd);
//			String key = loginName + "#" + System.currentTimeMillis() + "#" + loginType + "#" + accessToken + "#" + source + "#" + IdGenUtil.uuid() + "#" + LOGIN_NAME_SIGN_KEY;
			return AesUtil.encrypt(result.toString(), LOGIN_NAME_AES_KEY, LOGIN_NAME_AES_IV);
		} catch (Exception e) {
			logger.error("loginerroe", e);
			throw new MessageException("err.access");
		}
	}
	
	private SaasAccessSecretKeyTO decryptLoginName(String accessSecretKey) throws MessageException {
		try {
			String decrypt = AesUtil.decrypt(accessSecretKey, LOGIN_NAME_AES_KEY, LOGIN_NAME_AES_IV);
			if(StringUtils.isNotBlank(decrypt) && decrypt.indexOf(LOGIN_NAME_SIGN_KEY) != -1) {
				String []arr = decrypt.split("#");
				
				if(arr.length >= 5) {
					SaasAccessSecretKeyTO result = new SaasAccessSecretKeyTO();
					result.setLoginName(arr[0]);
					result.setLoginTime(NumberUtils.isLong(arr[1]) ? Long.parseLong(arr[1]) : 0);
					result.setLoginType(arr[2]);
					result.setAccessToken(arr[3]);
					if(StringUtils.length(arr[4]) < 20) {
						result.setSource(arr[4]);
					}
					
					if(decrypt.indexOf(SPEC_SPLIT) != -1) {
						result.setExtraLoginCd(decrypt.substring(decrypt.indexOf(SPEC_SPLIT) + SPEC_SPLIT.length()));
					}
					return result;
				}
			}
			logger.error("login sign error");
			throw new MessageException("err.access");
		} catch (Exception e) {
			logger.error(WebContextHolder.getClientIp() + ":accessSecretKey=" + accessSecretKey + ":" + e.getMessage());
			throw new MessageException("err.access");
		}
	}

	
	/**
	 * 
	 * tm + expiredInterval < 当前时间, 则视为过期
	 * tm > 当前时间  + expiredInterval, 则视为过期
	 * @param tm
	 * @param expiredInterval
	 * @return
	 */
	private boolean checkTimeout(String tm, String expiredInterval) {
		if(StringUtils.isNotBlank(tm)) {
			try {
				long paramTm = Long.parseLong(tm);
				long curTm = System.currentTimeMillis() / 1000;
				long expiredInv = Long.parseLong(expiredInterval);

				if(!((paramTm + expiredInv < curTm) || paramTm > curTm + expiredInv)) {
					return false;
				}
			} catch(NumberFormatException e) {
				//忽略传入错误的时间戳
			}  catch(Exception e) {
				logger.error("", e);
			}
		}
		return true;
	}

	// removed for v2.0.4 chenbl KNZT-5371
	/*private AuthResult doLoginAccount(String clientId, String loginName, String pwd, String keep, String loginType, String source, String verifyCode) throws MessageException {
		if(StringUtils.isBlank(loginName)) {
			logger.error("err.system.account.mobile.required");
			throw new MessageException("err.access");
		}
		if(StringUtils.isBlank(pwd)) {
			logger.error("err.system.account.password.required");
			throw new MessageException("err.access");
		}
		
		User user = userService.getByLoginName(loginName);
		
		if (user != null) {
			if ("CRM".equals(source)) {
				if (StringUtils.isBlank(user.getEmail())) {
					logger.warn(String.format("account: %s, email doesn't exist, ip: %s", loginName, StringUtils.getRemoteAddr(Servlets.getRequest())));
					throw new MessageException("err.email.invalid");
				}
				saasEmailService.validateEmailCode(user.getEmail(), verifyCode);
			}
			userService.checkFailCount4AccountLogin(loginName);
			userService.checkUserValid4Login(user);
			// added for V2.0.8 ZS-499 【产业招商云平台II】【WEB】-优化-用户登录与企业账号编辑时，增加账号有效期校验
			userService.checkCompanyValid(user.getCompany());

			boolean validPwd = UserService.validatePassword(pwd, user.getPassword());
			if(!validPwd) {
				int remainderCount = userService.addFailCount4AccountLogin(loginName);
				// updated for v1.7.6 KNZT-3349
				String message = "Incorrect email or password";
				if(remainderCount >= 0) {
					message += ", you have " + remainderCount + " attempts remaining today.";
				}
				throw new MessageException("msg:" + message);
			}
			AuthResult result = new AuthResult();
			result.setUser(user);
			return result;
		} else {
			logger.warn(" [loginmobile] account not exist " + loginName + ";" + StringUtils.getRemoteAddr(Servlets.getRequest()));
			throw new MessageException("err.system.account.not.exist");
		}
	}*/
	
	public void doLogout(String loginType, String ...loginNameArr) throws MessageException {
		if(loginNameArr == null || loginNameArr.length == 0) {
			loginNameArr = new String[] {UserUtils.getUserLoginName()};
		}
		
		for(String loginName : loginNameArr) {
			clearLoginNameFromRedis(loginType, loginName);
		}
	}
	
	public List<MenuHome> getMenuHomeCacheList() {
		return userService.listMenuHome();
	}


	/**
	 * 生成第三方加密Token
	 * @param loginName
	 * @param secretKey
	 * @return
	 * @throws Exception
	 */
	public String generateToken(String loginName,String secretKey) throws Exception {
		return generateToken(loginName, null, null, null,secretKey);
	}

	/**
	 * 生成token
	 * @param loginName
	 * @param role
	 * @param name
	 * @param email
	 * @param secretKey
	 * @throws Exception
	 */
	public String generateToken(String loginName, String role, String name, String email,String secretKey) throws Exception {
		if(StringUtils.isBlank(loginName)) {
			logger.error("loginName is null");
			throw new MessageException("err.access");
		}
		if(StringUtils.isBlank(secretKey)) {
			logger.error("secretKey is null");
			throw new MessageException("err.access");
		}
		Map<String, String> result = new HashMap<String, String>();
		result.put("loginName", loginName);
		if(StringUtils.isBlank(name)) {
			result.put("name", loginName);
		} else {
			result.put("name", name);
		}
		if(StringUtils.isNotBlank(role)) {
			if(!(ROLE_ADMIN.equals(role) || ROLE_STAFF.equals(role))) {
				logger.error("role is null");
				throw new MessageException("err.access");
			}
		} else {
			role = ROLE_STAFF;
		}
		result.put("role", role);
		result.put("email", email == null ? "" : email);
		result.put("timespan", String.valueOf(System.currentTimeMillis()/1000));

		String content = JSON.toJSONString(result);
		String aesIV = (secretKey == null? "": secretKey.substring(0,16).toLowerCase());
		return AesUtil.encrypt(content, AES_KEY, aesIV);
	}

	// added for v1.6.0 KNZT-2666
	@DingErrorMsg(referenceNoPrefix = "logoutUser-")
	public void logoutUserByCompanyId(String companyId, String companyName) throws MessageException {
		List<User> userList = userService.findUserByCompanyId4Select(companyId);
		List<String> failedLoginNameList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(userList)) {
			for (User user : userList) {
				try {
					clearLoginNameFromRedis("", user.getLoginName());
				} catch (Exception e) {
					logger.error("登出失败" + user.getLoginName(), e);
					failedLoginNameList.add(user.getLoginName());
				}
			}
		}
		if (CollectionUtils.isNotEmpty(failedLoginNameList)) {
			logger.error("companyId: " + companyId + ", companyName: " + companyName + " logout fail for " + failedLoginNameList.toString());
			throw new MessageException("err.logout.user");
		}
	}

	// 踢掉指定公司里的除了指定用户的其他用户的登录态
	@DingErrorMsg(referenceNoPrefix = "logoutUser-")
	public void logoutUserByCompanyIdExceptSelf(String companyId, String selfUserId) throws MessageException {
		List<User> userList = userService.findUserByCompanyId4Select(companyId);
		List<String> failedLoginNameList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(userList)) {
			for (User user : userList) {
				if (StringUtils.equals(user.getId(), selfUserId)) {
					continue;
				}
				try {
					clearLoginNameFromRedis("", user.getLoginName());
				} catch (Exception e) {
					logger.error("登出失败" + user.getLoginName(), e);
					failedLoginNameList.add(user.getLoginName());
				}
			}
		}
		if (CollectionUtils.isNotEmpty(failedLoginNameList)) {
			logger.error("companyId: " + companyId + " logout fail for " + failedLoginNameList.toString());
			throw new MessageException("err.logout.user");
		}
	}

	// added for v1.6.9 KNZT-3040
	@DingErrorMsg(referenceNoPrefix = "logoutUser-")
	public void logoutUser(String loginName) throws MessageException {
		try {
			clearLoginNameFromRedis("", loginName);
		} catch (Exception e) {
			logger.error("登出失败" + loginName);
			throw new MessageException("err.logout.user");
		}
	}

	/**
	 * added for v2.0.5 fengsw KNZT-5574
	 * 校验自助账号是否有效
	 * 
	 * @param company
	 * @throws MessageException
	 */
	public void validateSelfAccountByEndDate(Company company) throws MessageException {
		if (company != null && company.getEndDate() != null) {
			Date curDt = DateUtils.getCurDate();
			if (company.getEndDate().before(curDt)) {
				if (CompTypeEnum.isSelfPay(company.getType())) {
					throw new MessageException("err.system.account.company.forbidden.client");
				}
			}
		}
	}
}
