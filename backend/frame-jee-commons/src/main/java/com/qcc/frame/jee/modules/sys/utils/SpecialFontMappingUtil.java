package com.qcc.frame.jee.modules.sys.utils;

import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.enums.TranslationMapEnum;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

// added for v1.9.3 KNZT-4312
public class SpecialFontMappingUtil {

    public static String replaceSpecialFont(String inputStr) {
        if (StringUtils.isBlank(inputStr)) {
            return inputStr;
        }
        return replaceSpecialFont(inputStr, getSpecialFontMap());
    }

    public static String replaceSpecialFont(String inputStr, Map<String, String> specialFontMap) {
        if (StringUtils.isBlank(inputStr) || MapUtils.isEmpty(specialFontMap)) {
            return inputStr;
        }
        for (Map.Entry<String, String> entry : specialFontMap.entrySet()) {
            inputStr = inputStr.replaceAll(entry.getKey(), entry.getValue());
        }
        return inputStr;
    }

    public static Map<String, String> getSpecialFontMap() {
        return TranslationMapUtils.getMap(TranslationMapEnum.SPECIAL_FONT_MAP);
    }
}
