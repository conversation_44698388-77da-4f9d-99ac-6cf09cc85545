package com.qcc.frame.jee.modules.sys.entity.mapping;

/**
 * 用户信息，包括分组、角色
 * <AUTHOR>
 * @datetime 26/5/2025 5:18 下午
 */
public class UserWithGroupRoleTO {
    private String userId;
    private String userName;
    private String userLastName;
    private String userEmail;
    private String compRoleId;
    private String compRoleDesc;
    private String groupId;
    private String groupName;
    private String mainUserId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public String getUserLastName() {
        return userLastName;
    }

    public void setUserLastName(String userLastName) {
        this.userLastName = userLastName;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getCompRoleId() {
        return compRoleId;
    }

    public void setCompRoleId(String compRoleId) {
        this.compRoleId = compRoleId;
    }

    public String getCompRoleDesc() {
        return compRoleDesc;
    }

    public void setCompRoleDesc(String compRoleDesc) {
        this.compRoleDesc = compRoleDesc;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getMainUserId() {
        return mainUserId;
    }

    public void setMainUserId(String mainUserId) {
        this.mainUserId = mainUserId;
    }
}
