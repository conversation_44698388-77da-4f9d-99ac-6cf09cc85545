package com.qcc.frame.jee.modules.sys.entity;


import com.qcc.frame.jee.commons.persistence.DataEntity;

public class SysSecurityConfig extends DataEntity<SysSecurityConfig> {
	
	private String secType;
	private String valueType;
	private String value;
	private String remark;

	public String getSecType() {
		return secType;
	}
	public void setSecType(String secType) {
		this.secType = secType;
	}
	public String getValueType() {
		return valueType;
	}
	public void setValueType(String valueType) {
		this.valueType = valueType;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
}
