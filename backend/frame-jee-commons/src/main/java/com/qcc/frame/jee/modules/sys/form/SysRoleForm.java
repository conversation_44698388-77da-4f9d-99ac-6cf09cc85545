package com.qcc.frame.jee.modules.sys.form;

import com.qcc.frame.jee.base.AbstractForm;
import com.qcc.frame.jee.modules.sys.condition.SysRoleCondition;
import com.qcc.frame.jee.modules.sys.entity.Role;

import java.util.List;

public class SysRoleForm extends AbstractForm<Role, SysRoleCondition> {
    private String id;
    private String name;        // 角色名称
    private String enname;        // 英文名称
    private String roleType;    // 权限类型
    private String remarks;     // 备注
    private List<String> menuIdList; // 菜单ID

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnname() {
        return enname;
    }

    public void setEnname(String enname) {
        this.enname = enname;
    }

    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<String> getMenuIdList() {
        return menuIdList;
    }

    public void setMenuIdList(List<String> menuIdList) {
        this.menuIdList = menuIdList;
    }
}
