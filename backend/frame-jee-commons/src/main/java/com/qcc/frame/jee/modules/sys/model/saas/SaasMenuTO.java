package com.qcc.frame.jee.modules.sys.model.saas;

import java.util.ArrayList;
import java.util.List;

public class SaasMenuTO implements java.io.Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7146007097285861217L;
	private String id;
	private String title;
	private String href;
	private String permission;
	private String isShow;
	
	private List<SaasMenuTO> subList = new ArrayList<>();

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}

	public String getPermission() {
		return permission;
	}

	public void setPermission(String permission) {
		this.permission = permission;
	}

	public String getIsShow() {
		return isShow;
	}

	public void setIsShow(String isShow) {
		this.isShow = isShow;
	}

	public List<SaasMenuTO> getSubList() {
		return subList;
	}

	public void setSubList(List<SaasMenuTO> subList) {
		this.subList = subList;
	}
	
	
}
