package com.qcc.frame.jee.modules.sys.excel;

import com.qcc.frame.jee.modules.sys.excel.model.ExportExcelSheetTO;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.File;
import java.util.LinkedHashSet;

public class ExportExcelSheetsUtil {

	/**
	 * 将多个数据集导出到不同Sheet, sheetName不能相同
	 * @param sheetList
	 * @return
	 */
	public static File exportDataWithSheets(LinkedHashSet<ExportExcelSheetTO> sheetList) {
		ExportExcel exportExcel = exportDataWithSheetsBase(sheetList);
		return exportExcel.getExcelFile();
	}

	private static ExportExcel exportDataWithSheetsBase(LinkedHashSet<ExportExcelSheetTO> sheetList) {
		ExportExcel exportExcel = null;
		SXSSFWorkbook wb = null;
		for(ExportExcelSheetTO sheetTO : sheetList) {
			if(wb == null) {
				exportExcel = new ExportExcel(sheetTO.getSheetName(), sheetTO.getTitle(), sheetTO.getExportClazz());
				exportExcel.setDataList(sheetTO.getDataList());
				wb = exportExcel.getWookbook();
			} else {
				exportExcel = new ExportExcel(wb, sheetTO.getSheetName(), sheetTO.getTitle(), sheetTO.getExportClazz());
				exportExcel.setDataList(sheetTO.getDataList());
			}
		}
		return exportExcel;
	}
}

