package com.backend.common.googleapi.model;

import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class PlacesAutoCompleteTO {

    private String placeId;
    private String text;

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static List<PlacesAutoCompleteTO> buildFrom(PlacesAutoCompleteResp resp) {
        if (resp == null || CollectionUtils.isEmpty(resp.getSuggestions())) {
            return new ArrayList<>();
        }
        return resp.getSuggestions().stream().map(suggestion -> {
                    PlacesAutoCompleteTO to = new PlacesAutoCompleteTO();
                    PlacesAutoCompleteResp.PlacePrediction placePrediction = MappingUtils.getValue(suggestion, PlacesAutoCompleteResp.Suggestion::getPlacePrediction);
                    to.setPlaceId(MappingUtils.getValue(placePrediction, PlacesAutoCompleteResp.PlacePrediction::getPlaceId));
                    PlacesAutoCompleteResp.Text text = MappingUtils.getValue(placePrediction, PlacesAutoCompleteResp.PlacePrediction::getText);
                    to.setText(MappingUtils.getValue(text, PlacesAutoCompleteResp.Text::getText));
                    return to;
                }).filter(to -> StringUtils.isNotBlank(to.getPlaceId()) && StringUtils.isNotBlank(to.getText()))
                .collect(Collectors.toList());
    }
}
