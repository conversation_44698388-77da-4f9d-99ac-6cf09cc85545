package com.backend.common.modules.common.service;

import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.global.gateway.corp.form.ListCorpBasicInfoForm;
import com.backend.common.global.gateway.corp.model.CorpBasicInfoTO;
import com.backend.common.modules.common.form.TransWrapper;
import com.qcc.frame.commons.ienum.AliyunTranslateSourceTypeEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.PinyinUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class CommonDataService {

    private static final Logger logger = LoggerFactory.getLogger(CommonDataService.class);
    @Autowired
    private TranslaterService translaterService;

    /**
     * 英文后置处理
     * 1、非实体, 直接机翻
     * 2、实体, 人员类型, 允许被拼音翻译的情况，走拼音翻译
     * 3、实体, (企业类型 or 空实体类型) and (keyNo为空 or keyNo为大陆企业), 机翻
     * 4、其他后置处理
     *
     * @param transWrapper
     */
    public void enPostProcessor(TransWrapper transWrapper) {
        if (transWrapper == null || transWrapper.isEmpty()) {
            return;
        }

        // 初始化机器翻译标记
        transWrapper.entryList().forEach(entry -> entry.setMachineTranslateFlag(false));

        // 从数据库查询填充英文(实体, 且不为人员, 且keyNo不为空) begin
        List<TransWrapper.Entry<?>> entriesNeedFillFromDb = new ArrayList<>();
        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            String enText = entry.getEnText();
            if (StringUtils.isNotBlank(enText)) {
                continue; // 英文名不为空, 跳过
            }
            String keyNo = entry.getKeyNo();
            // 实体, 且不为人员, 且keyNo不为空
            if (entry.getIfEntity() && !entry.isPerson() && StringUtils.isNotBlank(keyNo)) {
                entriesNeedFillFromDb.add(entry);
            }
        }
        if (CollectionUtils.isNotEmpty(entriesNeedFillFromDb)) {
            Set<String> keyNos = MappingUtils.getSet(entriesNeedFillFromDb, TransWrapper.Entry::getKeyNo);
            List<CorpBasicInfoTO> corpBasicInfoTOS = CorpGatewayInterface.listBasicInfo(ListCorpBasicInfoForm.build(keyNos));
            Map<String, String> keyNo2NameEnMap = MappingUtils.getMap(corpBasicInfoTOS, CorpBasicInfoTO::getKeyNo, CorpBasicInfoTO::getNameEnOriginal);
            for (TransWrapper.Entry<?> entry : entriesNeedFillFromDb) {
                String enText = keyNo2NameEnMap.get(entry.getKeyNo());
                if (StringUtils.isNotBlank(enText)) {
                    entry.setEnText(enText);
                }
            }
        }
        // 从数据库查询填充英文(实体, 且不为人员, 且keyNo不为空) end

        List<TransWrapper.Entry<?>> entriesNeedPinyin = new ArrayList<>();
        List<TransWrapper.Entry<?>> entriesNeedMachineTranslate = new ArrayList<>();
        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            detectTransType(transWrapper, entry, entriesNeedPinyin, entriesNeedMachineTranslate);
        }

        // 处理需要拼音翻译的项 begin
        for (TransWrapper.Entry<?> entry : entriesNeedPinyin) {
            String localText = entry.getLocalText();
            if (StringUtils.isNotBlank(localText)) {
                List<String> strList = StringUtils.split2ListV2(localText);
                String enText = strList.stream()
                        .filter(StringUtils::isNotBlank)
                        .map(PinyinUtils::chineseNameToPinyin)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", "));
                entry.setEnText(enText);
                entry.setMachineTranslateFlag(true);
            }
        }
        // 处理需要拼音翻译的项 end

        // 处理需要机器翻译的项 begin
        // 按照机翻源语言分组, 每组源语言单独翻译
        Map<String, List<TransWrapper.Entry<?>>> sourceLanguage2EntriesMap = new HashMap<>();
        for (TransWrapper.Entry<?> entry : entriesNeedMachineTranslate) {
            String sourceLanguage = entry.getSourceArea() != null ? AliyunTranslateSourceTypeEnum.getSourceLanguageByNationCode(entry.getSourceArea().getNameCode()) : null;
            List<TransWrapper.Entry<?>> thisEntries = sourceLanguage2EntriesMap.getOrDefault(sourceLanguage, new ArrayList<>());
            thisEntries.add(entry);
            sourceLanguage2EntriesMap.put(sourceLanguage, thisEntries);
        }
        for (Map.Entry<String, List<TransWrapper.Entry<?>>> sourceLanguage2EntriesMapEntry : sourceLanguage2EntriesMap.entrySet()) {
            String sourceLanguage = sourceLanguage2EntriesMapEntry.getKey();
            List<TransWrapper.Entry<?>> thisEntries = sourceLanguage2EntriesMapEntry.getValue();
            Set<String> textSet = thisEntries.stream()
                    .map(TransWrapper.Entry::getLocalText)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(textSet)) {
                Map<String, String> text2EnTextMap = translaterService.getEnglishTextBatchSimpleWithTransType(textSet, sourceLanguage, null, null, null);
                if (MapUtils.isNotEmpty(text2EnTextMap)) {
                    for (TransWrapper.Entry<?> entry : thisEntries) {
                        String localText = entry.getLocalText();
                        if (StringUtils.isNotBlank(localText)) {
                            entry.setEnText(text2EnTextMap.get(localText));
                            entry.setMachineTranslateFlag(true);
                        }
                    }
                }
            }
        }
        // 处理需要机器翻译的项 end

        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            String localText = entry.getLocalText();
            String enText = entry.getEnText();
            // 本地语言和英语相同(本地语言字段包含英文字段、或本地语言本身就是英文等)
            boolean ifLocalTextEnglishSame = ifLocalTextEnglishSame(localText, enText);
            if (ifLocalTextEnglishSame) {
                entry.setMachineTranslateFlag(false); // 本地语言和英语相同, 机翻标记设为false
                if (entry.ifReplace()) {
                    // 如果是将本地语言翻译成英文替换原字段的 do nothing
                } else {
                    entry.setEnText(null);
                }
            }

            if (entry.isCorp()) {
                // 企业实体 英文改为纯大写
                enText = entry.getEnText(); // 重新获取更新后的英文
                if (StringUtils.isNotBlank(enText)) {
                    entry.setEnText(StringUtils.upperCase(enText));
                } else if (ifLocalTextEnglishSame) {
                    entry.setLocalText(StringUtils.upperCase(entry.getLocalText()));
                }
            }
        }
    }

    private static void detectTransType(TransWrapper transWrapper,
                                        TransWrapper.Entry<?> entry,
                                        List<TransWrapper.Entry<?>> entriesNeedPinyin,
                                        List<TransWrapper.Entry<?>> entriesNeedMachineTranslate) {
        String localText = entry.getLocalText();
        String enText = entry.getEnText();
        if (StringUtils.isNotBlank(enText) || StringUtils.isBlank(localText)) {
            return; // 英文名不为空 or 本地语言为空, 跳过
        }
        GlobalAreaEnum sourceArea = transWrapper.getSourceArea();
        if (sourceArea == null) {
            return;
        }
        // todo 这里逻辑需要跟王强 伟臣确认
        if (sourceArea == GlobalAreaEnum.SG
                || sourceArea == GlobalAreaEnum.NZ
                || sourceArea == GlobalAreaEnum.MY) {
            // do nothing
        } else if (sourceArea == GlobalAreaEnum.CN
                || sourceArea == GlobalAreaEnum.HK
                || sourceArea == GlobalAreaEnum.TW) {
            if (entry.isPerson()) {
                if (!StringUtils.isContainEnglish(localText)) {
                    if (StringUtils.isContainPunctuation(localText)
                            || localText.length() >= 5) {
                        // 包含符号 or 长度大于等于5
                        entriesNeedMachineTranslate.add(entry);
                    } else {
                        entriesNeedPinyin.add(entry);
                    }
                }
            } else {
                if (StringUtils.isContainChinese(localText)) {
                    entriesNeedMachineTranslate.add(entry);
                }
            }
        } else {
            entriesNeedMachineTranslate.add(entry);
        }
    }

    private static boolean ifLocalTextEnglishSame(String localText, String enText) {
        if ((StringUtils.isBlank(localText) && StringUtils.isBlank(enText))) {
            return true;
        } else if (StringUtils.isNotBlank(localText) && StringUtils.isNotBlank(enText)) {
            localText = StringUtils.removeEmTags(localText);
            enText = StringUtils.removeEmTags(enText);
            return StringUtils.containsIgnoreCase(localText, enText);
        } else {
            return false;
        }
    }
}
