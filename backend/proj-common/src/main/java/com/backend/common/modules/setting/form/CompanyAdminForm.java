package com.backend.common.modules.setting.form;

import com.backend.common.entity.mapping.FunctionCountInfoTO;
import com.backend.common.entity.mapping.RoleInfoTO;
import com.backend.common.modules.api.model.ApiChargeUnitSaveTO;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.qcc.frame.jee.modules.sys.form.CompanyAddressUpdateForm;

import java.math.BigDecimal;
import java.util.List;

public class CompanyAdminForm {
	private String ids;
	private String id;
	private String companyName;
	private String loginName;
	private String phone;
	private String email;
	private int subUserNum;
	private String beginDate;
	private String endDate;
	private String proKey;
	private String proSecretKey;
	private String proMainLoginName;
	private String yunjuKey;
	private String yunjuSecretKey;
	private String loginPwd;
	// updated for v1.9.8 KNZT-4538
//	private List<FunctionCountInfoTO> functionTable;
	private BigDecimal functionTotalCount;
	private List<RoleInfoTO> roleInfoTable;
	private String shortName;//added for v1.2.8 KNZT-1346 增加英文名称
	private String openApiUserId; // 开放平台userId
	private Boolean enableOpenApi;//added for KNZT-1420
	private String businessScenario; // added for v1.3.9 KNZT-2131 应用场景
//	private String chargeMode; // added  for v1.3.9 KNZT-2101 计费标准 updated for v1.8.2 KNZT-3441
	private List<CompChargeUnitTO> compChargeUnitList; // added for v1.5.8 KNZT-2587
	private String dataSave;// added for v1.5.8 KNZT-2611
	private String showCredit; // added for v1.8.2 KNZT-3680
	private String canUpgrade; // added for v1.8.2 KNZT-3700
	private boolean innerAccount;// added for v1.6.4 KNZT-2806
	private String emailNotify;//是否邮件通知用户
	private Integer type;//账号类型
	private String trackingContent; // 跟踪记录内容 added for v1.9.0 KNZT-4093
	private List<ApiChargeUnitSaveTO> apiChargeUnitSaveTOList; // api价格服务列表 added for v1.9.7 KNZT-4708
	private Boolean enableApi; // 是否启用api服务 added for v1.9.7 KNZT-4708
	private Integer overseasOrderLimit; // 境外订单次数限制 added for v1.9.8 KNZT-4538
	private boolean enableMfa; // MFA验证策略 added for v2.0.4 chenbl KNZT-5371
	private String securityPolicy; // 密码策略 added for v2.0.4 chenbl KNZT-5442
	
	private String disableAccount;// 禁用账号 Y-是；N-撤销禁用 added for fengsw KNZT-5574
	private String uiDefaultVersion; // ui默认版本(v1:旧版本,v2:新版本) added for v2.1.7 chenbl KNZT-6477
	private String duplicateOrderRemind;
	private String category;
	private CompanyAddressUpdateForm companyAddress;
	private BigDecimal topUpLimitAmount;// added for v.2.2.7 fengsw KNZT-7785 在线充值限额
	public String getIds() {
		return ids;
	}
	public void setIds(String ids) {
		this.ids = ids;
	}
	public String getLoginPwd() {
		return loginPwd;
	}
	public void setLoginPwd(String loginPwd) {
		this.loginPwd = loginPwd;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public int getSubUserNum() {
		return subUserNum;
	}
	public void setSubUserNum(int subUserNum) {
		this.subUserNum = subUserNum;
	}
	public String getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getProKey() {
		return proKey;
	}
	public void setProKey(String proKey) {
		this.proKey = proKey;
	}
	public String getProSecretKey() {
		return proSecretKey;
	}
	public void setProSecretKey(String proSecretKey) {
		this.proSecretKey = proSecretKey;
	}
	public String getProMainLoginName() {
		return proMainLoginName;
	}
	public void setProMainLoginName(String proMainLoginName) {
		this.proMainLoginName = proMainLoginName;
	}
	public String getYunjuKey() {
		return yunjuKey;
	}
	public void setYunjuKey(String yunjuKey) {
		this.yunjuKey = yunjuKey;
	}
	public String getYunjuSecretKey() {
		return yunjuSecretKey;
	}
	public void setYunjuSecretKey(String yunjuSecretKey) {
		this.yunjuSecretKey = yunjuSecretKey;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}

//	public List<FunctionCountInfoTO> getFunctionTable() {
//		return functionTable;
//	}
//
//	public void setFunctionTable(List<FunctionCountInfoTO> functionTable) {
//		this.functionTable = functionTable;
//	}

	public BigDecimal getFunctionTotalCount() {
		return functionTotalCount;
	}

	public void setFunctionTotalCount(BigDecimal functionTotalCount) {
		this.functionTotalCount = functionTotalCount;
	}

	public List<RoleInfoTO> getRoleInfoTable() {
		return roleInfoTable;
	}

	public void setRoleInfoTable(List<RoleInfoTO> roleInfoTable) {
		this.roleInfoTable = roleInfoTable;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getOpenApiUserId() {
		return openApiUserId;
	}

	public void setOpenApiUserId(String openApiUserId) {
		this.openApiUserId = openApiUserId;
	}

	public Boolean getEnableOpenApi() {
		return enableOpenApi;
	}

	public void setEnableOpenApi(Boolean enableOpenApi) {
		this.enableOpenApi = enableOpenApi;
	}

	public String getBusinessScenario() {
		return businessScenario;
	}

	public void setBusinessScenario(String businessScenario) {
		this.businessScenario = businessScenario;
	}

	// updated for v1.8.2 KNZT-3441
//	public String getChargeMode() {
//		return chargeMode;
//	}
//
//	public void setChargeMode(String chargeMode) {
//		this.chargeMode = chargeMode;
//	}

	public List<CompChargeUnitTO> getCompChargeUnitList() {
		return compChargeUnitList;
	}

	public void setCompChargeUnitList(List<CompChargeUnitTO> compChargeUnitList) {
		this.compChargeUnitList = compChargeUnitList;
	}

	public String getDataSave() {
		return dataSave;
	}

	public void setDataSave(String dataSave) {
		this.dataSave = dataSave;
	}

	public String getShowCredit() {
		return showCredit;
	}

	public void setShowCredit(String showCredit) {
		this.showCredit = showCredit;
	}

	public String getCanUpgrade() {
		return canUpgrade;
	}

	public void setCanUpgrade(String canUpgrade) {
		this.canUpgrade = canUpgrade;
	}

	public boolean getInnerAccount() {
		return innerAccount;
	}

	public void setInnerAccount(boolean innerAccount) {
		this.innerAccount = innerAccount;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getEmailNotify() {
		return emailNotify;
	}

	public void setEmailNotify(String emailNotify) {
		this.emailNotify = emailNotify;
	}

	public String getTrackingContent() {
		return trackingContent;
	}

	public void setTrackingContent(String trackingContent) {
		this.trackingContent = trackingContent;
	}

	public boolean isInnerAccount() {
		return innerAccount;
	}

	public List<ApiChargeUnitSaveTO> getApiChargeUnitSaveTOList() {
		return apiChargeUnitSaveTOList;
	}

	public void setApiChargeUnitSaveTOList(List<ApiChargeUnitSaveTO> apiChargeUnitSaveTOList) {
		this.apiChargeUnitSaveTOList = apiChargeUnitSaveTOList;
	}

	public Boolean getEnableApi() {
		return enableApi;
	}

	public void setEnableApi(Boolean enableApi) {
		this.enableApi = enableApi;
	}

	public Integer getOverseasOrderLimit() {
		return overseasOrderLimit;
	}

	public void setOverseasOrderLimit(Integer overseasOrderLimit) {
		this.overseasOrderLimit = overseasOrderLimit;
	}

	public boolean getEnableMfa() {
		return enableMfa;
	}

	public void setEnableMfa(boolean enableMfa) {
		this.enableMfa = enableMfa;
	}

	public String getSecurityPolicy() {
		return securityPolicy;
	}

	public void setSecurityPolicy(String securityPolicy) {
		this.securityPolicy = securityPolicy;
	}

	public String getDisableAccount() {
		return disableAccount;
	}

	public void setDisableAccount(String disableAccount) {
		this.disableAccount = disableAccount;
	}

	public String getUiDefaultVersion() {
		return uiDefaultVersion;
	}

	public void setUiDefaultVersion(String uiDefaultVersion) {
		this.uiDefaultVersion = uiDefaultVersion;
	}

	public String getDuplicateOrderRemind() {
		return duplicateOrderRemind;
	}

	public void setDuplicateOrderRemind(String duplicateOrderRemind) {
		this.duplicateOrderRemind = duplicateOrderRemind;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public CompanyAddressUpdateForm getCompanyAddress() {
		return companyAddress;
	}

	public void setCompanyAddress(CompanyAddressUpdateForm companyAddress) {
		this.companyAddress = companyAddress;
	}

	public BigDecimal getTopUpLimitAmount() {
		return topUpLimitAmount;
	}

	public void setTopUpLimitAmount(BigDecimal topUpLimitAmount) {
		this.topUpLimitAmount = topUpLimitAmount;
	}
}
