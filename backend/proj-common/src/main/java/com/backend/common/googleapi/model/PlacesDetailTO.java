package com.backend.common.googleapi.model;

import com.google.common.collect.Lists;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;

import java.util.List;

@Data
public class PlacesDetailTO {

    private String addressLine1;
    private String addressLine2;
    private String country;
    private String province;
    private String city;
    private String postalCode;
}
