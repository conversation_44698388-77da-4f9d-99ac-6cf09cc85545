package com.backend.common.modules.delivery.model;

import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * added for v1.9.5 KNZT-4117
 * <AUTHOR>
 * @datetime 2024/9/2 14:03
 */
public class StatementMonthlyListTO {
    private Date issueDate;
    private String invoiceNo;
    private String statementMonth;
    private BigDecimal amountUsed;
    private String amountUsedStr;
    private String url;
    private String amountStd;


    public static StatementMonthlyListTO build(TblCompStatementMonthly statementMonthly, String amountStd) {
        StatementMonthlyListTO res = new StatementMonthlyListTO();
        res.setIssueDate(statementMonthly.getIssueDate());
        res.setInvoiceNo(statementMonthly.getInvoiceNo());
        YearMonth yearMonth = YearMonth.parse(statementMonthly.getStatementMonth().toString(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        String yearMonthEn = DateUtils.formatYearMonthEn(yearMonth, DateUtils.DATE_FORMATMMMYYYY);
        res.setStatementMonth(yearMonthEn);
        res.setAmountUsed(statementMonthly.getAmountUsed());
        res.setUrl(statementMonthly.getUrl());
        res.setAmountStd(amountStd);
        if (Objects.nonNull(statementMonthly.getAmountUsed())) {
            res.setAmountUsedStr(NumberUtils.format2ScaleWithThousandSplit(statementMonthly.getAmountUsed()));
        }
        return res;
    }

    public static List<StatementMonthlyListTO> build(List<TblCompStatementMonthly> statementMonthlyList, String amountStd) {
        if (CollectionUtils.isEmpty(statementMonthlyList)) {
            return null;
        }
        return statementMonthlyList.stream().map(k -> build(k, amountStd)).collect(Collectors.toList());
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public String getStatementMonth() {
        return statementMonth;
    }

    public void setStatementMonth(String statementMonth) {
        this.statementMonth = statementMonth;
    }

    public BigDecimal getAmountUsed() {
        return amountUsed;
    }

    public void setAmountUsed(BigDecimal amountUsed) {
        this.amountUsed = amountUsed;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAmountStd() {
        return amountStd;
    }

    public void setAmountStd(String amountStd) {
        this.amountStd = amountStd;
    }

    public String getAmountUsedStr() {
        return amountUsedStr;
    }

    public void setAmountUsedStr(String amountUsedStr) {
        this.amountUsedStr = amountUsedStr;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }
}
