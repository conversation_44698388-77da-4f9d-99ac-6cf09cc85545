package com.backend.common.modules.report_data.corp.oversea;

import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.model.OrderData4LoopResult;
import com.backend.common.modules.report_data.corp.oversea.base.AbstractReportDataService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.overseamongo.entity.QccOvsBasic;
import com.backend.common.overseamongo.entity.QccOvsBasicInfo;
import com.backend.common.overseamongo.entity.RealtimeOrderOfficialWeb;
import com.backend.common.overseamongo.model.TwRealtimeOrderOfficialWebTO;
import com.backend.common.overseamongo.service.RealtimeOrderOfficialWebService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.qcc.frame.jee.commons.utils.MappingUtils.getValue;

@Service
public class TwBasicReportDataService extends AbstractReportDataService<TwRealtimeOrderOfficialWebTO> {

    @Autowired
    private RealtimeOrderOfficialWebService realtimeOrderOfficialWebService;
    @Autowired
    private CommonDataService commonDataService;

    @Override
    public List<ReportTypeEnum> getReportTypes() {
        return Lists.newArrayList(ReportTypeEnum.TW_BASIC);
    }

    @Override
    public boolean isDataSuccess(ReportDataGetResultForm form) {
        if (super.doCheckOrderDataSuccess(form)) {
            return true;
        }
        boolean dataSuccess = false;
        RealtimeOrderOfficialWeb entity = realtimeOrderOfficialWebService.getByApiOrderNo(form.getApiOrderNo());
        if (entity != null) {
            if (entity.getApiCallback() != null && entity.getApiCallback().equals(1)) {
                dataSuccess = true;
            } else {
                logger.info("RealtimeOrderOfficialWeb ApiCallback is null or not 1, apiOrderNo: " + form.getApiOrderNo());
            }
        } else {
            logger.info("RealtimeOrderOfficialWeb is null, apiOrderNo: " + form.getApiOrderNo());
        }
        return dataSuccess;
    }

    @Override
    protected BiConsumer<OrderData4LoopResult, TwRealtimeOrderOfficialWebTO> getPropSetter() {
        return OrderData4LoopResult::setTwRealData;
    }

    @Override
    public TwRealtimeOrderOfficialWebTO getData(ReportDataGetResultForm form) {
        TblCompReportOrder tblCompReportOrder = commTblCompReportOrderService.get(form.getOrderId());
        if (tblCompReportOrder == null) {
            logger.warn("TblCompReportOrder is null, orderId: " + form.getOrderId());
            return null;
        }
        RealtimeOrderOfficialWeb entity = realtimeOrderOfficialWebService.getByApiOrderNo(form.getApiOrderNo());
        if (entity == null) {
            logger.info("RealtimeOrderOfficialWeb is null, apiOrderNo: " + form.getApiOrderNo());
            return null;
        }
        QccOvsBasic qccOvsBasic = ovsQccOvsBasicService.getQccOvsBasicByKeyNo(entity.getCompKeyno());
        if (qccOvsBasic == null) {
            logger.error("qccOvsBasic is null, keyNo: " + entity.getCompKeyno());
            return null;
        }
        return convertToTO(entity, qccOvsBasic, tblCompReportOrder);
    }

    public TwRealtimeOrderOfficialWebTO convertToTO(RealtimeOrderOfficialWeb entity, QccOvsBasic qccOvsBasic, TblCompReportOrder tblCompReportOrder) {
        if (entity == null) {
            return null;
        }

        // 初始化返回对象
        TwRealtimeOrderOfficialWebTO to = new TwRealtimeOrderOfficialWebTO();
        TwRealtimeOrderOfficialWebTO.CompanyInfo companyInfoTO = new TwRealtimeOrderOfficialWebTO.CompanyInfo();
        TwRealtimeOrderOfficialWebTO.ContactInfo contactInfoTO = new TwRealtimeOrderOfficialWebTO.ContactInfo();
        TwRealtimeOrderOfficialWebTO.ShareCapitalInfo shareCapitalInfoTO = new TwRealtimeOrderOfficialWebTO.ShareCapitalInfo();
        TwRealtimeOrderOfficialWebTO.ShareHolderInfo shareHolderInfoTO = new TwRealtimeOrderOfficialWebTO.ShareHolderInfo();
        TwRealtimeOrderOfficialWebTO.KeyPersonInfo keyPersonInfoTO = new TwRealtimeOrderOfficialWebTO.KeyPersonInfo();
        TwRealtimeOrderOfficialWebTO.StockInfo stockInfoTO = new TwRealtimeOrderOfficialWebTO.StockInfo();
        TwRealtimeOrderOfficialWebTO.HistoryInfo historyInfoTO = new TwRealtimeOrderOfficialWebTO.HistoryInfo();
        TwRealtimeOrderOfficialWebTO.AdditionalInfo additionalInfoTO = new TwRealtimeOrderOfficialWebTO.AdditionalInfo();
        to.setCompanyInfo(companyInfoTO);
        to.setContactInfo(contactInfoTO);
        to.setShareCapitalInfo(shareCapitalInfoTO);
        to.setShareHolderInfo(shareHolderInfoTO);
        to.setKeyPersonInfo(keyPersonInfoTO);
        to.setStockInfo(stockInfoTO);
        to.setHistoryInfo(historyInfoTO);
        to.setAdditionalInfo(additionalInfoTO);

        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.TW);

        RealtimeOrderOfficialWeb.CompanyInfo companyInfo = entity.getCompanyInfo();
        RealtimeOrderOfficialWeb.ContactInformation contactInformation = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getContactInformation);
        List<RealtimeOrderOfficialWeb.CompAddress> compAddressList = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompAddress);

        // map TO
        Date orderCreateDate = getValue(tblCompReportOrder, TblCompReportOrder::getCreateDate);
        to.setLastUpdatedDate(DateUtils.formatDate(orderCreateDate));
        QccOvsBasicInfo basicInfo = getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
        String compClass = getValue(basicInfo, QccOvsBasicInfo::getCompClassDetail);
        to.setCompClass(compClass);

        // map CompanyInfo
        companyInfoTO.setCompNo(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompNo));
        companyInfoTO.setStatus(getStatus(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompStatus),
                getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompStatusLabel)));
        companyInfoTO.setIncorporationDate(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIncorporationDate));
        companyInfoTO.setLegalForm(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getEntityType));
        companyInfoTO.setPlaceOfIncorporation(GlobalAreaEnum.TW.getShortNameEn());
        List<TwRealtimeOrderOfficialWebTO.CnEnValue> registeredAddressList = getFromCompAddressList(compAddressList, "LocationofCompany");
        companyInfoTO.setRegisteredAddressList(registeredAddressList);
        registeredAddressList.forEach(address -> transWrapper.add4NonEntity(address, TwRealtimeOrderOfficialWebTO.CnEnValue::setCn, TwRealtimeOrderOfficialWebTO.CnEnValue::setEn, TwRealtimeOrderOfficialWebTO.CnEnValue::getCn, TwRealtimeOrderOfficialWebTO.CnEnValue::getEn));
        List<RealtimeOrderOfficialWeb.Industry> industryList = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIndustry);
        if (CollectionUtils.isNotEmpty(industryList)) {
            companyInfoTO.setBusinessActivityList(industryList.stream().map(industry -> new TwRealtimeOrderOfficialWebTO.CodeDesc(industry.getCode(), industry.getDesc())).collect(Collectors.toList()));
            companyInfoTO.getBusinessActivityList().forEach(businessActivity -> transWrapper.add4NonEntityWithOverwrite(businessActivity, TwRealtimeOrderOfficialWebTO.CodeDesc::setDescEn, TwRealtimeOrderOfficialWebTO.CodeDesc::getDesc));
        }

        // map ContactInfo
        contactInfoTO.setWebsiteList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getWebsite, new ArrayList<RealtimeOrderOfficialWeb.Website>())
                .stream().map(website -> new TwRealtimeOrderOfficialWebTO.TypeAndValue(website.getType(), website.getWebsite()))
                .collect(Collectors.toList()));
        contactInfoTO.setPhoneNumberList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getPhone, new ArrayList<RealtimeOrderOfficialWeb.Phone>())
                .stream().map(phone -> new TwRealtimeOrderOfficialWebTO.TypeAndValue(phone.getType(), phone.getPhone()))
                .collect(Collectors.toList()));
        contactInfoTO.setEmailList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getEmail, new ArrayList<RealtimeOrderOfficialWeb.Email>())
                .stream().map(email -> new TwRealtimeOrderOfficialWebTO.TypeAndValue(email.getType(), email.getEmail()))
                .collect(Collectors.toList()));
        contactInfoTO.setFaxList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getFax, new ArrayList<RealtimeOrderOfficialWeb.Fax>())
                .stream().map(fax -> new TwRealtimeOrderOfficialWebTO.TypeAndValue(fax.getType(), fax.getFax()))
                .collect(Collectors.toList()));
        List<TwRealtimeOrderOfficialWebTO.CnEnValue> businessAddressList = getFromCompAddressList(compAddressList, "AddressofBusiness");
        contactInfoTO.setBusinessAddressList(businessAddressList);
        businessAddressList.forEach(address -> transWrapper.add4NonEntity(address, TwRealtimeOrderOfficialWebTO.CnEnValue::setCn, TwRealtimeOrderOfficialWebTO.CnEnValue::setEn, TwRealtimeOrderOfficialWebTO.CnEnValue::getCn, TwRealtimeOrderOfficialWebTO.CnEnValue::getEn));

        // map ShareCapital
        shareCapitalInfoTO.setIssuedShareCapital(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getAmountCapital));
        shareCapitalInfoTO.setPaidUpCapital(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getPaidInCapital));
        shareCapitalInfoTO.setParValuePerShare(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getShareValue));
        shareCapitalInfoTO.setNumberOfShares(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getTotalNumberOfShares));
        shareCapitalInfoTO.setSpecialVotingRights(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIsSpecialVotingRights));
        shareCapitalInfoTO.setSpecificIssuesVeto(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIsSpecificIssuesVeto));
        shareCapitalInfoTO.setProhibited(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIsProhibited));

        // map ShareHolderInfo
        List<RealtimeOrderOfficialWeb.Shareholder> shareholders = getValue(entity, RealtimeOrderOfficialWeb::getShareholders);
        if (CollectionUtils.isNotEmpty(shareholders)) {
            List<TwRealtimeOrderOfficialWebTO.ShareHolderItem> shareHolderItems = shareholders.stream().map(shareholderItem -> {
                TwRealtimeOrderOfficialWebTO.ShareHolderItem shareHolderItemTO = new TwRealtimeOrderOfficialWebTO.ShareHolderItem();
                shareHolderItemTO.setNumberOfShares(shareholderItem.getNumberOfShares());
                shareHolderItemTO.setPercentOfClass(shareholderItem.getPercentOfClass());
                if (CollectionUtils.isNotEmpty(shareholderItem.getShareholder())) {
                    List<RealtimeOrderOfficialWeb.ShareholderInfo> shareholderItemDetailList = shareholderItem.getShareholder();
                    shareHolderItemTO.setDetails(shareholderItemDetailList.stream().map(shareholderItemDetail -> {
                        TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail shareHolderItemDetailTO = new TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail();
                        shareHolderItemDetailTO.setKeyNo(StringUtils.getIfNotPersonKeyNo(shareholderItemDetail.getKeyNo()));
                        shareHolderItemDetailTO.setName(shareholderItemDetail.getName());
                        shareHolderItemDetailTO.setNameEn(shareholderItemDetail.getNameEn());
                        shareHolderItemDetailTO.setEntityType(shareholderItemDetail.getEntityType());
                        shareHolderItemDetailTO.setNationCode(shareholderItemDetail.getNationCode());
                        shareHolderItemDetailTO.setNationDesc(GlobalAreaEnum.getShortNameEnByNameCode(shareholderItemDetail.getNationCode()));
                        shareHolderItemDetailTO.setIdNo(shareholderItemDetail.getIdentificationNoOrig());
                        shareHolderItemDetailTO.setIdType(shareholderItemDetail.getIdentificationNumberType());
                        Integer entityType = getEntityType4Trans(shareHolderItemDetailTO.getEntityType(), shareHolderItemDetailTO.getKeyNo());
                        transWrapper.add4Entity(shareHolderItemDetailTO, TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail::setName, TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail::setNameEn, TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail::getName, TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail::getNameEn, TwRealtimeOrderOfficialWebTO.ShareHolderItemDetail::getKeyNo, entityType);
                        return shareHolderItemDetailTO;
                    }).collect(Collectors.toList()));
                }
                return shareHolderItemTO;
            }).collect(Collectors.toList());
            CollectionUtils.sort(shareHolderItems, sh -> NumberUtils.parseNumber(sh.getPercentOfClass(), 0).intValue(), false);
            shareHolderInfoTO.setItems(shareHolderItems);
        }

        // map KeyPersonInfo
        if (StringUtils.equals(compClass, "Company")) {
            String directorsTermStartDate = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getDirectorsTermStartDate);
            String directorsTermEndDate = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getDirectorsTermEndDate);
            List<RealtimeOrderOfficialWeb.Director> directors = getValue(entity, RealtimeOrderOfficialWeb::getDirectors);
            if (CollectionUtils.isNotEmpty(directors)) {
                List<TwRealtimeOrderOfficialWebTO.DirectorItem> directorItems = directors.stream()
                        .filter(directorItem -> StringUtils.equals(directorItem.getType(), "DirectorsSupervisors"))
                        .map(directorItem -> {
                            TwRealtimeOrderOfficialWebTO.DirectorItem directorItemTO = new TwRealtimeOrderOfficialWebTO.DirectorItem();
                            directorItemTO.setKeyNo(directorItem.getKeyNo());
                            directorItemTO.setName(directorItem.getName());
                            directorItemTO.setNameEn(directorItem.getNameEn());
                            directorItemTO.setEntityType(directorItem.getEntityType());
                            directorItemTO.setNationCode(directorItem.getNationCode());
                            directorItemTO.setNationDesc(GlobalAreaEnum.getShortNameEnByNameCode(directorItemTO.getNationCode()));
                            directorItemTO.setPosition(directorItem.getPosition());
                            directorItemTO.setIdNo(directorItem.getIdentificationNoOrig());
                            directorItemTO.setIdType(directorItem.getIdentificationNumberType());
                            directorItemTO.setRepresentedCompName(directorItem.getRepresentedCompany());
                            directorItemTO.setRepresentedCompNameEn(directorItem.getRepresentedCompanyEn());
                            directorItemTO.setRepresentedCompKeyNo(directorItem.getRepresentedCompKeyno());
                            directorItemTO.setRegisteredTermOfOfficeStartDate(directorsTermStartDate);
                            directorItemTO.setRegisteredTermOfOfficeEndDate(directorsTermEndDate);
                            transWrapper.add4Entity(directorItemTO, TwRealtimeOrderOfficialWebTO.DirectorItem::setRepresentedCompName, TwRealtimeOrderOfficialWebTO.DirectorItem::setRepresentedCompNameEn, TwRealtimeOrderOfficialWebTO.DirectorItem::getRepresentedCompName, TwRealtimeOrderOfficialWebTO.DirectorItem::getRepresentedCompNameEn, TwRealtimeOrderOfficialWebTO.DirectorItem::getRepresentedCompKeyNo, TransWrapper.ENTRY_TYPE_CORP);
                            Integer entityType4Trans = getEntityType4Trans(directorItemTO.getEntityType(), directorItemTO.getKeyNo());
                            transWrapper.add4Entity(directorItemTO, TwRealtimeOrderOfficialWebTO.DirectorItem::setName, TwRealtimeOrderOfficialWebTO.DirectorItem::setNameEn, TwRealtimeOrderOfficialWebTO.DirectorItem::getName, TwRealtimeOrderOfficialWebTO.DirectorItem::getNameEn, TwRealtimeOrderOfficialWebTO.DirectorItem::getKeyNo, entityType4Trans);
                            return directorItemTO;
                        }).collect(Collectors.toList());
                directorItems.sort(TwBasicReportDataService::directorComparator);
                keyPersonInfoTO.setDirectorItems(directorItems);

                List<TwRealtimeOrderOfficialWebTO.ManagementItem> managementItems = directors.stream()
                        .filter(directorItem -> StringUtils.equals(directorItem.getType(), "ManagerialPersonnel"))
                        .map(directorItem -> {
                            TwRealtimeOrderOfficialWebTO.ManagementItem managementItemTO = new TwRealtimeOrderOfficialWebTO.ManagementItem();
                            managementItemTO.setKeyNo(directorItem.getKeyNo());
                            managementItemTO.setName(directorItem.getName());
                            managementItemTO.setNameEn(directorItem.getNameEn());
                            managementItemTO.setEntityType(directorItem.getEntityType());
                            managementItemTO.setNationCode(directorItem.getNationCode());
                            managementItemTO.setNationDesc(GlobalAreaEnum.getShortNameEnByNameCode(managementItemTO.getNationCode()));
                            managementItemTO.setPosition("Managerial Personnel");
                            managementItemTO.setAppointmentDate(directorItem.getAppointmentDate());
                            Integer entityType4Trans = getEntityType4Trans(managementItemTO.getEntityType(), managementItemTO.getKeyNo());
                            transWrapper.add4Entity(managementItemTO, TwRealtimeOrderOfficialWebTO.ManagementItem::setName, TwRealtimeOrderOfficialWebTO.ManagementItem::setNameEn, TwRealtimeOrderOfficialWebTO.ManagementItem::getName, TwRealtimeOrderOfficialWebTO.ManagementItem::getNameEn, TwRealtimeOrderOfficialWebTO.ManagementItem::getKeyNo, entityType4Trans);
                            return managementItemTO;
                        }).collect(Collectors.toList());
                managementItems.sort(TwBasicReportDataService::managementComparator);
                keyPersonInfoTO.setManagementItems(managementItems);
            }
        } else if (StringUtils.equals(compClass, "Business")) {
            List<RealtimeOrderOfficialWeb.Representative> representativeList = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getRepresentative);
            if (CollectionUtils.isNotEmpty(representativeList)) {
                List<TwRealtimeOrderOfficialWebTO.DirectorItem> directorItems = representativeList.stream()
                        .map(representativeItem -> {
                            TwRealtimeOrderOfficialWebTO.DirectorItem directorItemTO = new TwRealtimeOrderOfficialWebTO.DirectorItem();
                            directorItemTO.setKeyNo(representativeItem.getKeyno());
                            directorItemTO.setName(representativeItem.getName());
                            directorItemTO.setNameEn(representativeItem.getNameEnRelated());
                            directorItemTO.setEntityType(representativeItem.getEntityType());
                            directorItemTO.setNationCode(representativeItem.getNationCode());
                            directorItemTO.setNationDesc(GlobalAreaEnum.getShortNameEnByNameCode(directorItemTO.getNationCode()));
                            directorItemTO.setPosition(representativeItem.getType());
                            directorItemTO.setIdNo(representativeItem.getIdentificationNoOrig());
                            directorItemTO.setIdType(representativeItem.getIdentificationNumberType());
                            Integer entityType4Trans = getEntityType4Trans(directorItemTO.getEntityType(), directorItemTO.getKeyNo());
                            transWrapper.add4Entity(directorItemTO, TwRealtimeOrderOfficialWebTO.DirectorItem::setName, TwRealtimeOrderOfficialWebTO.DirectorItem::setNameEn, TwRealtimeOrderOfficialWebTO.DirectorItem::getName, TwRealtimeOrderOfficialWebTO.DirectorItem::getNameEn, TwRealtimeOrderOfficialWebTO.DirectorItem::getKeyNo, entityType4Trans);
                            return directorItemTO;
                        }).collect(Collectors.toList());
                directorItems.sort(TwBasicReportDataService::directorComparator);
                keyPersonInfoTO.setDirectorItems(directorItems);
            }
        }

        // map StockInfo
        RealtimeOrderOfficialWeb.StockInfo stockInfo = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getStockInfo);
        stockInfoTO.setCode(getValue(stockInfo, RealtimeOrderOfficialWeb.StockInfo::getStockSymbol));
        stockInfoTO.setSymbol(getValue(stockInfo, RealtimeOrderOfficialWeb.StockInfo::getStockSname));
        stockInfoTO.setStockType(getValue(stockInfo, RealtimeOrderOfficialWeb.StockInfo::getStockType));
        stockInfoTO.setIndustry(getValue(stockInfo, RealtimeOrderOfficialWeb.StockInfo::getIndustry));

        // map HistoryInfo
        List<RealtimeOrderOfficialWeb.History> historyList = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getHistory);
        if (CollectionUtils.isNotEmpty(historyList)) {
            List<TwRealtimeOrderOfficialWebTO.HistoryInfoItem> items = historyList.stream().map(history -> {
                TwRealtimeOrderOfficialWebTO.HistoryInfoItem historyInfoItemTO = new TwRealtimeOrderOfficialWebTO.HistoryInfoItem();
                historyInfoItemTO.setApprovalDate(history.getApproveDate());
                historyInfoItemTO.setRegistrationNumber(history.getCompNo());
                historyInfoItemTO.setCompanyName(history.getCompNameTc());
                historyInfoItemTO.setIssuedShareCapital(history.getAmountCapital());
                historyInfoItemTO.setPaidUpCapital(history.getPaidInCapital());
                historyInfoItemTO.setRegisteredAddress(history.getAddressTc());
                return historyInfoItemTO;
            }).collect(Collectors.toList());
            CollectionUtils.sort(items, TwRealtimeOrderOfficialWebTO.HistoryInfoItem::getApprovalDate, false);
            historyInfoTO.setItems(items);
        }

        // map AdditionalInfo
        additionalInfoTO.setRegistrationAuthority(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getRegistrationAuthority));
        transWrapper.add4NonEntityWithOverwrite(additionalInfoTO, TwRealtimeOrderOfficialWebTO.AdditionalInfo::setRegistrationAuthority, TwRealtimeOrderOfficialWebTO.AdditionalInfo::getRegistrationAuthority);
        additionalInfoTO.setDateOfApproval(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getLastUpdatedDate));
        additionalInfoTO.setDisclosedOwnershipType(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getEquityStatus));
        additionalInfoTO.setCloselyHeldCompany(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIsCloselyHeldCompany));
        additionalInfoTO.setSuspensionStartDate(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getSuspensionStartDate));
        additionalInfoTO.setSuspensionEndDate(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getSuspensionEndDate));
        additionalInfoTO.setSuspensionApprovalAuthority(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getSuspensionApprovingAuthority));
        transWrapper.add4NonEntity(additionalInfoTO, TwRealtimeOrderOfficialWebTO.AdditionalInfo::setSuspensionApprovalAuthority, TwRealtimeOrderOfficialWebTO.AdditionalInfo::setSuspensionApprovalAuthorityEn, TwRealtimeOrderOfficialWebTO.AdditionalInfo::getSuspensionApprovalAuthority, TwRealtimeOrderOfficialWebTO.AdditionalInfo::getSuspensionApprovalAuthorityEn);
        RealtimeOrderOfficialWeb.IntellectualPropertyInfo intellectualPropertyInfo = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIntellectualPropertyInfo);
        additionalInfoTO.setTrademark(getValue(intellectualPropertyInfo, RealtimeOrderOfficialWeb.IntellectualPropertyInfo::getTrademark));
        commonDataService.enPostProcessor(transWrapper);
        return to;
    }

    private static Integer getEntityType4Trans(String entityType, String keyNo) {
        Integer entityTypeTrans = null;
        if (StringUtils.equals(entityType, "Individual")
                || StringUtils.getIfPerson(keyNo)) {
            entityTypeTrans = TransWrapper.ENTRY_TYPE_PERSON;
        } else if (StringUtils.equals(entityType, "Company")) {
            entityTypeTrans = TransWrapper.ENTRY_TYPE_CORP;
        }
        return entityTypeTrans;
    }

    private static List<TwRealtimeOrderOfficialWebTO.CnEnValue> getFromCompAddressList(List<RealtimeOrderOfficialWeb.CompAddress> compAddressList, String type) {
        if (CollectionUtils.isEmpty(compAddressList)) {
            return new ArrayList<>();
        }
        return compAddressList.stream()
                .filter(k -> type.equals(k.getType()))
                .map(compAddress -> new TwRealtimeOrderOfficialWebTO.CnEnValue(compAddress.getAddressTc(), compAddress.getAddress()))
                .collect(Collectors.toList());
    }

    /**
     * 获取状态显示值，返回类似 “Active (Registered)”
     *
     * @param status      原始值
     * @param statusLabel 映射值
     * @return
     */
    private static String getStatus(String status, String statusLabel) {
        String res = null;
        if (StringUtils.isNotBlank(statusLabel)) {
            if (StringUtils.isNotBlank(status)) {
                res = statusLabel + " (" + status + ")";
            } else {
                res = statusLabel;
            }
        } else {
            res = status;
        }
        return res;
    }

    private static int directorComparator(TwRealtimeOrderOfficialWebTO.DirectorItem directorItem1,
                                          TwRealtimeOrderOfficialWebTO.DirectorItem directorItem2) {
        // 按职位优先级排序
        List<String> positionOrder = Lists.newArrayList("Representative", "Director", "Supervisor", "Partner"); // 职位从高往低顺序列表
        String position1 = directorItem1.getPosition();
        String position2 = directorItem2.getPosition();
        int positionIndex1 = positionOrder.indexOf(position1);
        int positionIndex2 = positionOrder.indexOf(position2);
        if (positionIndex1 != positionIndex2) {
            return positionIndex1 - positionIndex2;
        }

        // 职位相同，按任职开始日期从大到小排序
        String startDate1 = directorItem1.getRegisteredTermOfOfficeStartDate();
        String startDate2 = directorItem2.getRegisteredTermOfOfficeStartDate();
        return -StringUtils.compare(startDate1, startDate2);
    }

    private static int managementComparator(TwRealtimeOrderOfficialWebTO.ManagementItem managementItem1,
                                            TwRealtimeOrderOfficialWebTO.ManagementItem managementItem2) {
        // 按任职开始日期从大到小排序
        String appointmentDate1 = managementItem1.getAppointmentDate();
        String appointmentDate2 = managementItem2.getAppointmentDate();
        return -StringUtils.compare(appointmentDate1, appointmentDate2);
    }
}
