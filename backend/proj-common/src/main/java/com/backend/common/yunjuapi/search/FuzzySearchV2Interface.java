package com.backend.common.yunjuapi.search;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.industry.entity.TblGlobalIndustry;
import com.backend.common.modules.industry.service.CommTblGlobalIndustryService;
import com.backend.common.modules.search.SearchBaseService;
import com.backend.common.yunjuapi.GlobalCompanyDetailsInterface;
import com.backend.common.yunjuapi.YunJuRequestForwardingGetDetailInterface;
import com.backend.common.yunjuapi.model.AdvanceSearchTO;
import com.backend.common.yunjuapi.model.ApiAutocompleteGlobalTO;
import com.backend.common.yunjuapi.model.ApiAutocompleteTOResult;
import com.backend.common.yunjuapi.model.Page;
import com.backend.common.yunjuapi.model.search.HitReason;
import com.backend.common.yunjuapi.model.search.SearchMultiSelectionResultResp;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.ApiErrorCodeEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.exception.MessageExceptionEnum;
import com.qcc.frame.commons.ienum.search.CorpSearchHitReasonFieldMappingV2Enum;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

// added for v2.2.1 chenbl KNZT-6709
public class FuzzySearchV2Interface {
    protected static Logger logger = LoggerFactory.getLogger(FuzzySearchV2Interface.class);
    private static CommTblGlobalIndustryService commTblGlobalIndustryService = SpringContextHolder.getBean(CommTblGlobalIndustryService.class);
    private static CommonDataService commonDataService = SpringContextHolder.getBean(CommonDataService.class);

    public static final String CHINA_FILTER_CONDITION = "{\"st\":[\"0\",\"4\"]}"; // 筛选大陆企业
    public static final String HK_FILTER_CONDITION = "{\"st\":[\"3\"]}"; // 筛选香港企业
    public static final String HK_AND_CHINA_FILTER_CONDITION = "{\"st\":[\"0\",\"4\",\"3\"]}"; // 筛选香港企业

    private static final List<String> SENSITIVE_WORDS = Lists.newArrayList("军工", "軍工", "军事", "軍事");

    private static boolean containsSensitive(String searchKey) {
        return SENSITIVE_WORDS.contains(searchKey);
    }

    private static String resolveFilter(String nationCode) {
        String filter = "";
        if (StringUtils.isNotBlank(nationCode)) {
            if (GlobalAreaEnum.HK.getNameCode().equals(nationCode)) {
                filter = HK_FILTER_CONDITION;
            } else {
                filter = CHINA_FILTER_CONDITION;
            }
        } else {
            filter = HK_AND_CHINA_FILTER_CONDITION;
        }
        return filter;
    }


    public static ApiAutocompleteTOResult search(String nationCode, String searchKey, String pageIndex, String pageSize) throws MessageException {
        if (containsSensitive(searchKey)) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        paramMap.put("pageIndex", pageIndex);
        paramMap.put("pageSize", pageSize);
        String filter = resolveFilter(nationCode);
        paramMap.put("filter", filter);
        paramMap.put("isKyc", true);
        ApiAutocompleteTOResult result = null;
        try {
            String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/ECILocal/SearchMultiSelection", "2", JSON.toJSONString(paramMap), false);
            logger.info("/ECILocal/SearchMultiSelection resp:" + resp);
            if (StringUtils.isBlank(resp)) {
                return null;
            }
            JSONObject jsonObj = JSON.parseObject(resp);
            if (jsonObj == null) {
                return null;
            }
            String respStatus = jsonObj.getString("Status");
            if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(respStatus) ||
                    ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(respStatus)) {
                result = new ApiAutocompleteTOResult();
                JSONArray jsonArray = jsonObj.getJSONArray("Result");
                if (CollectionUtils.isNotEmpty(jsonArray)) {
                    List<SearchMultiSelectionResultResp> apiTos = jsonArray.toJavaList(SearchMultiSelectionResultResp.class);
                    result.setResult(transform4CorpSearch(apiTos));
                }
                Page paging = new Page();
                JSONObject pagingJo = jsonObj.getJSONObject("Paging");
                if (pagingJo != null) {
                    paging.setTotalRecords(pagingJo.getInteger("TotalRecords"));
                }
                result.setPaging(paging);
            } else if (ApiErrorCodeEnum.CODE_ERROR_215.getCode().equals(jsonObj.getString("Status"))) {
                logger.warn("搜索包含宽泛词, searchKey: " + searchKey);
                throw new MessageException(MessageExceptionEnum.TOO_MANY_RESULTS);
            } else {
                logger.error("/ECILocal/SearchMultiSelection error, resp:" + resp);
            }
        } catch (MessageException e) {
            throw e;
        } catch (Exception e) {
            logger.error("/ECILocal/SearchMultiSelection:", e);
        }
        return result;
    }

    private static List<ApiAutocompleteGlobalTO> transform4CorpSearch(List<SearchMultiSelectionResultResp> tos) {
        if (CollectionUtils.isEmpty(tos)) {
            return new ArrayList<>();
        }
        Set<String> industryCodes = tos.stream().map(SearchMultiSelectionResultResp::getIndustry)
                .filter(Objects::nonNull)
                .map(industry -> StringUtils.getNotBlankStr(industry.getSmallCategoryCode(), industry.getMiddleCategoryCode(), industry.getSubIndustryCode(), industry.getIndustryCode()))
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, TblGlobalIndustry> code2Industry4CnMapping = new HashMap<>();
        if (CollectionUtils.isNotEmpty(industryCodes)) {
            code2Industry4CnMapping.putAll(commTblGlobalIndustryService.getCode2Industry4CnMapping(industryCodes));
        }
        List<ApiAutocompleteGlobalTO> resultList = new ArrayList<>();
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
        for (SearchMultiSelectionResultResp apiTO : tos) {
            ApiAutocompleteGlobalTO globalTO = new ApiAutocompleteGlobalTO();
            globalTO.setKeyNo(apiTO.getKeyNo());
            globalTO.setName(apiTO.getName());
            globalTO.setImgUrl(apiTO.getImageUrl());
            globalTO.setCreditCode(apiTO.getCreditCode());
            globalTO.setRegNo(apiTO.getNo());
            globalTO.setNewRegNo(apiTO.getNewRegNo());
            if (apiTO.getStartDate() != null) {
                try {
                    globalTO.setStartDate(DateUtils.formatDate(new Date(apiTO.getStartDate())));
                } catch (Exception e) {
                    logger.error("日期格式转换失败: " + apiTO.getStartDate());
                }
            }
            globalTO.setAddress(apiTO.getAddress());

            SearchMultiSelectionResultResp.Industry industry = apiTO.getIndustry();
            if (industry != null) {
                String industryCode = StringUtils.getNotBlankStr(industry.getSmallCategoryCode(), industry.getMiddleCategoryCode(), industry.getSubIndustryCode(), industry.getIndustryCode());
                TblGlobalIndustry tblGlobalIndustry = code2Industry4CnMapping.get(industryCode);
                if (tblGlobalIndustry != null) {
                    globalTO.setIndustryList(Lists.newArrayList(
                            new ApiAutocompleteGlobalTO.Industry(StringUtils.equals(tblGlobalIndustry.getInd1Code(), industryCode) ? industryCode : tblGlobalIndustry.getInd1Code() + industryCode,
                                    StringUtils.getNotBlankStr(tblGlobalIndustry.getInd4Name(), tblGlobalIndustry.getInd3Name(), tblGlobalIndustry.getInd2Name(), tblGlobalIndustry.getInd1Name()),
                                    StringUtils.getNotBlankStr(tblGlobalIndustry.getInd4NameEn(), tblGlobalIndustry.getInd3NameEn(), tblGlobalIndustry.getInd2NameEn(), tblGlobalIndustry.getInd1NameEn()))));
                }
            }

            fillHitReason(globalTO, apiTO.getHitReasons(),
                    StringUtils.startsWith(apiTO.getKeyNo(), Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX) ? GlobalAreaEnum.HK : GlobalAreaEnum.CN);
            globalTO.setQccCode(apiTO.getQccCode());
            transWrapper.add4Entity(globalTO, ApiAutocompleteGlobalTO::setName, ApiAutocompleteGlobalTO::setEnName, ApiAutocompleteGlobalTO::getName, ApiAutocompleteGlobalTO::getEnName, ApiAutocompleteGlobalTO::getKeyNo, TransWrapper.ENTRY_TYPE_CORP);
            resultList.add(globalTO);
        }
        commonDataService.enPostProcessor(transWrapper);
        return resultList;
    }

    private static void fillHitReason(ApiAutocompleteGlobalTO globalTO, List<SearchMultiSelectionResultResp.HitReason> apiHitReasons, GlobalAreaEnum globalAreaEnum) {
        if (globalTO == null || CollectionUtils.isEmpty(apiHitReasons) || globalAreaEnum == null) {
            return;
        }
        try {
            if (globalAreaEnum == GlobalAreaEnum.CN) {
                fillHitReason4Cn(globalTO, apiHitReasons, globalAreaEnum);
            } else if (globalAreaEnum == GlobalAreaEnum.HK) {
                fillHitReason4Hk(globalTO, apiHitReasons, globalAreaEnum);
            }
        } catch (Exception e) {
            logger.error("填充HIT REASON失败", e);
        }
    }

    private static void fillHitReason4Cn(ApiAutocompleteGlobalTO globalTO, List<SearchMultiSelectionResultResp.HitReason> apiHitReasons, GlobalAreaEnum globalAreaEnum) {
        int maxEmContentLengthOfListField = 0;
        List<HitReason> otherHitReasonsTemp = new ArrayList<>(); // 列表显示字段外的命中字段
        for (SearchMultiSelectionResultResp.HitReason apiHitReason : apiHitReasons) {
            String field = apiHitReason.getField();
            if (StringUtils.isBlank(field)) {
                continue;
            }
            String hitReasonValue = apiHitReason.getValue();
            if (StringUtils.isBlank(hitReasonValue)) {
                continue;
            }
            CorpSearchHitReasonFieldMappingV2Enum enumByApiFieldName = CorpSearchHitReasonFieldMappingV2Enum.getEnumByApiFieldName(field, globalAreaEnum);
            if (enumByApiFieldName == null) {
                continue;
            }
            if (enumByApiFieldName == CorpSearchHitReasonFieldMappingV2Enum.CN_EnglishName) {
                // 英文名特殊处理，因为会有多个来源，需要判断是不是当前的英文名
                String str1 = StringUtils.removeEmTags(hitReasonValue);
                if (StringUtils.equals(str1, globalTO.getEnName())) {
                    globalTO.setEnName(hitReasonValue);
                    maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getEnName()));
                }
            } else if (enumByApiFieldName == CorpSearchHitReasonFieldMappingV2Enum.CN_Name) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getName()));
            } else if (enumByApiFieldName == CorpSearchHitReasonFieldMappingV2Enum.CN_CreditCode) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getCreditCode()));
            } else {
                otherHitReasonsTemp.add(new HitReason(field, enumByApiFieldName.getGlobalFieldName(), Lists.newArrayList(hitReasonValue), SearchBaseService.getEmContentLength(hitReasonValue)));
            }
        }
        HitReason hitReasonWithMaxEmContentLength = otherHitReasonsTemp.stream().max(Comparator.comparing(HitReason::getEmContentLength)).orElse(null);
        if (hitReasonWithMaxEmContentLength != null && hitReasonWithMaxEmContentLength.getEmContentLength() > maxEmContentLengthOfListField) {
            globalTO.setHitReason(hitReasonWithMaxEmContentLength);
        }
    }

    private static void fillHitReason4Hk(ApiAutocompleteGlobalTO globalTO, List<SearchMultiSelectionResultResp.HitReason> apiHitReasons, GlobalAreaEnum globalAreaEnum) {
        int maxEmContentLengthOfListField = 0;
        List<HitReason> otherHitReasonsTemp = new ArrayList<>(); // 列表显示字段外的命中字段
        for (SearchMultiSelectionResultResp.HitReason apiHitReason : apiHitReasons) {
            String field = apiHitReason.getField();
            if (StringUtils.isBlank(field)) {
                continue;
            }
            String hitReasonValue = apiHitReason.getValue();
            if (StringUtils.isBlank(hitReasonValue)) {
                continue;
            }
            CorpSearchHitReasonFieldMappingV2Enum enumByApiFieldName = CorpSearchHitReasonFieldMappingV2Enum.getEnumByApiFieldName(field, globalAreaEnum);
            if (enumByApiFieldName == null) {
                continue;
            }
            if (enumByApiFieldName == CorpSearchHitReasonFieldMappingV2Enum.HK_EnglishName) {
                // 英文名特殊处理，因为会有多个来源，需要判断是不是当前的英文名
                String str1 = StringUtils.removeEmTags(hitReasonValue);
                if (StringUtils.equals(str1, globalTO.getEnName())) {
                    globalTO.setEnName(hitReasonValue);
                    maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getName()));
                }
            } else if (enumByApiFieldName == CorpSearchHitReasonFieldMappingV2Enum.HK_Name) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getName()));
            } else if (enumByApiFieldName == CorpSearchHitReasonFieldMappingV2Enum.HK_NewRegNo) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getCreditCode()));
            } else {
                otherHitReasonsTemp.add(new HitReason(field, enumByApiFieldName.getGlobalFieldName(), Lists.newArrayList(hitReasonValue), SearchBaseService.getEmContentLength(hitReasonValue)));
            }
        }
        HitReason hitReasonWithMaxEmContentLength = otherHitReasonsTemp.stream().max(Comparator.comparing(HitReason::getEmContentLength)).orElse(null);
        if (hitReasonWithMaxEmContentLength != null && hitReasonWithMaxEmContentLength.getEmContentLength() > maxEmContentLengthOfListField) {
            globalTO.setHitReason(hitReasonWithMaxEmContentLength);
        }
    }
}
