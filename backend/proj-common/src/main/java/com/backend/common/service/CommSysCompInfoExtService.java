package com.backend.common.service;

import com.backend.common.entity.CompInfoExt;
import com.backend.common.mapper.SysCompInfoExtDao;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.ServiceException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class CommSysCompInfoExtService extends CrudService<SysCompInfoExtDao, CompInfoExt> {
	
	@Autowired
	@Lazy
	private CommSysCompanyService commsysCompanyService;
	
	public List<String> retrieveInfoByCompanyIdAndType(String companyId, String type) {
		return dao.retrieveInfoByCompanyIdAndType(companyId, type);
	}

	// added for v2.0.7 chenbl KNZT-6595
	public String retrieveFirstInfoByCompanyIdAndType(String companyId, String type) {
		List<String> list = retrieveInfoByCompanyIdAndType(companyId, type);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		return list.get(0);
	}
	
	public List<String> retrieveInfoByCompanyIdAndType(String companyId, String companyIdExtraInfo, String type) {
		return dao.retrieveInfoByCompanyIdAndType(companyId + companyIdExtraInfo, type);
	}
	
	public List<String> retrieveCompanyIdByType(String type) {
		return dao.retrieveCompanyIdByType(type);
	}
	
	public List<String> retrieveCompanyIdByTypeAndValue(String type, String value) {
		return dao.retrieveCompanyIdByTypeAndValue(type, value);
	}
	
	public CompInfoExt retrieveCompInfoExtInfo(String companyId, String type) {
		List<CompInfoExt> list = dao.retrieveCompInfoExtInfo(companyId, type);
		if(list != null && !list.isEmpty()) {
			return list.get(0);
		}
		return null;
	}

	// added for v2.0.7 chenbl KNZT-6595
	public void saveExtValue(String companyId, String type, List<String> newValues) {
		List<String> valuesDb = this.retrieveInfoByCompanyIdAndType(companyId, type);
		newValues = newValues != null ? newValues : new ArrayList<>();
		newValues = newValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
		Set<String> dbSet = new HashSet<>(valuesDb);
		Set<String> newSet = new HashSet<>(newValues);
		List<String> toAdd = newSet.stream().filter(value -> !dbSet.contains(value)).collect(Collectors.toList());
		List<String> toRemove = dbSet.stream().filter(value -> !newSet.contains(value)).collect(Collectors.toList());

		toAdd.forEach(value -> insertValue(companyId, type, value));
		toRemove.forEach(value -> deleteValue(companyId, type, value));
	}

	private void insertValue(String companyId, String type, String value) {
		if (StringUtils.isBlank(value)) {
			return;
		}
		CompInfoExt compInfoExt = new CompInfoExt();
		compInfoExt.setCompanyId(companyId);
		compInfoExt.setType(type);
		compInfoExt.setValue(value);
		save(compInfoExt);
	}

	private void deleteValue(String companyId, String type, String value) {
		dao.deleteValue(companyId, type, value, UserUtils.getUserId());
	}
	
	public void testInsert() {
		if("dev".equals(Global.getConfig("project.env"))) {
			String type = "TEST" + DateUtils.getDateTime();
			logger.info(" testInsert - type = " + type);
			CompInfoExt ext = new CompInfoExt();
			ext.setType(type);
			ext.setValue("ssss");
			ext.setDesc("desc");
			this.save(ext);
		}
	}
	public void testInsertTran() {
		if("dev".equals(Global.getConfig("project.env"))) {
			String type = "TEST" + DateUtils.getDateTime();
			logger.info(" testInsertTran - type = " + type);
			CompInfoExt ext = new CompInfoExt();
			ext.setType(type);
			ext.setValue("ssss");
			ext.setDesc("desc");
			this.save(ext);
			throw new ServiceException("eeeeeeeeeeeee");
		}
	}

	/**
	 * added for v.2.2.7 fengsw KNZT-7785
	 * 根据公司id和type获取value，如果value为空，则返回默认值
	 *
	 * @param companyId
	 * @param type
	 * @param defaultValue
	 * @return
	 */
	public String retrieveInfoValueByCompanyIdAndTypeWithDefault(String companyId, String type, String defaultValue) {
		String value = retrieveFirstInfoByCompanyIdAndType(companyId, type);
		if (StringUtils.isBlank(value)) {
			return defaultValue;
		}
		return value;
	}

	/**
	 * added for v.2.2.7 fengsw KNZT-7785
	 * 保存公司扩展信息
	 *
	 * @param companyId
	 * @param type
	 * @param value
	 */
	public void saveColExtValue(String companyId, String type, String value) {
		CompInfoExt ext = this.retrieveCompInfoExtInfo(companyId, type);
		if (ext != null) {
			ext.setValue(value);
		} else {
			ext = new CompInfoExt();
			ext.setCompanyId(companyId);
			ext.setType(type);
			ext.setValue(value);
		}
		this.save(ext);
	}

}
