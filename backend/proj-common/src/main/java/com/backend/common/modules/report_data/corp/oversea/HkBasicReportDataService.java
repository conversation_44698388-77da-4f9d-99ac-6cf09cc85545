package com.backend.common.modules.report_data.corp.oversea;

import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.report.model.OrderData4LoopResult;
import com.backend.common.modules.report_data.corp.oversea.base.AbstractReportDataService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.backend.common.yunjuapi.GlobalCompanyDetailsInterface;
import com.backend.common.yunjuapi.model.HkCorpIdentityInfoProcessor;
import com.backend.common.yunjuapi.model.HkCorpRealTimeInfoTOResult;
import com.backend.common.yunjuapi.model.hk.IdentificationObj;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.translation.HkIndustryTranslationUtil;
import com.qcc.frame.jee.modules.sys.translation.HkTranslationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;

@Service
public class HkBasicReportDataService extends AbstractReportDataService<HkCorpRealTimeInfoTOResult> {

    private static final Logger logger = LoggerFactory.getLogger(HkBasicReportDataService.class);

    @Autowired
    private SysConfigCacheService sysConfigCacheService;

    @Override
    public List<ReportTypeEnum> getReportTypes() {
        return Lists.newArrayList(ReportTypeEnum.HK_BAS);
    }

    @Override
    public boolean isDataSuccess(ReportDataGetResultForm form) {
        if (super.doCheckOrderDataSuccess(form)) {
            return true;
        }
        HkCorpRealTimeInfoTOResult data = getData(form);
        return data != null && data.getData() != null;
    }

    @Override
    protected BiConsumer<OrderData4LoopResult, HkCorpRealTimeInfoTOResult> getPropSetter() {
        return OrderData4LoopResult::setHkRealData;
    }

    @Override
    public HkCorpRealTimeInfoTOResult getData(ReportDataGetResultForm form) {
        HkCorpRealTimeInfoTOResult hkCorpRealTimeInfoTO = GlobalCompanyDetailsInterface.getHKDataByOrderNo(form.getApiOrderNo());
        fill4TO(hkCorpRealTimeInfoTO, form.getCompanyId(), form.getKeyNo(), form.getBankUser());
        return hkCorpRealTimeInfoTO;
    }

    @Override
    protected void doAfterInitOrderResult(OrderData4LoopResult result) {
        if (sysConfigCacheService.isNotInWorkingHours(LocalDateTime.now())) {
            result.setWorkdayFlag(Constants.NO);
        }
    }

    public void fill4TO(HkCorpRealTimeInfoTOResult realResult, String companyId, String keyNo, boolean isBankUser) {
        if (realResult != null && realResult.getData() != null) {
            // 解密董事
            // added for v1.7.8 KNZT-3321
            decryptForHkBasic(realResult);
            // 映射持股类型 added for v1.9.0 KNZT-4164
            mappingShareClass(realResult);
            mappingBusinessNature(realResult); // added for v1.9.1 KNZT-4258
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.HK);
            HkCorpRealTimeInfoTOResult.ResData data = MappingUtils.getValue(realResult, HkCorpRealTimeInfoTOResult::getData);

            HkCorpRealTimeInfoTOResult.ResData.Basic basic = MappingUtils.getValue(data, HkCorpRealTimeInfoTOResult.ResData::getBasic);
            transWrapper.add4Entity(basic, HkCorpRealTimeInfoTOResult.ResData.Basic::setCompanyNameChn, HkCorpRealTimeInfoTOResult.ResData.Basic::setCompanyNameEng, HkCorpRealTimeInfoTOResult.ResData.Basic::getCompanyNameChn, HkCorpRealTimeInfoTOResult.ResData.Basic::getCompanyNameEng, t -> keyNo, TransWrapper.ENTRY_TYPE_CORP);
            transWrapper.add4NonEntityWithReplace(basic, HkCorpRealTimeInfoTOResult.ResData.Basic::setAddress, HkCorpRealTimeInfoTOResult.ResData.Basic::getAddress);

            List<HkCorpRealTimeInfoTOResult.ResData.Shareholder> shareholders = MappingUtils.getValue(data, HkCorpRealTimeInfoTOResult.ResData::getShareholders);
            transWrapper.batchAdd4Entity(shareholders, HkCorpRealTimeInfoTOResult.ResData.Shareholder::setFullNameChn, HkCorpRealTimeInfoTOResult.ResData.Shareholder::setFullNameEng, HkCorpRealTimeInfoTOResult.ResData.Shareholder::getFullNameChn, HkCorpRealTimeInfoTOResult.ResData.Shareholder::getFullNameEng, null, o -> detectEntityType4Trans(o.getType()))
                    .batchAdd4NonEntityWithReplace(shareholders, HkCorpRealTimeInfoTOResult.ResData.Shareholder::setAddress, HkCorpRealTimeInfoTOResult.ResData.Shareholder::getAddress).batchAdd4NonEntityWithReplace(shareholders, HkCorpRealTimeInfoTOResult.ResData.Shareholder::setAddress, HkCorpRealTimeInfoTOResult.ResData.Shareholder::getAddress);

            List<HkCorpRealTimeInfoTOResult.ResData.Director> directors = MappingUtils.getValue(data, HkCorpRealTimeInfoTOResult.ResData::getDirectors);
            transWrapper.batchAdd4Entity(directors, HkCorpRealTimeInfoTOResult.ResData.Director::setFullNameChn, HkCorpRealTimeInfoTOResult.ResData.Director::setFullNameEng, HkCorpRealTimeInfoTOResult.ResData.Director::getFullNameChn, HkCorpRealTimeInfoTOResult.ResData.Director::getFullNameEng, null, o -> detectEntityType4Trans(o.getType()));

            List<HkCorpRealTimeInfoTOResult.ResData.CompanySecretary> companySecretaryList = MappingUtils.getValue(data, HkCorpRealTimeInfoTOResult.ResData::getCompanySecretary);
            transWrapper.batchAdd4Entity(companySecretaryList, HkCorpRealTimeInfoTOResult.ResData.CompanySecretary::setFullNameChn, HkCorpRealTimeInfoTOResult.ResData.CompanySecretary::setFullNameEng, HkCorpRealTimeInfoTOResult.ResData.CompanySecretary::getFullNameChn, HkCorpRealTimeInfoTOResult.ResData.CompanySecretary::getFullNameEng, null, o -> detectEntityType4Trans(o.getType()))
                    .batchAdd4NonEntityWithReplace(companySecretaryList, HkCorpRealTimeInfoTOResult.ResData.CompanySecretary::setAddress, HkCorpRealTimeInfoTOResult.ResData.CompanySecretary::getAddress);

            commonDataService.enPostProcessor(transWrapper);
        }
    }

    // added for v1.7.8 KNZT-3321
    // updated for v1.9.1 KNZT-3885
    private void decryptForHkBasic(HkCorpRealTimeInfoTOResult realResult) {
//        String userKeyByCompanyId = OpenApiUserInfoInterface.getUserKeyByCompanyId(companyId);
        String userKeyByCompanyId = Global.getConfig("openApi.global.qcc.com.key");
        if (StringUtils.isBlank(userKeyByCompanyId)) {
            logger.error("userKeyByCompanyId获取失败");
            return;
        }
        // updated for v2.0.9 fengsw KNZT-5608 解密董事、公司秘书数据
        List<HkCorpRealTimeInfoTOResult.ResData.Director> directors = Optional.ofNullable(realResult)
                .map(HkCorpRealTimeInfoTOResult::getData)
                .map(HkCorpRealTimeInfoTOResult.ResData::getDirectors)
                .orElse(null);
        processIdentificationData(directors, userKeyByCompanyId);

        List<HkCorpRealTimeInfoTOResult.ResData.CompanySecretary> companySecretaryList = Optional.ofNullable(realResult)
                .map(HkCorpRealTimeInfoTOResult::getData)
                .map(HkCorpRealTimeInfoTOResult.ResData::getCompanySecretary)
                .orElse(null);
        processIdentificationData(companySecretaryList, userKeyByCompanyId);
    }

    /**
     * 解密并设置对应字段值
     *
     * @param processors
     * @param userKeyByCompanyId
     */
    public static void processIdentificationData(List<? extends HkCorpIdentityInfoProcessor> processors, String userKeyByCompanyId) {
        if (CollectionUtils.isNotEmpty(processors)) {
            for (HkCorpIdentityInfoProcessor processor : processors) {
                List<IdentificationObj> identificationObjs = GlobalCompanyDetailsInterface.decryptFromIdentificationString(processor.getIdentificationString(), userKeyByCompanyId);
                if (CollectionUtils.isNotEmpty(identificationObjs)) {
                    GlobalCompanyDetailsInterface.desensitize(identificationObjs);
                    processor.setHkId(getIdentificationNumberByType(identificationObjs, "1"));
                    processor.setCnId(getIdentificationNumberByType(identificationObjs, "2"));
                    IdentificationObj passportObj = getIdentificationObjByType(identificationObjs, "3");
                    if (passportObj != null) {
                        processor.setPassportNo(passportObj.getIdentificationNumber());
                        processor.setPassportCountry(passportObj.getPassportCountry());
                    }
                }
            }
        }
    }

    /**
     * 根据类型获取身份证号
     */
    private static String getIdentificationNumberByType(List<IdentificationObj> identificationObjs, String type) {
        if (CollectionUtils.isEmpty(identificationObjs)) return null;
        return identificationObjs.stream()
                .filter(identificationObj -> type.equals(identificationObj.getIdentificationType()))
                .findFirst()
                .map(IdentificationObj::getIdentificationNumber)
                .orElse(null);
    }

    /**
     * 根据类型获取IdentificationObj对象
     */
    private static IdentificationObj getIdentificationObjByType(List<IdentificationObj> identificationObjs, String type) {
        if (CollectionUtils.isEmpty(identificationObjs)) return null;
        return identificationObjs.stream()
                .filter(identificationObj -> type.equals(identificationObj.getIdentificationType()))
                .findFirst()
                .orElse(null);
    }    // 映射持股类型 added for v1.9.0 KNZT-4164

    private static void mappingShareClass(HkCorpRealTimeInfoTOResult realResult) {
        Optional.ofNullable(realResult)
                .map(HkCorpRealTimeInfoTOResult::getData)
                .map(HkCorpRealTimeInfoTOResult.ResData::getShareholders)
                .ifPresent(hkCorpShareHolderDetailTOS ->
                        hkCorpShareHolderDetailTOS.forEach(hkCorpShareHolderDetailTO -> hkCorpShareHolderDetailTO.setClassofShares(HkTranslationUtil.mappingShareClass(hkCorpShareHolderDetailTO.getClassofShares())))
                );
    }

    // added for v1.9.1 KNZT-4258
    private static void mappingBusinessNature(HkCorpRealTimeInfoTOResult realResult) {
        Optional.ofNullable(realResult)
                .map(HkCorpRealTimeInfoTOResult::getData)
                .map(HkCorpRealTimeInfoTOResult.ResData::getBasic)
                .ifPresent(hkCorpBasicDetailTO -> {
                    hkCorpBasicDetailTO.setBusinessNatureDes(HkIndustryTranslationUtil.getIndustryEn(hkCorpBasicDetailTO.getBusinessNatureCode(), hkCorpBasicDetailTO.getBusinessNatureDes()));
                });
    }

    private static Integer detectEntityType4Trans(String entityType) {
        if (StringUtils.equals("自然人", entityType)) {
            return TransWrapper.ENTRY_TYPE_PERSON;
        } else if (StringUtils.equals("法人团体", entityType)) {
            return TransWrapper.ENTRY_TYPE_CORP;
        }
        return null;
    }

}
