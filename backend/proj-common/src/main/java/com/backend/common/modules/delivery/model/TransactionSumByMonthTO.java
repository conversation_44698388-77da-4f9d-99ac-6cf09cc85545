package com.backend.common.modules.delivery.model;

import java.math.BigDecimal;

/**
 * added for v1.9.2 KNZT-3894
 */
public class TransactionSumByMonthTO {

    private Integer monthInt;

    private BigDecimal changedUnitTotal;

    public Integer getMonthInt() {
        return monthInt;
    }

    public void setMonthInt(Integer monthInt) {
        this.monthInt = monthInt;
    }

    public BigDecimal getChangedUnitTotal() {
        return changedUnitTotal;
    }

    public void setChangedUnitTotal(BigDecimal changedUnitTotal) {
        this.changedUnitTotal = changedUnitTotal;
    }
}
