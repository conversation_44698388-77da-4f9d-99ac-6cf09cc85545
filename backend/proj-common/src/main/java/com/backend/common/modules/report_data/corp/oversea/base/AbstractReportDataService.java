package com.backend.common.modules.report_data.corp.oversea.base;

import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.model.OrderData4LoopResult;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * @param <R> 数据TO
 */
public abstract class AbstractReportDataService<R> {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    protected CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    protected OvsQccOvsBasicService ovsQccOvsBasicService;
    @Autowired
    protected CommonDataService commonDataService;

    /**
     * 支持的reportType
     *
     * @return
     */
    public abstract List<ReportTypeEnum> getReportTypes();

//    public abstract List<CanBuyTO> canOrder(Collection<String> keyNos);

    /**
     * 订单状态是数据已成功
     *
     * @param form
     * @return
     */
    protected boolean doCheckOrderDataSuccess(ReportDataGetResultForm form) {
        TblCompReportOrder tblCompReportOrder = commTblCompReportOrderService.get(form.getOrderId());
        return doCheckOrderDataSuccess(tblCompReportOrder);
    }

    /**
     * 订单状态是数据已成功
     *
     * @param order
     * @return
     */
    protected boolean doCheckOrderDataSuccess(TblCompReportOrder order) {
        if (order == null) {
            logger.error("TblCompReportOrder is null");
            return false;
        }
        return OrderStatusEnum.getDataSuccessStatus().contains(order.getRptStatus());
    }

    /**
     * 检查数据是否已处理成功
     * 注意: 底层数据落表不代表数据已处理成功(比如股东里公司匹配逻辑会插入记录后再处理, 然后更新),
     * 实现此方法时需要跟数据确认数据处理成功的判断逻辑。
     *
     * @param form
     * @return
     */
    public abstract boolean isDataSuccess(ReportDataGetResultForm form);

    protected OrderData4LoopResult initResult(ReportDataGetResultForm form, TblCompReportOrder order) {
        OrderData4LoopResult result = new OrderData4LoopResult();
        if (order != null) {
            result.setReportType(order.getReportType());
            result.setOrderCreateDate(order.getCreateDate());
        }
        doAfterInitOrderResult(result);
        return result;
    }

    /**
     * added for v2.3.3 fengsw KNZT-8350
     * 初始化订单结果后处理，子类可覆盖此方法进行额外操作
     * 当前仅有香港报告Basic、Basic+ar需要处理是否非工作日判断，新加坡Basic处理特定类型的非工作判断
     * 后续其他类型报告需要处理数据，可覆盖此方法重新实现
     *
     * @param result
     */
    protected void doAfterInitOrderResult(OrderData4LoopResult result) {
    }

    public OrderData4LoopResult getResult(ReportDataGetResultForm form) {
        TblCompReportOrder order = commTblCompReportOrderService.get(form.getOrderId());
        OrderData4LoopResult result = initResult(form, order);
        if (!doCheckOrderDataSuccess(order)) {
            return result;
        }
        BiConsumer<OrderData4LoopResult, R> propSetter = getPropSetter();
        propSetter.accept(result, getData(form));
        return result;
    }

    protected abstract BiConsumer<OrderData4LoopResult, R> getPropSetter();

    protected abstract R getData(ReportDataGetResultForm form);

    protected String formatCountryCity(String country, String city) {
        if (StringUtils.isBlank(city)) {
            return country;
        } else if (StringUtils.isBlank(country)) {
            return city;
        } else {
            return city + ", " + country;
        }
    }
}
