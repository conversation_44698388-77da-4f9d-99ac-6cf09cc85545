package com.backend.common.modules.delivery.mapper;

import org.apache.ibatis.annotations.Param;

import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.backend.common.modules.delivery.model.StatementMonthly4ManagementTO;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;

import java.util.List;

@MyBatisDao
public interface TblCompStatementMonthlyDao extends CrudDao<TblCompStatementMonthly> {
    TblCompStatementMonthly getByCompanyAndYearMonth(@Param("companyId") String companyId, @Param("statementMonth") Integer statementMonth);

    /**
     * 根据发票号查询月度报表
     * @param invoiceNo 发票号
     * @return TblCompStatementMonthly
     */
    TblCompStatementMonthly getByInvoiceNo(@Param("invoiceNo") String invoiceNo);
    
    /**
     * 月度账单管理分页查询
     * @param condition 查询条件
     * @return 账单列表
     */
    List<StatementMonthly4ManagementTO> page4Management(@Param("obj") StatementMonthly4ManagementCondition condition);

    List<TblCompStatementMonthly> listByIds(@Param("ids")List<String> statementMonthlyIdList);

    List<TblCompStatementMonthly> getUnfinishedPayments();
}
