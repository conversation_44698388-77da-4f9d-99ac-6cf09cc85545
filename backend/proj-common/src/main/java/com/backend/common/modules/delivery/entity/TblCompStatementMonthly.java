package com.backend.common.modules.delivery.entity;

import java.util.Date;
import java.math.BigDecimal;

import com.qcc.frame.jee.commons.persistence.DataEntity;


/**
 * added for v1.9.5 KNZT-4117
 * <AUTHOR>
 * @datetime 2024/8/30 10:09
 */
public class TblCompStatementMonthly extends DataEntity<TblCompStatementMonthly> {
	
	private Integer statementMonth;
	private Date issueDate;
	private String invoiceNo;
	private BigDecimal startCredit;
	private BigDecimal startAmount;
	private BigDecimal endCredit;
	private BigDecimal endAmount;
	private BigDecimal endGiftedCredit;
	private BigDecimal creditsToppedUp;
	private BigDecimal creditsGifted;
	private BigDecimal creditsUsed;
	private BigDecimal amountUsed;
	private BigDecimal creditsRefunded;
	private BigDecimal creditsExpired;
	private String url;
	private BigDecimal totalCreditDue;
	private BigDecimal totalAmountDue;
	private String returnStatus;
	private String kzzContractNo;

	public Integer getStatementMonth() {
		return statementMonth;
	}
	public void setStatementMonth(Integer statementMonth) {
		this.statementMonth = statementMonth;
	}
	public Date getIssueDate() {
		return issueDate;
	}
	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}
	public String getInvoiceNo() {
		return invoiceNo;
	}
	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}
	public BigDecimal getStartCredit() {
		return startCredit;
	}
	public void setStartCredit(BigDecimal startCredit) {
		this.startCredit = startCredit;
	}
	public BigDecimal getStartAmount() {
		return startAmount;
	}
	public void setStartAmount(BigDecimal startAmount) {
		this.startAmount = startAmount;
	}
	public BigDecimal getEndCredit() {
		return endCredit;
	}
	public void setEndCredit(BigDecimal endCredit) {
		this.endCredit = endCredit;
	}
	public BigDecimal getEndAmount() {
		return endAmount;
	}
	public void setEndAmount(BigDecimal endAmount) {
		this.endAmount = endAmount;
	}
	public BigDecimal getEndGiftedCredit() {
		return endGiftedCredit;
	}
	public void setEndGiftedCredit(BigDecimal endGiftedCredit) {
		this.endGiftedCredit = endGiftedCredit;
	}
	public BigDecimal getCreditsToppedUp() {
		return creditsToppedUp;
	}
	public void setCreditsToppedUp(BigDecimal creditsToppedUp) {
		this.creditsToppedUp = creditsToppedUp;
	}
	public BigDecimal getCreditsGifted() {
		return creditsGifted;
	}
	public void setCreditsGifted(BigDecimal creditsGifted) {
		this.creditsGifted = creditsGifted;
	}
	public BigDecimal getCreditsUsed() {
		return creditsUsed;
	}
	public void setCreditsUsed(BigDecimal creditsUsed) {
		this.creditsUsed = creditsUsed;
	}
	public BigDecimal getAmountUsed() {
		return amountUsed;
	}
	public void setAmountUsed(BigDecimal amountUsed) {
		this.amountUsed = amountUsed;
	}
	public BigDecimal getCreditsRefunded() {
		return creditsRefunded;
	}
	public void setCreditsRefunded(BigDecimal creditsRefunded) {
		this.creditsRefunded = creditsRefunded;
	}
	public BigDecimal getCreditsExpired() {
		return creditsExpired;
	}
	public void setCreditsExpired(BigDecimal creditsExpired) {
		this.creditsExpired = creditsExpired;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public BigDecimal getTotalCreditDue() {
		return totalCreditDue;
	}
	public void setTotalCreditDue(BigDecimal totalCreditDue) {
		this.totalCreditDue = totalCreditDue;
	}
	public BigDecimal getTotalAmountDue() {
		return totalAmountDue;
	}
	public void setTotalAmountDue(BigDecimal totalAmountDue) {
		this.totalAmountDue = totalAmountDue;
	}
	public String getReturnStatus() {
		return returnStatus;
	}
	public void setReturnStatus(String returnStatus) {
		this.returnStatus = returnStatus;
	}

	public String getKzzContractNo() {
		return kzzContractNo;
	}

	public void setKzzContractNo(String kzzContractNo) {
		this.kzzContractNo = kzzContractNo;
	}
}
