package com.backend.common.yunjuapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.backend.common.yunjuapi.model.*;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * // added for v1.1.6 KNZT-820
 */
public class RelationInterface {

    private static TranslaterService translaterService = SpringContextHolder.getBean(TranslaterService.class);

//
//    // updated for v1.6.5 KNZT-2810 方法重载，入参加入needCompanyKey 为false则代表使用默认key, true代表使用主账号绑定的key
//    public static List<ActualControllerTO> getActualControllerV3(String corpKeyNo) {
//        return getActualControllerV3(corpKeyNo, true);
//    }
//
//    public static List<ActualControllerTO> getActualControllerV3(String corpKeyNo, boolean needCompanyKey) {
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("keyNo", corpKeyNo);
//        paramMap.put("isCollapsed", true);
//        paramMap.put("isOnlyMainPath", true);
//
//        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/Relation/GetSuspectedActualControllerV3", "2", JSON.toJSONString(paramMap), needCompanyKey);
//        JSONObject jsonObject = JsonUtils.parseObject(resp);
//        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("Result") != null) {
//            List<ActualControllerTO> actualControllerTOList = new ArrayList<>();
//            // updated for v1.5.1 KNZT-2258
////            Map<String, ActualControllerTO> keyNoActualControllerMap = new HashMap<>();
//            Set<String> corpKeyNos = new HashSet<>();
//
//            if (jsonObject.getJSONObject("Result").getJSONObject("ActualControl") != null) {
//                String controlPercent = jsonObject.getJSONObject("Result").getJSONObject("ActualControl").getString("ControlPercent");
//
//                JSONArray personArr = jsonObject.getJSONObject("Result").getJSONObject("ActualControl").getJSONArray("PersonList");
//                if (personArr != null && !personArr.isEmpty()) {
//                    for (int i = 0; i < personArr.size(); i++) {
//                        ActualControllerTO to =  new ActualControllerTO();
//                        JSONObject obj = personArr.getJSONObject(i);
//                        String keyNo = obj.getString("KeyNo");
//                        // updated for v1.5.1 KNZT-2258
//                        to.setKeyNo(keyNo);
//                        to.setActualControllerName(obj.getString("Name"));
//                        if ((StringUtils.equals("2", obj.getString("Org")) || StringUtils.equals("-2", obj.getString("Org"))) && !PinyinUtils.notConvert2Pinyin(to.getActualControllerName())) {
//                            to.setActualControllerNameEn(PinyinUtils.chineseNameToPinyin(to.getActualControllerName()));
//                        }
//                        to.setDirectShareHolding(obj.getString("Percent"));
//                        if (StringUtils.isNotBlank(controlPercent)) {
//                            to.setProportionOfVotingRight(controlPercent+"%");
//                        }
//                        to.setUltimateOwnershipInterests(obj.getString("PercentTotal"));
//
//                        if (StringUtils.isNotBlank(keyNo) && !StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX)) {
//                            // updated for v1.5.1 KNZT-2258
////                            keyNoActualControllerMap.put(keyNo, to);
//                            corpKeyNos.add(keyNo);
//                        }
//
//                        actualControllerTOList.add(to);
//                    }
//                }
//            } else {
//                JSONArray jsonArray = jsonObject.getJSONObject("Result").getJSONArray("Names");
//                if (jsonArray != null && !jsonArray.isEmpty()) {
//                    for (int i = 0; i < jsonArray.size(); i++) {
//                        ActualControllerTO to = new ActualControllerTO();
//
//                        JSONObject obj = jsonArray.getJSONObject(i);
//                        String keyNo = obj.getString("KeyNo");
//                        // updated for v1.5.1 KNZT-2258
//                        to.setKeyNo(keyNo);
//                        to.setActualControllerName(obj.getString("Name"));
//                        // updated for v1.2.2 KNZT-1112
////                        if (StringUtils.equals("2", obj.getString("Org")) && !PinyinUtils.notConvert2Pinyin(to.getActualControllerName())) {
////                            to.setActualControllerNameEn(PinyinUtils.chineseNameToPinyin(to.getActualControllerName()));
////                        }
//                        if (StringUtils.isBlank(keyNo) || StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX)) {
//                            CompanyDetailsInterface.enNamePostProcessor(to, ActualControllerTO::setActualControllerNameEn, ActualControllerTO::getKeyNo, ActualControllerTO::getActualControllerName, ActualControllerTO::getActualControllerNameEn);
//                        }
//                        to.setDirectShareHolding(obj.getString("PartnerPercent"));
//                        to.setProportionOfVotingRight(obj.getString("ControlPercent"));
//                        to.setUltimateOwnershipInterests(obj.getString("PercentTotal"));
//
//                        int isActual = obj.getIntValue("IsActual");
//                        if (isActual < 1) {
//                            return null; // isActual >= 1，则为实际 控制人，否则，为疑似，现在逻辑是 疑似的，不返回
//                        }
//
//
//                        if (StringUtils.isNotBlank(keyNo) && !StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX)) {
//                            // updated for v1.5.1 KNZT-2258
////                            keyNoActualControllerMap.put(keyNo, to);
//                            corpKeyNos.add(keyNo);
//                        } else if (StringUtils.isBlank(keyNo) && StringUtils.isBlank(to.getActualControllerNameEn())) { // updated for v1.2.1 KNZT-970
//                            if (TranslaterService.isTranslated(to.getActualControllerName())) {
//                                String enTxt = translaterService.getEnglishText(to.getActualControllerName(), "", to.getActualControllerName(), SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
//                                if (StringUtils.isNotBlank(enTxt)) {
//                                    to.setActualControllerNameEn(enTxt);
//                                }
//                            }
//                        }
//                        // updated for v1.5.1 KNZT-2258
//                        JSONArray pathsJa = obj.getJSONArray("Paths");
//                        if(CollectionUtils.isNotEmpty(pathsJa)){
//                            to.setPaths(pathsJa.toJavaObject(new TypeReference<List<List<ActualControllerPathTO>>>(){}));
//                        }
//                        JSONArray collapsedPathsJa = obj.getJSONArray("CollapsedPaths");
//                        if (CollectionUtils.isNotEmpty(collapsedPathsJa)) {
//                            to.setCollapsedPaths(collapsedPathsJa.toJavaObject(new TypeReference<List<ActualControllerCollapsedPathTO>>() {
//                            }));
//                        }
//                        actualControllerTOList.add(to);
//                    }
//                }
//            }
//
//// updated for v1.5.1 KNZT-2258
////            if (!keyNoActualControllerMap.isEmpty()) {
////                List<String> keyNoList = new ArrayList<>(keyNoActualControllerMap.keySet());
////                // updated for v1.2.1 KNZT-970
////                List< AdvanceSearchTO > englishNameListByKeyNoList = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(keyNoList);
////                if (CollectionUtils.isNotEmpty(englishNameListByKeyNoList)) {
////                    for (AdvanceSearchTO keyNoEnNameTO : englishNameListByKeyNoList) {
////                        if (StringUtils.isNotBlank(keyNoEnNameTO.getNameEn())) {
////                            ActualControllerTO actualControllerTO = keyNoActualControllerMap.get(keyNoEnNameTO.getKeyNo());
////                            if (actualControllerTO != null) {
////                                if (StringUtils.isBlank(actualControllerTO.getActualControllerNameEn())) {
////                                    actualControllerTO.setActualControllerNameEn(keyNoEnNameTO.getNameEn());
////                                }
////                                if (StringUtils.isNotBlank(actualControllerTO.getActualControllerNameEn()) && StringUtils.equals(actualControllerTO.getActualControllerNameEn(), actualControllerTO.getActualControllerName())) {
////                                    actualControllerTO.setActualControllerNameEn(null);
////                                }
////                            }
////                        }
////                    }
////                }
////            }
//            // 将paths中需要翻译的企业keyNo提取出来
//            for (ActualControllerTO actualControllerTO : actualControllerTOList) {
//                if (CollectionUtils.isNotEmpty(actualControllerTO.getPaths())) {
//                    corpKeyNos.addAll(actualControllerTO.getPaths().stream()
//                            .flatMap(Collection::stream).map(ActualControllerPathTO::getKeyNo).filter(keyNo -> StringUtils.isNotBlank(keyNo) && !keyNo.startsWith(Constants.CorpTypePrefix.PERSON_PREFIX))
//                            .collect(Collectors.toSet()));
//                }
//                if (CollectionUtils.isNotEmpty(actualControllerTO.getCollapsedPaths())) {
//                    // 将collapsedPaths中需要翻译的企业keyNo提取出来
//                    corpKeyNos.addAll(actualControllerTO.getCollapsedPaths().stream()
//                            .map(ActualControllerCollapsedPathTO::getPaths).flatMap(Collection::stream).map(ActualControllerPathTO::getKeyNo)
//                            .filter(keyNo -> StringUtils.isNotBlank(keyNo) && !keyNo.startsWith(Constants.CorpTypePrefix.PERSON_PREFIX)).collect(Collectors.toSet()));
//                    corpKeyNos.addAll(actualControllerTO.getCollapsedPaths().stream()
//                            .map(ActualControllerCollapsedPathTO::getChildren).flatMap(Collection::stream).flatMap(Collection::stream).map(ActualControllerPathTO::getKeyNo)
//                            .filter(keyNo -> StringUtils.isNotBlank(keyNo) && !keyNo.startsWith(Constants.CorpTypePrefix.PERSON_PREFIX)).collect(Collectors.toSet()));
//                }
//            }
//            // updated for v1.5.3 KNZT-2528
//            Map<String, AdvanceSearchTO> keyNo2AdvanceSearchMap = new HashMap<>(0);
//            if (!corpKeyNos.isEmpty()) {
//                List<String> keyNoList = new ArrayList<>(corpKeyNos);
//                // updated for v1.2.1 KNZT-970
//                List< AdvanceSearchTO > englishNameListByKeyNoList = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(keyNoList, needCompanyKey);
//                if (CollectionUtils.isNotEmpty(englishNameListByKeyNoList)) {
//                    keyNo2AdvanceSearchMap.putAll(englishNameListByKeyNoList.stream().collect(Collectors.toMap(AdvanceSearchTO::getKeyNo, Function.identity(), (k1, k2) -> k1)));
//                }
//            }
//            for (ActualControllerTO actualControllerTO : actualControllerTOList) {
//                if(StringUtils.isNotBlank(actualControllerTO.getKeyNo())){
//                    AdvanceSearchTO advanceSearchTO = keyNo2AdvanceSearchMap.get(actualControllerTO.getKeyNo());
//                    if(advanceSearchTO != null && StringUtils.isNotBlank(advanceSearchTO.getNameEn())){
//                        if (StringUtils.isBlank(actualControllerTO.getActualControllerNameEn())) {
//                            actualControllerTO.setActualControllerNameEn(advanceSearchTO.getNameEn());
//                        }
//                        if (StringUtils.isNotBlank(actualControllerTO.getActualControllerNameEn()) && StringUtils.equals(actualControllerTO.getActualControllerNameEn(), actualControllerTO.getActualControllerName())) {
//                            actualControllerTO.setActualControllerNameEn(null);
//                        }
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(actualControllerTO.getPaths())) {
//                    List<ActualControllerPathTO> paths = actualControllerTO.getPaths().stream().flatMap(Collection::stream).collect(Collectors.toList());
//                    for (ActualControllerPathTO pathsTO : paths) {
//                        populateNameEnForPath(pathsTO, keyNo2AdvanceSearchMap);
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(actualControllerTO.getCollapsedPaths())) {
//                    for (ActualControllerCollapsedPathTO collapsedPathItemTO : actualControllerTO.getCollapsedPaths()) {
//                        List<ActualControllerPathTO> paths = collapsedPathItemTO.getPaths().stream().collect(Collectors.toList());
//                        for (ActualControllerPathTO pathsTO : paths) {
//                            populateNameEnForPath(pathsTO, keyNo2AdvanceSearchMap);
//                        }
//                        List<ActualControllerPathTO> childPaths = collapsedPathItemTO.getChildren().stream().flatMap(Collection::stream).collect(Collectors.toList());
//                        for (ActualControllerPathTO pathsTO : childPaths) {
//                            populateNameEnForPath(pathsTO, keyNo2AdvanceSearchMap);
//                        }
//                    }
//                }
//            }
////            for (ActualControllerTO controller : actualControllerTOList) {
////                if (StringUtils.isBlank(controller.getActualControllerNameEn())) {
////
////                }
////            }
//
//            return actualControllerTOList;
//        }
//        return null;
//    }
//
//    /**
//     * added for v1.5.1 KNZT-2258
//     */
//    private static void populateNameEnForPath(ActualControllerPathTO pathsTO, Map<String, AdvanceSearchTO> keyNo2AdvanceSearchMap) {
//        // 翻译name
//        if (StringUtils.isNotBlank(pathsTO.getKeyNo())) {
//            AdvanceSearchTO advanceSearchTO = keyNo2AdvanceSearchMap.get(pathsTO.getKeyNo());
//            if (advanceSearchTO != null) {
//                pathsTO.setNameEn(advanceSearchTO.getNameEn());
//            }
//        }
//        CompanyDetailsInterface.enNamePostProcessor(pathsTO, ActualControllerPathTO::setNameEn, ActualControllerPathTO::getKeyNo, ActualControllerPathTO::getName, ActualControllerPathTO::getNameEn);
//        // 翻译地址标签 tags->type为112/115/402. tags->name
//        if (CollectionUtils.isNotEmpty(pathsTO.getTags())) {
//            for (ActualControllerPathTagTO pathTagTO : pathsTO.getTags()) {
//                boolean isArea = 112 == pathTagTO.getType() || 115 == pathTagTO.getType() || 402 == pathTagTO.getType();
//                if (isArea && StringUtils.isNotBlank(pathTagTO.getName())) {
//                    // updated for v1.9.1 KNZT-4101 映射调整
//                    GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(pathTagTO.getName());
//                    if (Objects.nonNull(areaEnum)) {
//                        pathTagTO.setNameEn(areaEnum.getShortNameEn());
//                    } else {
//                        String englishText = translaterService.getEnglishText(pathTagTO.getName(), pathsTO.getKeyNo(), pathsTO.getName(), SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
//                        pathTagTO.setNameEn(englishText);
//                    }
//                }
//            }
//        }
//    }


    /**
     * // removed for v1.2.1 KNZT-970
     */
//    public static List<KeyNoEnNameTO> getEnglishNameListByKeyNoList(List<String> corpKeyNoList) {
//
//        Map<String, Object> param = new HashMap<>();
//        param.put("keyNos", corpKeyNoList);
//        List<KeyNoEnNameTO> resultList = new ArrayList<>();
//        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/ECILocal/BatchExportDetails", "2", JSON.toJSONString(param));
//        JSONObject jsonObject = JsonUtils.parseObject(resp);
//        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONArray("Result") != null && !jsonObject.getJSONArray("Result") .isEmpty()) {
//            for (int i = 0; i < jsonObject.getJSONArray("Result").size(); i++) {
//                KeyNoEnNameTO to = new KeyNoEnNameTO();
//                JSONObject obj = jsonObject.getJSONArray("Result").getJSONObject(i);
//                String keyNo = obj.getString("KeyNo");
//                String englishName = obj.getString("EnglishName");
//                to.setEnglishName(englishName);
//                to.setKeyNo(keyNo);
//                resultList.add(to);
//            }
//        }
//        return resultList;
//    }


}
