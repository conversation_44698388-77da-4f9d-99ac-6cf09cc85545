package com.backend.common.googleapi.model;

import java.util.List;

public class PlacesAutoCompleteResp {

    private List<Suggestion> suggestions;

    public List<Suggestion> getSuggestions() {
        return suggestions;
    }

    public void setSuggestions(List<Suggestion> suggestions) {
        this.suggestions = suggestions;
    }

    public static final class Suggestion {
        private PlacePrediction placePrediction;

        public PlacePrediction getPlacePrediction() {
            return placePrediction;
        }

        public void setPlacePrediction(PlacePrediction placePrediction) {
            this.placePrediction = placePrediction;
        }
    }

    public static final class PlacePrediction {
        private String placeId;
        private Text text;

        public String getPlaceId() {
            return placeId;
        }

        public void setPlaceId(String placeId) {
            this.placeId = placeId;
        }

        public Text getText() {
            return text;
        }

        public void setText(Text text) {
            this.text = text;
        }
    }

    public static final class Text {
        private String text;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }
    }
}
