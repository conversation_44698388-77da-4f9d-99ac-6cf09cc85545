package com.backend.common.modules.open.service;

import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.DeliveryBusinessService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.stripeapi.StripePaymentInterface;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.CommDelayedTaskTypeEnum;
import com.qcc.frame.commons.ienum.DeliveryContracProdAccTypeEnum;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.PayStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.service.ApiUserLoginService;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.stripe.model.Event;
import com.stripe.model.EventDataObjectDeserializer;
import com.stripe.model.PaymentIntent;
import com.stripe.model.StripeObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 支付 service
 * added for v1.9.3 KNZT-4193
 *
 * <AUTHOR>
 * @datetime 2024/8/6 11:39
 */
@Service
public class PaymentBusinessService {
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private ApiUserLoginService apiUserLoginService;
    @Autowired
    private DeliveryBusinessService deliveryBusinessService;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblContractDeliveryProdAccService prodAccService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private CommSysCompInfoFuncCountService compInfoFuncCountService;
    @Autowired
    private RedisService redisService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 处理stripe webhook事件
     * <a href="https://docs.stripe.com/api/webhook_endpoints"/>事件枚举参考</a>
     *
     * @param event
     * @return
     */
    public void handleStripeWebhook(Event event) throws MessageException {
        String type = event.getType();
        if (!StringUtils.equals(type, Constants.Delivery.EVENT_PAYMENT_INTENT_SUCCEEDED)
                && !StringUtils.equals(type, Constants.Delivery.EVENT_PAYMENT_INTENT_CANCELED)
                && !StringUtils.equals(type, Constants.Delivery.EVENT_CHARGE_REFUNDED)) {
            logger.info("stripe webhook unhandled type:{}", type);
            return;
        }
        EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
        boolean present = dataObjectDeserializer.getObject().isPresent();
        MsgExceptionUtils.failBuild(!present, "stripe webhook data is null");
        StripeObject stripeObject = dataObjectDeserializer.getObject().get();
        if (!(stripeObject instanceof PaymentIntent)) {
            logger.info("stripe webhook unhandled type:{}, stripeObject:{}", type, stripeObject.getClass());
            return;
        }
        PaymentIntent oriPaymentIntent = (PaymentIntent) stripeObject;
        String paymentIntentId = oriPaymentIntent.getId();
        PaymentIntent paymentIntent = StripePaymentInterface.retrievePaymentIntent(paymentIntentId);
        MsgExceptionUtils.checkIsNull(paymentIntent, "webhook paymentIntent is null");
        logger.info("stripe webhook {} execute begin, paymentIntentId:{}", type, paymentIntentId);
        try {
            switch (type) {
                case Constants.Delivery.EVENT_PAYMENT_INTENT_SUCCEEDED: // 支付成功,提交订单
                    handleSuccess(paymentIntent);
                    break;
                case Constants.Delivery.EVENT_PAYMENT_INTENT_CANCELED: // 支付取消,流转订单状态
                    handleCancel(paymentIntent);
                    break;
                case Constants.Delivery.EVENT_CHARGE_REFUNDED: // 支付退款,告警提醒确认是否正常
                    sysDingMsgNewTranService.sendBusinessReminder(AlarmTypeEnum.STRIPE_REFUND, paymentIntent.getId());
                    break;
                default:
                    logger.info("stripe webhook unhandled type:{}", type);
                    break;
            }
        } catch (Exception e) {
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.STRIPE_WEBHOOK_HANDLE_ERROR, e, type , paymentIntentId);
            logger.error("stripe webhook error, type:{}, paymentIntentId:{}", type, paymentIntentId);
            throw e;
        }
        logger.info("stripe webhook {} execute end, paymentIntentId:{}", type, paymentIntentId);
    }

    /**
     * 处理支付取消事件
     * @param paymentIntent
     * @throws MessageException
     */
    public void handleCancel(PaymentIntent paymentIntent) throws MessageException {
        MsgExceptionUtils.failBuild(!StringUtils.equals(paymentIntent.getStatus(), Constants.StripePaymentIntent.STATUS_CANCELED),
                "webhook paymentIntent status not match, current:" + paymentIntent.getStatus());
        List<TblCompReportOrder> orderList = commTblCompReportOrderService.getByPayRelId(paymentIntent.getId());
        if (CollectionUtils.isEmpty(orderList)) {
            logger.info("stripe webhook paymentIntentCanceled, order is null, paymentIntentId:{}", paymentIntent.getId());
            return;
        }
        for (TblCompReportOrder order : orderList) {
            if (PayStatusEnum.CANCEL.getCode().equals(order.getPayStatus()) || OrderStatusEnum.FAILED.getCode().equals(order.getRptStatus())) {
                logger.info("stripe webhook paymentIntentCanceled, orderId:{} already canceled, skip", order.getId());
                continue;
            }
            MsgExceptionUtils.failBuild(!StringUtils.equals(order.getRptStatus(), OrderStatusEnum.NONE.getCode()),
                    "webhook order status not match, current:" + order.getRptStatus());
            MsgExceptionUtils.failBuild(!StringUtils.equals(order.getPayStatus(), PayStatusEnum.UNPAID.getCode()),
                    "webhook order pay status not match, current:" + order.getPayStatus());
            order.transitionStatus(OrderStatusEnum.FAILED);
            commTblCompReportOrderService.save(order);
            logger.info("stripe webhook paymentIntentCanceled, orderId:{}", order.getId());
        }

    }


    /**
     * 处理支付成功事件
     * @param paymentIntent
     * @throws MessageException
     */
    public void handleSuccess(PaymentIntent paymentIntent) throws MessageException {
        // 取消超时任务
        redisService.removeDelayedCommTask(CommDelayedTaskTypeEnum.PAYMENT_INTENT_CANCEL, paymentIntent.getId());

        MsgExceptionUtils.failBuild(!StringUtils.equals(paymentIntent.getStatus(), Constants.StripePaymentIntent.STATUS_SUCCEED),
                "webhook paymentIntent status not match, current:" + paymentIntent.getStatus());

        TblContractDelivery existTopUpContract = contractDeliveryService.getByPayRelId(paymentIntent.getId());
        if (Objects.nonNull(existTopUpContract)) {
            logger.info("stripe webhook paymentIntentSucceeded, existTopUpContract is not null, paymentIntentId:{}", paymentIntent.getId());
            return;
        }
        // 创建充值合同
        TblContractDeliveryProdAcc prodAcc = createTopUpContract(paymentIntent);

        List<TblCompReportOrder> orderList = commTblCompReportOrderService.getByPayRelId(paymentIntent.getId());
        if (CollectionUtils.isEmpty(orderList)) {
            logger.info("stripe webhook paymentIntentSucceeded, order is null, paymentIntentId:{}", paymentIntent.getId());
            return;
        }
        BigDecimal totalUnit = orderList.stream().map(TblCompReportOrder::getTotalUnit).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal paymentAmount = BigDecimal.valueOf(paymentIntent.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        BigDecimal creditToChargeByProdAcc = totalUnit.subtract(paymentAmount);



        // 校验额度
        String companyId = orderList.get(0).getCompanyId();
        SysCompInfoFuncCount funcCount = compInfoFuncCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(funcCount, "err.amount.insufficient");
        if (funcCount.calRemainCount().compareTo(creditToChargeByProdAcc) < 0) {
            for (TblCompReportOrder order : orderList) {
                order.transitionStatus(OrderStatusEnum.FAILED);
                commTblCompReportOrderService.save(order);
                logger.info("PaymentIntent handleSuccess, but funcCount insufficient, cancel orderId:{}", order.getId());
            }
            logger.info("PaymentIntent handleSuccess exit, paymentIntentId:{}", paymentIntent.getId());
            return;
        }


        // 锁定余额账户
        prodAccService.lockValidAccByCompanyId(companyId);

        // 财税、法律订单放到最后处理
        orderList.sort((o1, o2) -> {
            String type1 = o1.getReportType();
            String type2 = o2.getReportType();
            if (StringUtils.equalsAny(type1, ReportTypeEnum.FIN_TAX.getCode(), ReportTypeEnum.LEGAL.getCode())) {
                return -1;  // 返回-1表示o1排在前面
            }
            if (StringUtils.equalsAny(type2, ReportTypeEnum.FIN_TAX.getCode(), ReportTypeEnum.LEGAL.getCode())) {
                return 1;   // 返回1表示o2排在前面
            }
            return 0;
        });


        // 处理订单
        for (TblCompReportOrder order : orderList) {
            if (PayStatusEnum.PAID.getCode().equals(order.getPayStatus())) {
                logger.info("PaymentIntent handleSuccess, orderId:{} already paid, skip", order.getId());
                continue;
            }
            MsgExceptionUtils.failBuild(!StringUtils.equals(order.getRptStatus(), OrderStatusEnum.NONE.getCode()),
                    "webhook order status not match, current:" + order.getRptStatus());
            MsgExceptionUtils.failBuild(!StringUtils.equals(order.getPayStatus(), PayStatusEnum.UNPAID.getCode()),
                    "webhook order pay status not match, current:" + order.getPayStatus());
            // 扣费
            transactionBusinessService.payOrderAndCreateTransaction(order, null, prodAcc.getId());
            // 执行扣费后的执行逻辑
            apiUserLoginService.loginUser(order.getUserId(), () -> {
                commTblCompReportOrderService.executeAfterOrderPaid(order);
                return null;
            });
        }
    }


    /**
     * 创建充值合同
     * added for lvcy v2.1.7 KNZT-6487
     * @param paymentIntent
     * @throws MessageException
     */
    private TblContractDeliveryProdAcc createTopUpContract(PaymentIntent paymentIntent) throws MessageException {
        Map<String, String> metadata = paymentIntent.getMetadata();
        BigDecimal paymentAmount = BigDecimal.valueOf(paymentIntent.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        String topUpNo = metadata.getOrDefault(Constants.StripeParam.TOP_UP_NO, contractDeliveryService.generateTopUpNo());
        String prodAccType = metadata.getOrDefault(Constants.StripeParam.PROD_ACC_TYPE, DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode());

        BigDecimal creditCount = BigDecimal.ZERO;
        int years = 0;
        if (DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode().equals(prodAccType)) {
            creditCount = paymentAmount;
            years = 1;
        } else if (DeliveryContracProdAccTypeEnum.TOP_UP.getCode().equals(prodAccType)) {
            String creditCountStr = metadata.get(Constants.StripeParam.CREDIT_COUNT);
            creditCount = new BigDecimal(creditCountStr);
            String yearsStr = metadata.get(Constants.StripeParam.YEARS);
            years = Integer.parseInt(yearsStr);
        }
        String companyId = metadata.get(Constants.StripeParam.COMPANY_ID);
        return deliveryBusinessService.createTopUpContract(companyId, topUpNo, prodAccType, years, creditCount, paymentAmount, paymentIntent.getId());
    }
}
