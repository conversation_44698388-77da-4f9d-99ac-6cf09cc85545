package com.backend.common.modules.common.form;

import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Data
public class TransWrapper {

    private static final Logger logger = LoggerFactory.getLogger(TransWrapper.class);

    public static final Integer ENTRY_TYPE_CORP = 1;
    public static final Integer ENTRY_TYPE_PERSON = 2;

    private final List<Entry<?>> entryList = new ArrayList<>(0);
    private GlobalAreaEnum sourceArea; // 源地区(用于机器翻译时指定源语言、判断数据所属区域等)

    /* 构造器 begin */
    public static TransWrapper buildWrapper() {
        return new TransWrapper();
    }

    public static TransWrapper buildWrapper(GlobalAreaEnum sourceArea) {
        TransWrapper transWrapper = new TransWrapper();
        transWrapper.setSourceArea(sourceArea);
        return transWrapper;
    }
    /* 构造器 end */

    /* 添加非实体 begin */
    public <T> TransWrapper add4NonEntity(T obj,
                                          BiConsumer<T, String> localTextSetter,
                                          BiConsumer<T, String> enTextSetter,
                                          BiConsumer<T, Boolean> machineTranslateFlagSetter,
                                          Function<T, String> localTextGetter,
                                          Function<T, String> enTextGetter) {
        buildAndAddEntry(obj, localTextSetter, enTextSetter, localTextGetter, enTextGetter).withMachineTranslateFlag(machineTranslateFlagSetter);
        return this;
    }

    public <T> TransWrapper add4NonEntity(T obj,
                                          BiConsumer<T, String> localTextSetter,
                                          BiConsumer<T, String> enTextSetter,
                                          Function<T, String> localTextGetter,
                                          Function<T, String> enTextGetter) {
        buildAndAddEntry(obj, localTextSetter, enTextSetter, localTextGetter, enTextGetter);
        return this;
    }

    public <T> TransWrapper batchAdd4NonEntity(List<T> objList,
                                               BiConsumer<T, String> localTextSetter,
                                               BiConsumer<T, String> enTextSetter,
                                               Function<T, String> localTextGetter,
                                               Function<T, String> enTextGetter) {
        if (CollectionUtils.isEmpty(objList)) {
            return this;
        }
        objList.forEach(obj -> buildAndAddEntry(obj, localTextSetter, enTextSetter, localTextGetter, enTextGetter));
        return this;
    }

    public <T> TransWrapper add4NonEntityWithReplace(T obj,
                                                     BiConsumer<T, String> setter,
                                                     Function<T, String> getter) {
        buildAndAddEntryWithReplace(obj, setter, getter);
        return this;
    }

    public <T> TransWrapper batchAdd4NonEntityWithReplace(List<T> objList,
                                                          BiConsumer<T, String> setter,
                                                          Function<T, String> getter) {
        if (CollectionUtils.isEmpty(objList)) {
            return this;
        }
        objList.forEach(obj -> buildAndAddEntryWithReplace(obj, setter, getter));
        return this;
    }
    /* 添加非实体 end */

    /* 添加实体 begin */
    public <T> TransWrapper add4Entity(T obj,
                                       BiConsumer<T, String> localTextSetter,
                                       BiConsumer<T, String> enTextSetter,
                                       BiConsumer<T, Boolean> machineTranslateFlagSetter,
                                       Function<T, String> localTextGetter,
                                       Function<T, String> enTextGetter,
                                       Function<T, String> keyNoGetter,
                                       Integer entityType,
                                       GlobalAreaEnum sourceArea) {
        buildAndAddEntry(obj, localTextSetter, enTextSetter, localTextGetter, enTextGetter).withEntity(keyNoGetter, entityType).withSourceArea(sourceArea).withMachineTranslateFlag(machineTranslateFlagSetter);
        return add(obj, localTextSetter, enTextSetter, machineTranslateFlagSetter, localTextGetter, enTextGetter, keyNoGetter, true, entityType, sourceArea);
    }

    public <T> TransWrapper add4Entity(T obj,
                                       BiConsumer<T, String> localTextSetter,
                                       BiConsumer<T, String> enTextSetter,
                                       BiConsumer<T, Boolean> machineTranslateFlagSetter,
                                       Function<T, String> localTextGetter,
                                       Function<T, String> enTextGetter,
                                       Function<T, String> keyNoGetter,
                                       Integer entityType) {
        return add4Entity(obj, localTextSetter, enTextSetter, machineTranslateFlagSetter, localTextGetter, enTextGetter, keyNoGetter, entityType, null);
    }

    public <T> TransWrapper add4Entity(T obj,
                                       BiConsumer<T, String> localTextSetter,
                                       BiConsumer<T, String> enTextSetter,
                                       Function<T, String> localTextGetter,
                                       Function<T, String> enTextGetter,
                                       Function<T, String> keyNoGetter,
                                       Integer entityType,
                                       GlobalAreaEnum sourceArea) {
        return add4Entity(obj, localTextSetter, enTextSetter, null, localTextGetter, enTextGetter, keyNoGetter, entityType, sourceArea);
    }

    public <T> TransWrapper add4Entity(T obj,
                                       BiConsumer<T, String> localTextSetter,
                                       BiConsumer<T, String> enTextSetter,
                                       Function<T, String> localTextGetter,
                                       Function<T, String> enTextGetter,
                                       Function<T, String> keyNoGetter,
                                       Integer entityType) {
        return add4Entity(obj, localTextSetter, enTextSetter, null, localTextGetter, enTextGetter, keyNoGetter, entityType);
    }

    public <T> TransWrapper batchAdd4Entity(List<T> objList,
                                            BiConsumer<T, String> localTextSetter,
                                            BiConsumer<T, String> enTextSetter,
                                            Function<T, String> localTextGetter,
                                            Function<T, String> enTextGetter,
                                            Function<T, String> keyNoGetter,
                                            Function<T, Integer> entityTypeGetter) {
        if (CollectionUtils.isEmpty(objList)) {
            return this;
        }
        objList.forEach(obj -> add4Entity(obj,
                localTextSetter,
                enTextSetter,
                localTextGetter,
                enTextGetter,
                keyNoGetter,
                entityTypeGetter == null ? null : entityTypeGetter.apply(obj)));
        return this;
    }

    /**
     * 添加实体(企业/人员)
     * 根据keyNo简单判断实体类型是否为人
     */
    public <T> TransWrapper add4EntityWithTypeFromKeyNo(T obj,
                                                        BiConsumer<T, String> localTextSetter,
                                                        BiConsumer<T, String> enTextSetter,
                                                        Function<T, String> localTextGetter,
                                                        Function<T, String> enTextGetter,
                                                        Function<T, String> keyNoGetter) {
        String keyNo = keyNoGetter == null ? null : keyNoGetter.apply(obj);
        Integer entityType = detectEntityTypeByKeyNo(keyNo);
        return add4Entity(obj, localTextSetter, enTextSetter, null, localTextGetter, enTextGetter, keyNoGetter, entityType);
    }

    public <T> TransWrapper batchAdd4EntityWithTypeFromKeyNo(List<T> objList,
                                                             BiConsumer<T, String> localTextSetter,
                                                             BiConsumer<T, String> enTextSetter,
                                                             Function<T, String> localTextGetter,
                                                             Function<T, String> enTextGetter,
                                                             Function<T, String> keyNoGetter) {
        if (CollectionUtils.isEmpty(objList)) {
            return this;
        }
        objList.forEach(obj -> add4EntityWithTypeFromKeyNo(obj,
                localTextSetter,
                enTextSetter,
                localTextGetter,
                enTextGetter,
                keyNoGetter));
        return this;
    }

    private static Integer detectEntityTypeByKeyNo(String keyNo) {
        Integer entityType = null;
        if (StringUtils.isNotBlank(keyNo)) {
            if (StringUtils.getIfPerson(keyNo)) {
                entityType = ENTRY_TYPE_PERSON;
            } else {
                entityType = ENTRY_TYPE_CORP;
            }
        }
        return entityType;
    }

    public <T> TransWrapper add4EntityWithReplace(T obj,
                                                  BiConsumer<T, String> textSetter,
                                                  Function<T, String> textGetter,
                                                  Function<T, String> keyNoGetter,
                                                  Integer entityType) {
        return add(obj, textSetter, textSetter, null, textGetter, null, keyNoGetter, true, entityType, sourceArea);
    }
    /* 添加实体 end */

    public <T> TransWrapper add(T obj,
                                BiConsumer<T, String> localTextSetter,
                                BiConsumer<T, String> enTextSetter,
                                BiConsumer<T, Boolean> machineTranslateFlagSetter,
                                Function<T, String> localTextGetter,
                                Function<T, String> enTextGetter,
                                Function<T, String> keyNoGetter,
                                boolean ifEntity,
                                Integer entityType,
                                GlobalAreaEnum sourceArea) {
        Entry<T> e = new Entry<>(this, obj, localTextSetter, enTextSetter, machineTranslateFlagSetter, localTextGetter, enTextGetter, keyNoGetter, ifEntity, entityType, sourceArea);
        if (!e.validate()) {
            logger.error("TransWrapper add validate failed");
            return this;
        }
        this.entryList.add(e);
        return this;
    }

    public <T> Entry<T> buildAndAddEntry(T obj,
                                         BiConsumer<T, String> localTextSetter,
                                         BiConsumer<T, String> enTextSetter,
                                         Function<T, String> localTextGetter,
                                         Function<T, String> enTextGetter) {
        Entry<T> entry = new Entry<>();
        entry.setTransWrapper(this);
        entry.setObj(obj);
        entry.setLocalTextSetter(localTextSetter);
        entry.setEnTextSetter(enTextSetter);
        entry.setLocalTextGetter(localTextGetter);
        entry.setEnTextGetter(enTextGetter);
        this.addEntry(entry);
        return entry;
    }

    public <T> Entry<T> buildAndAddEntryWithReplace(T obj,
                                                    BiConsumer<T, String> textSetter,
                                                    Function<T, String> textGetter) {
        Entry<T> entry = new Entry<>();
        entry.setTransWrapper(this);
        entry.setObj(obj);
        entry.setLocalTextSetter(textSetter);
        entry.setEnTextSetter(textSetter);
        entry.setLocalTextGetter(textGetter);
        this.addEntry(entry);
        return entry;
    }

    public TransWrapper addEntry(Entry<?> entry) {
        this.entryList.add(entry);
        return this;
    }

    public boolean isEmpty() {
        return this.entryList.isEmpty();
    }

    public List<Entry<?>> entryList() {
        return this.entryList;
    }

    @Data
    @NoArgsConstructor
    public static final class Entry<T> {

        private TransWrapper transWrapper;
        private T obj; // NOT NULL, 对象
        private BiConsumer<T, String> localTextSetter; // NOT NULL, 本地语言Setter
        private BiConsumer<T, String> enTextSetter; // NOT NULL, 英语Setter
        private BiConsumer<T, Boolean> machineTranslateFlagSetter; // 机翻标记Setter
        private Function<T, String> localTextGetter; // NOT NULL, 本地语言文本Getter
        private Function<T, String> enTextGetter; // NOT NULL, 英语文本Getter
        private Function<T, String> keyNoGetter; // keyNo Getter
        private boolean ifEntity; // 是否是实体
        private Integer entityType; // 实体类型(1-公司, 2-人)
        private GlobalAreaEnum sourceArea;

        public Entry(TransWrapper transWrapper,
                     T obj,
                     BiConsumer<T, String> localTextSetter,
                     BiConsumer<T, String> enTextSetter,
                     BiConsumer<T, Boolean> machineTranslateFlagSetter,
                     Function<T, String> localTextGetter,
                     Function<T, String> enTextGetter,
                     Function<T, String> keyNoGetter,
                     boolean ifEntity,
                     Integer entityType,
                     GlobalAreaEnum sourceArea) {
            this.transWrapper = transWrapper;
            this.obj = obj;
            this.localTextSetter = localTextSetter;
            this.enTextSetter = enTextSetter;
            this.machineTranslateFlagSetter = machineTranslateFlagSetter;
            this.localTextGetter = localTextGetter;
            this.enTextGetter = enTextGetter;
            this.keyNoGetter = keyNoGetter;
            this.ifEntity = ifEntity;
            this.entityType = entityType;
            this.sourceArea = sourceArea;
        }

        public Entry<T> withEntity(Function<T, String> keyNoGetter,
                                   Integer entityType) {
            this.ifEntity = true;
            this.keyNoGetter = keyNoGetter;
            this.entityType = entityType;
            return this;
        }

        public Entry<T> withSourceArea(GlobalAreaEnum sourceArea) {
            this.sourceArea = sourceArea;
            return this;
        }

        public Entry<T> withMachineTranslateFlag(BiConsumer<T, Boolean> machineTranslateFlagSetter) {
            this.machineTranslateFlagSetter = machineTranslateFlagSetter;
            return this;
        }

        public boolean validate() {
            return obj != null && localTextSetter != null && enTextSetter != null && localTextGetter != null;
        }

        public GlobalAreaEnum getSourceArea() {
            return ObjectUtils.defaultIfNull(this.sourceArea, this.transWrapper.getSourceArea());
        }

        public boolean isCorp() {
            return ENTRY_TYPE_CORP.equals(entityType);
        }

        public boolean isPerson() {
            return ENTRY_TYPE_PERSON.equals(entityType);
        }

        public boolean ifReplace() {
            return localTextSetter == enTextSetter;
        }

        public void setLocalText(String localText) {
            if (localTextSetter != null) {
                localTextSetter.accept(obj, localText);
            }
        }

        public void setEnText(String enText) {
            if (enTextSetter != null) {
                enTextSetter.accept(obj, enText);
            }
        }

        public void setMachineTranslateFlag(boolean machineTranslateFlag) {
            if (machineTranslateFlagSetter != null) {
                machineTranslateFlagSetter.accept(obj, machineTranslateFlag);
            }
        }

        public String getLocalText() {
            return localTextGetter == null ? null : localTextGetter.apply(obj);
        }

        public String getEnText() {
            return enTextGetter == null ? null : enTextGetter.apply(obj);
        }

        public String getKeyNo() {
            return keyNoGetter == null ? null : keyNoGetter.apply(obj);
        }

        public boolean getIfEntity() {
            return ifEntity;
        }

        @Override
        public String toString() {
            return "Entry{" +
                    "obj=" + obj +
                    ", localTextSetter=" + localTextSetter +
                    ", enTextSetter=" + enTextSetter +
                    ", machineTranslateFlagSetter=" + machineTranslateFlagSetter +
                    ", localTextGetter=" + localTextGetter +
                    ", enTextGetter=" + enTextGetter +
                    ", keyNoGetter=" + keyNoGetter +
                    ", ifEntity=" + ifEntity +
                    ", entityType=" + entityType +
                    ", sourceArea=" + sourceArea +
                    '}';
        }
    }
}
