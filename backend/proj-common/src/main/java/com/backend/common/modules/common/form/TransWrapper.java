package com.backend.common.modules.common.form;

import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Data
public class TransWrapper {

    private static final Logger logger = LoggerFactory.getLogger(TransWrapper.class);

    public static final Integer ENTRY_TYPE_CORP = 1;
    public static final Integer ENTRY_TYPE_PERSON = 2;

    private final List<Entry<?>> entryList = new ArrayList<>(0);
    private GlobalAreaEnum sourceArea; // 源地区(用于机器翻译时指定源语言、判断数据所属区域等)

    /* 构造器 begin */
    public static TransWrapper buildWrapper() {
        return new TransWrapper();
    }

    public static TransWrapper buildWrapper(GlobalAreaEnum sourceArea) {
        TransWrapper transWrapper = new TransWrapper();
        transWrapper.setSourceArea(sourceArea);
        return transWrapper;
    }

    public <T> TransWrapper add4Entity(T obj,
                                       BiConsumer<T, String> localTextSetter,
                                       BiConsumer<T, String> enTextSetter,
                                       Function<T, String> localTextGetter,
                                       Function<T, String> enTextGetter,
                                       Function<T, String> keyNoGetter,
                                       Integer entityType) {
        buildAndAddEntry(obj, localTextSetter, enTextSetter, localTextGetter, enTextGetter).withEntity(keyNoGetter, entityType);
        return this;
    }

    public <T> TransWrapper batchAdd4EntityWithTypeFromKeyNo(List<T> objList,
                                                             BiConsumer<T, String> localTextSetter,
                                                             BiConsumer<T, String> enTextSetter,
                                                             Function<T, String> localTextGetter,
                                                             Function<T, String> enTextGetter,
                                                             Function<T, String> keyNoGetter) {
        if (CollectionUtils.isEmpty(objList)) {
            return this;
        }
        objList.forEach(obj -> buildAndAddEntry(obj, localTextSetter, enTextSetter, localTextGetter, enTextGetter).withEntity(keyNoGetter, true));
        return this;
    }

    public <T> Entry<T> buildAndAddEntry(T obj,
                                         BiConsumer<T, String> localTextSetter,
                                         BiConsumer<T, String> enTextSetter,
                                         Function<T, String> localTextGetter,
                                         Function<T, String> enTextGetter) {
        Entry<T> entry = new Entry<>();
        entry.setSourceArea(this.sourceArea);
        entry.setObj(obj);
        entry.setLocalTextSetter(localTextSetter);
        entry.setEnTextSetter(enTextSetter);
        entry.setLocalTextGetter(localTextGetter);
        entry.setEnTextGetter(enTextGetter);
        this.addEntry(entry);
        return entry;
    }

    public <T> Entry<T> buildAndAddEntryWithOverwrite(T obj,
                                                      BiConsumer<T, String> textSetter,
                                                      Function<T, String> textGetter) {
        Entry<T> entry = new Entry<>();
        entry.setSourceArea(this.sourceArea);
        entry.setObj(obj);
        entry.setLocalTextSetter(textSetter);
        entry.setEnTextSetter(textSetter);
        entry.setLocalTextGetter(textGetter);
        this.addEntry(entry);
        return entry;
    }

    public TransWrapper addEntry(Entry<?> entry) {
        this.entryList.add(entry);
        return this;
    }

    public boolean isEmpty() {
        return this.entryList.isEmpty();
    }

    public List<Entry<?>> entryList() {
        return this.entryList;
    }

    @Data
    @NoArgsConstructor
    public static final class Entry<T> {

        private T obj; // NOT NULL, 对象
        private BiConsumer<T, String> localTextSetter; // NOT NULL, 本地语言Setter
        private BiConsumer<T, String> enTextSetter; // NOT NULL, 英语Setter
        private BiConsumer<T, Boolean> machineTranslateFlagSetter; // 机翻标记Setter
        private Function<T, String> localTextGetter; // NOT NULL, 本地语言文本Getter
        private Function<T, String> enTextGetter; // NOT NULL, 英语文本Getter
        private Function<T, String> keyNoGetter; // keyNo Getter
        private boolean ifEntity; // 是否是实体
        private Integer entityType; // 实体类型(1-公司, 2-人)
        private GlobalAreaEnum sourceArea;

        public Entry<T> withEntity(Function<T, String> keyNoGetter,
                                   Integer entityType) {
            return withEntity(keyNoGetter, entityType, false);
        }

        public Entry<T> withEntity(Function<T, String> keyNoGetter,
                                   boolean autoDetectEntityTypeByKeyNo) {
            return withEntity(keyNoGetter, null, autoDetectEntityTypeByKeyNo);
        }

        public Entry<T> withEntity(Function<T, String> keyNoGetter,
                                   Integer entityType,
                                   boolean autoDetectEntityTypeByKeyNo) {
            this.ifEntity = true;
            this.keyNoGetter = keyNoGetter;
            this.entityType = entityType;

            if (entityType == null && autoDetectEntityTypeByKeyNo) {
                String keyNo = this.getKeyNo();
                if (StringUtils.isNotBlank(keyNo)) {
                    if (StringUtils.getIfPerson(keyNo)) {
                        this.entityType = ENTRY_TYPE_PERSON;
                    } else {
                        this.entityType = ENTRY_TYPE_CORP;
                    }
                }
            }
            return this;
        }

        public Entry<T> withSourceArea(GlobalAreaEnum sourceArea) {
            this.sourceArea = sourceArea;
            return this;
        }

        public Entry<T> withMachineTranslateFlag(BiConsumer<T, Boolean> machineTranslateFlagSetter) {
            this.machineTranslateFlagSetter = machineTranslateFlagSetter;
            return this;
        }

        public boolean isCorp() {
            return ENTRY_TYPE_CORP.equals(entityType);
        }

        public boolean isPerson() {
            return ENTRY_TYPE_PERSON.equals(entityType);
        }

        public boolean ifOverwrite() {
            return localTextSetter == enTextSetter;
        }

        public void setLocalText(String localText) {
            if (localTextSetter != null) {
                localTextSetter.accept(obj, localText);
            }
        }

        public void setEnText(String enText) {
            if (enTextSetter != null) {
                enTextSetter.accept(obj, enText);
            }
        }

        public void setMachineTranslateFlag(boolean machineTranslateFlag) {
            if (machineTranslateFlagSetter != null) {
                machineTranslateFlagSetter.accept(obj, machineTranslateFlag);
            }
        }

        public String getLocalText() {
            return localTextGetter == null ? null : localTextGetter.apply(obj);
        }

        public String getEnText() {
            return enTextGetter == null ? null : enTextGetter.apply(obj);
        }

        public String getKeyNo() {
            return keyNoGetter == null ? null : keyNoGetter.apply(obj);
        }

        public boolean getIfEntity() {
            return ifEntity;
        }
    }
}
