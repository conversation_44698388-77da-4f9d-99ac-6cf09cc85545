package com.backend.common.yunjuapi.model.hk.basic.annual;

import com.backend.common.yunjuapi.model.HkCorpIdentityInfoProcessor;
import com.qcc.frame.commons.ienum.HkCompanyTypeEnum;
import com.qcc.frame.commons.ienum.HkPositionEnum;
import com.qcc.frame.commons.ienum.HkStatusEnum;
import com.qcc.frame.commons.ienum.WindingUpModeEnum;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;

import java.util.List;

// added for v1.7.8 KNZT-3320
@Data
public class HkCorpInfoBasicAnnualResult {

    private String dataStatus;
    private ResData data;

    @Data
    public final static class ResData {
        private Basic basic;
        private RealTimeData realTimeData;
        private AnnualReturnData annualReturnData;
        private String arVerify = "N";// added for v1.8.7 KNZT-3965

        @Data
        public final static class Basic {
            private String companyNumber;
            private String businessRegistrationNumber;
            private String companyNameChn;
            private String companyNameEng;
            private String companyType;
            private String companyTypeEn;// added for 1.9.9 KNZT-4805 公司类型
            private String registrationDate;
            private String status;
            private String statusEn;
            private String address;
            private String officeEffectiveDate; // 注册处有效时间 added for v1.8.6 KNZT-3509
            private String windingUpMode;
            private String windingUpModeEn; // 清盘模式 added for v1.9.7 KNZT-4786
            private String registerOfCharges;
            private String registerOfChargesEn;
            private String dissolutionDate;
            private String important;
            private String incorporationPlace;
            private String incorporationPlaceEn;// added for 1.9.9 chenbl KNZT-4805
            private String remarks;
            private String remarksEn;
            private List<NameRecord> nameRecordList;
            private String businessNatureCode;
            private String businessNatureDes;
            private String mortgages; // 按揭与押记 added for v1.9.1 KNZT-4177

            private String businessName; // 商业名称 added for v2.0.2 KNZT-4836

            public void setCompanyType(String companyType) {
                this.companyType = companyType;
                this.companyTypeEn = HkCompanyTypeEnum.getCompanyTypeEnByCompanyTypeCn(companyType);
            }

            public void setStatus(String status) {
                this.status = status;
                this.statusEn = HkStatusEnum.getStatusEnByStatusCn(status);
            }

            public void setWindingUpMode(String windingUpMode) {
                this.windingUpMode = windingUpMode;
                this.windingUpModeEn = WindingUpModeEnum.getEnByCn(windingUpMode);
            }

            public void setRegisterOfCharges(String registerOfCharges) {
                this.registerOfCharges = registerOfCharges;
                if (StringUtils.isNotBlank(registerOfCharges)) {
                    this.registerOfChargesEn = StringUtils.equals("有", registerOfCharges) ? "Available" : "Unavailable";
                }
            }

            @Data
            public final static class NameRecord {
                private String startDate;
                private String name;
                private String enName;
            }
        }

        @Data
        public final static class RealTimeData {
            private String retrievalTime;
            private List<Director> directors;
            private List<Shareholder> shareholders;
            private List<ShareCapital> shareCapital; //股本 added for v2.0.2 KNZT-4784
            private List<CompanySecretary> companySecretary; // 公司秘书 added for v2.0.2 KNZT-4783

            @Data
            public final static class Director implements HkCorpIdentityInfoProcessor {
                private String fullNameEng;
                private String fullNameChn;
                private String type;
                private String typeEn;
                private String companyNumber;
                private String businessRegistrationNumber;// 商业登记号码
                private String identificationString;
                private String passportCountry; // 护照签发国家/地区
                private String hkId; // 香港身份证号
                private String passportNo; // 护照号码
                private String cnId; // 中国身份证号

                public void setType(String type) {
                    this.type = HkPositionEnum.formatPosition(type);
                    this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
                }
            }

            @Data
            public final static class Shareholder {
                private String address;
                private String classofShares;
                private String fullNameEng;
                private String fullNameChn;
                private String currentHolding;
                private String percentofClass;
                private String totalNumber;
                private String type;
                private String typeEn;
                private String uniformSocialCreditCode;
                private String deadline; // Position as at added for v1.8.6 KNZT-3509

                public void setType(String type) {
                    this.type = HkPositionEnum.formatPosition(type);
                    this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
                }
            }

            @Data
            public final static class ShareCapital {
                private String currency;// 货币单位
                private String totalAmount;// 注册资本
                private String totalAmountPaid;// 实缴资本
                private String totalAmountPaidCurrency;// 已缴款额货币
            }

            @Data
            public final static class CompanySecretary implements HkCorpIdentityInfoProcessor {
                private String fullNameEng;
                private String fullNameChn;
                private String type;
                private String typeEn;
                private String companyNumber;// 公司编号
                private String businessRegistrationNumber;// 商业登记号码
                private String address;// 地址
                private String appointedDate;
                private String identificationString;
                private String passportCountry; // 护照签发国家/地区
                private String hkId; // 香港身份证号
                private String passportNo; // 护照号码
                private String cnId; // 中国身份证号
                private String important;

                public void setType(String type) {
                    this.type = type;
                    this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
                }
            }
        }

        @Data
        public final static class AnnualReturnData {
            private String madeUpReturnDate;
            private String financialStatementStartDate; // 会计期次-开始日期 （周年申报表时 存在会计期次数据）added for v2.0.2 KNZT-5000
            private String financialStatementEndDate; // 会计期次-结束日期
            private String documentDate; // 公告日期 added for v1.9.9 chenbl KNZT-5125
            private String documentYear; // 公告年份 added for v1.9.9 chenbl KNZT-5125
            private String documentType;
            ; // 公告类型 added for v2.1.2 chenbl KNZT-6194
            private Integer verified;
            private List<ShareCapital> shareCapital; // 股本 added for v2.0.2 KNZT-4784
            private List<Director> directors;
            private List<CompanySecretary> companySecretary; // 公司秘书 added for v2.0.2 KNZT-4783
            private List<Shareholder> shareholders;
            private PresentorReference presentorReference; // 提交人信息 added for v2.0.2 KNZT-4785
            private String originalFile;

            @Data
            public final static class Director implements HkCorpIdentityInfoProcessor {
                private String fullNameChn;
                private String fullNameEng;
                private String type;
                private String typeEn;
                private String emailAddress;
                private String capacity;
                private String companyCode;// 商业登记号码
                private String address;
                private String identificationString;
                private String passportCountry; // 护照签发国家/地区
                private String passportCountryOri; // 护照签发国家/地区(原)
                private String hkId; // 香港身份证号
                private String passportNo; // 护照号码
                private String cnId; // 中国身份证号

                public void setType(String type) {
                    this.type = HkPositionEnum.formatPosition(type);
                    this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
                }
            }

            @Data
            public final static class Shareholder {
                private String address;
                private String classofShares;
                private String fullNameEng;
                private String fullNameChn;
                private String currentHolding;
                private String percentofClass;
                private String totalNumber;
            }

            @Data
            public final static class ShareCapital {
                private String classofShares;
                private String currency;
                private String totalAmount;
                private String totalAmountPaid;
                private String totalAmountPaidCurrency;
                private String totalNumber;

            }

            @Data
            public final static class CompanySecretary implements HkCorpIdentityInfoProcessor {
                private String fullNameEng;
                private String fullNameChn;
                private String type;
                private String typeEn;
                private String emailAddress;
                private String companyCode;// 商业登记号码
                private String address;
                private String identificationString;
                private String passportCountry; // 护照签发国家/地区
                private String hkId; // 香港身份证号
                private String passportNo; // 护照号码
                private String cnId; // 中国身份证号

                public void setType(String type) {
                    this.type = type;
                    this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
                }
            }

            @Data
            public final static class PresentorReference {
                private String nameChn;
                private String nameEng;
                private String address;
                private String tel;
                private String fax;
                private String email;
                private String reference;// 档号
            }
        }
    }
}
