package com.backend.common.modules.setting.service;


import com.alibaba.fastjson.JSONObject;
import com.backend.common.entity.mapping.FunctionCountInfoTO;
import com.backend.common.modules.api.entity.TblCompApiOrderResp;
import com.backend.common.modules.api.service.CommExternalApiDataService;
import com.backend.common.modules.api.service.CommTblCompApiOrderRespService;
import com.backend.common.modules.api.service.CommTblCompApiOrderService;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdPeriodService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryTransactionService;
import com.backend.common.modules.delivery.service.DeliveryBusinessService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.monitor.service.MonitorBusinessService;
import com.backend.common.modules.open.form.TrialApplyForm;
import com.backend.common.modules.open.service.TrialApplyService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblCompReportOrderHkDoc;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblCompReportOrderHkDocService;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.condition.SysCompanyCondition;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.oversea.model.HongKongAnnouncementsTO;
import com.backend.common.service.CommCompUserService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.oversea.OverseaHongKongAnnouncementsService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.OrderHkDocRelTypeEnum;
import com.qcc.frame.commons.ienum.PayStatusEnum;
import com.qcc.frame.commons.ienum.PayTypeEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.jee.commons.cache.QccRedisManager;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.qcc.frame.jee.modules.sys.service.SaasLoginService;
import com.qcc.frame.jee.modules.sys.service.SysCompInfoColExtService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.resps.Tuple;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_NAME_UI_DEFAULT_VERSION;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_VALUE_UI_VERSION_V1;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_VALUE_UI_VERSION_V2;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.EXT_TYPE;

@Service
public class DataPatchInterface {
	@Autowired
	private CommSysCompanyService commSysCompanyService;
	@Autowired
	private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
	@Autowired
	private CommTblCompReportOrderService commTblCompReportOrderService;
	@Autowired
	private TransactionBusinessService transactionBusinessService;
	@Autowired
	private CommTblContractDeliveryProdAccService prodAccService;
	@Autowired
	private CommTblContractDeliveryService deliveryService;
	@Autowired
	private CommTblCompApiOrderService apiOrderService;
	@Autowired
	private CommTblContractDeliveryTransactionService transactionService;
	@Autowired
	private UserService userService;
	@Autowired
	private DeliveryBusinessService deliveryBusinessService;
	@Autowired
	private PlatformMgntService platformMgntService;
	@Autowired
	private CommTblReportChargeUnitService chargeUnitService;
	@Autowired
	private CommTblContractDeliveryProdPeriodService prodPeriodService;
	@Autowired
	private RedisService redisService;
	@Autowired
	private QccRedisManager qccRedisManager;
	@Autowired
	private TrialApplyService trialApplyService;
	@Autowired
	private CommTblCompApiOrderRespService apiOrderRespService;
	@Autowired
	private MonitorBusinessService monitorBusinessService;
	@Autowired
	private CommTblCompReportOrderHkDocService hkDocService;
	@Autowired
	private OverseaHongKongAnnouncementsService overseaHongKongAnnouncementsService;
	@Autowired
	private CommExternalApiDataService apiDataService;
	@Autowired
	private SaasLoginService saasLoginService;
	@Autowired
	private SysCompInfoColExtService sysCompInfoColExtService;

	private static final Logger logger = LoggerFactory.getLogger(DataPatchInterface.class);


/*

	public void patchTrialToSignAndRunBill(ContractDeliveryForm form) throws MessageException {
		form.checkParam();
		List<String> companyIdList = form.getAllocationList().stream().map(AllocateCompanyUnitForm::getCompanyId).collect(Collectors.toList());
		for (String companyId : companyIdList) {
			Company company = commSysCompanyService.get(companyId);
			MsgExceptionUtils.checkIsNull(company);
			MsgExceptionUtils.failBuild(!CompTypeEnum.TRIAL.getCode().equals(company.getType()));
			// 切换公司类型
			commSysCompanyService.updateCompanyType(companyId, CompTypeEnum.SIGN.getCode());
			SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.readOnlyGetCompFuncCount(companyId);
			logger.info("patchTrialToSignAndRunBill, companyId:{}, ori funcCount total:{} consumed:{}", companyId, funcCount.getTotalCount(), funcCount.getConsumedCount());
		}
		// 处理交付数据
		deliveryBusinessService.delivery(form);

		BigDecimal actualDiscountAmount = BigDecimal.ZERO;
		for (String companyId : companyIdList) {
			List<TblContractDelivery> contractDeliveryList = deliveryService.getByCompanyId(companyId);
			TblContractDelivery contractDelivery = contractDeliveryList.get(0);
			actualDiscountAmount = contractDelivery.getActualDiscountAmount();
			// 处理 流水
			List<TblCompReportOrder> orderList = commTblCompReportOrderService.listByCompany(companyId);
			for (TblCompReportOrder order : orderList) {
				transactionBusinessService.createTransaction4Order(order, contractDelivery.getId());
			}
			logger.info("patchTrialToSignAndRunBill createTransaction4Order, companyId:{}, orderList size:{}", companyId, orderList.size());
			// 处理 日账、月账
			runDailyBill(companyId);
			// 修正funcCount数据
			commSysCompInfoFuncCountService.updateFuncCountByValidProdAcc(companyId);
		}
		// 处理月账
		YearMonth nowYM = YearMonth.now();
		YearMonth beginYearMonth = YearMonth.from(DateUtils.toLocalDate(form.getBeginDate()));
		YearMonth endYearMonth = YearMonth.from(DateUtils.toLocalDate(form.getEndDate()));
		for (YearMonth ym = beginYearMonth;
			 (ym.isBefore(endYearMonth) || ym.equals(endYearMonth)) && (ym.isBefore(nowYM) || ym.equals(nowYM));
			 ym = ym.plusMonths(1)) {
			transactionBusinessService.processProdAccTransactionMonthly(form.getContractNo(), actualDiscountAmount, ym);
			logger.info("runBill processProdAccTransactionMonthly, contractNo:{}, ym:{}", form.getContractNo(), ym);
		}
	}
*/


/*	public void runDailyBill(String companyId) throws MessageException {
		List<TblContractDeliveryProdAcc> prodAccList = prodAccService.getValidAccByCompanyId(companyId);
		for (TblContractDeliveryProdAcc prodAcc : prodAccList) {
			LocalDate beginDate = DateUtils.toLocalDate(prodAcc.getBeginDate());
			LocalDate endDate = DateUtils.toLocalDate(prodAcc.getEndDate());
			LocalDate now = LocalDate.now();
			for (LocalDate date = beginDate; date.isBefore(endDate) && date.isBefore(now); date = date.plusDays(1)) {
				transactionBusinessService.processProdAccTransactionDaily(prodAcc, date);
			}
			logger.info("runBill processProdAccTransactionDaily, companyId:{}, beginDate:{}, endDate:{}", companyId, beginDate, endDate);
		}
	}*/

	public void runMonthlyBill(List<String> contractNoList, String targetYMString) {
		List<TblContractDelivery> contractList = deliveryService.getByContractNoList(contractNoList);
		if (StringUtils.isBlank(targetYMString)) {
			YearMonth beginYM = YearMonth.of(2024, 4);
			YearMonth endYM = YearMonth.now();
			for (YearMonth ym = beginYM; ym.isBefore(endYM) || ym.equals(endYM); ym = ym.plusMonths(1)) {
				handleContractMonthlyBill(contractList, ym);
			}
		} else {
			YearMonth targetYM = YearMonth.parse(targetYMString);
			handleContractMonthlyBill(contractList, targetYM);
		}

	}

	private void handleContractMonthlyBill(List<TblContractDelivery> contractList, YearMonth ym) {
		for (TblContractDelivery contract : contractList) {
			String contractNo = contract.getContractNo();
			if (ym.isBefore(YearMonth.of(2024, 8))) {
				continue;
			}
			LocalDate beginDate = DateUtils.toLocalDate(contract.getBeginDate());
			LocalDate endDate = DateUtils.toLocalDate(contract.getEndDate());
			if (StringUtils.isNotBlank(contractNo) && DateUtils.isMonthInRange(beginDate, endDate, ym)) {
				if (contract.getId().startsWith("PERIOD_PROD")) {
					transactionBusinessService.processPeriodProdMonthly(contractNo, ym);
				} else {
					transactionBusinessService.processContractTransactionMonthly(contractNo, contract.getActualDiscountAmount(), ym);
				}
			}
		}
	}

	public void compensateOrderTransaction(List<String> orderIdList) throws MessageException {
		List<TblCompReportOrder> orderList = commTblCompReportOrderService.batchGet(orderIdList);
		String companyId = orderList.get(0).getCompanyId();
		List<TblContractDelivery> contractDeliveryList = deliveryService.getByCompanyId(companyId);
		TblContractDelivery contractDelivery = contractDeliveryList.get(0);

		for (TblCompReportOrder order : orderList) {
			if (BigDecimal.ZERO.compareTo(order.getTotalUnit()) > 0) {
				logger.error("compensateTransaction order refunded, orderId:{}", order.getId());
				continue;
			}
			List<TblContractDeliveryTransaction> transactionList = transactionService.getByRelId(order.getId(), TransactionTypeEnum.ORDER.getCode());
			if (CollectionUtils.isNotEmpty(transactionList)) {
				logger.info("compensateTransaction order exist transaction, orderId:{}, transactionList:{}", order.getId(), JSONObject.toJSONString(transactionList));
				continue;
			}
			transactionBusinessService.payOrderAndCreateTransaction(order, contractDelivery.getId());
		}
	}


	public void patchEncryptUserId(List<String> userIdList) throws MessageException {
		if (CollectionUtils.isEmpty(userIdList)) {
			List<String> companyIdList = commSysCompanyService.getCompanyIdByCondition(null);
			for (String companyId : companyIdList) {
				List<String> companyUserIdList = userService.getAllUserIdByCompanyId(companyId);
				for (String userId : companyUserIdList) {
					try {
						String encryptUserId = CommCompUserService.generateEncryptUserId();
						userService.updateEncryptUserId(userId, encryptUserId);
						logger.info("patchEncryptUserId, userId:{}, encryptUserId:{}", userId, encryptUserId);
					} catch (Exception e) {
						logger.error("patchEncryptUserId error, userId:{}", userId);
					}
				}
			}
		} else {
			for (String userId : userIdList) {
				try {
					String encryptUserId = CommCompUserService.generateEncryptUserId();
					userService.updateEncryptUserId(userId, encryptUserId);
					logger.info("patchEncryptUserId, userId:{}, encryptUserId:{}", userId, encryptUserId);
				} catch (Exception e) {
					logger.error("patchEncryptUserId error, userId:{}", userId);
				}
			}
		}
	}

	// added for lvcy v2.0.1 KNZT-5322
	@Transactional
	public void patchCompanySelfAccount(String companyName, String email, Integer accountNum) throws MessageException {
		Company company = platformMgntService.createSelfAccountCompany(CompTypeEnum.COMPANY_SELF.getCode(), companyName, "", "", email, "", null, accountNum + 1, null);

		List<TblReportChargeUnit> chargeUnitList = chargeUnitService.listChargeUnitByCompany(company.getId());

		List<CompChargeUnitTO> compChargeUnitTOList = chargeUnitList.stream().map(k -> {
			CompChargeUnitTO compChargeUnitTO = new CompChargeUnitTO();
			BeanUtils.copyProperties(k, compChargeUnitTO);
			if (StringUtils.equalsAny(k.getReportType(), ReportTypeEnum.LEGAL.getCode(), ReportTypeEnum.OFFICIAL_WATERMARK.getCode(),
					ReportTypeEnum.HK_BAS.getCode(), ReportTypeEnum.HK_BAS_AR.getCode(), ReportTypeEnum.SG_BAS.getCode())) {
				compChargeUnitTO.setEnabled(Constants.NO);
			}
			if (StringUtils.equalsAny(k.getReportType(), ReportTypeEnum.PERS_360.getCode(), ReportTypeEnum.CORP_360.getCode())) {
				compChargeUnitTO.setEnabled(Constants.YES);
			}
			return compChargeUnitTO;
		}).collect(Collectors.toList());
		chargeUnitService.saveOrUpdateSysCompChargeUnitList(company.getId(), compChargeUnitTOList);

		User user = userService.getByLoginName(company.getId(), email);
		if (Objects.nonNull(user)) {
			user.setPassword(UserService.entryptPassword(email.substring(0, email.indexOf("@"))+"!2024"));
			user.setStatus(Constants.Sys.USER_STATUS_ACTIVE);
			userService.save(user);
		}
    }

	/**
	 * added for v2.0.8 fengsw KNZT-5806
	 * 手动触发账单推送, 合同编号集合，指定年月
	 * updated for v2.1.1 fengsw KNZT-6123
	 * @param targetContractNoList
	 * @param targetYMString
	 */
	public void pushConfirmAmountContract2KzzByManual(List<String> targetContractNoList, String targetYMString) {
		if (StringUtils.isBlank(targetYMString)) {
			YearMonth beginYM = YearMonth.of(2024, 4);
			YearMonth endYM = YearMonth.now().minusMonths(1);// 去除当月，避免当月数据未完成，从而推送到客找找
			for (YearMonth ym = beginYM; ym.isBefore(endYM) || ym.equals(endYM); ym = ym.plusMonths(1)) {
				transactionBusinessService.pushConfirmAmountContract2KzzByManual(targetContractNoList, ym);
			}
		} else {
			YearMonth targetYM = YearMonth.parse(targetYMString);
			YearMonth yearMonth = YearMonth.now();
			if (yearMonth.equals(targetYM)) {// 去除当月，避免当月数据未完成，从而推送到客找找
				logger.info("push yearMonth:{} data same as the current month", targetYMString);
				return;
			}
			transactionBusinessService.pushConfirmAmountContract2KzzByManual(targetContractNoList, targetYM);
		}
	}

    // added for v2.0.8 chenbl KNZT-5830
    public void correctDelayedTaskKey() {
        String env = Global.getConfig("project.env");
        String oldKey = "glb:" + env + ":delayed_task:glb:" + env + ":delayed_task:comm";
        List<Tuple> delayedTasksByEnd = qccRedisManager.zrangeByScoreWithScores(StringUtils.getBytes(oldKey), 0, Double.MAX_VALUE);
        logger.info("修正延时队列redisKey，条数：" + delayedTasksByEnd.size());
        if (CollectionUtils.isNotEmpty(delayedTasksByEnd)) {
            for (Tuple tuple : delayedTasksByEnd) {
                redisService.addDelayedCommTask(tuple.getElement(), Double.valueOf(tuple.getScore()).longValue());
            }
            qccRedisManager.del(StringUtils.getBytes(oldKey));
        }
        logger.info("修正延时队列redisKey完成");
    }


	/**
	 * 
	 * @param companyIdList 
	 */
	public void patchSelfOrderTopUp(List<String> companyIdList) {
		logger.info("patchSelfOrderTopUp 开始执行patchSelfOrderTopUp批量处理订单");

		int totalProcessed = 0;


		if (CollectionUtils.isNotEmpty(companyIdList)) {
			companyIdList = companyIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
		} else {
			SysCompanyCondition companyCondition = new SysCompanyCondition();
			companyCondition.setTypeList(Lists.newArrayList(CompTypeEnum.COMPANY_SELF.getCode(), CompTypeEnum.CLIENT.getCode()));
			companyIdList = commSysCompanyService.getCompanyIdByCondition(companyCondition);
		}


		for (String companyId : companyIdList) {
			try {
				logger.info("patchSelfOrderTopUp 处理公司ID：{}", companyId);
				List<TblCompReportOrder> orderList = commTblCompReportOrderService.listByCompany(companyId).stream()
						.filter(k -> StringUtils.equals(k.getPayType(), PayTypeEnum.ONLINE.getCode())
								&& StringUtils.equals(k.getPayStatus(), PayStatusEnum.PAID.getCode()))
						.collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(orderList)) {
					for (TblCompReportOrder order : orderList) {
						try {
							logger.info("patchSelfOrderTopUp 处理公司ID：{} 订单ID：{}", companyId, order.getId());
							deliveryBusinessService.patchTopUpConsumedOrder(order);
							totalProcessed++;
							logger.info("patchSelfOrderTopUp 处理公司ID：{}， 订单ID：{} 完成，当前处理订单数量：{}", companyId, order.getId(), totalProcessed);
						} catch (Exception e) {
							logger.error("patchSelfOrderTopUp 处理公司ID：{}，订单ID：{} 失败", companyId, order.getId());
						}
					}
				}
				commSysCompInfoFuncCountService.updateFuncCountByValidProdAcc(companyId);
			} catch (Exception e) {
				logger.error("patchSelfOrderTopUp 处理公司ID：{} 失败", companyId, e);
			}
			logger.info("patchSelfOrderTopUp 处理公司ID：{} 完成，处理订单数量：{}", companyId, totalProcessed);
		}
		logger.info("patchSelfOrderTopUp 完成处理所有公司, 处理订单数量：{}", totalProcessed);
	}


	public void patchSelfOrderTopUpByOrderId(List<String> orderIdList) {
		logger.info("patchSelfOrderTopUpByOrderId 开始执行patchSelfOrderTopUpByOrderId批量处理订单");

		int totalProcessed = 0;

		List<TblCompReportOrder> orderList = commTblCompReportOrderService.batchGet(orderIdList).stream()
			.filter(k -> StringUtils.equals(k.getPayType(), PayTypeEnum.ONLINE.getCode())
					&& StringUtils.equals(k.getPayStatus(), PayStatusEnum.PAID.getCode()))
			.collect(Collectors.toList());

		for (TblCompReportOrder order : orderList) {
			try {
				logger.info("patchSelfOrderTopUpByOrderId 处理订单ID：{}", order.getId());
				deliveryBusinessService.patchTopUpConsumedOrder(order);
				commSysCompInfoFuncCountService.updateFuncCountByValidProdAcc(order.getCompanyId());
			} catch (Exception e) {
				logger.error("patchSelfOrderTopUpByOrderId 处理订单ID：{} 失败", order.getId(), e);
			}
			totalProcessed++;
			logger.info("patchSelfOrderTopUpByOrderId 处理订单ID：{} 完成，当前处理订单数量：{}", order.getId(), totalProcessed);
		}
		logger.info("patchSelfOrderTopUpByOrderId 完成处理所有订单, 处理订单数量：{}", totalProcessed);
	}


	public void patchTransactionCompanyRemainUnit(List<String> companyIdList) {
		logger.info("patchTransactionCompanyRemainUnit 开始执行批量处理公司流水的companyRemainUnit");

		int totalProcessed = 0;


		if (CollectionUtils.isNotEmpty(companyIdList)) {
			companyIdList = companyIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
		} else {
			SysCompanyCondition companyCondition = new SysCompanyCondition();
			companyCondition.setTypeList(Lists.newArrayList(CompTypeEnum.SIGN.getCode()));
			companyCondition.setPayTypeList(Lists.newArrayList(CompPayTypeEnum.AFTER.getCode()));
			companyIdList = commSysCompanyService.getCompanyIdByCondition(companyCondition);
		}
		
		if (CollectionUtils.isEmpty(companyIdList)) {
			logger.info("patchTransactionCompanyRemainUnit 没有找到符合条件的公司");
			return;
		}
		
		logger.info("patchTransactionCompanyRemainUnit 需要处理的公司数量：{}", companyIdList.size());
		
		for (String companyId : companyIdList) {
			try {
				// 获取公司所有的流水记录并按dateInt正序排列
				List<TblContractDeliveryTransaction> transactions = transactionService.getByCompanyId(companyId);

				if (CollectionUtils.isEmpty(transactions)) {
					logger.info("companyId: {} 没有找到流水记录", companyId);
					continue;
				}
				
				// 按dateInt正序排序，过滤掉 dateInt 为空的记录
				transactions = transactions.stream()
						.filter(tx -> tx.getDateInt() != null)
						.sorted(Comparator.comparing(TblContractDeliveryTransaction::getDateInt).thenComparing(TblContractDeliveryTransaction::getCreateDate))
						.collect(Collectors.toList());
				

				BigDecimal totalConsumedUnit = BigDecimal.ZERO;
				for (TblContractDeliveryTransaction transaction : transactions) {
					Integer dateInt = transaction.getDateInt();
					if (dateInt == null) {
						logger.warn("流水记录dateInt为空, transactionId: {}", transaction.getId());
						continue;
					}
					
					// 更新累计消耗量
					BigDecimal changedUnit = transaction.getChangedUnit() != null
						? transaction.getChangedUnit() : BigDecimal.ZERO;
					totalConsumedUnit = totalConsumedUnit.add(changedUnit);
					

					// 计算剩余额度
					BigDecimal companyRemainUnit = Constants.MAX_UNIT.subtract(totalConsumedUnit);
					
					// 更新流水记录
					transaction.setCompanyRemainUnit(companyRemainUnit);
					transactionService.save(transaction);
					logger.info("companyId: {} transactionId: {} companyRemainUnit: {}", companyId, transaction.getId(), companyRemainUnit);
					totalProcessed++;
				}
				TblContractDeliveryTransaction lastTransaction = transactions.get(transactions.size() - 1);
				FunctionCountInfoTO functionCountInfoTO = prodAccService.calcCompFuncCount(companyId);
				BigDecimal remainUnit = functionCountInfoTO.getTotalCount().subtract(functionCountInfoTO.getConsumedCount());
				if (remainUnit.compareTo(lastTransaction.getCompanyRemainUnit()) != 0) {
					logger.error("transaction companyRemainUnit: {}, funcCount remainUnit: {}", lastTransaction.getCompanyRemainUnit(), remainUnit);
				}

				logger.info("companyId: {} 处理完成，更新流水记录数：{}", companyId, transactions.size());
				
			} catch (Exception e) {
				logger.error("处理公司流水异常, companyId: {}, 错误: ", companyId,  e);
			}
		}
		
		logger.info("patchTransactionCompanyRemainUnit 完成处理所有公司流水, 共更新流水记录数：{}", totalProcessed);
	}


	/**
	 * 管理员手动创建用户试用申请数据
	 * 
	 * @param trialApplyFormList
	 * @param sendNotifyEmail
	 */
	public void patchUserTrialApply(List<TrialApplyForm> trialApplyFormList, String sendNotifyEmail) {
		for (TrialApplyForm trialApplyForm : trialApplyFormList) {
            try {
                trialApplyService.patchUserTrialApply(trialApplyForm, sendNotifyEmail);
            } catch (Exception e) {
                logger.error("处理用户申请异常, trialApplyForm: {}, 错误: ", trialApplyForm,  e);
            }
        }
	}

    public void patchExternalApiData4Customer(List<TblCompApiOrderResp> apiOrderList) {
		for (TblCompApiOrderResp apiOrderResp : apiOrderList) {
			try {
				TblCompApiOrderResp orderResp = apiOrderRespService.getByCompanyIdAndOrderNo(apiOrderResp.getCompanyId(), apiOrderResp.getOrderNo());
				orderResp.setDataStatus(Constants.YES);
				orderResp.setResp(apiOrderResp.getResp());
				apiOrderRespService.save(orderResp);
			} catch (Exception e) {
				logger.error("处理用户接口数据异常, companyId: {}, orderNo:{} 错误: ", apiOrderResp.getCompanyId(), apiOrderResp.getOrderNo(),  e);
			}
		}		
    }

	public void patchMonitorEventNotify(List<String> userIdList, Date beginDate, Date endDate) {
		List<User> userList = userService.listByIds(userIdList);
		if(CollectionUtils.isNotEmpty(userList)){
			monitorBusinessService.monitorChangeEventNotify(userList, beginDate, endDate);
		}
	}

	public void patchHkDoc() {
		logger.info("开始为KYC Basic + Annual Return/Incorporation Form订单补偿tbl_comp_report_order_hk_doc数据");
		List<TblCompReportOrder> tblCompReportOrders = commTblCompReportOrderService.listNoHkDoc();
		logger.info("待处理条数: " + tblCompReportOrders.size());
		if (CollectionUtils.isEmpty(tblCompReportOrders)) {
			logger.info("没有找到需要处理的订单");
			return;
		}
		Set<String> apiOrderNos = tblCompReportOrders.stream().map(TblCompReportOrder::getApiOrderNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(apiOrderNos)) {
			logger.info("没有找到需要处理的订单");
			return;
		}
		Map<String, HongKongAnnouncementsTO> apiOrderNo2HongKongAnnouncementsTOMap = overseaHongKongAnnouncementsService.getApiOrderNo2HongKongAnnouncementsTOMap(apiOrderNos);
		List<TblCompReportOrderHkDoc> hkDocList = Lists.newArrayList();
		for (TblCompReportOrder tblCompReportOrder : tblCompReportOrders) {
			HongKongAnnouncementsTO hongKongAnnouncementsTO = apiOrderNo2HongKongAnnouncementsTOMap.get(tblCompReportOrder.getApiOrderNo());
			if (hongKongAnnouncementsTO != null) {
				TblCompReportOrderHkDoc hkDocEntity = new TblCompReportOrderHkDoc();
				hkDocEntity.setRelId(tblCompReportOrder.getId());
				hkDocEntity.setRelType(OrderHkDocRelTypeEnum.ORDER.getCode());
				hkDocEntity.setUserId(tblCompReportOrder.getUserId());
				hkDocEntity.inject(hongKongAnnouncementsTO);
				hkDocList.add(hkDocEntity);
			} else {
				logger.error("没有找到订单关联的文档信息, apiOrderNo:{}", tblCompReportOrder.getApiOrderNo());
			}
		}
		if (CollectionUtils.isNotEmpty(hkDocList)) {
			hkDocService.batchPartialInsert(hkDocList);
		}
		logger.info("成功处理条数: " + hkDocList.size());
		logger.info("补偿完成");
	}

	/**
	 * 补偿api order 数据处理
	 * 
	 * @param apiOrderIdList
	 */
	public void patchExternalApiData4CustomerV2(List<String> apiOrderIdList) {
		for (String apiOrderId : apiOrderIdList) {
			try {
				apiDataService.savaApiData2DB(apiOrderId);
			} catch (Exception e) {
				logger.error("处理用户接口数据异常, apiOrderId:{} 错误: ", apiOrderId, e);
			}
		}
	}

	public void patchUiVersion(List<String> companyIds) {
		logger.info("开始处理UI版本");
		if (CollectionUtils.isEmpty(companyIds)) {
			logger.info("入参为空, 处理全部符合条件的数据");
			companyIds = sysCompInfoColExtService.listCompanyIdsWithValue(EXT_TYPE, COL_NAME_UI_DEFAULT_VERSION, COL_VALUE_UI_VERSION_V1);
		}
		logger.info("待处理条数: " + companyIds.size());
		for (String companyId : companyIds) {
			try {
				commSysCompanyService.configUiDefaultVersion4platformMgnt(companyId, COL_VALUE_UI_VERSION_V2);
				saasLoginService.logoutUserByCompanyId(companyId, companyId);
				logger.info("已处理, companyId: " + companyId);
			} catch (Exception e) {
				logger.error("数据处理异常, companyId: " + companyId, e);
			}
		}
		logger.info("成功处理UI版本");
	}
}
