package com.backend.common.yunjuapi.model;

import com.qcc.frame.commons.ienum.HkCompanyTypeEnum;
import com.qcc.frame.commons.ienum.HkPositionEnum;
import com.qcc.frame.commons.ienum.HkStatusEnum;
import com.qcc.frame.commons.ienum.WindingUpModeEnum;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * added for v1.0.8 KNZT-451
 * updated for v2.0.2 KNZT-5187 3001接口改造
 */
@Data
public class HkCorpRealTimeInfoTOResult {

    private String dataStatus;
    private ResData data;

    @Data
    public final static class ResData {
        private Basic basic;
        private List<ShareCapital> shareCapital; // added for v2.0.2 KNZT-4784
        private List<Director> directors;
        private List<CompanySecretary> companySecretary;
        private List<Shareholder> shareholders;
        private String retrievalTime;

        @Data
        public final static class Basic {
            private String companyNumber;
            private String businessRegistrationNumber;
            private String companyNameChn;
            private String companyNameEng;
            private String companyType;
            private String companyTypeEn;// 公司类型
            private String registrationDate;
            private String status;
            private String statusEn;
            private String address;
            private String officeEffectiveDate; // 注册处有效时间
            private String windingUpMode;
            private String windingUpModeEn; // 清盘模式
            private String registerOfCharges;
            private String registerOfChargesEn;
            private String dissolutionDate;
            private String important;
            private String incorporationPlace;
            private String incorporationPlaceEn;
            private String remarks;
            private String remarksEn;
            private List<NameRecord> nameRecordList;
            private String businessNatureCode;
            private String businessNatureDes;

            public void setCompanyType(String companyType) {
                this.companyType = companyType;
                this.companyTypeEn = HkCompanyTypeEnum.getCompanyTypeEnByCompanyTypeCn(companyType);
            }

            public void setStatus(String status) {
                this.status = status;
                this.statusEn = HkStatusEnum.getStatusEnByStatusCn(status);
            }

            public void setWindingUpMode(String windingUpMode) {
                this.windingUpMode = windingUpMode;
                this.windingUpModeEn = WindingUpModeEnum.getEnByCn(windingUpMode);
            }

            public void setRegisterOfCharges(String registerOfCharges) {
                this.registerOfCharges = registerOfCharges;
                if (StringUtils.isNotBlank(registerOfCharges)) {
                    this.registerOfChargesEn = StringUtils.equals("有", registerOfCharges) ? "Available" : "Unavailable";
                }
            }

            @Data
            public final static class NameRecord {
                private String startDate;
                private String name;
                private String enName;
            }
        }

        @Data
        public final static class Director implements HkCorpIdentityInfoProcessor {
            private String fullNameEng;
            private String fullNameChn;
            private String type;
            private String typeEn;
            private String companyNumber;
            private String businessRegistrationNumber;// 商业登记号码
            private String identificationString;
            private String passportCountry; // 护照签发国家/地区
            private String hkId; // 香港身份证号
            private String passportNo; // 护照号码
            private String cnId; // 中国身份证号

            public void setType(String type) {
                this.type = HkPositionEnum.formatPosition(type);
                this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
            }
        }

        @Data
        public final static class Shareholder {
            private String address;
            private String classofShares;
            private String fullNameEng;
            private String fullNameChn;
            private String currentHolding;
            private String percentofClass;
            private String totalNumber;
            private String type;
            private String typeEn;
            private String uniformSocialCreditCode;
            private String deadline; // 持股状况截至日期 Deadline for shareholding status

            public void setType(String type) {
                this.type = HkPositionEnum.formatPosition(type);
                this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
            }
        }

        @Data
        public final static class ShareCapital {
            private String currency;// 货币单位
            private String totalAmount;// 注册资本
            private String totalAmountPaid;// 实缴资本
            private String totalAmountPaidCurrency;// 已缴款额货币
        }

        @Data
        public final static class CompanySecretary implements HkCorpIdentityInfoProcessor {
            private String fullNameEng;
            private String fullNameChn;
            private String type;
            private String typeEn;
            private String companyNumber;// 公司编号
            private String businessRegistrationNumber;// 商业登记号码
            private String address;// 地址
            private String appointedDate;
            private String identificationString;
            private String passportCountry; // 护照签发国家/地区
            private String hkId; // 香港身份证号
            private String passportNo; // 护照号码
            private String cnId; // 中国身份证号
            private String important;

            public void setType(String type) {
                this.type = type;
                this.typeEn = HkPositionEnum.getPositionEnByPosition(type);
            }
        }
    }
}
