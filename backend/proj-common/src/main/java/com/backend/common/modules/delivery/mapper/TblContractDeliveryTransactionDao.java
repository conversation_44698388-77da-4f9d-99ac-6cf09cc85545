package com.backend.common.modules.delivery.mapper;

import com.backend.common.entity.mapping.CompanyConsumedInfoTO;
import com.backend.common.modules.delivery.model.*;
import com.qcc.frame.jee.base.AbstractCondition;
import org.apache.ibatis.annotations.Param;

import com.backend.common.modules.delivery.condition.TransactionPageCondition;
import com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MyBatisDao
public interface TblContractDeliveryTransactionDao extends CrudDao<TblContractDeliveryTransaction> {

    List<TblContractDeliveryTransaction> getByRelId(@Param("relId") String relId, @Param("transactionType") String transactionType);

    BigDecimal sumChangedUnitByProAccAndDate(@Param("contractDeliveryProdAccId") String contractDeliveryProdAccId, @Param("dateInt") Integer dateInt);

    BigDecimal sumChangedUnitByContractDeliveryAndDate(@Param("contractDeliveryId") String contractDeliveryId,
                                                       @Param("beginDate") Integer beginDate, @Param("endDate") Integer endDate);

    List<TblContractDeliveryTransaction> getChangedByCompanyIdAdnDateRange(@Param("companyId") String companyId, @Param("beginDate") Integer beginDateInt, @Param("endDate") Integer endDateInt);

    void updateProdAccIdByProdAccId(@Param("beforeProdAccId") String beforeProdAccId, @Param("afterProdAccId") String afterProdAccId);

    BigDecimal sumChangedUnitByProAccAndDateRange(@Param("contractDeliveryProdAccId") String contractDeliveryProdAccId,
                                                  @Param("beginDate") Integer beginDate, @Param("endDate") Integer endDate);

    List<TransactionContractTO> pageTransactionContract(AbstractCondition<TblContractDeliveryTransaction> condition);

    BigDecimal sumChangedUnitByContractDeliveryWithType(@Param("contractDeliveryId") String contractDeliveryId,
                                                        @Param("beginDate") Integer beginDate, @Param("endDate") Integer endDate,
                                                        @Param("transactionTypeList") List<String> transactionTypeList);

    List<TransactionOrderUserConsumeTO> getChangedSumOrderUnitGroupByUser(@Param("contractDeliveryId") String contractDeliveryId, @Param("beginDate") Integer beginDate, @Param("endDate") Integer endDate, @Param("transactionList") List<String> transactionList);

    BigDecimal sumCorrectOrderUnitByContractDeliveryAndDate(@Param("contractDeliveryId") String contractDeliveryId,
                                                            @Param("beginDate") Integer beginDate, @Param("endDate") Integer endDate);

    BigDecimal sumChangedUnitByProAccAndDateRangeByContractNo(@Param("contractNo") String contractNo,
            @Param("beginDateInt") Integer beginDateInt, @Param("endDateInt") Integer endDateInt);

    BigDecimal sumCnChangedUnitByContractNoAndDateRange(@Param("contractNo") String contractNo, @Param("beginDateInt") Integer beginDateInt, @Param("endDateInt") Integer endDateInt, @Param("transactionTypeList") List<String> transactionTypeList);

    List<TransactionListTO> pageTransaction(TransactionPageCondition condition);
    List<TransactionBO> pageTransaction4UserComp(TransactionPageCondition condition);

    List<TransactionItemConsumeTO> getCompanyConsumeItemGroupByOrderTypeUnit(@Param("companyId") String companyId, @Param("beginDateInt") int beginDateInt,
                                                                             @Param("endDateInt") int endDateInt, @Param("transactionTypeList") List<String> transactionTypeList);

    /**
     * added for v2.1.3 fengsw KNZT-6263
     * 获取时间范围内的公司账号额度消耗情况
     * 
     * @param companyIdList
     * @param startDate
     * @param endDate
     * @return
     */
    List<CompanyConsumedInfoTO> listCompanyConsumedInfoWithDateRange(@Param("companyIdList") List<String> companyIdList, @Param("startDate")  Date startDate, @Param("endDate")  Date endDate);

    void correctSelfOrderTransaction(@Param("contractDeliveryId") String contractDeliveryId, @Param("prodAccId") String prodAccId, @Param("relId") String relId, @Param("beforeRemainUnit") BigDecimal beforeRemainUnit);

    /**
     * 根据公司ID和时间范围查询公司账号额度消耗情况
     * added for v2.1.7 lvcy KNZT-6583
     * 
     * @param companyId
     * @param beginDateInt
     * @param endDateInt
     * @param transactionTypeList
     * @return
     */
    BigDecimal sumChangeUnitByCompanyIdAndDateRange(@Param("companyId") String companyId, @Param("beginDateInt") int beginDateInt, @Param("endDateInt") int endDateInt, @Param("transactionTypeList") List<String> transactionTypeList);

    /**
     * 根据公司ID查询所有流水记录
     * 
     * @param companyId 公司ID
     * @return 流水记录列表
     */
    List<TblContractDeliveryTransaction> findByCompanyId(@Param("companyId") String companyId);

    /**
     * 根据合同ID和时间范围查询的订单流水关联的调整流水，根据用户汇总
     * 
     * @param contractDeliveryId 合同ID
     * @param beginDateInt 开始日期
     * @param endDateInt 结束日期
     * @return 调整流水的总和
     */
    List<TransactionOrderUserConsumeTO> getCorrectedUnitRelateOrderTransactionByUser(@Param("contractDeliveryId") String contractDeliveryId,
                                                                                     @Param("beginDateInt") int beginDateInt, @Param("endDateInt") int endDateInt);
    /**
     * added for v.2.2.7 fengsw KNZT-7785
     * 根据充值类型和流水类型查询公司账号的额度情况
     *
     * @param companyId
     * @param category
     * @param typeList
     * @return
     */
    BigDecimal sumChangeUnitByCategoryAndType(@Param("companyId") String companyId, @Param("category") String category, @Param("typeList") List<String> typeList);

    /**
     * 根据用户ID列表、开始日期和结束日期查询用户订单消费情况
     *
     * @param userIdList              用户ID列表
     * @param beginDateInt            开始日期
     * @param endDateInt              结束日期
     * @param transactionList 订单类型列表
     * @return 用户订单消费情况列表
     */
    List<TransactionOrderUserConsumeTO> calcTransactionConsumeGroupByUser(@Param("companyId") String companyId,
                                                                          @Param("userIdList") List<String> userIdList,
                                                                          @Param("beginDateInt") int beginDateInt,
                                                                          @Param("endDateInt") int endDateInt,
                                                                          @Param("transactionList") List<String> transactionList);

    /**
     * 计算公司在指定时间范围内的总额度消耗（不按用户分组）
     *
     * @param companyId           公司ID
     * @param beginDateInt        开始日期
     * @param endDateInt          结束日期
     * @param transactionTypeList 交易类型列表
     * @return 总额度消耗
     */
    BigDecimal calcCompanyTotalConsumeByDateRange(@Param("companyId") String companyId,
                                                  @Param("beginDateInt") int beginDateInt,
                                                  @Param("endDateInt") int endDateInt,
                                                  @Param("transactionTypeList") List<String> transactionTypeList);
}
