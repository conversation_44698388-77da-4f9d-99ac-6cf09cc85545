package com.backend.common.openapi.model;

import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 客找找合同详情接口
 * <AUTHOR>
 * @datetime 2024/6/26 18:01
 */
public class KzzContractDetailTO {

    private Long contractId;
    private String code;
    private Long customerId;
    private Date beginDate;
    private Date expiredDate;
    private Integer approval;

    private List<ContractProduct> contractProducts;
    private List<Attachment> attachments;
    private List<PaymentSheet> paymentSheets;


    // 非接口直接返回，从内部嵌入对象得到
    private String customerName;
    private String abbreviation;
    private String quotationCode;
    private String payType;
    private String isThirdPart; // added for lvcy v2.0.6 KNZT-5655
    private Long signerId; // 签订人id added for fengsw v2.0.8 KNZT-5806
    private Long contactId;
    private Long currencyId; // 币种单位id added for fengsw v2.0.8 KNZT-5806
    private int serviceYear;// 服务期限
    private String signingParty;// 签约主体
    private String invoiceMethod;// 开票方式
    private String invoiceName;// 客户开票名称
    private String statementType;// 结算周期
    private Long customerUserId;// 客户负责人 added for fengsw v2.2.7 KNZT-7687
    public boolean judgeInvalid() {
        List<Integer> approvedStatusList = Lists.newArrayList(-2, 0, 11);
        return Objects.isNull(approval) || !approvedStatusList.contains(approval);
    }


    public static class ContractProduct {
        private Integer productId;// 产品id
        private String categoryName; // 服务名称
        private BigDecimal price; // 原单价
        private BigDecimal discountRate; // 折扣率
        private BigDecimal discountPrice; // 折后单价
        private Integer quantity; // 单元数量
        private BigDecimal totalPrice; // 总价
        private Integer period; // 服务期限
        private Integer timeLimits;// 服务期次
        private Integer discountType;
        private BigDecimal freeCount;
        private Integer consumptionStatus;

        // 非接口直接返回，从内部嵌入对象得到
        private String productCode;
        private String productName;


        public Integer getProductId() {
            return productId;
        }

        public void setProductId(Integer productId) {
            this.productId = productId;
        }

        public String getCategoryName() {
            return categoryName;
        }

        public void setCategoryName(String categoryName) {
            this.categoryName = categoryName;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public BigDecimal getDiscountRate() {
            return discountRate;
        }

        public void setDiscountRate(BigDecimal discountRate) {
            this.discountRate = discountRate;
        }

        public BigDecimal getDiscountPrice() {
            return discountPrice;
        }

        public void setDiscountPrice(BigDecimal discountPrice) {
            this.discountPrice = discountPrice;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }

        public Integer getPeriod() {
            return period;
        }

        public void setPeriod(Integer period) {
            this.period = period;
        }

        public Integer getTimeLimits() {
            return timeLimits;
        }

        public void setTimeLimits(Integer timeLimits) {
            this.timeLimits = timeLimits;
        }

        public Integer getDiscountType() {
            return discountType;
        }

        public void setDiscountType(Integer discountType) {
            this.discountType = discountType;
        }

        public BigDecimal getFreeCount() {
            return freeCount;
        }

        public void setFreeCount(BigDecimal freeCount) {
            this.freeCount = freeCount;
        }

        public Integer getConsumptionStatus() {
            return consumptionStatus;
        }

        public void setConsumptionStatus(Integer consumptionStatus) {
            this.consumptionStatus = consumptionStatus;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }
    }
    
    public static class PaymentSheet{
        private String code;//回款单编号
        private int contractId;// 合同id
        private int paymentSheetId;// 回款单
        private int paymentId; // 
        private int paymentType;// 
        private Date paymentDate;//回款日期
        private BigDecimal amount;//回款金额
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getContractId() {
            return contractId;
        }

        public void setContractId(int contractId) {
            this.contractId = contractId;
        }

        public int getPaymentSheetId() {
            return paymentSheetId;
        }

        public void setPaymentSheetId(int paymentSheetId) {
            this.paymentSheetId = paymentSheetId;
        }

        public int getPaymentId() {
            return paymentId;
        }

        public void setPaymentId(int paymentId) {
            this.paymentId = paymentId;
        }

        public int getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(Integer paymentType) {
            this.paymentType = paymentType;
        }

        public void setPaymentType(int paymentType) {
            this.paymentType = paymentType;
        }

        public Date getPaymentDate() {
            return paymentDate;
        }

        public void setPaymentDate(Date paymentDate) {
            this.paymentDate = paymentDate;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }
    }

    public static class Attachment {
        private String name;
        private String url;
        private String fileType;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getExpiredDate() {
        return expiredDate;
    }

    public void setExpiredDate(Date expiredDate) {
        this.expiredDate = expiredDate;
    }

    public List<ContractProduct> getContractProducts() {
        return contractProducts;
    }

    public void setContractProducts(List<ContractProduct> contractProducts) {
        this.contractProducts = contractProducts;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public void setAbbreviation(String abbreviation) {
        this.abbreviation = abbreviation;
    }

    public String getQuotationCode() {
        return quotationCode;
    }

    public void setQuotationCode(String quotationCode) {
        this.quotationCode = quotationCode;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public List<PaymentSheet> getPaymentSheets() {
        return paymentSheets;
    }

    public void setPaymentSheets(List<PaymentSheet> paymentSheets) {
        this.paymentSheets = paymentSheets;
    }

    public Integer getApproval() {
        return approval;
    }

    public void setApproval(Integer approval) {
        this.approval = approval;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getIsThirdPart() {
        return isThirdPart;
    }

    public void setIsThirdPart(String isThirdPart) {
        this.isThirdPart = isThirdPart;
    }

    public Long getSignerId() {
        return signerId;
    }

    public void setSignerId(Long signerId) {
        this.signerId = signerId;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(Long currencyId) {
        this.currencyId = currencyId;
    }

    public int getServiceYear() {
        return serviceYear;
    }

    public void setServiceYear(int serviceYear) {
        this.serviceYear = serviceYear;
    }

    public String getInvoiceMethod() {
        return invoiceMethod;
    }

    public void setInvoiceMethod(String invoiceMethod) {
        this.invoiceMethod = invoiceMethod;
    }

    public String getSigningParty() {
        return signingParty;
    }

    public void setSigningParty(String signingParty) {
        this.signingParty = signingParty;
    }

    public String getInvoiceName() {
        return invoiceName;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getStatementType() {
        return statementType;
    }

    public void setStatementType(String statementType) {
        this.statementType = statementType;
    }

    public Long getCustomerUserId() {
        return customerUserId;
    }

    public void setCustomerUserId(Long customerUserId) {
        this.customerUserId = customerUserId;
    }
}
