package com.backend.common.modules.delivery.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 月度账单管理展示对象
 */
public class StatementMonthly4ManagementTO {
    
    private String id; // 账单ID
    private String companyId; // 公司ID
    private String invoiceNo; // 发票号
    private String organization; // 组织名称（公司简称）
    private Integer companyType; // 账号类型
    private String companyTypeDesc; // 账号类型描述
    private String payType; // 付费类型
    private String payTypeDesc; // 付费类型描述
    private Date updateDate; // 更新日期
    private Integer statementMonth; // 账单月份
    private String statementMonthDesc; // 账单月份描述
    private String amountStd; // 币种
    private BigDecimal totalAmountDue; // 月度金额
    private BigDecimal returnAmount; // 回款金额
    private String returnStatus; // 回款状态
    private String returnStatusDesc; // 回款状态描述
    private String url; // 账单下载地址
    private Boolean canDownload = false; // 是否可以下载
    private Boolean canConfirm = false; // 是否可以确认
    private Boolean canReGenerate = false; // 是否可以重新生成

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public Integer getCompanyType() {
        return companyType;
    }

    public void setCompanyType(Integer companyType) {
        this.companyType = companyType;
    }

    public String getCompanyTypeDesc() {
        return companyTypeDesc;
    }

    public void setCompanyTypeDesc(String companyTypeDesc) {
        this.companyTypeDesc = companyTypeDesc;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayTypeDesc() {
        return payTypeDesc;
    }

    public void setPayTypeDesc(String payTypeDesc) {
        this.payTypeDesc = payTypeDesc;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getStatementMonth() {
        return statementMonth;
    }

    public void setStatementMonth(Integer statementMonth) {
        this.statementMonth = statementMonth;
    }

    public String getAmountStd() {
        return amountStd;
    }

    public void setAmountStd(String amountStd) {
        this.amountStd = amountStd;
    }

    public BigDecimal getTotalAmountDue() {
        return totalAmountDue;
    }

    public void setTotalAmountDue(BigDecimal totalAmountDue) {
        this.totalAmountDue = totalAmountDue;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    public String getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(String returnStatus) {
        this.returnStatus = returnStatus;
    }

    public String getReturnStatusDesc() {
        return returnStatusDesc;
    }

    public void setReturnStatusDesc(String returnStatusDesc) {
        this.returnStatusDesc = returnStatusDesc;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Boolean getCanConfirm() {
        return canConfirm;
    }

    public void setCanConfirm(Boolean canConfirm) {
        this.canConfirm = canConfirm;
    }

    public Boolean getCanReGenerate() {
        return canReGenerate;
    }

    public void setCanReGenerate(Boolean canReGenerate) {
        this.canReGenerate = canReGenerate;
    }

    public Boolean getCanDownload() {
        return canDownload;
    }
    
    public void setCanDownload(Boolean canDownload) {
        this.canDownload = canDownload;
    }

    public String getStatementMonthDesc() {
        return statementMonthDesc;
    }

    public void setStatementMonthDesc(String statementMonthDesc) {
        this.statementMonthDesc = statementMonthDesc;
    }
    
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
} 