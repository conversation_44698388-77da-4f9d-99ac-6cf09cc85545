package com.backend.common.yunjuapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.backend.common.yunjuapi.form.CourtCaseForm;
import com.backend.common.yunjuapi.form.ShellCompanySearchForm;
import com.backend.common.yunjuapi.model.*;
import com.backend.common.yunjuapi.model.merchant.*;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.*;
import com.qcc.frame.commons.ienum.financial.TaxCollectionItemEnum;
import com.qcc.frame.commons.ienum.merchant.*;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.AmountUnit;
import com.qcc.frame.jee.commons.model.GroupItem;
import com.qcc.frame.jee.commons.model.Item;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.ApiStatusException;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import com.qcc.frame.jee.modules.sys.translation.*;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CompanyDetailsInterface {

    protected static Logger logger = LoggerFactory.getLogger(CompanyDetailsInterface.class);

    public static final String ACTIVE_STATUS_EN = "Active";
    public static final String INACTIVE_STATUS_EN = "Inactive";
    public static final String UBO_PATH_FLAG  = "#_#";
    private static TranslaterService translaterService = SpringContextHolder.getBean(TranslaterService.class);
    private static CommTblCompReportOrderService commTblCompReportOrderService = SpringContextHolder.getBean(CommTblCompReportOrderService.class);
    private static SysConfigCacheService sysConfigCacheService = SpringContextHolder.getBean(SysConfigCacheService.class);
    private static SysDingMsgNewTranService sysDingMsgNewTranService = SpringContextHolder.getBean(SysDingMsgNewTranService.class);
    private static CommonDataService commonDataService = SpringContextHolder.getBean(CommonDataService.class);

    // added for v1.6.0 KNZT-2716
    private final static Map<String, String> COLOR_MAP = new HashMap<>();

    static {
        COLOR_MAP.put("0", "E59E9E");
        COLOR_MAP.put("1", "72C1A0");
        COLOR_MAP.put("2", "E5B072");
        COLOR_MAP.put("3", "AE9EE5");
        COLOR_MAP.put("4", "9EA6E5");
        COLOR_MAP.put("5", "8BABE5");
        COLOR_MAP.put("6", "72BCCE");
        COLOR_MAP.put("7", "E5A687");
        COLOR_MAP.put("8", "C7AE8E");
        COLOR_MAP.put("9", "97BB72");
        COLOR_MAP.put("a", "D294D2");
        COLOR_MAP.put("b", "E5BF72");
        COLOR_MAP.put("c", "BD97DF");
        COLOR_MAP.put("d", "7BB1DD");
    }

    /**
     * added for v1.6.5
     * @param searchKey
     * @return
     * @throws MessageException
     */
    public static ApiGlobalCorpDetailTO getECIInfoVerifyInfo4Global(String searchKey) throws MessageException{
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/EnterpriseInfoOversea/Verify";//云聚编号9009
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        try {
            String resp;
            resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_20000);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                        ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONObject("Result") != null && jsonObj.getJSONObject("Result").getJSONObject("Data") != null) {
                        ECIInfoVerifyTO result = jsonObj.getJSONObject("Result").getJSONObject("Data").toJavaObject(ECIInfoVerifyTO.class);
                        if (result != null) {
                            return convert2ApiGlobalCorpDetailTO(result);
                        }
                    }
                    return null;
                } else {
//                    throw new MessageException("msg:" + jsonObj.getString("msg"));
                    logger.error("call /EnterpriseInfoOversea/Verify error: " + resp);
                }
            }
        } catch (Exception e) {
            logger.error("", e);
        }
        return null;

    }

    private static ApiGlobalCorpDetailTO convert2ApiGlobalCorpDetailTO(ECIInfoVerifyTO result) {
        ApiGlobalCorpDetailTO globalCorpDetailTO = new ApiGlobalCorpDetailTO();
        globalCorpDetailTO.setCorpName(result.getName());
        globalCorpDetailTO.setEnglishName(result.getEnglishName());
        if(StringUtils.startsWith(result.getKeyNo(), Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX)) {
            globalCorpDetailTO.setCreditCode(StringUtils.getNotBlankStr(result.getCreditCode(), result.getNo()));
        } else {
            globalCorpDetailTO.setCreditCode(result.getCreditCode());
        }

        globalCorpDetailTO.setImageUrl(result.getImageUrl());//added for v1.0.6 KNZT-323
        globalCorpDetailTO.setNo(result.getNo());
        globalCorpDetailTO.setOrgNo(result.getOrgNo()); // added for v2.1.3 chenbl KNZT-6295
        globalCorpDetailTO.setStatus(result.getStatus());
        globalCorpDetailTO.setShortStatus(result.getShortStatus()); // added for v1.8.7 KNZT-3972
        globalCorpDetailTO.setAddress(result.getAddress());
        globalCorpDetailTO.setStartDate(StringUtils.left(result.getStartDate(), 10));
        globalCorpDetailTO.setContactInfo(result.getContactInfo());
        if (globalCorpDetailTO.getContactInfo() == null) {
            globalCorpDetailTO.setContactInfo(new ContactInfoTO());
        }
        // removed for v6.7.2 KNZT-84
//        globalCorpDetailTO.setJurisdiction("China Mainland");// 移动到填充目标对象的方法中 CommTblCompReportOrderService.populateOrderByCorpDetail 和 CommTblCompReportOrderService.populateOrderByCorpDetail
        globalCorpDetailTO.setRegNo(result.getRegNo());//added for v1.2.3 KNZT-1168
        globalCorpDetailTO.setMultipleOper(result.getMultipleOper()); // added for v1.6.5 KNZT-2804
        if (Objects.nonNull(result.getArea())) {
            globalCorpDetailTO.setProvince(result.getArea().getProvince());
        }
        // added for v1.8.0 KNZT-3595
        globalCorpDetailTO.setJurisdiction(FuzzySearchInterface.getJurisdictionByKeyNo(result.getKeyNo()));
        globalCorpDetailTO.setEntType(result.getEntTypeFlag());
        globalCorpDetailTO.setIndustryInfo(result.getIndustry());// added for v2.1.4 fengsw KNZT-6360 补充行业信息和qccCode
        globalCorpDetailTO.setQccCode(result.getQccCode());
        globalCorpDetailTO.setKeyNo(result.getKeyNo());
        if (StringUtils.isNotBlank(result.getOrganizationalCode())) {// added for v2.1.4 KNZT-6360 补充企业类型
            String[] codeArray = result.getOrganizationalCode().split(",");
            if (codeArray.length > 0) {
                String companyTypeCnByCode = CompanyTypeEnum.getCompanyTypeCnByCode(codeArray[0]);
                globalCorpDetailTO.setCompanyType(StringUtils.isNotBlank(companyTypeCnByCode) ? companyTypeCnByCode : result.getEconKind());
                globalCorpDetailTO.setCompanyTypeEn(CompanyTypeEnum.getCompanyTypeEnByCompanyTypeCn(globalCorpDetailTO.getCompanyType()));
                String localCompanyName = globalCorpDetailTO.getCorpName();
                if (StringUtils.isBlank(globalCorpDetailTO.getEnglishName())
                        && StringUtils.isNotBlank(localCompanyName)
                        && CompanyTypeEnum.COMPANY_TYPE15.getCompanyTypeCode().equals(codeArray[0])) {
                    String englishText = translaterService.getEnglishText(localCompanyName, result.getKeyNo(), localCompanyName, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
                    globalCorpDetailTO.setEnglishName(englishText);
                }
            }
        }
        return globalCorpDetailTO;
    }


    /**
     * added for v1.0.2 KNZT-262
     * @param searchKey
     * @return
     * @throws MessageException
     */
    /*public static ApiGlobalCorpBasicDetailTO getGlobalCorpBasicInfo(String searchKey) throws MessageException{//多个执行事务合伙人未调整
        String url = domainUrl + "/EnterpriseInfoOversea/Verify";//云聚编号9009 // TODO: 后期会换到陈鹏包的开发平台接口
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        try {
            String resp = HttpUtils.getYunJuApi(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_10000);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                        ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONObject("Result") != null && jsonObj.getJSONObject("Result").getJSONObject("Data") != null) {
                        ApiGlobalCorpBasicDetailTO corpBasicDetailTO = new ApiGlobalCorpBasicDetailTO();
                        corpBasicDetailTO.setRegistrationNo(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("No"));
                        corpBasicDetailTO.setRegisteredAddress(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("Address"));
                        corpBasicDetailTO.setEstablishmentDate(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("StartDate"));
                        corpBasicDetailTO.setStatusOfBusiness(null);
                        corpBasicDetailTO.setRegistrationStatus(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("Status"));
                        corpBasicDetailTO.setUnifiedSocialCreditCode(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("CreditCode"));
                        corpBasicDetailTO.setOrganizationCode(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("OrgNo"));
                        corpBasicDetailTO.setLegalRepresentative(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("OperName"));
                        corpBasicDetailTO.setRegisteredCapital(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("RegistCapi"));
                        if (jsonObj.getJSONObject("Result").getJSONObject("Data").getJSONObject("Industry") != null) {
                            corpBasicDetailTO.setIndustry(jsonObj.getJSONObject("Result").getJSONObject("Data").getJSONObject("Industry").getString("Industry"));
                        }
                        corpBasicDetailTO.setCompanyType(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("EconKind"));
                        corpBasicDetailTO.setDateOfApproval(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("CheckDate"));
                        corpBasicDetailTO.setTermStart(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("TermStart"));
                        corpBasicDetailTO.setTermEnd(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("TermEnd"));
                        corpBasicDetailTO.setRegistrationAuthority(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("BelongOrg"));
                        return corpBasicDetailTO;
                    }
                    return null;
                } else if (StringUtils.isNotBlank(jsonObj.getString("msg"))) {
                    logger.error("call getGlobalCorpBasicInfo error " + jsonObj.getString("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("", e);
        }
        return null;

    }*/

    // added  for v1.7.2 KNZT-3104
    public static CorpBasicDetail4UnitTO getCorpBasicInfo4Unit(String searchKey, String includeExtraFields) throws MessageException{
        return getCorpBasicInfo4Unit(searchKey, includeExtraFields, true);
    }

    /**
     * added for v1.0.6 KNZT-323
     * updated for v1.7.2 KNZT-3104
     * @param searchKey
     * @return
     * @throws MessageException
     */
    public static CorpBasicDetail4UnitTO getCorpBasicInfo4Unit(String searchKey, String includeExtraFields, boolean needCompanyKey){
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/EnterpriseInfoOversea/Verify";//云聚编号9009
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        if(StringUtils.isNotBlank(includeExtraFields)) {
            paramMap.put("includeExtraFields", includeExtraFields);
        }
        try {
            String resp = null;
            if (needCompanyKey) {
                resp = HttpUtils.getYunJuApi(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            } else {
                resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            }
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                        ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONObject("Result") != null && jsonObj.getJSONObject("Result").getJSONObject("Data") != null) {
                        JSONObject jsonData = jsonObj.getJSONObject("Result").getJSONObject("Data");
                        CorpBasicDetail4UnitTO corpBasicDetailTO = new CorpBasicDetail4UnitTO();
                        String keyNo = jsonData.getString("KeyNo");
                        String creditCode = jsonData.getString("CreditCode");
                        String no = jsonData.getString("No");
                        //added for v1.2.3 KNZT-1168 返回商业登记号码
                        String regNo = jsonData.getString("RegNo");
                        if(StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX)) {
                            corpBasicDetailTO.setCreditCode(StringUtils.getNotBlankStr(regNo,creditCode, no));
                        } else {
                            corpBasicDetailTO.setCreditCode(creditCode);
                        }
                        // added for v1.7.1 KNZT-3079
                        corpBasicDetailTO.setOrgNo(jsonData.getString("OrgNo"));
                        corpBasicDetailTO.setRegistrationNo(no);
                        // added for v2.0.2 chenbl KNZT-5271
                        corpBasicDetailTO.setTaxNo(jsonData.getString("TaxNo"));
                        corpBasicDetailTO.setImExCode(jsonData.getString("ImExCode"));
                        corpBasicDetailTO.setScale(jsonData.getString("Scale"));
                        // added for v1.6.0 KNZT-2716
                        String shortName = jsonData.getString("ShortName");
                        String imageUrl = jsonData.getString("ImageUrl");
                        corpBasicDetailTO.setImageUrl(getLogoImageUrl(imageUrl, keyNo, shortName));//added for v1.0.6 KNZT-323

                        corpBasicDetailTO.setOperType(OperTypeEnum.getOperTypeCnByCode(jsonData.getString("OperType")));
                        corpBasicDetailTO.setOperTypeEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(corpBasicDetailTO.getOperType()));//updated for v1.1.9 KNZT-945
                        corpBasicDetailTO.setCompanyName(jsonData.getString("EnglishName"));
                        corpBasicDetailTO.setCompanyNameInLocalLanguage(jsonData.getString("Name"));
                        JSONArray formerNameArray = jsonData.getJSONArray("OriginalName");
                        // added for KNZT-1100【新增】【国际版】后台：企业在线解锁补充曾用名英文
                        JSONArray originalEnNameArray = jsonData.getJSONArray("OriginalEnName");
                        List<CorpOriginalEnName> originalEnNameList = new ArrayList<>();
                        Map<String, List<CorpOriginalEnName>> listMap = new HashMap<>();
                        if (originalEnNameArray != null && originalEnNameArray.size() > 0) {
                            originalEnNameList = originalEnNameArray.toJavaList(CorpOriginalEnName.class);
                        }
                        // updated for KNZT-1197【bug】KYC： 华人创新集团（武汉）投资管理合伙企业（有限合伙） 曾用名 没返回英文名称
                        if (CollectionUtils.isNotEmpty(originalEnNameList)) {
                            listMap = originalEnNameList.stream().collect(Collectors.groupingBy(item->StringUtils.changeCnCurvesToEn(item.getCompName())));
                        }
                        if (CollectionUtils.isNotEmpty(formerNameArray)) {
                            List<FormerNameTO> formerNameTOList = new ArrayList<>();
                            for (int i = 0; i < formerNameArray.size(); i++) {
                                JSONObject jsonObject = formerNameArray.getJSONObject(i);
                                if (Objects.nonNull(jsonObject)) {
                                    FormerNameTO formerNameTO = new FormerNameTO();
                                    String name = jsonObject.getString("Name");
                                    formerNameTO.setName(name);
                                    // 加上英文名
                                    if (listMap != null && listMap.containsKey(StringUtils.changeCnCurvesToEn(name))) {
                                        List<CorpOriginalEnName> corpOriginalEnNames = listMap.get(StringUtils.changeCnCurvesToEn(name));
                                        formerNameTO.setEnName(CollectionUtils.isNotEmpty(corpOriginalEnNames) ? corpOriginalEnNames.get(0).getEnglishName() : "");
                                    }
                                    formerNameTO.setChangeDate(jsonObject.getString("ChangeDate"));
                                    formerNameTO.setStartDate(jsonObject.getString("StartDate"));
                                    formerNameTOList.add(formerNameTO);
                                }
                            }
                            corpBasicDetailTO.setFormerName(formerNameTOList);
                        }
                        // updated for v1.7.2 KNZT-3104
//                        corpBasicDetailTO.setRegistrationStatus(jsonData.getString("Status"));
                        corpBasicDetailTO.setRegistrationStatusCn(jsonData.getString("ShortStatus"));
                        corpBasicDetailTO.setUnifiedSocialCreditCode(jsonData.getString("CreditCode"));
                        if (jsonData.getJSONObject("StaffHisCntInfo") != null) {
                            JSONObject staffHisCnt = jsonData.getJSONObject("StaffHisCntInfo");
                            if (staffHisCnt.getJSONObject("Top") != null) {
                                JSONObject staffHisCntTop = staffHisCnt.getJSONObject("Top");
                                corpBasicDetailTO.setStaffCount(staffHisCntTop.getString("Data")); // 员工人数 update
                            }
                        }
                        // added for v1.7.1 KNZT-3079
                        // updated for v1.7.3 KNZT-3275
                        corpBasicDetailTO.setInsuredCount(resolveAnnualInfo(jsonData.getString("InsuredCount")));
                        corpBasicDetailTO.setShardHolderTitleType(jsonData.getString("ShardHolderTitleType"));
                        JSONArray stockInfoJa = jsonData.getJSONArray("StockInfo");
                        // updated for v1.6.3 KNZT-2838
                        List<String> stockTypes = new ArrayList<>();
                        List<String> stockNumbers = new ArrayList<>();
                        if (stockInfoJa != null) {
                            List<JSONObject> stockInfoJos = stockInfoJa.toJavaList(JSONObject.class);
                            for (JSONObject stockInfoItem : stockInfoJos) {
                                String stockType = stockInfoItem.getString("StockType");
                                if (StringUtils.isNotBlank(stockType)) {
                                    stockTypes.add(stockType);
                                }
                                String stockNumber = stockInfoItem.getString("StockNumber");
                                if (StringUtils.isNotBlank(stockNumber)) {
                                    stockNumbers.add(stockNumber);
                                }
                            }
                        }
                        List<String> listedStockTypes = Lists.newArrayList("A股", "港股", "美股", "新三板");
                        corpBasicDetailTO.setListingStatus(CollectionUtils.intersection(stockTypes, listedStockTypes).size() > 0);
                        corpBasicDetailTO.setStockNumber(StringUtils.join(stockNumbers, ", "));
                        if (StringUtils.isBlank(corpBasicDetailTO.getUnifiedSocialCreditCode())) {
                            corpBasicDetailTO.setUnifiedSocialCreditCode(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("No"));
                        }
//                    corpBasicDetailTO.setLegalRepresentative(jsonObj.getJSONObject("Result").getJSONObject("Data").getString("OperName"));
                        /*JSONArray nameArray = jsonData.getJSONArray("OperName");
                        if (CollectionUtils.isNotEmpty(nameArray)) {
                            List<String> nameList = new ArrayList<>();
                            // updated for v1.1.2 KNZT-675
                            for (int i = 0; i < nameArray.size(); i++) {
                                if (StringUtils.length(nameArray.getString(i)) > 1) {
                                    nameList.add(nameArray.getString(i));
                                }
                            }
                            if (CollectionUtils.isNotEmpty(nameList)) {
                                corpBasicDetailTO.setLegalRepresentative(nameList);
                                if (nameList.size() == 1 && !PinyinUtils.notConvert2Pinyin(nameList.get(0)) && !StringUtils.isCorpNameCn(nameList.get(0))) {
                                    corpBasicDetailTO.setLegalRepresentativeEn(Collections.singletonList(PinyinUtils.chineseNameToPinyin(nameList.get(0))));
                                }
                            }
                        }*///removed v1.1.5 KNZT-706
                        // convertJsonObj2OperNameList(jsonData, corpBasicDetailTO);//added v1.1.5 KNZT-706
                        corpBasicDetailTO.setLegalRepresentativeInfo(convert2LegalRepresentativeInfoTO(jsonData));
                        corpBasicDetailTO.setRegisteredCapital(jsonData.getString("RegistCapi"));
                        corpBasicDetailTO.setRealCapital(jsonData.getString("RealCapi")); // added for v2.1.3 chenbl KNZT-6231
                        if (jsonData.getJSONObject("Industry") != null) {
                            //updated for v1.8.8 KNZT-4024 行业门类数据保留，然后设置门类、大类、中类、小类的码值
                            IndustryInfoTO industryInfo = new IndustryInfoTO();
                            industryInfo.setIndustry(jsonData.getJSONObject("Industry").getString("Industry"));
                            industryInfo.setIndustryEn(IndustryEnum.getIndustryEnByIndustryCn(industryInfo.getIndustry()));
                            industryInfo.setIndustryCode(jsonData.getJSONObject("Industry").getString("IndustryCode"));
                            industryInfo.setSubIndustryCode(jsonData.getJSONObject("Industry").getString("SubIndustryCode"));
                            industryInfo.setMiddleIndustryCode(jsonData.getJSONObject("Industry").getString("MiddleCategoryCode"));
                            industryInfo.setSmallIndustryCode(jsonData.getJSONObject("Industry").getString("SmallCategoryCode"));
                            corpBasicDetailTO.setIndustryInfo(industryInfo);
                        }
                        String organizationalCodeStr = jsonData.getString("OrganizationalCode");
                        if (StringUtils.isNotBlank(organizationalCodeStr)) {
                            String[] codeArray = organizationalCodeStr.split(",");
                            if (codeArray.length > 0) {
                                String companyTypeCnByCode = CompanyTypeEnum.getCompanyTypeCnByCode(codeArray[0]);
                                corpBasicDetailTO.setCompanyType(StringUtils.isNotBlank(companyTypeCnByCode) ? companyTypeCnByCode : jsonData.getString("EconKind"));
                                corpBasicDetailTO.setCompanyTypeEn(CompanyTypeEnum.getCompanyTypeEnByCompanyTypeCn(corpBasicDetailTO.getCompanyType()));
                                // added for v1.8.5 KNZT-3823
                                String localCompanyName = corpBasicDetailTO.getCompanyNameInLocalLanguage();
                                if (StringUtils.isBlank(corpBasicDetailTO.getCompanyName())
                                        && StringUtils.isNotBlank(localCompanyName)
                                        && CompanyTypeEnum.COMPANY_TYPE15.getCompanyTypeCode().equals(codeArray[0])) {
                                    String englishText = translaterService.getEnglishText(localCompanyName, keyNo, localCompanyName, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
                                    corpBasicDetailTO.setCompanyName(englishText);
                                }
                            }
                        }
                        corpBasicDetailTO.setEstablishmentDate(jsonData.getString("StartDate"));
                        corpBasicDetailTO.setDateOfApproval(jsonData.getString("CheckDate"));
                        corpBasicDetailTO.setTermStart(jsonData.getString("TermStart"));
                        corpBasicDetailTO.setTermEnd(jsonData.getString("TermEnd"));
                        corpBasicDetailTO.setRegistrationAuthority(jsonData.getString("BelongOrg"));
                        JSONObject area = jsonObj.getJSONObject("Result").getJSONObject("Data").getJSONObject("Area");
                        String registrationAuthorityEn = translateRegistrationAuthority(corpBasicDetailTO.getRegistrationAuthority());
                        corpBasicDetailTO.setRegistrationAuthorityEn(registrationAuthorityEn);
                        if (Objects.nonNull(area) && StringUtils.isNotBlank(area.getString("Province"))) {//added for v1.1.2 KNZT-664
                            String province = area.getString("Province");
                            String provinceEn = ProvinceEnum.getProvinceEnNameByProvinceName(province);
                            corpBasicDetailTO.setAreaEn(provinceEn);
                            String city = "";
                            if (StringUtils.contains(province, "上海") || StringUtils.contains(province, "北京")
                                    || StringUtils.contains(province, "重庆") || StringUtils.contains(province, "天津")) {
                                city = Objects.nonNull(area.getString("County")) ? area.getString("County") : "";
                            }else {
                                city = Objects.nonNull(area.getString("City")) ? area.getString("City") : "";
                            }
                            corpBasicDetailTO.setArea(province + city);
                            String cityEn = CityEnum.getCityEnNameByCityName(city);
                            if (StringUtils.isNotBlank(cityEn)) {
                                corpBasicDetailTO.setAreaEn(cityEn + ", " +provinceEn);
                            }
                        }
//                        corpBasicDetailTO.setRegistrationAuthorityEn(RegistrationEnum.getRegistrationEnNameByRegistrationName(corpBasicDetailTO.getRegistrationAuthority()));
                        corpBasicDetailTO.setRegisteredAddress(jsonData.getString("Address"));
                        if(StringUtils.length(corpBasicDetailTO.getRegisteredAddress()) <= 1) {
                            corpBasicDetailTO.setRegisteredAddress(null);
                        }
                        corpBasicDetailTO.setAddressPostalCode(jsonData.getString("AddressPostalCode")); // added for v2.0.7 chenbl KNZT-2102
                        if (jsonData.getJSONObject("ContactInfo") != null) {
                            corpBasicDetailTO.setContactInfo(jsonData.getJSONObject("ContactInfo").toJavaObject(ContactInfoTO.class));
                        }
                        if (corpBasicDetailTO.getContactInfo() == null) {
                            corpBasicDetailTO.setContactInfo(new ContactInfoTO());
                        } else {
                            // added for v1.5.2 KNZT-2319【新增】【国际版】补充大陆企业的电话，邮箱的来源数据信息（后端）
                            mappingTelEmailSourceTypeToEn(corpBasicDetailTO.getContactInfo());
                        }

//                        List<String> corpKeyNoList = new ArrayList<>();

                        // added for v1.1.7 KNZT-821
                        JSONArray partnerList = jsonData.getJSONArray("PartnerList");//工商登记
                        if (partnerList != null && !partnerList.isEmpty()) {
                            corpBasicDetailTO.setPartnerList(partnerList.toJavaList(CorpPartnerTO.class));
                            /* removed for v2.0.2 chenbl KNZT-5310
                            for (CorpPartnerTO p : corpBasicDetailTO.getPartnerList()) {
                                // 如果没有keyNo或者是人名，才需要兜底 // updated for v1.2.1 KNZT-970
                                if (StringUtils.isBlank(p.getKeyNo()) || StringUtils.startsWith(p.getKeyNo(), Constants.CorpTypePrefix.PERSON_PREFIX)) {
                                    p.setEnglishName(getEnNameByCnNameEnd(p.getKeyNo(), p.getStockName(), p.getEnglishName(), p.getArea())); // updated for v1.2.4 KNZT-1220
                                } else if (StringUtils.isNotBlank(p.getKeyNo())){
                                    corpKeyNoList.add(p.getKeyNo());
                                }
                                populatePartnerAreaInfo(p); // added for v1.3.9 KNZT-2132 注入股东区域信息
                            }*/

//                            CompanyPartnerResult partnerResult = ECILocalInterface.getPartnerWithGroup(jsonData.getString("KeyNo"), "Partners", "1", "1");
//                            if (partnerResult != null && partnerResult.getPaging() != null) {
//                                corpBasicDetailTO.setPartnerTotalRecords(partnerResult.getPaging().getTotalRecords());
//                            }
                            corpBasicDetailTO.setPartnerTotalRecords(jsonData.getIntValue("PartnerTotalCount"));
                        }

                        JSONArray pubPartnerList = jsonData.getJSONArray("PubPartnerList");//最新公示
                        if (pubPartnerList != null && !pubPartnerList.isEmpty()) {
                            corpBasicDetailTO.setPubPartnerList(pubPartnerList.toJavaList(CorpPartnerTO.class));
                            /* removed for v2.0.2 chenbl KNZT-5310
                            for (CorpPartnerTO p : corpBasicDetailTO.getPubPartnerList()) {
                                if (StringUtils.isBlank(p.getKeyNo()) || StringUtils.startsWith(p.getKeyNo(), Constants.CorpTypePrefix.PERSON_PREFIX)) {
                                    p.setEnglishName(getEnNameByCnNameEnd(p.getKeyNo(), p.getStockName(), p.getEnglishName(), p.getArea())); // updated for v1.2.4 KNZT-1220
                                } else if (StringUtils.isNotBlank(p.getKeyNo())){
                                    corpKeyNoList.add(p.getKeyNo());
                                }
                                populatePartnerAreaInfo(p); // added for v1.3.9 KNZT-2132 注入股东区域信息
                            }*/

//                            CompanyPartnerResult ipoPartnerResult = ECILocalInterface.getPartnerWithGroup(jsonData.getString("KeyNo"), "IpoPartners", "1", "1");
//                            if (ipoPartnerResult != null) {
//                                if (ipoPartnerResult.getPaging() != null) {
//                                    corpBasicDetailTO.setPubPartnerTotalRecords(ipoPartnerResult.getPaging().getTotalRecords());
//                                }
//                                if (CollectionUtils.isNotEmpty(ipoPartnerResult.getResult())) {
//                                    corpBasicDetailTO.setUnitType(ipoPartnerResult.getResult().get(0).getUnitType());
//                                }
//                            }
                            corpBasicDetailTO.setPubPartnerTotalRecords(jsonData.getIntValue("PubPartnerTotalCount"));
                            if(CollectionUtils.isNotEmpty(corpBasicDetailTO.getPubPartnerList())) {
                                corpBasicDetailTO.setUnitType(corpBasicDetailTO.getPubPartnerList().get(0).getUnitType());
                            }
                        }
                        // updated for v1.2.1 KNZT-970
                        /* removed for v2.0.2 chenbl KNZT-5310
                        if (CollectionUtils.isNotEmpty(corpKeyNoList)) {
                            // updated for v1.7.2 KNZT-3104
                            List<AdvanceSearchTO> advanceSearchTOS = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(corpKeyNoList, needCompanyKey);
                            Map<String, String> keyNoEnNameMap = new HashMap<>();
                            if (CollectionUtils.isNotEmpty(advanceSearchTOS)) {
                                for (AdvanceSearchTO advanceSearchTO : advanceSearchTOS) {
                                    keyNoEnNameMap.put(advanceSearchTO.getKeyNo(), advanceSearchTO.getNameEn());
                                }
                                setEnglishName4PartnerByInterface(corpBasicDetailTO.getPubPartnerList(), keyNoEnNameMap);
                                setEnglishName4PartnerByInterface(corpBasicDetailTO.getPartnerList(), keyNoEnNameMap);
                            }

                        }*/
                        // added for v2.0.2 chenbl KNZT-5310
                        populateInfoByAdvanceSearch(corpBasicDetailTO.getPubPartnerList(), corpBasicDetailTO.getPartnerList(), needCompanyKey);

                        JSONArray employeeList = jsonData.getJSONArray("EmployeeList");
                        if (employeeList != null && !employeeList.isEmpty()) {
                            List<CorpEmployeeTO> employeeToList = employeeList.toJavaList(CorpEmployeeTO.class);
                            // added for v1.5.5 KNZT-2537 增加国际地区
                            corpBasicDetailTO.setEmployeeList(employeeToList);
                            populateEmployee(employeeToList);
//                            CorpEmployeeResult employeesResult = ECILocalInterface.getEmployeeList(jsonData.getString("KeyNo"), "Employees", "1", "1");
//                            if (employeesResult != null && employeesResult.getPaging() != null) {
//                                corpBasicDetailTO.setEmployeeTotalRecords(employeesResult.getPaging().getTotalRecords());
//                            }
                            corpBasicDetailTO.setEmployeeTotalRecords(jsonData.getIntValue("EmployeeCount"));
                        }

                        JSONArray pubEmployeeList = jsonData.getJSONArray("PubEmployeeList");
                        if (pubEmployeeList != null && !pubEmployeeList.isEmpty()) {
                            List<CorpEmployeeTO> pubEmployeeToList = pubEmployeeList.toJavaList(CorpEmployeeTO.class);
                            corpBasicDetailTO.setPubEmployeeList(pubEmployeeToList);
//                            CorpEmployeeResult ipoEmployeesResult = ECILocalInterface.getEmployeeList(jsonData.getString("KeyNo"), "IpoEmployees", "1", "1");
//                            if (ipoEmployeesResult != null && ipoEmployeesResult.getPaging() != null) {
//                                corpBasicDetailTO.setPubEmployeeTotalRecords(ipoEmployeesResult.getPaging().getTotalRecords());
//                            }
                            populateEmployee(pubEmployeeToList);
                            corpBasicDetailTO.setPubEmployeeTotalRecords(jsonData.getIntValue("PubEmployeeTotalCount"));
                        }

                        // added for v1.2.3 KNZT-1140
                        JSONObject revokeInfo = jsonData.getJSONObject("RevokeInfo");
                        if (revokeInfo != null) {
                            corpBasicDetailTO.setCancelDate(revokeInfo.getString("CancelDate"));
                            corpBasicDetailTO.setRevokeDate(revokeInfo.getString("RevokeDate"));
                        }
                        //added for v1.2.7 KNZT-1266 补充营业收入，净利润，总资产
                        if (jsonData.getJSONObject("FinancingInfo") != null) {
                            corpBasicDetailTO.setFinancingInfo(jsonData.getJSONObject("FinancingInfo").toJavaObject(CorpFinancingInfoTO.class));
                        }
                        //added for v1.2.8 KNZT-1409 补充经营地址信息
                        corpBasicDetailTO.setAnnualAddress(jsonData.getString("AnnualAddress"));
                        // added for v1.5.3 KNZT-2330 参考ACRA报告， 地址变更的时间 Date of Address ，当前注册地址和经营地址，不能区分时间先后
                        // updated for v1.7.1 KNZT-3079
                        /* JSONObject annualReportAddressInfo = jsonData.getJSONObject("LatestAnnualReportAddrInfo");
                        if (annualReportAddressInfo != null && StringUtils.isNotBlank(annualReportAddressInfo.getString("Year"))) {
                            corpBasicDetailTO.setAnnualAddressYear(annualReportAddressInfo.getString("Year"));
                        } */
                        // added for v1.7.1 KNZT-3079
                        JSONObject latestContactInAR = jsonData.getJSONObject("LatestContactInAR");
                        if (latestContactInAR != null) {
                            // updated for v1.7.3 KNZT-3275
                            corpBasicDetailTO.setAnnualAddress(resolveAnnualInfo(latestContactInAR.getString("Address")));
                            corpBasicDetailTO.setAnnualAddressYear(resolveAnnualInfo(latestContactInAR.getString("Year")));
                            corpBasicDetailTO.setAnnualTel(resolveAnnualInfo(latestContactInAR.getString("Tel")));
                        }
                        corpBasicDetailTO.setAnnualAddressPostalCode(jsonData.getString("AnnualAddressPostalCode")); // added for v2.0.7 chenbl KNZT-2102
                        // added for v1.3.1 KNZT-1597 补充分支机构标识
                        corpBasicDetailTO.setIsBranch(jsonData.getIntValue("IsBranch"));
                        // added for v1.3.6 KNZT-1824
                        corpBasicDetailTO.setScope(jsonData.getString("Scope"));
                        // added for v1.7.1 KNZT-3079
                        corpBasicDetailTO.setScopeEn(translaterService.getEnglishText(corpBasicDetailTO.getScope(), "", corpBasicDetailTO.getCompanyName(), SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME));
                        corpBasicDetailTO.setCompanyDesc(jsonData.getString("CompanyDesc"));
                        corpBasicDetailTO.setEconKind(jsonData.getString("EconKind"));
                        corpBasicDetailTO.setTPQYTPCount(jsonData.getIntValue("TPQYTPCount"));
                        corpBasicDetailTO.setTPGQCTCount(jsonData.getIntValue("TPGQCTCount"));
                        corpBasicDetailTO.setSameTelAddressList(Optional.ofNullable(jsonData.getJSONArray("SameTelAddressList"))
                                .orElse(new JSONArray())
                                .toJavaList(JSONObject.class)
                                .stream().map(jsonObject -> {
                                    SameTelAddressTO sameTelAddressTO = new SameTelAddressTO();
                                    sameTelAddressTO.setValue(jsonObject.getString("Value"));
                                    sameTelAddressTO.setCount(jsonObject.getString("Count"));
                                    // added for v1.5.1 KNZT-2274【新增】【国际版】共同电话，邮箱，地址企业，弹框展示企业列表清单（后端）
                                    sameTelAddressTO.setKey(jsonObject.getString("Key"));
                                    return sameTelAddressTO;
                                }).collect(Collectors.toList()));
                        corpBasicDetailTO.setSameEmailList(Optional.ofNullable(jsonData.getJSONArray("SameEmailList"))
                                .orElse(new JSONArray())
                                .toJavaList(JSONObject.class)
                                .stream().map(jsonObject -> {
                                    SameEmailTO sameEmailTO = new SameEmailTO();
                                    sameEmailTO.setValue(jsonObject.getString("Value"));
                                    sameEmailTO.setCount(jsonObject.getString("Count"));
                                    // added for v1.5.1 KNZT-2274【新增】【国际版】共同电话，邮箱，地址企业，弹框展示企业列表清单（后端）
                                    sameEmailTO.setKey(jsonObject.getString("Key"));
                                    return sameEmailTO;
                                }).collect(Collectors.toList()));
                        // added for v1.5.1 KNZT-2275
                        corpBasicDetailTO.setTelAgent(jsonData.getString("TelAgent"));
                        // added for v1.5.5 KNZT-2565
                        corpBasicDetailTO.setEntTypeFlag(jsonData.getString("EntTypeFlag"));
                        // added for v2.0.6 fengsw KNZT-5582 英文名来源处理
                        if (StringUtils.isNotBlank(corpBasicDetailTO.getCompanyName())) {
                            String sourceType = mappingCorpEnNameSourceType(keyNo, corpBasicDetailTO.getCompanyName(), needCompanyKey);
                            corpBasicDetailTO.setEnNameSourceType(sourceType);
                        }
                        // added for v2.0.6 fengsw KNZT-5381 返回企业qccCode
                        corpBasicDetailTO.setQccCode(jsonData.getString("QccCode"));
                        // added for v2.1.4 fengsw KNZT-6360 leiCode
                        QccEntDetailInfoTO entDetailInfoTO = CompanyDetailsInterface.getQccDetailByEntCode(keyNo, QccEntCodeTypeEnum.KEY_NO.getCode());
                        corpBasicDetailTO.setLeiCode(Objects.nonNull(entDetailInfoTO) ? entDetailInfoTO.getLeiCode() : null);
                        // 地址相同时，邮编互相补偿 added for v2.0.7 chenbl KNZT-2102
                        if (StringUtils.equals(corpBasicDetailTO.getRegisteredAddress(), corpBasicDetailTO.getAnnualAddress())) {
                            corpBasicDetailTO.setAddressPostalCode(StringUtils.getNotBlankStr(corpBasicDetailTO.getAddressPostalCode(), corpBasicDetailTO.getAnnualAddressPostalCode()));
                            corpBasicDetailTO.setAnnualAddressPostalCode(StringUtils.getNotBlankStr(corpBasicDetailTO.getAnnualAddressPostalCode(), corpBasicDetailTO.getAddressPostalCode()));
                        }
                        addCurrName2FormerName(corpBasicDetailTO); // added for v2.1.5 chenbl KNZT-6441
                        return corpBasicDetailTO;
                    }
                    return null;
                } else {
//                    throw new MessageException("msg:" + jsonObj.getString("msg"));
                    logger.error("/EnterpriseInfoOversea/Verify:" + resp);
                    if("openapi".equals(Global.getConfig("application.type"))){
                        throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
                    }
                }
            } else if ("openapi".equals(Global.getConfig("application.type"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        } catch (Exception e) {
            logger.error("/EnterpriseInfoOversea/Verify", e);
            if ("openapi".equals(Global.getConfig("application.type"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        return null;

    }

    // added for v2.1.5 chenbl KNZT-6442
    private static void addCurrName2FormerName(CorpBasicDetail4UnitTO corpBasicDetailTO) {
        if (corpBasicDetailTO == null) {
            return;
        }
        List<FormerNameTO> formerName = corpBasicDetailTO.getFormerName();
        if (CollectionUtils.isEmpty(formerName)) {
            formerName = new ArrayList<>();
            corpBasicDetailTO.setFormerName(formerName);
        }
        String currStartDate = null;
        if (CollectionUtils.isNotEmpty(formerName)) {
            // 取历史信息最后一条变更日期
            currStartDate = formerName.get(0).getChangeDate();
        } else {
            // 没有历史信息的，取成立日期
            currStartDate = corpBasicDetailTO.getEstablishmentDate();
        }
        if (StringUtils.isNotBlank(currStartDate)) {
            FormerNameTO currTO = new FormerNameTO();
            currTO.setStartDate(currStartDate);
            currTO.setName(corpBasicDetailTO.getCompanyNameInLocalLanguage());
            currTO.setEnName(corpBasicDetailTO.getCompanyName());
            formerName.add(0, currTO);
        }
    }

    /**
     * added for v1.8.6 KNZT-3822 查询大陆企业基本信息
     * @param searchKey
     * @return
     */
    public static CorpBasicDetail4UnitTO getCorpBasicInfo4LEI(String searchKey) {
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/EnterpriseInfoOversea/Verify";//云聚编号9009
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        try {
            String resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                        ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONObject("Result") != null && jsonObj.getJSONObject("Result").getJSONObject("Data") != null) {
                        JSONObject jsonData = jsonObj.getJSONObject("Result").getJSONObject("Data");
                        CorpBasicDetail4UnitTO corpBasicDetailTO = new CorpBasicDetail4UnitTO();
                        String keyNo = jsonData.getString("KeyNo");
                        String creditCode = jsonData.getString("CreditCode");
                        String no = jsonData.getString("No");
                        String regNo = jsonData.getString("RegNo");
                        if(StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX)) {
                            corpBasicDetailTO.setCreditCode(StringUtils.getNotBlankStr(regNo,creditCode, no));
                        } else {
                            corpBasicDetailTO.setCreditCode(creditCode);
                        }
                        String shortName = jsonData.getString("ShortName");
                        String imageUrl = jsonData.getString("ImageUrl");
                        corpBasicDetailTO.setImageUrl(getLogoImageUrl(imageUrl, keyNo, shortName));//added for v1.0.6 KNZT-323

                        corpBasicDetailTO.setCompanyName(jsonData.getString("EnglishName"));
                        corpBasicDetailTO.setCompanyNameInLocalLanguage(jsonData.getString("Name"));
                        corpBasicDetailTO.setRegistrationStatusCn(jsonData.getString("ShortStatus"));
                        String organizationalCodeStr = jsonData.getString("OrganizationalCode");
                        if (StringUtils.isNotBlank(organizationalCodeStr)) {
                            String[] codeArray = organizationalCodeStr.split(",");
                            if (codeArray.length > 0) {
                                String localCompanyName = corpBasicDetailTO.getCompanyNameInLocalLanguage();
                                if (StringUtils.isBlank(corpBasicDetailTO.getCompanyName())
                                        && StringUtils.isNotBlank(localCompanyName)
                                        && CompanyTypeEnum.COMPANY_TYPE15.getCompanyTypeCode().equals(codeArray[0])) {
                                    String englishText = translaterService.getEnglishText(localCompanyName, keyNo, localCompanyName, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
                                    corpBasicDetailTO.setCompanyName(englishText);
                                }
                            }
                        }
                        corpBasicDetailTO.setEstablishmentDate(jsonData.getString("StartDate"));
                        corpBasicDetailTO.setRegisteredAddress(jsonData.getString("Address"));
                        if(StringUtils.length(corpBasicDetailTO.getRegisteredAddress()) <= 1) {
                            corpBasicDetailTO.setRegisteredAddress(null);
                        }
                        String enTxt = translaterService.getEnglishText(corpBasicDetailTO.getRegisteredAddress(), keyNo, corpBasicDetailTO.getCompanyNameInLocalLanguage(), SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                        if(StringUtils.isNotBlank(enTxt) && !StringUtils.equals(enTxt, corpBasicDetailTO.getRegisteredAddress())) {
                            corpBasicDetailTO.setRegisteredAddressEn(enTxt);
                        }
                        return corpBasicDetailTO;
                    }
                    return null;
                } else {
                    logger.error("/EnterpriseInfoOversea/Verify:" + resp);
                }
            }
        } catch (Exception e) {
            logger.error("/EnterpriseInfoOversea/Verify", e);
        }
        return null;
    }

    public static CorpBasicDetail4UnitTO getCorpBasicInfo4ExternalAPI(String searchKey, String includeExtraFields) {
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/EnterpriseInfoOversea/Verify";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        if (StringUtils.isNotBlank(includeExtraFields)) {
            paramMap.put("includeExtraFields", includeExtraFields);
        }
        try {
            String resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                        ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONObject("Result") != null && jsonObj.getJSONObject("Result").getJSONObject("Data") != null) {
                        JSONObject jsonData = jsonObj.getJSONObject("Result").getJSONObject("Data");
                        CorpBasicDetail4UnitTO corpBasicDetailTO = new CorpBasicDetail4UnitTO();
                        corpBasicDetailTO.setRegisteredCapital(jsonData.getString("RegistCapi"));
                        corpBasicDetailTO.setEmployeeTotalRecords(jsonData.getIntValue("EmployeeCount"));
                        corpBasicDetailTO.setPubEmployeeTotalRecords(jsonData.getIntValue("PubEmployeeTotalCount"));
                        corpBasicDetailTO.setPubPartnerTotalRecords(jsonData.getIntValue("PubPartnerTotalCount"));
                        corpBasicDetailTO.setPartnerTotalRecords(jsonData.getIntValue("PartnerTotalCount"));
                        corpBasicDetailTO.setCompanyNameInLocalLanguage(jsonData.getString("Name"));
                        String organizationalCodeStr = jsonData.getString("OrganizationalCode");
                        String keyNo = jsonData.getString("KeyNo");
                        corpBasicDetailTO.setCompanyName(jsonData.getString("EnglishName"));
                        String creditCode = jsonData.getString("CreditCode");
                        String no = jsonData.getString("No");
                        String regNo = jsonData.getString("RegNo");
                        if(StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX)) {
                            corpBasicDetailTO.setCreditCode(StringUtils.getNotBlankStr(regNo,creditCode, no));
                        } else {
                            corpBasicDetailTO.setCreditCode(creditCode);
                        }
                        if (StringUtils.isNotBlank(organizationalCodeStr)) {
                            String[] codeArray = organizationalCodeStr.split(",");
                            if (codeArray.length > 0) {
                                String companyTypeCnByCode = CompanyTypeEnum.getCompanyTypeCnByCode(codeArray[0]);
                                corpBasicDetailTO.setCompanyType(StringUtils.isNotBlank(companyTypeCnByCode) ? companyTypeCnByCode : jsonData.getString("EconKind"));
                                corpBasicDetailTO.setCompanyTypeEn(CompanyTypeEnum.getCompanyTypeEnByCompanyTypeCn(corpBasicDetailTO.getCompanyType()));
                                String localCompanyName = corpBasicDetailTO.getCompanyNameInLocalLanguage();
                                if (StringUtils.isBlank(corpBasicDetailTO.getCompanyName())
                                        && StringUtils.isNotBlank(localCompanyName)
                                        && CompanyTypeEnum.COMPANY_TYPE15.getCompanyTypeCode().equals(codeArray[0])) {
                                    String englishText = translaterService.getEnglishText(localCompanyName, keyNo, localCompanyName, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
                                    corpBasicDetailTO.setCompanyName(englishText);
                                }
                            }
                        }
                        return corpBasicDetailTO;
                    }
                    return null;
                } else {
                    logger.error("/EnterpriseInfoOversea/Verify:" + resp);
                    if ("openapi".equals(Global.getConfig("application.type"))) {
                        throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
                    }
                }
            } else if ("openapi".equals(Global.getConfig("application.type"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        } catch (Exception e) {
            logger.error("/EnterpriseInfoOversea/Verify", e);
            if ("openapi".equals(Global.getConfig("application.type"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        return null;
    }
    
    
    /**
     * added for v1.8.7 KNZT-3978
     * 获取最新公示股东和主要人员
     * @param searchKey
     * @return
     */
    public static CorpBasicDetail4UnitTO getCorpInfo4KeyPersonsAndPubEmployee(String searchKey, String includeExtraFields) {
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/EnterpriseInfoOversea/Verify";//云聚编号9009
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        paramMap.put("includeExtraFields", includeExtraFields);
        try {
            String resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                        ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONObject("Result") != null && jsonObj.getJSONObject("Result").getJSONObject("Data") != null) {
                        JSONObject jsonData = jsonObj.getJSONObject("Result").getJSONObject("Data");
                        CorpBasicDetail4UnitTO corpBasicDetailTO = new CorpBasicDetail4UnitTO();
                        JSONArray pubPartnerList = jsonData.getJSONArray("PubPartnerList");//最新公示股东
                        if (pubPartnerList != null && !pubPartnerList.isEmpty()) {
                            corpBasicDetailTO.setPubPartnerTotalRecords(jsonData.getIntValue("PubPartnerTotalCount"));
                        }
                        JSONArray pubEmployeeList = jsonData.getJSONArray("PubEmployeeList");//最新公示主要人员
                        if (pubEmployeeList != null && !pubEmployeeList.isEmpty()) {
                            corpBasicDetailTO.setPubEmployeeTotalRecords(jsonData.getIntValue("PubEmployeeTotalCount"));
                        }
                        return corpBasicDetailTO;
                    }
                    return null;
                } else {
                    logger.error("/EnterpriseInfoOversea/Verify:" + resp);
                }
            }
        } catch (Exception e) {
            logger.error("/EnterpriseInfoOversea/Verify", e);
        }
        return null;
    }
    // updated for v1.7.3 KNZT-3275
    private static String resolveAnnualInfo(String annualInfo) {
        if (StringUtils.contains(annualInfo, "不公示")) {
            return "Not disclosed";
        }
        return annualInfo;
    }

    /**
     * added for v1.6.0 KNZT-2716
     * 根据keyNo映射logo对应的颜色
     * @param keyNo
     * @return
     */
    public static String getLogoColor(String keyNo) {
        String index = "0";
        if (StringUtils.isNotBlank(keyNo)) {
            // 将 keyNo 字符串转换成字符数组，并查找第一个字符小于等于 'd' 的字符
            char[] chars = keyNo.toCharArray();
            for (char c : chars) {
                if (c <= 'd') {
                    index = Character.toString(c);
                    break;
                }
            }
        }
        return COLOR_MAP.getOrDefault(index, "");
    }

    /**
     *
     * added for v1.6.0 KNZT-2716
     * @param imageUrl
     * @param keyNo
     * @param shortName
     * @return
     */
    public static String getLogoImageUrl(String imageUrl, String keyNo, String shortName) {
        if (StringUtils.isBlank(imageUrl) || StringUtils.isBlank(keyNo) || StringUtils.isBlank(shortName)) {
            return imageUrl;
        }
        String autoStr = "/auto/";
        boolean auto = imageUrl.contains(autoStr);
        if (!auto) {
            return imageUrl;
        }
        String urlPure = StringUtils.removeParamsFromUrl(imageUrl);
        Map<String, String> params = StringUtils.getParamsFromUrl(imageUrl);
        params.putIfAbsent("shortName", shortName);
        params.putIfAbsent("color", getLogoColor(keyNo));
        return StringUtils.addParamsToUrl(urlPure, params);
    }

    /**
     * added for v1.5.5 KNZT-2537
     * 处理主要人员的字段
     * @param employeeTOList
     * @return
     */
    public static void populateEmployee(List<CorpEmployeeTO> employeeTOList) {
        if (CollectionUtils.isEmpty(employeeTOList)) {
            return;
        }
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
        for (CorpEmployeeTO employeeTO : employeeTOList) {
            // 地区
            // updated for v1.9.1 KNZT-4101 映射调整
            GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(employeeTO.getArea());
            employeeTO.setAreaEn(Objects.nonNull(areaEnum) ? areaEnum.getShortNameEn() : null);
            transWrapper.buildAndAddEntry(employeeTO, CorpEmployeeTO::setArea, CorpEmployeeTO::setAreaEn, CorpEmployeeTO::getArea, CorpEmployeeTO::getAreaEn);
            employeeTO.setAreaRiskLevel(GlobalAreaEnum.judgeRiskLevel(areaEnum));
            if (StringUtils.isBlank(employeeTO.getArea())) {
                transWrapper.buildAndAddEntry(employeeTO, CorpEmployeeTO::setName, CorpEmployeeTO::setEnglishName, CorpEmployeeTO::getName, CorpEmployeeTO::getEnglishName).withEntity(CorpEmployeeTO::getKeyNo, true);
            }
        }
        commonDataService.enPostProcessor(transWrapper);
    }

    /**
     * added for v1.5.2 KNZT-2319【新增】【国际版】补充大陆企业的电话，邮箱的来源数据信息（后端）
     * 映射一下邮箱和电话的来源
     * 
     * @param contactInfo
     */
    private static void mappingTelEmailSourceTypeToEn(ContactInfoTO contactInfo) {
        if(Objects.nonNull(contactInfo)){
            if (CollectionUtils.isNotEmpty(contactInfo.getMoreTelList())) {
                for (TelEmailTO telEmailTO : contactInfo.getMoreTelList()) {
                    if (StringUtils.isNotBlank(telEmailTO.getSourceFrom())) {
                        telEmailTO.setSourceFromEn(SourceFromTypeEnum.getTelSourceFromTypeEnByCode(telEmailTO.getSourceFrom()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(contactInfo.getMoreEmailList())) {
                for (TelEmailTO telEmailTO : contactInfo.getMoreEmailList()) {
                    if (StringUtils.isNotBlank(telEmailTO.getSourceFrom())) {
                        telEmailTO.setSourceFromEn(SourceFromTypeEnum.getEmailSourceFromTypeEnByCode(telEmailTO.getSourceFrom()));
                    }
                }
            }
            if (StringUtils.isNotBlank(contactInfo.getTelSource()) && StringUtils.isBlank(contactInfo.getTelSourceEn())) {
                contactInfo.setTelSourceEn(SourceFromTypeEnum.getTelSourceFromTypeEnByCode(contactInfo.getTelSource()));
            }
            if (StringUtils.isNotBlank(contactInfo.getEmailSource()) && StringUtils.isBlank(contactInfo.getEmailSourceEn())) {
                contactInfo.setEmailSourceEn(SourceFromTypeEnum.getEmailSourceFromTypeEnByCode(contactInfo.getEmailSource()));
            }
        }
    }

    // added for v2.0.2 chenbl KNZT-5310
    public static void populateInfoByAdvanceSearch(List<CorpPartnerTO> pubPartnerList, List<CorpPartnerTO> partnerList, boolean needCompanyKey) {
        // 页面股东展示逻辑：最新公示 > 工商公示
        List<CorpPartnerTO> partnerList4Show = CollectionUtils.isNotEmpty(pubPartnerList) ? pubPartnerList : partnerList;
        if (CollectionUtils.isNotEmpty(partnerList4Show)) {
            Set<String> corpKeyNos = partnerList4Show.stream().map(CorpPartnerTO::getKeyNo)
                    .filter(StringUtils::isNotBlank)
                    .filter(keyNo -> !keyNo.startsWith(Constants.CorpTypePrefix.PERSON_PREFIX))
                    .collect(Collectors.toSet());
            Map<String, AdvanceSearchTO> keyNo2AdvanceSearchMap = new HashMap<>();
            // updated for v2.1.5 chenbl KNZT-6053
            if (CollectionUtils.isNotEmpty(corpKeyNos)) {
                List<AdvanceSearchTO> advanceSearchTOS = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(new ArrayList<>(corpKeyNos), needCompanyKey);
                if (CollectionUtils.isNotEmpty(advanceSearchTOS)) {
                    keyNo2AdvanceSearchMap.putAll(advanceSearchTOS
                            .stream().filter(to -> StringUtils.isNotBlank(to.getKeyNo()))
                            .collect(Collectors.toMap(AdvanceSearchTO::getKeyNo, Function.identity(), (k1, k2) -> k1)));
                }
            }
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            for (CorpPartnerTO p : partnerList4Show) {
                populatePartnerAreaInfo(p);
                if (StringUtils.isBlank(p.getKeyNo()) || StringUtils.startsWith(p.getKeyNo(), Constants.CorpTypePrefix.PERSON_PREFIX)) {
                    if (StringUtils.isNotBlank(p.getStockNameEn())) {
                        p.setEnglishName(p.getStockNameEn());
                    }
                } else {
                    AdvanceSearchTO advanceSearchTO = keyNo2AdvanceSearchMap.get(p.getKeyNo());
                    if (advanceSearchTO != null) {
                        p.setRegNo(advanceSearchTO.getRegNo());
                        p.setEnglishName(advanceSearchTO.getNameEn());
                        if (StringUtils.isBlank(p.getArea())) {
                            GlobalAreaEnum globalAreaEnum = GlobalAreaTranslationUtil.getGlobalArea(advanceSearchTO.getJurisdiction());
                            if (globalAreaEnum != null) {
                                p.setArea(globalAreaEnum.getShortNameCn());
                                p.setAreaEn(globalAreaEnum.getShortNameEn());
                                p.setAreaCode(globalAreaEnum.getNameCode());
                                p.setAreaRiskLevel(GlobalAreaEnum.judgeRiskLevel(globalAreaEnum));
                            }
                        }
                    }
                }
                // added for v2.0.5 chenbl KNZT-4527
                p.setGlobalStockType(StockTypeTranslationUtil.getGlobalStockType(p.getKeyNo(), p.getStockTypeCode(), p.getStockType()));
                p.setJurisdiction(p.getArea());
                p.setJurisdictionEn(p.getAreaEn());
                if (StringUtils.isBlank(p.getArea())) {
                    transWrapper.buildAndAddEntry(p, CorpPartnerTO::setStockName, CorpPartnerTO::setEnglishName, CorpPartnerTO::getStockName, CorpPartnerTO::getEnglishName).withEntity(CorpPartnerTO::getKeyNo, true);
                }
            }
            commonDataService.enPostProcessor(transWrapper);
        }
    }

    /**
     * added for v1.3.9 KNZT-2132
     * 注入股东区域信息
     *
     * @param p 股东
     * @return
     */
    public static void populatePartnerAreaInfo(CorpPartnerTO p) {
        if (StringUtils.isNotBlank(p.getArea())) {
            // updated for v1.9.1 KNZT-4101 映射调整
            GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(p.getArea());
            if (areaEnum != null) {
                p.setAreaCode(areaEnum.getNameCode());
                p.setAreaEn(areaEnum.getShortNameEn());
                p.setAreaRiskLevel(GlobalAreaEnum.judgeRiskLevel(areaEnum));
            } else {
                // 映射不到的将中文置空（前端无英文时会显示中文）
                p.setArea(null);
            }
        }
    }

    public static void removeSensitiveInfo(List<CorpPartnerTO> partners, boolean isBank) {
        if (CollectionUtils.isEmpty(partners)) {
            return;
        }
        if (!isBank) {
            // 非银行客户隐藏股东国籍信息
            partners.forEach(p -> {
                p.setArea(null);
                p.setAreaCode(null);
                p.setAreaEn(null);
                p.setJurisdiction(null);
                p.setJurisdictionEn(null);
            });
        }
    }

    public static String translateRegistrationAuthority(String str) {
        if (StringUtils.isBlank(str) || str.contains(",")  || str.contains("，")
                || str.contains("、")  || str.contains("/")  || str.contains("\\")) {
            return "";
        }
        return StateOrgansTranslationUtil.getStateOrgansEnByCn(str); // updated for v1.8.7 KNZT-3940
    }

    private static LegalRepresentativeInfoTO convert2LegalRepresentativeInfoTO(JSONObject jsonData) {
        LegalRepresentativeInfoTO legalRepresentativeInfoTO = new LegalRepresentativeInfoTO();
        String operType = jsonData.getString("OperType");

        List<LegalRepresentativeInfoTO.Item> items = new ArrayList<>();
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
        JSONArray multipleOperJa = jsonData.getJSONArray("MultipleOper");
        if (CollectionUtils.isNotEmpty(multipleOperJa)) {
            List<JSONObject> multipleOperList = multipleOperJa.toJavaList(JSONObject.class);
            Set<String> corpKeyNos = multipleOperList.stream().map(multipleOperItemJo -> multipleOperItemJo.getString("KeyNo"))
                    .filter(StringUtils::isNotBlank)
                    .filter(keyNo -> !keyNo.startsWith(Constants.CorpTypePrefix.PERSON_PREFIX))
                    .collect(Collectors.toSet());
            Map<String, String> keyNo2EnMap = GlobalCompanyDetailsInterface.batchGetCorpKeyNo2EnMap(corpKeyNos, false);
            for (JSONObject multipleOperItemJo : multipleOperList) {
                LegalRepresentativeInfoTO.Item item = new LegalRepresentativeInfoTO.Item();
                String keyNo = multipleOperItemJo.getString("KeyNo");
                String name = multipleOperItemJo.getString("Name");
                String nameEn = keyNo2EnMap.get(keyNo);
                item.setKeyNo(keyNo);
                item.setName(name);
                item.setNameEn(nameEn);
                if (OperTypeEnum.EXECUTING_MANAGING_PARTNER.getOperTypeCode().equals(operType)) {
                    transWrapper.buildAndAddEntry(item, LegalRepresentativeInfoTO.Item::setName, LegalRepresentativeInfoTO.Item::setNameEn, LegalRepresentativeInfoTO.Item::getName, LegalRepresentativeInfoTO.Item::getNameEn).withEntity(LegalRepresentativeInfoTO.Item::getKeyNo, true);
                } else {
                    transWrapper.buildAndAddEntry(item, LegalRepresentativeInfoTO.Item::setName, LegalRepresentativeInfoTO.Item::setNameEn, LegalRepresentativeInfoTO.Item::getName, LegalRepresentativeInfoTO.Item::getNameEn).withEntity(LegalRepresentativeInfoTO.Item::getKeyNo, TransWrapper.ENTRY_TYPE_PERSON);
                }

                JSONArray assignorListJa = multipleOperItemJo.getJSONArray("AssignorList");
                if (CollectionUtils.isNotEmpty(assignorListJa)) {
                    List<LegalRepresentativeInfoTO.Assignor> assignorList = new ArrayList<>();
                    List<JSONObject> assignorJoList = assignorListJa.toJavaList(JSONObject.class);
                    for (JSONObject assignorJo : assignorJoList) {
                        LegalRepresentativeInfoTO.Assignor assignor = new LegalRepresentativeInfoTO.Assignor();
                        String assignorKeyNo = assignorJo.getString("KeyNo");
                        String assignorName = assignorJo.getString("Name");
                        assignor.setKeyNo(assignorKeyNo);
                        assignor.setName(assignorName);
                        transWrapper.buildAndAddEntry(assignor, LegalRepresentativeInfoTO.Assignor::setName, LegalRepresentativeInfoTO.Assignor::setNameEn, LegalRepresentativeInfoTO.Assignor::getName, LegalRepresentativeInfoTO.Assignor::getNameEn).withEntity(LegalRepresentativeInfoTO.Assignor::getKeyNo, true);
                        assignorList.add(assignor);
                    }
                    item.setAssignorList(assignorList);
                }
                items.add(item);
            }
        } else {
            JSONArray operNameJa = jsonData.getJSONArray("OperName");
            if (CollectionUtils.isNotEmpty(operNameJa)) {
                List<String> operNameList = operNameJa.toJavaList(String.class);
                for (String operName : operNameList) {
                    LegalRepresentativeInfoTO.Item item = new LegalRepresentativeInfoTO.Item();
                    item.setName(operName);
                    if (!PinyinUtils.notConvert2Pinyin(operName) && !StringUtils.isCorpNameCn(operName)) {
                        item.setNameEn(PinyinUtils.chineseNameToPinyin(operName));
                    }
                    items.add(item);
                }
            }
        }
        legalRepresentativeInfoTO.setItems(items);
        commonDataService.enPostProcessor(transWrapper);
        return legalRepresentativeInfoTO;
    }

    /**
     * added for v1.0.6 KNZT-322
     * updated for v1.6.5 KNZT-2810 方法重载，入参加入needCompanyKey 为false则代表使用默认key, true代表使用主账号绑定的key
     * @param companyName
     * @return
     * @throws MessageException
     */
    public static StockResult4UnitTO getCorpUBO4Unit(String companyName) throws MessageException {
        return getCorpUBO4Unit(companyName, true);
    }

    /**
     * added for v1.0.6 KNZT-322
     * @param companyName
     * @return
     * @throws MessageException
     */
    public static StockResult4UnitTO getCorpUBO4Unit(String companyName, boolean needCompanyKey) throws MessageException {
        try {
            String param = "companyName=" + companyName + "&percent=" + 25;
            String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listStockWithPercentLimit", "1", param, needCompanyKey);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("status"))) {
                    if (jsonObj.getJSONObject("result") != null) {
                        StockResult4UnitTO corpUBO4Unit = jsonObj.getJSONObject("result").toJavaObject(StockResult4UnitTO.class);
                        handleCorpUBO4UnitInfo(corpUBO4Unit);//updated for v2.0.5 fengsw KNZT-5516
                        return corpUBO4Unit;
                    }
                    return null;
                } else {
//                    throw new MessageException("msg:" + jsonObj.getString("msg"));
                    if (!ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("status"))) {
                        logger.error("/StockList4Report/GetInfo " + resp);
                    }
                    if (!ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("status"))
                            && "openapi".equals(Global.getConfig("application.type"))) {
                        throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
                    }
                }
            } else if ("openapi".equals(Global.getConfig("application.type"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
            // 查询失败 返回结果为空 有异常
        } catch (Exception e) {
            logger.error("/StockList4Report/GetInfo ", e);
            if ("openapi".equals(Global.getConfig("application.type"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        return null;

    }


    /**
     * added for v2.0.6 fengsw KNZT-5582 英文名来源处理
     *
     * @param keyNo
     * @param companyName
     * @param needCompanyKey
     * @return
     */
    public static String mappingCorpEnNameSourceType(String keyNo, String companyName, boolean needCompanyKey) {
        if (StringUtils.isNotBlank(companyName)) {
            CorpEnNameResult corpEnNameResult = listCorpEnglishNameByKeyNo(keyNo, needCompanyKey);
            if (Objects.nonNull(corpEnNameResult) && CollectionUtils.isNotEmpty(corpEnNameResult.getDetails())) {
                List<CorpOriginalEnName> details = corpEnNameResult.getDetails();
                CorpOriginalEnName corpOriginalEnName = details.stream().filter(k -> StringUtils.equals(companyName, k.getEnglishName())).findFirst().orElse(null);
                if (corpOriginalEnName != null && StringUtils.isNotBlank(corpOriginalEnName.getSource())) {
                    return sysConfigCacheService.getCompanyEnglishSource(corpOriginalEnName.getSource());
                }
            }
        }
        return null;
    }

    /**
     * added for v1.8.7 KNZT-3988
     * 将B端接口返回的UBO数据，根据前端逻辑，渲染成List
     * <strong>暂未接入到提供给前端的接口，后续可优化收口逻辑到后端</strong>
     *
     * @param currentUBO
     * @return List<UBOListVO>
     */
    public static List<UboVO> transfer2UBOListVO(StockResult4UnitTO currentUBO) {
        List<UboVO> uboListVOList = Lists.newArrayList();
        if (Objects.isNull(currentUBO)) {
            return uboListVOList;
        }

        if (StringUtils.isNotBlank(currentUBO.getRemark())) {
            if (CollectionUtils.isNotEmpty(currentUBO.getHehuoPersonList()) || CollectionUtils.isNotEmpty(currentUBO.getOtherBeneList())) {
                List<OtherBeneTO> beneList = CollectionUtils.isNotEmpty(currentUBO.getHehuoPersonList()) ?
                        currentUBO.getHehuoPersonList() : currentUBO.getOtherBeneList();
                for (OtherBeneTO bene : beneList) {
                    UboVO beneUBO = new UboVO();
                    beneUBO.setKeyNo(bene.getKeyNo());
                    beneUBO.setBenefitType(bene.getBenifitType());
                    beneUBO.setPosition(bene.getRole());
                    beneUBO.setPositionEn(bene.getRoleEn());
                    beneUBO.setName(bene.getName());
                    beneUBO.setNameEn(bene.getNameEn());
                    uboListVOList.add(beneUBO);
                }
                uboListVOList.get(0).setRemark(currentUBO.getRemark());
                uboListVOList.get(0).setRemarkEn(currentUBO.getRemarkEn());
            } else {
                UboVO operUBO = new UboVO();
                operUBO.setKeyNo(currentUBO.getKeyNo());
                operUBO.setBenefitType(currentUBO.getBenifitType());
                operUBO.setPosition(currentUBO.getPosition());
                operUBO.setPositionEn(currentUBO.getPositionEn());
                operUBO.setName(currentUBO.getOperName());
                operUBO.setNameEn(currentUBO.getOperNameEn());
                operUBO.setRemark(currentUBO.getRemark());
                operUBO.setRemarkEn(currentUBO.getRemarkEn());
                uboListVOList.add(operUBO);

                if (CollectionUtils.isNotEmpty(currentUBO.getExecutives())) {
                    for (Executive executive : currentUBO.getExecutives()) {
                        UboVO executiveUBO = new UboVO();
                        executiveUBO.setKeyNo(executive.getKeyNo());
                        executiveUBO.setBenefitType(executive.getBenifitType());
                        executiveUBO.setPosition(executive.getPosition());
                        executiveUBO.setPositionEn(executive.getPositionEn());
                        executiveUBO.setName(executive.getName());
                        executiveUBO.setNameEn(executive.getNameEn());
                        uboListVOList.add(executiveUBO);
                    }
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(currentUBO.getBreakThroughListReport())) {
                for (StockDetail4UnitTO breakThrough : currentUBO.getBreakThroughListReport()) {
                    UboVO breakThroughUBO = new UboVO();
                    breakThroughUBO.setKeyNo(breakThrough.getKeyNo());
                    breakThroughUBO.setBenefitType(breakThrough.getBenifitType());
                    breakThroughUBO.setPosition(breakThrough.getRole());
                    breakThroughUBO.setPositionEn(breakThrough.getRoleEn());
                    breakThroughUBO.setName(breakThrough.getName());
                    breakThroughUBO.setNameEn(breakThrough.getNameEn());
                    breakThroughUBO.setDetailInfoList(breakThrough.getDetailInfoList());
                    breakThroughUBO.setTotalStockPercent(breakThrough.getTotalStockPercent());
                    uboListVOList.add(breakThroughUBO);
                }
            }
        }

        return  uboListVOList;
    }


    /**
     * added for v1.7.8 KNZT-3359 【优化】【国际版】UBO链路中补充企业中文名
     * 处理ubo path中的中英文名称，格式为 英文+" "+中文
     * updated for v1.9.0 KNZT-4143
     * updated for v1.9.9 KNZT-4985 从UBO链路中解析中间控股公司 补充公司的管辖地、注册号信息
     * 
     * @param stockDetail4UnitTO
     * @param companyList
     */
    private static void handleUBOPathNameAndEnName(StockDetail4UnitTO stockDetail4UnitTO, Map<String, StockIntermediateCompanyTO> companyMap) {
        List<StockDetailTO> detailInfoList = stockDetail4UnitTO.getDetailInfoList();
        if (CollectionUtils.isNotEmpty(detailInfoList)) {
            for (StockDetailTO stockDetailTO : detailInfoList) {
                String path = stockDetailTO.getPath();
                if (StringUtils.isNotBlank(path) && path.contains(Constants.INVEST)) {
                    // UBO链路拼接过来的数据（成立日期、管辖地、注册号等信息） 需要去掉
                    List<String> needRemoveFromPathList = new ArrayList<>();
                    String[] arr = path.split(Constants.INVEST);
                    List<StockPathInfo> stockPathInfoList = new ArrayList<>();
                    StockPathInfo stockPathInfo = new StockPathInfo();
                    String uboPerson = arr[0].substring(0, arr[0].lastIndexOf("("));
                    String uboPersonNameEn = "";
                    stockPathInfo.setName(uboPerson);
                    if (!PinyinUtils.notConvert2Pinyin(uboPerson)) {
                        String perNameEn = PinyinUtils.chineseNameToPinyin(uboPerson);
                        if (StringUtils.startsWith(stockDetail4UnitTO.getKeyNo(), Constants.CorpTypePrefix.PERSON_PREFIX)) {
                            stockPathInfo.setNameEn(perNameEn);
                            uboPersonNameEn = perNameEn;
                        }
                    }
                    stockPathInfo.setPercent(arr[0].substring(arr[0].lastIndexOf("(") + 1, arr[0].lastIndexOf(")")));
                    // 链路开头设置keyNo信息，用于前端区分是人或企业
                    stockPathInfo.setKeyNo(stockDetail4UnitTO.getKeyNo());
                    stockPathInfoList.add(stockPathInfo);
                    for (int j = 1; j <= arr.length - 1; j++) {
                        StockPathInfo stockPathInfo1 = new StockPathInfo();
                        String corpName = arr[j];
                        String[] corpNames = corpName.split(Constants.CORP_NAME_REGEX);
                        if (corpNames.length >= 5) {
                            stockPathInfo1.setNameEn(corpNames[0]);
                            stockPathInfo1.setName(corpNames[1]);
                            // 解析开始日期赋值、地区、注册号 并赋值
                            // updated for v2.0.2 fengsw KNZT-5376【bug】中介控股公司注册号返回错误 (辽宁集源经贸有限公司) 剔除空值占位符
                            if (corpNames[4].contains("%")) {
                                String jurisdiction = corpNames[4].substring(0, corpNames[4].lastIndexOf("("));
                                needRemoveFromPathList.add(jurisdiction);

                                stockPathInfo1.setJurisdiction(GlobalAreaTranslationUtil.getShortNameEn4Hk(jurisdiction));
                                stockPathInfo1.setPercent(corpNames[4].substring(corpNames[4].lastIndexOf("(") + 1, corpNames[4].lastIndexOf(")")));
                            } else {
                                needRemoveFromPathList.add(corpNames[4]);
                                stockPathInfo1.setJurisdiction(GlobalAreaTranslationUtil.getShortNameEn4Hk(corpNames[4]));
                            }
                            stockPathInfo1.setStartDate(corpNames[2]);
                            stockPathInfo1.setRegNo(corpNames[3]);
                            needRemoveFromPathList.add(stockPathInfo1.getStartDate());
                            needRemoveFromPathList.add(stockPathInfo1.getRegNo());
                            if (UBO_PATH_FLAG.equals(stockPathInfo1.getStartDate())) {
                                stockPathInfo1.setStartDate(null);
                            }
                            if (UBO_PATH_FLAG.equals(stockPathInfo1.getRegNo())) {
                                stockPathInfo1.setRegNo(null);
                            }
                        } else {
                            // 为1则表示企业英文名也没找到
                            stockPathInfo1.setName(corpNames[0]);
                        }
                        stockPathInfoList.add(stockPathInfo1);
                    }
                    // 中间控股公司 （去掉链路的开头和结尾）
                    if (stockPathInfoList.size() > 2) {
                        for (int i = 1; i < stockPathInfoList.size() - 1; i++) {
                            StockPathInfo stockPathInfo1 = stockPathInfoList.get(i);
                            if (!companyMap.containsKey(stockPathInfo1.getName())) {
                                StockIntermediateCompanyTO company = new StockIntermediateCompanyTO();
                                company.setName(stockPathInfo1.getName());
                                company.setNameEn(stockPathInfo1.getNameEn());
                                company.setJurisdiction(stockPathInfo1.getJurisdiction());
                                company.setRegNo(stockPathInfo1.getRegNo());
                                companyMap.put(company.getName(), company);
                            }
                        }
                    }
                    stockDetailTO.setStockPathInfoList(stockPathInfoList);
                    // 链路中的日期需要去掉
                    String pathStr = uboPersonNameEn + " " + uboPerson + arr[0].substring(arr[0].lastIndexOf("(")) + path.substring(path.indexOf(Constants.INVEST)).replaceAll(Constants.CORP_NAME_REGEX, "");
                    for (String keyWord : needRemoveFromPathList) {
                        pathStr = pathStr.replaceAll(keyWord, "");
                    }
                    pathStr = pathStr.trim();
                    stockDetailTO.setPath(pathStr);
                }
            }
        }
    }

    /**
     * added v1.1.7 KNZT-758
     * @param operName
     * @return
     */
    private static String convertSpecOtherBeneName2En(String operName) {
        if(StringUtils.isNotBlank(operName)) {
            int index = operName.lastIndexOf("(基金业协会备案)");
            if(index != -1) {
                String oper = operName.substring(0, index);
                return PinyinUtils.chineseNameToPinyin(oper) + " (Registered with Asset Management Association of China)";
            }
            index = operName.lastIndexOf("(工商公示)");
            if(index != -1) {
                String oper = operName.substring(0, index);
                return PinyinUtils.chineseNameToPinyin(oper);
            }
            return PinyinUtils.chineseNameToPinyin(operName);
        }
        return "";
    }

    /**
     * added for v1.8.2 KNZT-3688 提供高管snapshot报告数据接口
     * 
     * @param keyNo
     * @param type
     * @param isValid
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws MessageException
     */
    public static SeniorPersonCorpInfoResult listSeniorPerson(String keyNo, String type, String isValid, String pageIndex, String pageSize) throws MessageException {
        return listSeniorPerson(keyNo, type, isValid, pageIndex, pageSize, true);
    }

    /**
     * added for v1.0.6 KNZT-336
     * updated for v1.8.2 KNZT-3688
     * @param personId 企业名称/社会信用代码/注册号
     * @param type 0：担任法定代表人；1：对外投资；2：在外任职
     * @param isValid 0：历史；1：有效
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws MessageException
     */
    public static SeniorPersonCorpInfoResult listSeniorPerson(String personId, String type, String isValid, String pageIndex, String pageSize, boolean needCompanyKey) throws MessageException {
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/ECISeniorPersonOversea/GetList"; // 云聚接口9008
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("personId", personId);
        paramMap.put("type", type);
        if (StringUtils.isNotBlank(isValid)) {
            paramMap.put("isValid", isValid);
        }

        if (StringUtils.isBlank(pageIndex)) {
            pageIndex = "1";
        }
        if (StringUtils.isBlank(pageSize)) {
            pageSize = "20";
        }
        paramMap.put("pageIndex", pageIndex);
        paramMap.put("pageSize", pageSize);
        try {
            String resp;
            if (needCompanyKey) {
                resp = HttpUtils.getYunJuApi(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            } else {
                resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_30000);
            }
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) || ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                    if (jsonObj.getJSONArray("Result") != null) {
                        SeniorPersonCorpInfoResult result = new SeniorPersonCorpInfoResult();
                        if (jsonObj.getJSONObject("Paging") != null) {
                            result.setPage(jsonObj.getJSONObject("Paging").toJavaObject(Page.class));
                        }
                        List<SeniorPersonCorpInfoTO> seniorPersonCorpInfoTOList = new ArrayList<>();
                        List<String> keyNoList = new ArrayList<>();
                        for (int i = 0; i < jsonObj.getJSONArray("Result").size(); i++) {
                            JSONObject obj = jsonObj.getJSONArray("Result").getJSONObject(i);
                            SeniorPersonCorpInfoTO to = new SeniorPersonCorpInfoTO();
                            String keyNo = obj.getString("KeyNo");
                            to.setKeyNo(keyNo);
                            keyNoList.add(keyNo);
                            to.setCompanyName(obj.getString("Name"));
                            if (!StringUtils.containsEnglish(to.getCompanyName())) {
                                to.setEnglishName(obj.getString("EnglishName"));
                            }
                            to.setUnifiedSocialCreditCode(obj.getString("CreditCode"));
                            String status = obj.getString("Status");

                            to.setEnStatus(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(status));
                            if (StringUtils.isBlank(to.getEnStatus())) {//added for v1.1.2 KNZT-690
                                to.setEnStatus(HkStatusEnum.getShortEnByShortStatus(status));
                            }
                            to.setStatus(status);
                            if (obj.getJSONArray("RelationList") != null) {
                                List<PersonRelationTO> relationList = obj.getJSONArray("RelationList").toJavaList(PersonRelationTO.class);
                                String job = "";
                                String shareHoldingRatio = "";
                                if (CollectionUtils.isNotEmpty(relationList)) {
                                    for (PersonRelationTO relation : relationList) {
                                        if ("1".equals(relation.getType())) {
                                            shareHoldingRatio = relation.getValue();
                                        } else if ("2".equals(relation.getType())) {
                                            job = relation.getValue();
                                        }
                                        if (StringUtils.equals(relation.getType(), type)) {
                                            to.setStartDate(relation.getStartDate());
                                            to.setEndDate(relation.getEndDate());
                                        }
                                    }

                                }
                                // added for KNZT-1375
                                to.setCheckDate(obj.getLongValue("CheckDate"));
                                to.setCancellationDate(obj.getLongValue("CancellationDate"));
                                to.setRevocationDate(obj.getLongValue("RevocationDate"));
                                to.setTypeOfAppointment(job);
                                if (StringUtils.isNotBlank(job)) {
                                    to.setTypeOfAppointmentEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(job));//updated for v1.1.9 KNZT-945
                                }
                                to.setShareHoldingRatio(shareHoldingRatio);
                            }

                            to.setRegistrationDate(obj.getString("Date"));
                            to.setRegisteredCapital(obj.getString("RegCap"));
                            if (obj.getJSONObject("Area") != null) {
                                to.setProvince(obj.getJSONObject("Area").getString("Province"));
                                if (StringUtils.isNotBlank(to.getProvince())) {
                                    to.setProvinceEn(ProvinceEnum.getProvinceEnNameByProvinceName(to.getProvince()));
                                    to.setRegionEn(GlobalAreaTranslationUtil.getShortNameEn(to.getProvince())); // added for v1.9.1 KNZT-4101
                                }
                            }
                            if (obj.getJSONObject("Industry") != null) {
                                to.setIndustry(obj.getJSONObject("Industry").getString("Industry"));
                                if (StringUtils.isNotBlank(to.getIndustry())) {
                                    to.setIndustryEn(IndustryEnum.getIndustryEnByIndustryCn(to.getIndustry()));
                                }
                            }
                            seniorPersonCorpInfoTOList.add(to);
                        }
                        // added for v1.8.2 KNZT-3762
                        if (CollectionUtils.isNotEmpty(keyNoList)) {
                            List<AdvanceSearchTO> advanceSearchTOS = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(keyNoList, needCompanyKey);
                            Map<String, String> keyNoEnNameMap = new HashMap<>();
                            if (CollectionUtils.isNotEmpty(advanceSearchTOS)) {
                                for (AdvanceSearchTO advanceSearchTO : advanceSearchTOS) {
                                    keyNoEnNameMap.put(advanceSearchTO.getKeyNo(), advanceSearchTO.getNameEn());
                                }
                                if (CollectionUtils.isNotEmpty(seniorPersonCorpInfoTOList)) {
                                    for (SeniorPersonCorpInfoTO p : seniorPersonCorpInfoTOList) {
                                        p.setEnglishName(keyNoEnNameMap.get(p.getKeyNo()));
                                        if (StringUtils.isNotBlank(p.getEnglishName()) && StringUtils.equals(p.getEnglishName(), p.getCompanyName())) {
                                            p.setEnglishName(null);
                                        }
                                    }
                                }
                            }
                        }

                        result.setResult(seniorPersonCorpInfoTOList);


                        return result;
                    }
                    return null;
                } else {
//                throw new MessageException("msg:" + jsonObj.getString("msg"));
                    logger.error("listSeniorPerson:" + resp);
                }
            }
        } catch (Exception e) {
            logger.error("", e);
        }
        return null;

    }
    
    
    /***
     * added for  高管履历个人展示
     * updated for v1.8.0
     * @param personId 人员Id 必传
     * @param keyNo 指定公司Id
     * @param roleType 任职 1 法定代表人 2 股东 3 高管
     * @param exitStatus 任职历程 0 未退出 1 已退出
     * @param companyStatus 公司状态
     * @param isRisk 是否有风险 0 无风险 1 有风险
     * @param isCoreCompany 是否为核心企业 1 核心企业
     * @param jobType 任职职位名称（用于图解报告）
     * @param province 省份（用于图解报告）
     * @param industry 行业（用于图解报告）
     * @param stockPercent 股东股比，形如 "start,end"（用于图解报告）
     * @param sortField 排序字段，支持starttime,registcapi,numberinsured
     * @param isSortAsc 默认 true
     * @param pageIndex 页码
     * @param pageSize  数据大小
     * @param searchType 非空代表 只查询数量不做任何逻辑处理
     * @return
     */
    public static PersonJobResult listPersonJob4Global(String personId, String keyNo, String roleType, String exitStatus, String companyStatus, String isRisk, String isCoreCompany,
                                                       String jobType, String province, String industry, String stockPercent, String sortField, String isSortAsc, String pageIndex, String pageSize, String searchType) {
        CorpSearchForm corpSearchForm = new CorpSearchForm(personId, keyNo, roleType, exitStatus, companyStatus, isRisk, isCoreCompany, jobType, province, industry, stockPercent, sortField, isSortAsc, pageIndex, pageSize, searchType);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listPersonJob4Global", "2", JSON.toJSONString(corpSearchForm));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)) {
            PersonJobResult personJobResult = jsonObject.toJavaObject(PersonJobResult.class);
            if(StringUtils.isBlank(searchType)) {
                if (personJobResult != null && personJobResult.getResultList() != null && personJobResult.getResultList().size() > 0) {
                    TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                    for (PersonJob personJob : personJobResult.getResultList()) {
                        personJob.setCompanyStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(personJob.getCompanyStatus()));
                        if (StringUtils.isBlank(personJob.getCompanyStatusEn())) {
                            // 可能是香港企业，需要再映射一下状态
                            personJob.setCompanyStatusEn(HkStatusEnum.getShortEnByShortStatus(personJob.getCompanyStatus()));
                            if (StringUtils.isBlank(personJob.getCompanyStatusEn())) {
                                // added for KNZT-1559
                                personJob.setCompanyStatusEn(HkStatusEnum.getStatusEnByStatusCn(personJob.getCompanyStatus()));
                            }
                        }
                        List<PersonJobRoleInfo> roleInfoList = personJob.getRoleInfos();
                        if (CollectionUtils.isNotEmpty(roleInfoList)) {
                            for (PersonJobRoleInfo personJobRoleInfo : roleInfoList) {
                                // 任职 1|4 法定代表人 2|5 股东 3|6 高管
                                if (personJobRoleInfo.getRoleType() == 1 || personJobRoleInfo.getRoleType() == 4 || personJobRoleInfo.getRoleType() == 2 || personJobRoleInfo.getRoleType() == 5) {
                                    personJobRoleInfo.setAllRoleName(personJobRoleInfo.getRoleName());
                                    personJobRoleInfo.setAllRoleNameEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(personJobRoleInfo.getRoleName()));
                                } else if (personJobRoleInfo.getRoleType() == 3 || personJobRoleInfo.getRoleType() == 6) {
                                    if (StringUtils.isNotBlank(personJobRoleInfo.getValue())) {
                                        JSONArray jsonArray = JSONArray.parseArray(personJobRoleInfo.getValue());
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            StringJoiner nameJoiner = new StringJoiner(",");
                                            for (Object o : jsonArray) {
                                                String name = (String) o;
                                                nameJoiner.add(name);
                                            }
                                            personJobRoleInfo.setAllRoleName(nameJoiner.toString());
                                            personJobRoleInfo.setAllRoleNameEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(nameJoiner.toString()));
                                        }
                                    }
                                }
                            }
                        }
                        transWrapper.buildAndAddEntry(personJob, PersonJob::setName, PersonJob::setEnglishName, PersonJob::getName, PersonJob::getEnglishName).withEntity(PersonJob::getKeyNo, true);
                    }
                    commonDataService.enPostProcessor(transWrapper);
                }
            }
            return personJobResult;
        }
        return null;
    }

    public static String getEnglishNameByTranslation(String name) {
        // 股东中有这种值： 中国人寿保险股份有限公司-传统-普通保险产品-005L-CT001沪
        if (StringUtils.isContainCorpNameCn(name) || TranslaterService.isTranslated(name)) {
            String enTxt = translaterService.getEnglishText(name, "", name, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
            if (StringUtils.isNotBlank(enTxt)) {
                return enTxt;
            }
        }
        return null;
    }


    // removed for v1.2.1 KNZT-970
//    public static String getEnNameByCnName(String keyNo, String stockName, String englishName) {
//        if (StringUtils.isBlank(englishName)) {
//            if ((StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX) || !StringUtils.isContainCorpNameCn(stockName) ) && !PinyinUtils.notConvert2Pinyin(stockName)) {
//                return PinyinUtils.chineseNameToPinyin(stockName);
//            } else if (StringUtils.isBlank(keyNo) ) {
//                return getEnglishNameByTranslation(stockName);
//            }
//        } else if (StringUtils.isNotBlank(englishName) && StringUtils.equals(englishName, stockName)) {
//            return null;
//        }
//        return englishName;
//    }

    /**
     * // added for v1.2.4 KNZT-1220
     * @param keyNo
     * @param stockName
     * @param englishName
     * @param area
     * @return
     */
    @Deprecated // 替代为 com.backend.common.modules.common.service.CommonDataService.enPostProcessor
    public static String getEnNameByCnNameEnd4Partner(String keyNo, String stockName, String englishName, String area) {
        if (StringUtils.isBlank(englishName)) {
            if ((StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX) || !StringUtils.isContainCorpNameCn(stockName)) && !PinyinUtils.notConvert2Pinyin(stockName)) {

                if(StringUtils.isNotBlank(stockName)){
                    if (StringUtils.isBlank(area) && !StringUtils.contains(stockName, "·") && !StringUtils.contains(stockName, "-") && StringUtils.length(stockName) < 5) {
                        return PinyinUtils.chineseNameToPinyin(stockName);
                    }
                    return null;
                }
                return PinyinUtils.chineseNameToPinyin(stockName);
            } else if (StringUtils.isBlank(keyNo)) {
                if(StringUtils.isNotBlank(stockName) && stockName.contains(",")){
                    String[] str = stockName.split(",");
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (String name : str) {
                        String englishNameByTranslation = getEnglishNameByTranslation(name);
                        if(StringUtils.isNotBlank(englishNameByTranslation)){
                            stringJoiner.add(englishNameByTranslation);
                        }
                    }
                    return stringJoiner.toString();
                }
                return getEnglishNameByTranslation(stockName);
            }
        }
        if (StringUtils.isNotBlank(englishName) && StringUtils.equals(englishName, stockName)) {
            return null;
        }
        return englishName;
    }

    @Deprecated // 替代为 com.backend.common.modules.common.service.CommonDataService.enPostProcessor
    public static String getEnNameByCnNameEnd(String keyNo, String stockName, String englishName) {
        return getEnNameByCnNameEnd(keyNo, stockName, englishName, null);
    }

    /**
     * 修正英文名
     * update for 1.3.8 KNZT-2096
     * @param keyNo
     * @param stockName 中文名
     * @param englishName 接口出参的英文名
     * @return 最终修正后的英文名
     */
    @Deprecated // 替代为 com.backend.common.modules.common.service.CommonDataService.enPostProcessor
    public static String getEnNameByCnNameEnd(String keyNo, String stockName, String englishName, String area) {
        if (StringUtils.isBlank(stockName)) {
            return null;
        }
        // 当英文名为空时
        if (StringUtils.isBlank(englishName) && !PinyinUtils.isContainsNoTranslateKeyWord(stockName)) {
            // 当 （keyNo是人员 或 不包含公司等关键词） 并且 允许被拼音翻译的情况，走拼音翻译
            if ((StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX) || !StringUtils.isContainCorpNameCn(stockName))
                    && !PinyinUtils.notConvert2Pinyin(stockName)
                    && StringUtils.isBlank(area)) {
                if(StringUtils.isNotBlank(stockName) && stockName.contains(",")){
                    String[] str = stockName.split(",");
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (String name : str) {
                        String chineseNameToPinyin = PinyinUtils.chineseNameToPinyin(name);
                        if(StringUtils.isNotBlank(chineseNameToPinyin)){
                        stringJoiner.add(chineseNameToPinyin);
                        }
                    }
                    englishName = stringJoiner.toString();
                } else {
                    englishName = PinyinUtils.chineseNameToPinyin(stockName);
                }
            // 当keyNo为空时 走机翻
            // updated for v1.5.2 KNZT-2334
            } else if (StringUtils.isBlank(keyNo) || keyNo.startsWith("gg")) {
                if(StringUtils.isNotBlank(stockName) && stockName.contains(",")){
                    String[] str = stockName.split(",");
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (String name : str) {
                        String englishNameByTranslation = getEnglishNameByTranslation(name);
                        if(StringUtils.isNotBlank(englishNameByTranslation)){
                            stringJoiner.add(englishNameByTranslation);
                        }
                    }
                    englishName = stringJoiner.toString();
                } else {
                    englishName = getEnglishNameByTranslation(stockName);
                }
            }
        }
        if (StringUtils.containsIgnoreCase(stockName, englishName)) {
            return null;
        }
        if (StringUtils.isEnglish(stockName)) {
            return null;
        }
        // 所有成功翻译或本身已有翻译的情况下，返回大写的英文名称
        if (!StringUtils.startsWith(keyNo, "p") && StringUtils.isNotBlank(englishName)) {
            return StringUtils.upperCase(englishName);
        }
        // updated for v1.6.4 KNZT-2812 是否命中特定关键词 "其他" "xxx人" "xxx个" 时，不翻译
        // removed for v1.6.6 KNZT-2917
//        if (PinyinUtils.isContainsNoTranslateKeyWord(stockName)) {
//            return null;
//        }
        return englishName;
    }

    /**
     * added for v2.0.3 chenbl KNZT-5375
     * 给人员类型，修正英文名(不会因为不满足人名翻译条件而走机翻)
     *
     * @param keyNo
     * @param stockName
     * @param englishName
     * @return
     */
    @Deprecated // 替代为 com.backend.common.modules.common.service.CommonDataService.enPostProcessor
    public static String getEnNameByCnNameEnd4Person(String keyNo, String stockName, String englishName) {
        if (StringUtils.isBlank(stockName)) {
            return null;
        }
        // 当英文名为空时
        if (StringUtils.isBlank(englishName) && !PinyinUtils.isContainsNoTranslateKeyWord(stockName)) {
            // 当 （keyNo是人员 或 不包含公司等关键词） 并且 允许被拼音翻译的情况，走拼音翻译
            if ((StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX) || !StringUtils.isContainCorpNameCn(stockName))
                    && !PinyinUtils.notConvert2Pinyin(stockName)) {
                if (StringUtils.isNotBlank(stockName) && stockName.contains(",")) {
                    String[] str = stockName.split(",");
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (String name : str) {
                        String chineseNameToPinyin = PinyinUtils.chineseNameToPinyin(name);
                        if (StringUtils.isNotBlank(chineseNameToPinyin)) {
                            stringJoiner.add(chineseNameToPinyin);
                        }
                    }
                    englishName = stringJoiner.toString();
                } else {
                    englishName = PinyinUtils.chineseNameToPinyin(stockName);
                }
            }
        }
        if (StringUtils.isNotBlank(englishName) && StringUtils.equals(englishName, stockName)) {
            return null;
        }
        if (StringUtils.containsEnglish(stockName) && StringUtils.containsEnglish(englishName)) {
            return null;
        }
        if (StringUtils.isNotBlank(englishName) && StringUtils.isNotBlank(stockName)) {
            String enLower = englishName.toLowerCase();
            String cnLower = stockName.toLowerCase();
            if (StringUtils.contains(enLower, cnLower) || StringUtils.contains(cnLower, enLower)) {
                return null;
            }
        }
        return englishName;
    }

    /**
     * added for v1.1.8 KNZT-918
     * @param corpKeyNo
     * @return
     */
    public static CorpScanningCountTO getCorpScanning(String corpKeyNo) {

        String proCompanyId = UserUtils.getUser().getCompany().getProKey();
        String param = "keyNo=" + corpKeyNo + "&companyId=" + proCompanyId;

        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCorpScanningCount4Global", "1", param);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("result") != null) {
            return jsonObject.getJSONObject("result").toJavaObject(CorpScanningCountTO.class);
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3104
    public static CoutZhiXingResult getZhiXingList(String corpKeyNo, String isValid, String pageIndex, String pageSize) {
        return getZhiXingList(corpKeyNo, isValid, pageIndex, pageSize, true);
    }

    /**
     * 被执行人
     * updated for v1.7.2 KNZT-3104
     * 
     * @param corpKeyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static CoutZhiXingResult getZhiXingList(String corpKeyNo, String isValid, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + corpKeyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize + "&isValid=" + isValid;;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getZhiXingList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            CoutZhiXingResult coutZhiXingResult = jsonObject.toJavaObject(CoutZhiXingResult.class);
            if (coutZhiXingResult != null && coutZhiXingResult.getResultList() != null && coutZhiXingResult.getResultList().size() > 0) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                List<CoutShiXinNameKeyNoColl> shixinList = MappingUtils.flattenAndGetList(coutZhiXingResult.getResultList(), CoutZhiXing::getSqrInfo);
                // 处理疑似申请执行人， 兜底英文名称处理
                if (CollectionUtils.isNotEmpty(shixinList)) {
                    shixinList.forEach(obj -> transWrapper.buildAndAddEntry(obj, CoutShiXinNameKeyNoColl::setName, CoutShiXinNameKeyNoColl::setEnglishName, CoutShiXinNameKeyNoColl::getName, CoutShiXinNameKeyNoColl::getEnglishName).withEntity(CoutShiXinNameKeyNoColl::getKeyNo, true));
                }
                List<CoutZhiXingNameKeyNoColl> zhixingList = MappingUtils.flattenAndGetList(coutZhiXingResult.getResultList(), CoutZhiXing::getNameKeyNoCollection);
                // 处理疑似申请执行人， 兜底英文名称处理 取值被执行人
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(zhixingList, CoutZhiXingNameKeyNoColl::setName, CoutZhiXingNameKeyNoColl::setEnglishName, CoutZhiXingNameKeyNoColl::getName, CoutZhiXingNameKeyNoColl::getEnglishName, CoutZhiXingNameKeyNoColl::getKeyNo);
                commonDataService.enPostProcessor(transWrapper);
            }
            return coutZhiXingResult;
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3104
    public static CoutShiXinResult getShiXinList(String keyNo, String isValid, String pageIndex, String pageSize) {
        return getShiXinList(keyNo, isValid, pageIndex, pageSize, true);
    }

    /**
     * 失信
     * updated for v1.7.2 KNZT-3104
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static CoutShiXinResult getShiXinList(String keyNo, String isValid, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize + "&isValid=" + isValid;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getShiXinList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            CoutShiXinResult coutShiXinResult = jsonObject.toJavaObject(CoutShiXinResult.class);
            if (coutShiXinResult != null && coutShiXinResult.getResultList() != null && coutShiXinResult.getResultList().size() > 0) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                List<CoutShiXinNameKeyNoColl> collList = new ArrayList<>();
                collList.addAll(MappingUtils.flattenAndGetList(coutShiXinResult.getResultList(), CoutShiXin::getSqrInfo));
                collList.addAll(MappingUtils.flattenAndGetList(coutShiXinResult.getResultList(), CoutShiXin::getNameKeyNoCollection));
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(collList, CoutShiXinNameKeyNoColl::setName, CoutShiXinNameKeyNoColl::setEnglishName, CoutShiXinNameKeyNoColl::getName, CoutShiXinNameKeyNoColl::getEnglishName, CoutShiXinNameKeyNoColl::getKeyNo);
                commonDataService.enPostProcessor(transWrapper);
                return coutShiXinResult;
            }
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3104
    public static PersonSumptuaryResult getSumptuaryList(String keyNo, String isValid, String pageIndex, String pageSize) {
        return getSumptuaryList(keyNo, isValid, pageIndex, pageSize, true);
    }
    /**
     * 限制高消费
     * updated for v1.7.2 KNZT-3104
     * @param keyNo
     * @param isValid 0-历史 1-当前
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static PersonSumptuaryResult getSumptuaryList(String keyNo, String isValid, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize + "&isValid=" + isValid;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getSumptuaryList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            PersonSumptuaryResult personSumptuaryResult = jsonObject.toJavaObject(PersonSumptuaryResult.class);
            if (personSumptuaryResult != null && personSumptuaryResult.getResultList() != null && !personSumptuaryResult.getResultList().isEmpty()) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(personSumptuaryResult.getResultList(), PersonSumptuary::setXianGaoLingObj, PersonSumptuary::setXianGaoLingObjEnName, PersonSumptuary::getXianGaoLingObj, PersonSumptuary::getXianGaoLingObjEnName, PersonSumptuary::getXianGaoLingKeyNo);
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(personSumptuaryResult.getResultList(), PersonSumptuary::setRelatedName, PersonSumptuary::setRelatedEnName, PersonSumptuary::getRelatedName, PersonSumptuary::getRelatedEnName, PersonSumptuary::getRelatedKeyNo);
                List<SqrInfo> sqrInfos = MappingUtils.flattenAndGetList(personSumptuaryResult.getResultList(), PersonSumptuary::getSqrInfo);
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(sqrInfos, SqrInfo::setName, SqrInfo::setEnglishName, SqrInfo::getName, SqrInfo::getEnglishName, SqrInfo::getKeyNo);
                commonDataService.enPostProcessor(transWrapper);
            }
            return personSumptuaryResult;
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3104
    public static LimitExitResult getLimitExitList(String keyNo, String pageIndex, String pageSize) {
        return getLimitExitList(keyNo, pageIndex, pageSize, true);
    }

    /**
     * 限制出境
     * updated for v1.7.2 KNZT-3104
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static LimitExitResult getLimitExitList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getLimitExitList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            LimitExitResult limitExitResult = jsonObject.toJavaObject(LimitExitResult.class);
            if (limitExitResult != null && limitExitResult.getResultList() != null && limitExitResult.getResultList().size() > 0) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                List<Oper> operList = new ArrayList<>();
                operList.addAll(MappingUtils.flattenAndGetList(limitExitResult.getResultList(), LimitExit::getLimitedPerson));
                operList.addAll(MappingUtils.flattenAndGetList(limitExitResult.getResultList(), LimitExit::getExecutedPerson));
                if (CollectionUtils.isNotEmpty(operList)) {
                    operList.forEach(obj -> transWrapper.buildAndAddEntry(obj, Oper::setName, Oper::setEnglishName, Oper::getName, Oper::getEnglishName).withEntity(Oper::getKeyNo, true));
                }
                commonDataService.enPostProcessor(transWrapper);
            }
            return limitExitResult;
        }
        return null;
    }

    /**
     * // added for v1.2.1 KNZT-970
     * @param partnerTOList
     * @param keyNoEnNameMap
     */
    public static void setEnglishName4PartnerByInterface(List<CorpPartnerTO> partnerTOList, Map<String, String> keyNoEnNameMap) {
        if (CollectionUtils.isNotEmpty(partnerTOList)) {
            for (CorpPartnerTO p : partnerTOList) {
                if(StringUtils.isNotBlank(p.getKeyNo()) && !StringUtils.startsWith(p.getKeyNo(), Constants.CorpTypePrefix.PERSON_PREFIX)) { // updated for v1.8.2 KNZT-3762
                    p.setEnglishName(keyNoEnNameMap.get(p.getKeyNo()));
                }
                if (StringUtils.isNotBlank(p.getEnglishName()) && StringUtils.equals(p.getEnglishName(), p.getStockName())) {
                    p.setEnglishName(null);
                }
            }
        }
    }

    /**
     * 分支机构
     * updated for v1.7.2 KNZT-3104
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static CompanyBranchResult getBranchList(String keyNo, String pageIndex, String pageSize) {
        return getBranchList(keyNo, pageIndex, pageSize, true);
    }

    /**
     * 分支机构
     * updated for v1.7.2 KNZT-3104
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static CompanyBranchResult getBranchList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCompanyBranches", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            CompanyBranchResult branchResult = jsonObject.toJavaObject(CompanyBranchResult.class);
            if (branchResult != null && branchResult.getResultList() != null && branchResult.getResultList().size() > 0) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                for (CompanyBranch companyBranch : branchResult.getResultList()) {
                    Oper oper = companyBranch.getOper();
                    if (oper != null && oper.getOrg() != null) {
                        //负责人，org -1 无法判断, -2:没有Id的人名,0:公司,1:社会组织,2:主要人员,3:香港公司,4:政府机构和学校,5:台湾公司，如果是-2跟2调用拼音
                        if (oper.getOrg() == -2 || oper.getOrg() == 2) {
                            transWrapper.buildAndAddEntry(oper, Oper::setName, Oper::setEnglishName, Oper::getName, Oper::getEnglishName).withEntity(Oper::getKeyNo, TransWrapper.ENTRY_TYPE_PERSON);
                        } else {
                            transWrapper.buildAndAddEntry(oper, Oper::setName, Oper::setEnglishName, Oper::getName, Oper::getEnglishName).withEntity(Oper::getKeyNo, true);
                        }
                    }
                    //登记状态，走映射
                    companyBranch.setEnStatus(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(companyBranch.getShortStatus()));
                    //地区（省市），需翻译，走映射
                    if (Objects.nonNull(companyBranch.getArea()) && StringUtils.isNotBlank(companyBranch.getArea().getProvince())) {//added for v1.1.2 KNZT-664
                        EnArea enArea = companyBranch.getArea();
                        String province = enArea.getProvince();
                        String provinceEn = ProvinceEnum.getProvinceEnNameByProvinceName(province);
                        companyBranch.getArea().setProvinceEn(provinceEn);
                        String city = "";
                        if (StringUtils.contains(province, "上海") || StringUtils.contains(province, "北京")
                                || StringUtils.contains(province, "重庆") || StringUtils.contains(province, "天津")) {
                            city = Objects.nonNull(enArea.getCounty()) ? enArea.getCounty() : "";
                        }else {
                            city = Objects.nonNull(enArea.getCity()) ? enArea.getCity() : "";
                        }
                        companyBranch.setProvinceCity(province + city);
                        String cityEn = CityEnum.getCityEnNameByCityName(city);
                        if (StringUtils.isNotBlank(cityEn)) {
                            companyBranch.setEnProvinceCity(cityEn + ", " +provinceEn);
                        } else {
                            companyBranch.setEnProvinceCity(provinceEn);
                        }
                        // updated for v2.0.2 chenbl KNZT-5360
                        companyBranch.setJurisdictionEn(GlobalAreaTranslationUtil.getShortNameEn(province)); // added for v1.9.5 KNZT-4624
                    }
                    companyBranch.setStartDateStr(DateUtils.toDateStr(companyBranch.getStartDate(),DateUtils.DATE_FORMAT));
                    // added for v1.7.1 KNZT-3101
                    companyBranch.setBelongOrgEn(translateRegistrationAuthority(companyBranch.getBelongOrg()));
                }
                if (CollectionUtils.isNotEmpty(branchResult.getResultList())) {
                    branchResult.getResultList().forEach(obj -> transWrapper.buildAndAddEntry(obj, CompanyBranch::setName, CompanyBranch::setEnglishName, CompanyBranch::getName, CompanyBranch::getEnglishName).withEntity(CompanyBranch::getKeyNo, true));
                }
                commonDataService.enPostProcessor(transWrapper);
            }
            return branchResult;
        } else if ("openapi".equals(Global.getConfig("application.type"))) { // added for v1.9.7 KNZT-4708
            if (jsonObject == null || !Constants.Result.SUCCESS_STR.equals(jsonObject.getString("status"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        return null;
    }

    /**
     * added for v1.2.2 KNZT-1045
     * @param corpKeyNo
     * @param includeFields 需要请求的维度计数，多个用逗号分隔 selfRisk-风险维度计数，personInfo-人员信息维度计数（包括投资任职等）
     * @return
     */
    public static PersonScanningCountTO getPersonScanningCount(String corpKeyNo, String includeFields) throws MessageException {
        String param = "keyNo=" + corpKeyNo;
        if (StringUtils.isNotBlank(includeFields)) {
            param += "&includeFields=" + includeFields;
        }

        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getPersonScanningCount4Global", "1", param);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("result") != null) {
            return jsonObject.getJSONObject("result").toJavaObject(PersonScanningCountTO.class);
        }
        return null;
    }

    /**
     * added for v1.2.2
     * @param corpKeyNo
     * @return
     */
    public static CorpHKDimensionCountTO getHKCorpScanningCount(String corpKeyNo) {
        String param = "keyNo=" + corpKeyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listHKCorpDimension4Global", "1", param);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("result") != null) {
            return jsonObject.getJSONObject("result").toJavaObject(CorpHKDimensionCountTO.class);
        }
        return null;
    }

    /**
     * added for v1.2.7 KNZT-1314
     * @param corpKeyNo
     * @return
     */
    public static HkAnnouncementTO getHKLatestAnnouncement(String corpKeyNo) {
        String param = "keyNo=" + corpKeyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getHKLatestAnnouncement", "1", param);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("result") != null) {
            return jsonObject.getJSONObject("result").toJavaObject(HkAnnouncementTO.class);
        }
        return null;
    }

    /**
     * added for v2.0.6 chenbl KNZT-5555
     */
    public static List<HistoryInfoTO.HistoryRegistCapiList> getHistoryRegistCapiList(String keyNo, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCoyHistoryInfo", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            HistoryInfoTO historyInfoTO = jsonObject.getJSONObject("result").toJavaObject(HistoryInfoTO.class);
            if (historyInfoTO != null) {
                return historyInfoTO.getRegistCapiList();
            }
        }
        return null;
    }

    // added for v1.8.0 KNZT-3596
    public static HistoryInfoTO getCoyHistoryInfo(String keyNo) {
        return getCoyHistoryInfo(keyNo, true);
    }

    /**
     * added for v1.8.0 KNZT-3596
     */
    public static HistoryInfoTO getCoyHistoryInfo(String keyNo, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCoyHistoryInfo", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        // updated for v2.1.5 chenbl KNZT-6442
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)) {
            HistoryInfoTO historyInfoTO = null;
            if (Objects.nonNull(jsonObject.getJSONObject("result"))) {
                historyInfoTO = jsonObject.getJSONObject("result").toJavaObject(HistoryInfoTO.class);
                if (historyInfoTO != null && CollectionUtils.isNotEmpty(historyInfoTO.getAddressList())) {
                    // todo 待优化，走批量翻译
                    for (HistoryInfoTO.HistoryAddressList historyAddressList : historyInfoTO.getAddressList()) {
                        String enTxt = translaterService.getEnglishText(historyAddressList.getAddress(), keyNo, null, SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                        if (StringUtils.isNotBlank(enTxt) && !StringUtils.equals(enTxt, historyAddressList.getAddress())) {
                            historyAddressList.setAddressEn(enTxt);
                        }
                    }
                }
            }
            if (historyInfoTO == null) {
                historyInfoTO = new HistoryInfoTO();
                historyInfoTO.setKeyNo(keyNo);
            }
            addCurr2History(keyNo, historyInfoTO, needCompanyKey);
            return historyInfoTO;
        }
        return null;
    }

    // added for v2.1.5 chenbl KNZT-6442
    private static void addCurr2History(String keyNo, HistoryInfoTO historyInfoTO, boolean needCompanyKey) {
        if (historyInfoTO == null) {
            return;
        }
        CorpBasicDetail4UnitTO corpBasicInfo4Unit = getCorpBasicInfo4Unit(keyNo, null, needCompanyKey);
        if (corpBasicInfo4Unit == null) {
            return;
        }
        String establishmentDate = corpBasicInfo4Unit.getEstablishmentDate();
        // 补充当前注册地址到历史信息中
        List<HistoryInfoTO.HistoryAddressList> addressList = historyInfoTO.getAddressList();
        if (CollectionUtils.isEmpty(addressList)) {
            addressList = new ArrayList<>();
            historyInfoTO.setAddressList(addressList);
        }
        String currAddressStartDate = null;
        if (CollectionUtils.isNotEmpty(addressList)) {
            currAddressStartDate = addressList.get(0).getChangeDate();
        } else {
            currAddressStartDate = establishmentDate;
        }
        String registeredAddress = corpBasicInfo4Unit.getRegisteredAddress();
        if (StringUtils.isNotBlank(currAddressStartDate) && StringUtils.isNotBlank(registeredAddress)) {
            HistoryInfoTO.HistoryAddressList currAddress = new HistoryInfoTO.HistoryAddressList();
            currAddress.setStartDate(currAddressStartDate);
            currAddress.setAddress(registeredAddress);
            String enTxt = translaterService.getEnglishText(registeredAddress, null, null, SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
            currAddress.setAddressEn(enTxt);
            addressList.add(0, currAddress);
        }

        // 补充当前注册资本到历史信息中
        List<HistoryInfoTO.HistoryRegistCapiList> registCapiList = historyInfoTO.getRegistCapiList();
        if (CollectionUtils.isEmpty(registCapiList)) {
            registCapiList = new ArrayList<>();
            historyInfoTO.setRegistCapiList(registCapiList);
        }
        String currRegistCapiStartDate = null;
        if (CollectionUtils.isNotEmpty(registCapiList)) {
            currRegistCapiStartDate = registCapiList.get(0).getChangeDate();
        } else {
            currRegistCapiStartDate = establishmentDate;
        }
        String registeredCapital = corpBasicInfo4Unit.getRegisteredCapital();
        if (StringUtils.isNotBlank(currRegistCapiStartDate) && StringUtils.isNotBlank(registeredCapital)) {
            HistoryInfoTO.HistoryRegistCapiList currRegistCapi = new HistoryInfoTO.HistoryRegistCapiList();
            currRegistCapi.setStartDate(currRegistCapiStartDate);
            currRegistCapi.setRegistCapi(registeredCapital);
            AmountUnit amountUnit = CurrencyUtils.transformAmount(registeredCapital);
            if (amountUnit != null) {
                if (amountUnit.getAmount() != null) {
                    currRegistCapi.setAmount(amountUnit.getAmount().toPlainString());
                }
                if (amountUnit.getUnit() != null) {
                    currRegistCapi.setUnit(amountUnit.getUnit().getCurrencyUnitName());
                }
            }
            registCapiList.add(0, currRegistCapi);
        }
    }

    // updated for v1.7.2 KNZT-3104
    public static HistOperResult getHistoryOperList(String keyNo){
        return getHistoryOperList(keyNo, true);
    }

    /**
     * added for v1.2.4 KNZT-1226 展示历史股东、历史法定代表人、历史主要人员
     * updated for v1.7.2 KNZT-3104
     */
    public static HistOperResult getHistoryOperList(String keyNo, boolean needCompanyKey){
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getHistoryOperInfo4Global", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            HistOperResult histOperResult = jsonObject.toJavaObject(HistOperResult.class);
            if (histOperResult != null && histOperResult.getResultList() != null && histOperResult.getResultList().size() > 0) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                for (HistOper historyOper : histOperResult.getResultList()) {
                    if (historyOper.getOrg() == -2 || historyOper.getOrg() == 2) {
                        transWrapper.buildAndAddEntry(historyOper, HistOper::setOperName, HistOper::setOperNameEn, HistOper::getOperName, HistOper::getOperNameEn).withEntity(HistOper::getKeyNo, TransWrapper.ENTRY_TYPE_PERSON);
                    } else if (StringUtils.isBlank(historyOper.getKeyNo())) {
                        transWrapper.buildAndAddEntry(historyOper, HistOper::setOperName, HistOper::setOperNameEn, HistOper::getOperName, HistOper::getOperNameEn).withEntity(HistOper::getKeyNo, true);
                    }
                }
                commonDataService.enPostProcessor(transWrapper);
            }
            return histOperResult;
        }
        return null;
    }

    // updated for v2.0.5 chenbl KNZT-5562
    //历史股东
    public static HistShareHolderResult getHistoryPartnerList(String keyNo, String pageIndex, String pageSize){
        return getHistoryPartnerList(keyNo, pageIndex, pageSize, true);
    }

    //历史股东
    public static HistShareHolderResult getHistoryPartnerList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey){
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getHistoryPartnerInfo4Global", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            HistShareHolderResult histShareHolderResult = jsonObject.toJavaObject(HistShareHolderResult.class);
            if (histShareHolderResult != null && histShareHolderResult.getResultList() != null && histShareHolderResult.getResultList().size() > 0) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                for (HistShareHolder histShareHolder : histShareHolderResult.getResultList()) {
                    // added for v1.3.9 KNZT-2237 历史股东补充国籍字段
                    if (StringUtils.isNotBlank(histShareHolder.getArea())) {
                        // updated for v1.9.1 KNZT-4101 映射调整
                        GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(histShareHolder.getArea());
                        histShareHolder.setAreaEn(Objects.nonNull(areaEnum) ? areaEnum.getShortNameEn() : null);
                        transWrapper.buildAndAddEntry(histShareHolder, HistShareHolder::setArea, HistShareHolder::setAreaEn, HistShareHolder::getArea, HistShareHolder::getAreaEn);
                        histShareHolder.setAreaRiskLevel(GlobalAreaEnum.judgeRiskLevel(areaEnum));
                    }
                    // 自然人股东 或者 法团股东
                    /* removed for v2.0.5 chenbl KNZT-4527
                    if (StringUtils.isNotBlank(histShareHolder.getType())) {
                        String typeEn = translaterService.getEnglishText(histShareHolder.getType(), "", "", SysConstants.TRANSLATE_HIST_FUNCTION_CORP_NAME);
                        if (StringUtils.isNotBlank(typeEn)) {
                            histShareHolder.setTypeEn(capitalizeFirstLetter(typeEn));
                        }
                    }*/
                    // added for v2.0.5 chenbl KNZT-4527
                    histShareHolder.setGlobalStockType(StockTypeTranslationUtil.getGlobalStockType(histShareHolder.getKeyNo(), histShareHolder.getStockTypeCode(), histShareHolder.getType()));
                }
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(histShareHolderResult.getResultList(), HistShareHolder::setPartnerName, HistShareHolder::setPartnerNameEn, HistShareHolder::getPartnerName, HistShareHolder::getPartnerNameEn, HistShareHolder::getKeyNo);
                commonDataService.enPostProcessor(transWrapper);
            }
            return histShareHolderResult;
        }
        return null;
    }

    public static String capitalizeFirstLetter(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        for (char c : input.toCharArray()) {
            if (capitalizeNext && Character.isLetter(c)) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(c);
            }
            if (c == ' ') {
                capitalizeNext = true;
            }
        }
        return result.toString();
    }

    // updated for v1.7.2 KNZT-3104
    public static HistEmployeeResult getHistoryEmployeeList(String keyNo, String pageIndex, String pageSize){
        return getHistoryEmployeeList(keyNo, pageIndex, pageSize, true);
    }

    // updated for v1.7.2 KNZT-3104
    //历史主要人员
    public static HistEmployeeResult getHistoryEmployeeList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey){
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getHistoryEmployeeInfo4Global", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            HistEmployeeResult histEmployeeResult = jsonObject.toJavaObject(HistEmployeeResult.class);
            if (histEmployeeResult != null && histEmployeeResult.getResultList() != null && histEmployeeResult.getResultList().size() > 0) {
                for (HistEmployee histEmployee : histEmployeeResult.getResultList()) {
                    //调用拼音
                    //added for v1.2.5 KNZT-1261 名字中带有拼音不翻译
                    if(!PinyinUtils.notConvert2Pinyin(histEmployee.getEmployeeName())) {
                        histEmployee.setEmployeeEnName(PinyinUtils.chineseNameToPinyin(histEmployee.getEmployeeName()));
                    }
                    histEmployee.setJobEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(histEmployee.getJob()));//updated for v1.1.9 KNZT-945
                }
            }
            return histEmployeeResult;
        }
        return null;
    }

    //企业详情页面初始化使用

    /**
     * updated for KNZT-1735【优化】【国际版】大陆公司：UBO，risking scaning，异步加载
     * histEmployee-历史主要人员
     * histShareHolder-历史股东
     * histOper-历史法人
     * 
     * @param keyNo
     * @param historyInfoQuery
     * @return
     */
    public static HistCorpInfo getHistoryCorpInfo(String keyNo, List<String> historyInfoQuery){
        HistCorpInfo histCorpInfo = new HistCorpInfo();
        if(CollectionUtils.isEmpty(historyInfoQuery)) return null;
        HistEmployeeResult histEmployeeResult = null;
        HistShareHolderResult histShareHolderResult = null;
        HistOperResult histOperResult = null;
        for (String searchInfoKey: historyInfoQuery) {
            if("histEmployee".equals(searchInfoKey)) {
                histEmployeeResult = getHistoryEmployeeList(keyNo, "1", "20");
            }
            if("histShareHolder".equals(searchInfoKey)){
                histShareHolderResult = getHistoryPartnerList(keyNo,"1","20");
            }
            if("histOper".equals(searchInfoKey)){
                histOperResult = getHistoryOperList(keyNo);
            }
        }
        if(histEmployeeResult == null && histShareHolderResult == null && histOperResult == null){
            return null;
        }
        if(histEmployeeResult != null ){
            histCorpInfo.setHistEmployeeResult(histEmployeeResult);
        }
        if(histShareHolderResult != null){
            histCorpInfo.setHistShareHolderResult(histShareHolderResult);
        }
        if(histOperResult!= null ){
            histCorpInfo.setHistOperResult(histOperResult);
        }
        return histCorpInfo;
    }
    
    /**
     * 
     * added for v1.2.9 KNZT-1411
     * 
     * @param keyNo
     * @return
     */
    public static String getCorpSnapShot4Global(String keyNo) {
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCorpSnapShot4Global", "1", param);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getString("result") != null) {
            return jsonObject.getString("result");
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3231
    public static CorpBusinessParentInfoTO getCorpParentInfo(String keyNo) {
        return getCorpParentInfo(keyNo, true);
    }

    /**
     * added for v1.2.9 KNZT-1416
     * updated for v1.7.2 KNZT-3231
     * @param keyNo
     * @return
     */
    public static CorpBusinessParentInfoTO getCorpParentInfo(String keyNo, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCorpParentInfo4Global", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("result") != null) {
            return jsonObject.getJSONObject("result").toJavaObject(CorpBusinessParentInfoTO.class);
        }
        return null;
    }

    /**
     * added for v1.3.1 KNZT-1513
     * updated for v1.8.2 KNZT-3688
     * 获取控制企业
     */
    public static ApiVipHoldCompResult listVipHoldComp4Global(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listVipHoldComp4Global", "2", JSON.toJSONString(param), needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ApiVipHoldCompResult result = jsonObject.toJavaObject(ApiVipHoldCompResult.class);
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
                for (ApiVipHoldCompTO comp : result.getResultList()) {
                    // 状态描述转英文
                    if (StringUtils.isNotBlank(comp.getShortStatus())) {
                        comp.setShortStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(comp.getShortStatus()));
                    }
                    // 所属省市转英文
                    CompAreaTO area = comp.getArea();
                    if (Objects.nonNull(area)) {
                        comp.setProvinceCity(generateProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                        comp.setProvinceCityEn(generateEnProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                    }
                    if (CollectionUtils.isNotEmpty(comp.getPaths())) {
                        for (List<ApiCompPathTO> pathList : comp.getPaths()) {
                            if (CollectionUtils.isEmpty(pathList)) {
                                continue;
                            }
                            for (ApiCompPathTO path : pathList) {
                                // added for v1.5.2 KNZT-2351 增加dataType的处理
                                if (Objects.equals(path.getDataType(), 5)) {
                                    path.setDataTypeNameEn(CompGraphItemEnum.C_MANAGING_PARTNER.getTypeEn());
                                }
                                // removed for v1.5.3 KNZT-2454
                                // added for v1.5.2 KNZT-2351 增加国籍区域翻译的处理
//                                if (CollectionUtils.isNotEmpty(path.getTags())) {
//                                    for (ApiCompPathTO.VipHoldCompPathTag tag : path.getTags()) {
//                                        if (Lists.newArrayList(112 , 115, 402).contains(tag.getType()) && StringUtils.isNotBlank(tag.getName())) {
//                                            GlobalAreaEnum areaEnum = GlobalAreaEnum.getByArea(tag.getName());
//                                            if (Objects.nonNull(areaEnum)) {
//                                                tag.setNameEn(areaEnum.getAreaEn());
//                                            } else {
//                                                String englishText = translaterService.getEnglishText(tag.getName(), "", "", SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
//                                                tag.setNameEn(englishText);
//                                            }
//                                        }
//                                    }
//                                }
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(result.getResultList())) {
                    result.getResultList().forEach(obj -> transWrapper.buildAndAddEntry(obj, ApiVipHoldCompTO::setName, ApiVipHoldCompTO::setNameEn, ApiVipHoldCompTO::getName, ApiVipHoldCompTO::getNameEn).withEntity(ApiVipHoldCompTO::getKeyNo, true));
                }
                List<List<ApiCompPathTO>> lists = MappingUtils.flattenAndGetList(result.getResultList(), ApiVipHoldCompTO::getPaths);
                List<ApiCompPathTO> paths = lists.stream().flatMap(Collection::stream).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(paths)) {
                    paths.forEach(obj -> transWrapper.buildAndAddEntry(obj, ApiCompPathTO::setName, ApiCompPathTO::setNameEn, ApiCompPathTO::getName, ApiCompPathTO::getNameEn).withEntity(ApiCompPathTO::getKeyNo, true));
                }
            }
            commonDataService.enPostProcessor(transWrapper);
            return result;
        }
        return null;
    }

    /**
     * added for v1.3.1 KNZT-1513
     * 根据中文省市区 生成 省市的英文描述
     * 格式为 Suzhou, Jiangsu
     */
    private static String generateEnProvinceCity (String province, String city, String county) {
        if (StringUtils.isBlank(province)) {
            return "";
        }
        String provinceEn = ProvinceEnum.getProvinceEnNameByProvinceName(province);

        city = judgeCityCounty(province, city, county);
        String cityEn = CityEnum.getCityEnNameByCityName(city);

        if (StringUtils.isNotBlank(cityEn)) {
            return cityEn + ", " + provinceEn;
        } else {
            return provinceEn;
        }
    }

    /**
     * added for v1.3.1 KNZT-1513
     * 根据中文省市区 生成 省市的中文描述
     * 格式为 江苏省苏州市
     */
    private static String generateProvinceCity (String province, String city, String county) {
        if (StringUtils.isBlank(province)) {
            return "";
        }
        city = judgeCityCounty(province, city, county);
        return province + city;
    }

    /**
     * added for v1.3.1 KNZT-1513
     * 根据省市判断city为 直辖市的区 or 地级市
     */
    private static String judgeCityCounty(String province, String city, String county) {
        if (StringUtils.contains(province, "上海") || StringUtils.contains(province, "北京")
                || StringUtils.contains(province, "重庆") || StringUtils.contains(province, "天津")) {
            city = Objects.nonNull(county) ? county : "";
        } else {
            city = Objects.nonNull(city) ? city : "";
        }
        return city;
    }

    // updated for v1.7.2 KNZT-3104
    public static ApiCompanyInvestmentResult listCompanyInvestment(String keyNo, String fundedRatioMin, String fundedRatioMax, String pageIndex, String pageSize){
        return listCompanyInvestment(keyNo, fundedRatioMin, fundedRatioMax, pageIndex, pageSize ,true);
    }

    // updated for v1.7.2 KNZT-3104
    public static ApiCompanyInvestmentResult listCompanyInvestment(String keyNo, String fundedRatioMin, String fundedRatioMax, String pageIndex, String pageSize, boolean needCompanyKey) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("fundedRatioMin", fundedRatioMin);
        param.put("fundedRatioMax", fundedRatioMax);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listCompanyInvestment4Global", "2", JSON.toJSONString(param), needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ApiCompanyInvestmentResult result = jsonObject.toJavaObject(ApiCompanyInvestmentResult.class);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
                for (InvestmentCompTO comp : result.getResultList()) {
                    if (StringUtils.isNotBlank(comp.getStatus())) {
                        // 状态描述转英文
                        comp.setStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(comp.getStatus()));
                    }
                    // 所属省市转英文
                    CompAreaTO area = comp.getArea();
                    if (Objects.nonNull(area)) {
                        comp.setProvinceCity(generateProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                        comp.setProvinceCityEn(generateEnProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                    }
                }
            }
            return result;
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3104
    public static ApiCompanyIndirectInvestmentResult listCompanyIndirectInvestment(String keyNo, String stockPercentMin, String stockPercentMax, String pageIndex, String pageSize){
        return listCompanyIndirectInvestment(keyNo, stockPercentMin, stockPercentMax, pageIndex, pageSize, true);
    }

    // updated for v1.7.2 KNZT-3104
    public static ApiCompanyIndirectInvestmentResult listCompanyIndirectInvestment(String keyNo, String stockPercentMin, String stockPercentMax, String pageIndex, String pageSize, boolean needCompanyKey) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("stockPercentMin", stockPercentMin);
        param.put("stockPercentMax", stockPercentMax);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listCompanyIndirectInvestment4Global", "2", JSON.toJSONString(param), needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ApiCompanyIndirectInvestmentResult result = jsonObject.toJavaObject(ApiCompanyIndirectInvestmentResult.class);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                for (IndirectInvestmentCompTO comp : result.getResultList()) {
                    if (StringUtils.isNotBlank(comp.getShortStatus())) {
                        // 状态描述转英文
                        comp.setShortStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(comp.getShortStatus()));
                    }
                    // 所属省市转英文
                    CompAreaTO area = comp.getArea();
                    if (Objects.nonNull(area)) {
                        comp.setProvinceCity(generateProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                        comp.setProvinceCityEn(generateEnProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                    }
                    // added for v1.6.3 KNZT-2821
                    if (CollectionUtils.isNotEmpty(comp.getPaths())) {
                        comp.getPaths().forEach(v -> v.stream().filter(Objects::nonNull).forEach(vipHoldCompPath -> {
                            transWrapper.buildAndAddEntry(vipHoldCompPath, VipHoldCompPath::setName, VipHoldCompPath::setNameEn, VipHoldCompPath::getName, VipHoldCompPath::getNameEn).withEntity(VipHoldCompPath::getKeyNo, true);
                        }));
                    }
                }
                commonDataService.enPostProcessor(transWrapper);
            }
            return result;
        }
        return null;
    }

    // updated for v1.7.2 KNZT-3104
    public static ApiCompanyHistoryInvestmentResult listCompanyHistoryInvestment(String keyNo, String pageIndex, String pageSize){
        return listCompanyHistoryInvestment(keyNo, pageIndex, pageSize, true);
    }

    // updated for v1.7.2 KNZT-3104
    public static ApiCompanyHistoryInvestmentResult listCompanyHistoryInvestment(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listCompanyHistoryInvestment4Global", "2", JSON.toJSONString(param), needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ApiCompanyHistoryInvestmentResult result = jsonObject.toJavaObject(ApiCompanyHistoryInvestmentResult.class);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
                for (HistoryInvestmentCompTO comp : result.getResultList()) {
                    // 状态描述转英文
                    comp.setStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(comp.getStatus()));
                    // 所属省市转英文
                    CompAreaTO area = comp.getArea();
                    if (Objects.nonNull(area)) {
                        comp.setProvinceCity(generateProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                        comp.setProvinceCityEn(generateEnProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                    }
                }
            }
            return result;
        }
        return null;
    }

    // added for v1.9.4 KNZT-4373
    public static JsonResultList<AllInvestmentTO> listSubsidiaries(String keyNo, String pageIndex, String pageSize) {
        return listSubsidiaries(keyNo, pageIndex, pageSize, true);
    }

    // added for v1.9.4 KNZT-4373
    public static JsonResultList<AllInvestmentTO> listSubsidiaries(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        Map<String, Object> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        param.put("sortField", "percenttotal");
        param.put("isSortAsc", "false");
        String urlParamStr = StringUtils.toUrlParamStr(param);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/listSubsidiaries", "1", urlParamStr, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<AllInvestmentTO> resultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<AllInvestmentTO>>() {
            });
            fill4AllInvestment(resultList);
            return resultList;
        } else if ("openapi".equals(Global.getConfig("application.type"))) { // added for v1.9.7 KNZT-4708
            if (jsonObject == null || !Constants.Result.SUCCESS_STR.equals(jsonObject.getString("status"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        return null;
    }

    // added for v1.9.4 KNZT-4373
    public static JsonResultList<AllInvestmentTO> listAffiliates(String keyNo, String pageIndex, String pageSize) {
        return listAffiliates(keyNo, pageIndex, pageSize, true);
    }

    // added for v1.9.4 KNZT-4373
    public static JsonResultList<AllInvestmentTO> listAffiliates(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        Map<String, Object> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        param.put("sortField", "percenttotal");
        param.put("isSortAsc", "false");
        String urlParamStr = StringUtils.toUrlParamStr(param);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/listAffiliates", "1", urlParamStr, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<AllInvestmentTO> resultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<AllInvestmentTO>>() {
            });
            fill4AllInvestment(resultList);
            return resultList;
        } else if ("openapi".equals(Global.getConfig("application.type"))) { // added for v1.9.7 KNZT-4708
            if (jsonObject == null || !Constants.Result.SUCCESS_STR.equals(jsonObject.getString("status"))) {
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        return null;
    }

    // added for v1.9.4 KNZT-4373
    public static JsonResultList<AllInvestmentTO> listCompanyAllInvestment(String keyNo, String pageIndex, String pageSize, String isValid, String totalPercent, String sortField, Boolean isSortAsc) {
        return listCompanyAllInvestment(keyNo, pageIndex, pageSize, isValid, totalPercent, sortField, isSortAsc, true);
    }

    // added for v1.9.4 KNZT-4373
    public static JsonResultList<AllInvestmentTO> listCompanyAllInvestment(String keyNo, String pageIndex, String pageSize, String isValid, String totalPercent, String sortField, Boolean isSortAsc, boolean needCompanyKey) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        param.put("isValid", isValid);
        param.put("totalPercent", totalPercent);
        param.put("sortField", sortField);
        param.put("isSortAsc", isSortAsc == null ? null : isSortAsc.toString());
        String urlParamStr = StringUtils.toUrlParamStr(param);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/listCompanyAllInvestment4Global", "1", urlParamStr, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<AllInvestmentTO> resultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<AllInvestmentTO>>() {
            });
            fill4AllInvestment(resultList);
            return resultList;
        }
        return null;
    }

    // added for v1.9.4 KNZT-4373
    private static void fill4AllInvestment(JsonResultList<AllInvestmentTO> resultList) {
        if (Objects.nonNull(resultList) && CollectionUtils.isNotEmpty(resultList.getResultList())) {
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            for (AllInvestmentTO to : resultList.getResultList()) {
                // 所属省市转英文
                AllInvestmentTO.Area area = to.getArea();
                if (Objects.nonNull(area)) {
                    to.setProvinceCity(generateProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                    to.setProvinceCityEn(generateEnProvinceCity(area.getProvinceName(), area.getCityName(), area.getCountyName()));
                    // updated for v2.0.2 chenbl KNZT-5360
                    to.setJurisdictionEn(GlobalAreaTranslationUtil.getShortNameEn(area.getProvinceName())); // added for v1.9.5 KNZT-4624
                }
            }
            transWrapper.batchAdd4EntityWithTypeFromKeyNo(resultList.getResultList(), AllInvestmentTO::setName, AllInvestmentTO::setNameEn, AllInvestmentTO::getName, AllInvestmentTO::getNameEn, AllInvestmentTO::getKeyNo);
            commonDataService.enPostProcessor(transWrapper);
        }
    }

    public static RltnOwnshpStrc getStockTz(String keyNo) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("level", "1");
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/cms_stocktz", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            StocktzApiResult apiResult = jsonObject.toJavaObject(StocktzApiResult.class);
            if (Objects.nonNull(apiResult) && Objects.nonNull(apiResult.getResult())) {
                RltnOwnshpStrc result = apiResult.getResult();
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                if (CollectionUtils.isNotEmpty(result.getEquityShareDetail())) {
                    result.getEquityShareDetail().forEach(obj -> transWrapper.buildAndAddEntry(obj, RltnOwnshpStrcDtlList::setName, RltnOwnshpStrcDtlList::setNameEn, RltnOwnshpStrcDtlList::getName, RltnOwnshpStrcDtlList::getNameEn).withEntity(RltnOwnshpStrcDtlList::getKeyNo, true));
                }
                commonDataService.enPostProcessor(transWrapper);
                return result;
            }
        }
        return null;
    }

    public static RltnOwnshpStrc getStockGdMix(String keyNo) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/cms_stockgdmix", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            StockgdmixApiResult apiResult = jsonObject.toJavaObject(StockgdmixApiResult.class);
            if (Objects.nonNull(apiResult) && Objects.nonNull(apiResult.getResult())) {
                RltnOwnshpStrc result = apiResult.getResult();
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                if (CollectionUtils.isNotEmpty(result.getEquityShareDetail())) {
                    result.getEquityShareDetail().forEach(obj -> transWrapper.buildAndAddEntry(obj, RltnOwnshpStrcDtlList::setName, RltnOwnshpStrcDtlList::setNameEn, RltnOwnshpStrcDtlList::getName, RltnOwnshpStrcDtlList::getNameEn).withEntity(RltnOwnshpStrcDtlList::getKeyNo, true));
                }
                commonDataService.enPostProcessor(transWrapper);
                return result;
            }
        }
        return null;
    }

    public static GraphOverviewResult getEnterpriseOverview(String keyNo, String corpType) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("corpType", corpType);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/enterpriseoverview", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            EnterpriseoverviewApiResult apiResult = jsonObject.toJavaObject(EnterpriseoverviewApiResult.class);
            if (Objects.nonNull(apiResult) && Objects.nonNull(apiResult.getResult())) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                // 状态
                GraphOverviewResult result = apiResult.getResult();
                GraphOverviewTag graphOverviewTag = Optional.ofNullable(result.getTags())
                        .orElse(new ArrayList<>(0))
                        .stream().filter(tagItem -> 903 == tagItem.getType())
                        .findFirst().orElse(null);
                if (graphOverviewTag != null) {
                    // updated for v1.6.3 KNZT-2872
                    if (keyNo.startsWith(Constants.CorpTypePrefix.FOREIGN_CORPORATE_PREFIX)) {
                        graphOverviewTag.setName(StringUtils.upperFirst(graphOverviewTag.getName()));
                    } else {
                        graphOverviewTag.setNameEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(graphOverviewTag.getName()));
                    }
                }
                transWrapper.buildAndAddEntry(result, GraphOverviewResult::setName, GraphOverviewResult::setEnName, GraphOverviewResult::getName, GraphOverviewResult::getEnName).withEntity(o -> keyNo, true);
                // 控制人
                GraphOverviewOper oper = result.getOper();
                transWrapper.buildAndAddEntry(oper, GraphOverviewOper::setName, GraphOverviewOper::setNameEn, GraphOverviewOper::getName, GraphOverviewOper::getNameEn).withEntity(GraphOverviewOper::getKeyNo, true);
                commonDataService.enPostProcessor(transWrapper);
                return result;
            }
        }
        return null;
    }


    /**
     * added for v1.3.7 KNZT-1783
     * 全球参控股企业
     */
    public static ApiOverseaRelatedCompResult listOverSeaRelatedComp(String keyNo, String pageIndex, String pageSize) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listOverSeaRelatedComp", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            ApiOverseaRelatedCompResult result = jsonObject.toJavaObject(ApiOverseaRelatedCompResult.class);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
                for (ApiOverseaRelatedCompTO comp : result.getResultList()) {
//                    if (StringUtils.isNotBlank(comp.getRelatedCompNameEn()) && StringUtils.isNotBlank(comp.getRelatedCompName())) {
//                        String enLower = comp.getRelatedCompNameEn().toLowerCase();
//                        String cnLower = comp.getRelatedCompName().toLowerCase();
//                        if (StringUtils.contains(enLower, cnLower) || StringUtils.contains(cnLower, enLower)) {
//                            comp.setRelatedCompNameEn(null);
//                        }
//                    }
                    // 翻译参控关系
                    comp.setRelationShipDescEn(CompRelationTypeEnum.translateEnByCn(comp.getRelationShipDesc()));
                    // 翻译所属地区
                    if (StringUtils.isNotBlank(comp.getRegisterAddress())) {
                        // updated for v1.5.0 KNZT-2216 增加区域风险等级
                        // updated for v1.9.1 KNZT-4101 映射调整
                        GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(comp.getRegisterAddress());
                        if (Objects.nonNull(areaEnum)) {
                            comp.setRegisterAddressEn(areaEnum.getShortNameEn());
                            comp.setRegisterAddressRiskLevel(GlobalAreaEnum.judgeRiskLevel(areaEnum));
                        }
                        transWrapper.buildAndAddEntry(comp, ApiOverseaRelatedCompTO::setRegisterAddress, ApiOverseaRelatedCompTO::setRegisterAddressEn, ApiOverseaRelatedCompTO::getRegisterAddress, ApiOverseaRelatedCompTO::getRegisterAddressEn);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(result.getResultList())) {
                result.getResultList().forEach(obj -> transWrapper.buildAndAddEntry(obj, ApiOverseaRelatedCompTO::setRelatedCompName, ApiOverseaRelatedCompTO::setRelatedCompNameEn, ApiOverseaRelatedCompTO::getRelatedCompName, ApiOverseaRelatedCompTO::getRelatedCompNameEn).withEntity(ApiOverseaRelatedCompTO::getRelatedCompKeyNo, true));
            }
            commonDataService.enPostProcessor(transWrapper);
            return result;
        }
        return null;
    }

    /**
     * added for v1.3.7 KNZT-1783
     * 全球参控股股东
     */
    public static ApiOverseaCorpPartnerResult listOverseaPartner(String keyNo, String pageIndex, String pageSize) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", pageSize);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listOverseaPartner", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ApiOverseaCorpPartnerResult result = jsonObject.toJavaObject(ApiOverseaCorpPartnerResult.class);
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
                for (ApiOverseaCorpPartnerTO partner : result.getResultList()) {
                    // 翻译所属地区
                    if (StringUtils.isNotBlank(partner.getArea())) {
                        // updated for v1.5.0 KNZT-2216 增加区域风险等级
                        // updated for v1.9.1 KNZT-4101 映射调整
                        GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(partner.getArea());
                        if (Objects.nonNull(areaEnum)) {
                            partner.setAreaEn(areaEnum.getShortNameEn());
                            partner.setAreaRiskLevel(GlobalAreaEnum.judgeRiskLevel(areaEnum));
                        }
                        transWrapper.buildAndAddEntry(partner, ApiOverseaCorpPartnerTO::setArea, ApiOverseaCorpPartnerTO::setAreaEn, ApiOverseaCorpPartnerTO::getArea, ApiOverseaCorpPartnerTO::getAreaEn);
                    }
                }
                if (CollectionUtils.isNotEmpty(result.getResultList())) {
                    result.getResultList().forEach(obj -> transWrapper.buildAndAddEntry(obj, ApiOverseaCorpPartnerTO::setStockName, ApiOverseaCorpPartnerTO::setStockNameEn, ApiOverseaCorpPartnerTO::getStockName, ApiOverseaCorpPartnerTO::getStockNameEn).withEntity(ApiOverseaCorpPartnerTO::getKeyNo, true));
                }
            }
            commonDataService.enPostProcessor(transWrapper);
            return result;
        }
        return null;
    }


    /**
     * added v1.3.4 KNZT-1517
     * 获取人员概要信息
     */
    public static ApiGraphPersonOverviewTO getPersonOverview(String keyNo) {
        Map<String, String> param = new HashMap<>();
        param.put("id", keyNo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getPersonOverview", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (Objects.nonNull(jsonObject)
                && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            ApiGraphPersonOverviewTO result = jsonObject.getJSONObject("result").toJavaObject(ApiGraphPersonOverviewTO.class);
            if (Objects.isNull(result)) {
                return null;
            }
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            transWrapper.buildAndAddEntry(result, ApiGraphPersonOverviewTO::setName, ApiGraphPersonOverviewTO::setNameEn, ApiGraphPersonOverviewTO::getName, ApiGraphPersonOverviewTO::getNameEn).withEntity(o -> keyNo, true);
            result.setJobTitleEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(result.getJobTitle()));
            commonDataService.enPostProcessor(transWrapper);
            return result;
        }
        return null;
    }


    /**
     * added v1.3.4 KNZT-1517
     * 获取关系图谱-企业维度信息
     */
    public static ApiCompGraphTO getEnterpriseGraph(String keyNo) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getEnterpriseGraph", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (Objects.nonNull(jsonObject)
                && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            ApiCompGraphTO graphTO = jsonObject.getJSONObject("result").toJavaObject(ApiCompGraphTO.class);
            return injectGraphEn(graphTO);
        } else {
            // removed for v1.8.1 KNZT-3671
            // logger.error("/innerslb/global/getEnterpriseGraph result is null, request : {}. response : {}", keyNo, jsonObject);
            return null;
        }
    }



    /**
     * added v1.3.4 KNZT-1517
     * 获取关系图谱-人员维度信息
     */
    public static ApiCompGraphTO getPersonGraph(String keyNo) {
        Map<String, String> param = new HashMap<>();
        param.put("keyNo", keyNo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getPersonGraph", "2", JSON.toJSONString(param));
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (Objects.nonNull(jsonObject)
                && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            ApiCompGraphTO graphTO = jsonObject.getJSONObject("result").toJavaObject(ApiCompGraphTO.class);
            return injectGraphEn(graphTO);
        } else {
            // removed for v1.8.1 KNZT-3671
            // logger.error("/innerslb/global/getPersonGraph result is null, request : {}. response : {}", keyNo, jsonObject);
            return null;
        }
    }



    /**
     * added v1.3.4 KNZT-1517
     * 注入关系图谱的翻译
     */
    private static ApiCompGraphTO injectGraphEn(ApiCompGraphTO graphTO) {
        if (Objects.isNull(graphTO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(graphTO.getChildren())) {
            return graphTO;
        }
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
        transWrapper.buildAndAddEntry(graphTO, ApiCompGraphTO::setName, ApiCompGraphTO::setNameEn, ApiCompGraphTO::getName, ApiCompGraphTO::getNameEn).withEntity(ApiCompGraphTO::getId, true);
        Set<String> showNodeTypeList = CompGraphItemEnum.getGraphShowNodeType();
        List<ApiCompGraphTO> subitemList = graphTO.getChildren().stream()
                .filter(item -> showNodeTypeList.contains(item.getNodeType()))
                .collect(Collectors.toList());
        for (ApiCompGraphTO item : subitemList) {
            item.setNameEn(CompGraphItemEnum.getEnByCn(item.getName(), item.getNodeType()));
            if (CollectionUtils.isEmpty(item.getChildren())) {
                continue;
            }
            for (ApiCompGraphTO child : item.getChildren()) {
                if (StringUtils.isNotBlank(child.getJob())) {
                    child.setJobEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(child.getJob()));
                }
            }
        }
        List<ApiCompGraphTO> apiCompGraphTOS = MappingUtils.flattenAndGetList(subitemList, ApiCompGraphTO::getChildren);
        if (CollectionUtils.isNotEmpty(apiCompGraphTOS)) {
            apiCompGraphTOS.forEach(obj -> transWrapper.buildAndAddEntry(obj, ApiCompGraphTO::setName, ApiCompGraphTO::setNameEn, ApiCompGraphTO::getName, ApiCompGraphTO::getNameEn).withEntity(ApiCompGraphTO::getId, true));
        }
        graphTO.setChildren(subitemList);
        commonDataService.enPostProcessor(transWrapper);
        return graphTO;
    }
    
    /**
     * added for v1.5.1 KNZT-2258
     * 获取实际控制人-最终受益股份
     */
    public static EnterpriseBeneficiaryGraphTO getEnterpriseBeneficiaryGraph(String keyNo, String partnerId, String partnerName) {
        List<String> paramList = new ArrayList<>();
        paramList.add("keyNo=" + keyNo);
        if (StringUtils.isNotBlank(partnerId)) {
            paramList.add("partnerId=" + partnerId);
        }
        if (StringUtils.isNotBlank(partnerName)) {
            paramList.add("partnerName=" + partnerName);
        }
        String param = StringUtils.join(paramList, "&");
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getEnterpriseBeneficiaryGraph", "2", param);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (Objects.nonNull(jsonObject)
                && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && Objects.nonNull(jsonObject.getJSONObject("result"))) {
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            EnterpriseBeneficiaryGraphTO graphTO = jsonObject.getJSONObject("result").toJavaObject(EnterpriseBeneficiaryGraphTO.class);
            transWrapper.buildAndAddEntry(graphTO, EnterpriseBeneficiaryGraphTO::setName, EnterpriseBeneficiaryGraphTO::setNameEn, EnterpriseBeneficiaryGraphTO::getName, EnterpriseBeneficiaryGraphTO::getNameEn).withEntity(EnterpriseBeneficiaryGraphTO::getKeyNo, true);
            if (CollectionUtils.isNotEmpty(graphTO.getPaths())) {
                graphTO.getPaths().forEach(obj -> transWrapper.buildAndAddEntry(obj, EnterpriseBeneficiaryGraphPathsTO::setName, EnterpriseBeneficiaryGraphPathsTO::setNameEn, EnterpriseBeneficiaryGraphPathsTO::getName, EnterpriseBeneficiaryGraphPathsTO::getNameEn).withEntity(EnterpriseBeneficiaryGraphPathsTO::getKeyNo, true));
            }
            List<List<EnterpriseBeneficiaryGraphPathsItemTO>> paths = MappingUtils.flattenAndGetList(graphTO.getPaths(), EnterpriseBeneficiaryGraphPathsTO::getPaths);
            List<EnterpriseBeneficiaryGraphPathsItemTO> pathList = paths.stream().flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pathList)) {
                pathList.forEach(obj -> transWrapper.buildAndAddEntry(obj, EnterpriseBeneficiaryGraphPathsItemTO::setName, EnterpriseBeneficiaryGraphPathsItemTO::setNameEn, EnterpriseBeneficiaryGraphPathsItemTO::getName, EnterpriseBeneficiaryGraphPathsItemTO::getNameEn).withEntity(EnterpriseBeneficiaryGraphPathsItemTO::getKeyNo, true));
            }
            commonDataService.enPostProcessor(transWrapper);
            return graphTO;
        } else {
            // removed for v1.8.1 KNZT-3671
            // logger.error("/innerslb/global/getEnterpriseBeneficiaryGraph result is null, request : {}. response : {}", keyNo, jsonObject);
            return null;
        }
    }

//    // updated for v1.7.2 KNZT-3104
//    public static EquityShareChangesTO getEquityShareChangesV2(String keyNo) {
//        return getEquityShareChangesV2(keyNo, true);
//    }
//
//    /**
//     * added for v1.5.3 KNZT-2383
//     * updated for v1.7.2 KNZT-3104
//     * 获取股东镜像
//     */
//    public static EquityShareChangesTO getEquityShareChangesV2(String keyNo, boolean needCompanyKey) {
//        List<String> paramList = new ArrayList<>();
//        paramList.add("keyNo=" + keyNo);
//        String param = StringUtils.join(paramList, "&");
//        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getEquityShareChangesV2", "2", param, needCompanyKey);
//        JSONObject jsonObject = JsonUtils.parseObject(resp);
//        if (Objects.nonNull(jsonObject)
//                && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
//                && Objects.nonNull(jsonObject.getJSONObject("result"))) {
//            EquityShareChangesTO equityShareChangesTO = jsonObject.getJSONObject("result").toJavaObject(EquityShareChangesTO.class);
//            if (CollectionUtils.isNotEmpty(equityShareChangesTO.getList())) {
//                for (EquityShareChangesDetailTO equityShareChangesDetailTO : equityShareChangesTO.getList()) {
//                    equityShareChangesDetailTO.setNameEn(getEnNameByCnNameEnd(equityShareChangesDetailTO.getKeyNo(), equityShareChangesDetailTO.getName(), equityShareChangesDetailTO.getNameEn()));
//                }
//            }
//            return equityShareChangesTO;
//        } else {
//            // removed for v1.8.1 KNZT-3671
//            // logger.error("/innerslb/global/getEquityShareChangesV2 result is null, request : {}. response : {}", keyNo, jsonObject);
//            return null;
//        }
//    }

    /**
     * added for v2.0.6 chenbl KNZT-5555
     * 获取工商历史股东
     */
    public static List<AnnualReportAllPartnersTO> getAnnualReportAllPartners(String keyNo, Integer endYear, boolean needCompanyKey) {
        List<String> paramList = new ArrayList<>();
        paramList.add("keyNo=" + keyNo);
        paramList.add("endYear=" + endYear);
        String param = StringUtils.join(paramList, "&");
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getAnnualReportAllPartners", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (Objects.nonNull(jsonObject)
                && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)) {
            JSONArray result = jsonObject.getJSONArray("resultList");
            if (CollectionUtils.isNotEmpty(result)) {
                List<AnnualReportAllPartnersTO> partnersTOS = result.toJavaList(AnnualReportAllPartnersTO.class);
                return partnersTOS;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * added for  v1.5.3 KNZT-2401
     * 查询高管合作伙伴链路
     * @param personId  人员id
     * @param connectPersonId 关联人员id
     * @param compKeyNo  代表企业keyNo
     * @return PersonPartnerResult
     */
    public static List<RelationShipsData> getPersonPartnerPath(String personId, String connectPersonId, String compKeyNo) {
        List<String> paramList = Lists.newArrayList();
        paramList.add("personId=" + personId);
        if (StringUtils.isNotBlank(connectPersonId)) {
            paramList.add("connectPersonId=" + connectPersonId);
        }
        if (StringUtils.isNotBlank(compKeyNo)) {
            paramList.add("compKeyNo=" + compKeyNo);
        }
        String param = StringUtils.join(paramList, "&");
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getPersonPartnerPath", "2", param);

        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (Objects.nonNull(jsonObject) && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR)
                && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            PersonPartnerPathResult pathResult = jsonObject.toJavaObject(PersonPartnerPathResult.class);
            if (Objects.nonNull(pathResult) && CollectionUtils.isNotEmpty(pathResult.getResultList())) {
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                for (RelationShipsData data : pathResult.getResultList()) {
                    RelationShipsGraph graph = data.getGraph();
                    if (Objects.isNull(graph)) {
                        continue;
                    }
                    // 节点翻译处理
                    if (CollectionUtils.isNotEmpty(graph.getNodes())) {
                        for (RelationShipsNodes node : graph.getNodes()) {
                            RelationShipsProperties properties = node.getProperties();
                            if (Objects.nonNull(properties)){
                                transWrapper.buildAndAddEntry(properties, RelationShipsProperties::setName, RelationShipsProperties::setNameEn, RelationShipsProperties::getName, RelationShipsProperties::getNameEn).withEntity(RelationShipsProperties::getKeyNo, true);
                            }
                        }
                    }
                    // 关系翻译处理
                    if (CollectionUtils.isNotEmpty(graph.getRelationships())) {
                        for (RelationShipsChild relation : graph.getRelationships()) {
                            RelationShipsPropertiesChild properties = relation.getProperties();
                            if (Objects.nonNull(properties) && StringUtils.isNotBlank(properties.getInLineText())) {
                                String textEn;
                                String text = properties.getInLineText();
                                if (text.contains("%")) {
                                    textEn = text.replace("持股", "Shareholding:");
                                    textEn = text.replace("投资", "Shareholding:");
                                } else {
                                    String[] splitPosition = StringUtils.split(text, ",");
                                    StringBuilder textEnBuilder = new StringBuilder();
                                    for (int i = 0; i < splitPosition.length; i++) {
                                        String subPositionEn = PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(splitPosition[i]);
                                        textEnBuilder.append(subPositionEn);
                                        if (i != splitPosition.length - 1) {
                                            textEnBuilder.append(",");
                                        }
                                    }
                                    textEn = String.valueOf(textEnBuilder);
                                }
                                properties.setInLineTextEn(textEn);
                            }
                        }
                    }
                }
                List<RelationShipsGraph> graphs = MappingUtils.getList(pathResult.getResultList(), RelationShipsData::getGraph);
                List<RelationShipsNodes> nodes = MappingUtils.flattenAndGetList(graphs, RelationShipsGraph::getNodes);
                List<RelationShipsProperties> properties = MappingUtils.getList(nodes, RelationShipsNodes::getProperties);
                transWrapper.batchAdd4EntityWithTypeFromKeyNo(properties, RelationShipsProperties::setName, RelationShipsProperties::setNameEn, RelationShipsProperties::getName, RelationShipsProperties::getNameEn, RelationShipsProperties::getKeyNo);
                commonDataService.enPostProcessor(transWrapper);
            }
            return pathResult.getResultList();
        } else {
            // removed for v1.8.1 KNZT-3671
            // logger.error("/innerslb/global/getPersonPartnerPath result is null, request : {}. response : {}", personId, jsonObject);
            return null;
        }
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<AdministrativePunishmentTO> getRiskPenaltySumList(String keyNo, String pageIndex, String pageSize) {
        return getRiskPenaltySumList(keyNo, pageIndex, pageSize, true);
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<AdministrativePunishmentTO> getRiskPenaltySumList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getRiskPenaltySumList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<AdministrativePunishmentTO> resultPage = jsonObject.toJavaObject(new TypeReference<JsonResultList<AdministrativePunishmentTO>>() {
            });
            if (resultPage != null) {
                List<AdministrativePunishmentTO> resultList = resultPage.getResultList();
                if (CollectionUtils.isNotEmpty(resultList)) {
//                    List<GetEnglishTextBatchParam> textBatchParams = IntStream.range(0, resultList.size())
//                            .mapToObj(i -> {
//                                AdministrativePunishmentTO toItem = resultList.get(i);
//                                return new GetEnglishTextBatchParam(String.valueOf(i), toItem.getPunishReason(), keyNo, null, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
//                            }).collect(Collectors.toList());
//                    Map<String, String> index2EnMap = translaterService.getEnglishTextBatch(textBatchParams);
                    for (int i = 0; i < resultList.size(); i++) {
                        AdministrativePunishmentTO toItem = resultList.get(i);
//                        toItem.setPunishReasonEn(index2EnMap.get(String.valueOf(i)));
                        // added for v1.8.5 KNZT-3846
                        if (CollectionUtils.isNotEmpty(toItem.getPunishCategoryObjs())) {
                            toItem.getPunishCategoryObjs().forEach(obj -> {
                                obj.setValueEn(AdministrativePunishmentTO.PunishCategoryEnum.getDescEnByCode(obj.getKey()));
                            });
                        }
                        toItem.setPunishOfficeEn(translateRegistrationAuthority(toItem.getPunishOffice()));
                    }
                }
            }
            return resultPage;
        }
        return null;
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<CorpExceptionInfoTO> getExceptionList(String keyNo, String pageIndex, String pageSize) {
        return getExceptionList(keyNo, pageIndex, pageSize, true);
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<CorpExceptionInfoTO> getExceptionList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getExceptionList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<CorpExceptionInfoTO> resultPage = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpExceptionInfoTO>>() {
            });
            if(resultPage != null) {
                List<CorpExceptionInfoTO> resultList = resultPage.getResultList();
                if (CollectionUtils.isNotEmpty(resultList)) {
                    // removed for v1.8.5 KNZT-3846
                    /*List<GetEnglishTextBatchParam> textBatchParams = IntStream.range(0, resultList.size())
                            .mapToObj(i -> {
                                CorpExceptionInfoTO toItem = resultList.get(i);
                                return new GetEnglishTextBatchParam(String.valueOf(i), toItem.getAddReason(), keyNo, null, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
                            }).collect(Collectors.toList());
                    Map<String, String> index2EnMap = translaterService.getEnglishTextBatch(textBatchParams);*/
                    for (int i = 0; i < resultList.size(); i++) {
                        CorpExceptionInfoTO toItem = resultList.get(i);
                        // removed for v1.8.5 KNZT-3846
                        // toItem.setAddReasonEn(index2EnMap.get(String.valueOf(i)));
                        // added for v1.8.5 KNZT-3846
                        CorpExceptionInfoTO.CorpExceptionReasonStandard addReasonStandard = toItem.getAddReasonStandard();
                        if (Objects.nonNull(addReasonStandard)) {
                            addReasonStandard.setValueEn(CorpExceptionInfoTO.AddReasonStandardEnum.getDescEnByCode(addReasonStandard.getKey()));
                        }
                        toItem.setDecisionOfficeEn(translateRegistrationAuthority(toItem.getDecisionOffice()));
                    }
                }
            }
            return resultPage;
        }
        return null;
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<CorpSeriousViolationTO> getSeriousViolationList(String keyNo, String pageIndex, String pageSize) {
        return getSeriousViolationList(keyNo, pageIndex, pageSize, true);
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<CorpSeriousViolationTO> getSeriousViolationList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getSeriousViolationList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<CorpSeriousViolationTO> resultPage = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpSeriousViolationTO>>() {
            });
            if(resultPage != null) {
                List<CorpSeriousViolationTO> resultList = resultPage.getResultList();
                if (CollectionUtils.isNotEmpty(resultList)) {
                    // removed for v1.8.5 KNZT-3846
                    /*List<GetEnglishTextBatchParam> textBatchParams = IntStream.range(0, resultList.size())
                            .mapToObj(i -> {
                                CorpSeriousViolationTO toItem = resultList.get(i);
                                return new GetEnglishTextBatchParam(String.valueOf(i), toItem.getAddReason(), keyNo, null, SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
                            }).collect(Collectors.toList());
                    Map<String, String> index2EnMap = translaterService.getEnglishTextBatch(textBatchParams);*/
                    for (int i = 0; i < resultList.size(); i++) {
                        CorpSeriousViolationTO toItem = resultList.get(i);
                        // removed for v1.8.5 KNZT-3846
                        // toItem.setAddReasonEn(index2EnMap.get(String.valueOf(i)));
                        // added for v1.8.5 KNZT-3846
                        CorpSeriousViolationTO.AddReasonStandard addReasonStandard = toItem.getAddReasonStandard();
                        if (Objects.nonNull(addReasonStandard)) {
                            addReasonStandard.setValueEn(CorpSeriousViolationTO.AddReasonStandardEnum.getDescEnByCode(addReasonStandard.getKey()));
                        }
                        toItem.setAddOfficeEn(translateRegistrationAuthority(toItem.getAddOffice()));
                    }
                }
            }
            return resultPage;
        }
        return null;
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<CorpPledgeV2TO> getPledgeList(String keyNo, String pageIndex, String pageSize) {
        return getPledgeList(keyNo, pageIndex, pageSize, true);
    }

    // added for v1.8.1 KNZT-3610
    public static JsonResultList<CorpPledgeV2TO> getPledgeList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        String param = "keyNo=" + keyNo + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getPledgeList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<CorpPledgeV2TO> resultPage = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpPledgeV2TO>>() {
            });
            if (resultPage != null) {
                List<CorpPledgeV2TO> resultList = resultPage.getResultList();
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
                if (CollectionUtils.isNotEmpty(resultList)) {
                    for (CorpPledgeV2TO corpPledgeV2TO : resultList) {
                        corpPledgeV2TO.setStatusEn(CorpPledgeV2TO.StatusEnum.getEnByCn(corpPledgeV2TO.getStatus()));
                    }
                    List<CorpPledgeV2TO.CorpPledgePledgorInfo> pledgorInfos = MappingUtils.flattenAndGetList(resultList, CorpPledgeV2TO::getPledgorInfo);
                    transWrapper.batchAdd4EntityWithTypeFromKeyNo(pledgorInfos, CorpPledgeV2TO.CorpPledgePledgorInfo::setName, CorpPledgeV2TO.CorpPledgePledgorInfo::setNameEn, CorpPledgeV2TO.CorpPledgePledgorInfo::getName, CorpPledgeV2TO.CorpPledgePledgorInfo::getNameEn, CorpPledgeV2TO.CorpPledgePledgorInfo::getKeyNo);
                    List<CorpPledgeV2TO.CorpRelatedCompanyInfo> relatedCompanyInfos = MappingUtils.getList(resultList, CorpPledgeV2TO::getRelatedCompanyInfo);
                    transWrapper.batchAdd4EntityWithTypeFromKeyNo(relatedCompanyInfos, CorpPledgeV2TO.CorpRelatedCompanyInfo::setName, CorpPledgeV2TO.CorpRelatedCompanyInfo::setNameEn, CorpPledgeV2TO.CorpRelatedCompanyInfo::getName, CorpPledgeV2TO.CorpRelatedCompanyInfo::getNameEn, CorpPledgeV2TO.CorpRelatedCompanyInfo::getKeyNo);
                    List<CorpPledgeV2TO.CorpPledgePledgeeInfo> pledgeeInfos = MappingUtils.flattenAndGetList(resultList, CorpPledgeV2TO::getPledgeeInfo);
                    transWrapper.batchAdd4EntityWithTypeFromKeyNo(pledgeeInfos, CorpPledgeV2TO.CorpPledgePledgeeInfo::setName, CorpPledgeV2TO.CorpPledgePledgeeInfo::setNameEn, CorpPledgeV2TO.CorpPledgePledgeeInfo::getName, CorpPledgeV2TO.CorpPledgePledgeeInfo::getNameEn, CorpPledgeV2TO.CorpPledgePledgeeInfo::getKeyNo);
                }
                commonDataService.enPostProcessor(transWrapper);
            }
            return resultPage;
        }
        return null;
    }

    // added for v1.9.0 KNZT-4145
    public static JsonResultList<CourtCaseTO> getCourtCaseList(CourtCaseForm form) {
        return getCourtCaseList(form, true);
    }

    // added for v1.9.0 KNZT-4145
    public static JsonResultList<CourtCaseTO> getCourtCaseList(CourtCaseForm form, boolean needCompanyKey) {
        if (StringUtils.isNotBlank(form.getRoleType())) {
            Set<String> roleTypes = Arrays.stream(form.getRoleType().split(",")).filter(LitigationTranslationUtil.RoleEnum.getCodes()::contains).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(roleTypes)) {
                // 无效的参数
                return null;
            } else {
                form.setRoleType(StringUtils.join(roleTypes, ","));
            }
        } else {
            form.setRoleType(StringUtils.join(LitigationTranslationUtil.RoleEnum.getCodes(), ","));
        }
        // 固定取三年内的
        Date now = new Date();
        Date threeYearsAgo = DateUtils.addYears(now, -3);
        form.setStartTime(String.valueOf(DateUtils.toDateSecond(threeYearsAgo)));
        form.setEndTime(String.valueOf(DateUtils.toDateSecond(now)));
        JSONObject paramJo = JsonUtils.parseObject(JSON.toJSONString(form));
        String param = StringUtils.toUrlParamStr(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/innerslb/global/getCourtCaseList", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<CourtCaseTO> resultPage = jsonObject.toJavaObject(new TypeReference<JsonResultList<CourtCaseTO>>() {
            });
            if (resultPage != null) {
                List<CourtCaseTO> resultList = resultPage.getResultList();
                if (CollectionUtils.isNotEmpty(resultList)) {
                    for (CourtCaseTO courtCaseTO : resultList) {
                        String parties = null;
                        String partiesEn = null;
                        List<CourtCaseTO.CaseRoleObj> plaintiffObjs = getPlaintiffObjs(courtCaseTO.getCaseRoleSearchObjs(), courtCaseTO.getProsecutorNames());
                        List<CourtCaseTO.CaseRoleObj> defendantObjs = getDefendantObjs(courtCaseTO.getCaseRoleSearchObjs(), courtCaseTO.getDefendantNames());
                        String plaintiffsStrCn = getCaseRoleObjsStrWithLimit4Cn(plaintiffObjs);
                        String plaintiffsStrEn = getCaseRoleObjsStrWithLimit4En(plaintiffObjs);
                        String defendantsStrCn = getCaseRoleObjsStrWithLimit4Cn(defendantObjs);
                        String defendantsStrEn = getCaseRoleObjsStrWithLimit4En(defendantObjs);
                        if (StringUtils.isNotBlank(plaintiffsStrCn) && StringUtils.isNotBlank(defendantsStrCn)) {
                            parties = String.format("%s与%s", plaintiffsStrCn, defendantsStrCn);
                            partiesEn = String.format("%s v. %s", plaintiffsStrEn, defendantsStrEn);
                        } else if (StringUtils.isNotBlank(plaintiffsStrCn)) {
                            parties = plaintiffsStrCn;
                            partiesEn = Constants.CourtCase.ONLY_ONE_PREFIX_EN + plaintiffsStrEn;
                        } else if (StringUtils.isNotBlank(defendantsStrCn)) {
                            parties = defendantsStrCn;
                            partiesEn = Constants.CourtCase.ONLY_ONE_PREFIX_EN + defendantsStrEn;
                        }
                        courtCaseTO.setParties(parties);
                        courtCaseTO.setPartiesEn(partiesEn);
                        courtCaseTO.setCaseRoleCodesOfCurr(getCaseRoleCodesOfCurr(form.getKeyNo(), courtCaseTO.getProsecutorNames(), courtCaseTO.getDefendantNames())); // added for v2.0.8 chenbl KNZT-5837
                        courtCaseTO.setCaseReasonEn(LitigationTranslationUtil.getCauseEnByCn(courtCaseTO.getCaseReason()));
                        courtCaseTO.setCaseTypeMain2En(LitigationTranslationUtil.getTypeEnByCode(courtCaseTO.getCaseTypeMain2()));
                        List<CourtCaseTO.TrailRoundDetail> trailRoundDetails = courtCaseTO.getTrailRoundDetails();
                        if (CollectionUtils.isNotEmpty(trailRoundDetails)) {
                            for (CourtCaseTO.TrailRoundDetail trailRoundDetail : trailRoundDetails) {
                                trailRoundDetail.setTrialRoundEn(LitigationTranslationUtil.getProceedingEnByCn(trailRoundDetail.getTrialRound()));
                                // added for v2.0.8 chenbl KNZT-5837
                                if (CollectionUtils.isNotEmpty(trailRoundDetail.getCaseRole())) {
                                    CourtCaseTO.CaseRole caseRoleObjOfCurr = trailRoundDetail.getCaseRole().stream().filter(caseRole -> StringUtils.equals(form.getKeyNo(), caseRole.getKeyNo())).findFirst().orElse(null);
                                    if (caseRoleObjOfCurr != null) {
                                        trailRoundDetail.setCaseRoleOfCurr(caseRoleObjOfCurr.getRole());
                                        trailRoundDetail.setCaseRoleEnOfCurr(getCaseRoleEn(trailRoundDetail.getCaseRoleOfCurr()));
                                    }
                                }
                            }
                        }
                    }
                }

                List<GroupItem> groupItems = resultPage.getGroupItems();
                if (CollectionUtils.isNotEmpty(groupItems)) {
                    // 处理案件身份
                    groupItems.stream().filter(groupItem -> StringUtils.equals(groupItem.getKey(), "Role")).findFirst().ifPresent(groupItem -> {
                        List<Item> items = groupItem.getItems();
                        if (CollectionUtils.isNotEmpty(items)) {
                            Iterator<Item> iterator = items.iterator();
                            while (iterator.hasNext()) {
                                Item item = iterator.next();
                                if (!LitigationTranslationUtil.RoleEnum.getCodes().contains(item.getValue())) {
                                    iterator.remove();
                                }
                                item.setDesc(LitigationTranslationUtil.RoleEnum.getEnByCode(item.getValue()));
                            }
                        }
                    });

                    // 处理案件类型
                    groupItems.stream().filter(groupItem -> StringUtils.equals(groupItem.getKey(), "caseTypeMain2")).findFirst().ifPresent(groupItem -> {
                        List<Item> items = groupItem.getItems();
                        if (CollectionUtils.isNotEmpty(items)) {
                            items.forEach(item -> item.setDesc(LitigationTranslationUtil.getTypeEnByCode(item.getValue())));
                        }
                    });

                    // 处理案件类型
                    groupItems.stream().filter(groupItem -> StringUtils.equals(groupItem.getKey(), "Reason")).findFirst().ifPresent(groupItem -> {
                        List<Item> items = groupItem.getItems();
                        if (CollectionUtils.isNotEmpty(items)) {
                            items.forEach(item -> item.setDesc(LitigationTranslationUtil.getCauseEnByCn(item.getValue())));
                        }
                    });
                }
            }
            return resultPage;
        }
        return null;
    }

    private static List<CourtCaseTO.CaseRoleObj> getPlaintiffObjs(List<CourtCaseTO.CaseRoleObj> caseRoleSearchObjs, List<String> prosecutorNames) {
        if (CollectionUtils.isEmpty(caseRoleSearchObjs) || CollectionUtils.isEmpty(prosecutorNames)) {
            return null;
        }
        return caseRoleSearchObjs.stream()
                .filter(caseRoleObj -> prosecutorNames.contains(caseRoleObj.getN()) || prosecutorNames.contains(caseRoleObj.getP()))
                .collect(Collectors.toList());
    }

    private static List<CourtCaseTO.CaseRoleObj> getDefendantObjs(List<CourtCaseTO.CaseRoleObj> caseRoleSearchObjs, List<String> defendantNames) {
        if (CollectionUtils.isEmpty(caseRoleSearchObjs) || CollectionUtils.isEmpty(defendantNames)) {
            return null;
        }
        return caseRoleSearchObjs.stream()
                .filter(caseRoleObj -> defendantNames.contains(caseRoleObj.getN()) || defendantNames.contains(caseRoleObj.getP()))
                .collect(Collectors.toList());
    }

    private static String getCaseRoleObjsStrWithLimit4En(List<CourtCaseTO.CaseRoleObj> caseRoleObjs) {
        if (CollectionUtils.isEmpty(caseRoleObjs)) {
            return null;
        }
        caseRoleObjs = caseRoleObjs.stream().limit(Constants.CourtCase.CASE_ROLE_LIMIT).collect(Collectors.toList());
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN)
                .batchAdd4EntityWithTypeFromKeyNo(caseRoleObjs, CourtCaseTO.CaseRoleObj::setShowName, CourtCaseTO.CaseRoleObj::setPE, CourtCaseTO.CaseRoleObj::getShowName, CourtCaseTO.CaseRoleObj::getPE, CourtCaseTO.CaseRoleObj::getN);
        commonDataService.enPostProcessor(transWrapper);
        String caseRoleObjsStr = caseRoleObjs.stream().map(caseRoleObj -> {
            String enName = StringUtils.isNotBlank(caseRoleObj.getPE()) ? caseRoleObj.getPE() : caseRoleObj.getShowName();
            if (!StringUtils.isContainChinese(enName)) {
                enName = StringUtils.upperCase(enName);
            }
            enName = StringUtils.replace(enName, "*", "⁕");
            return enName;
        }).collect(Collectors.joining(", "));
        if (caseRoleObjs.size() > Constants.CourtCase.CASE_ROLE_LIMIT) {
            caseRoleObjsStr += Constants.CourtCase.OVER_LIMIT_SUFFIX_EN;
        }
        return caseRoleObjsStr;
    }

    private static String getCaseRoleObjsStrWithLimit4Cn(List<CourtCaseTO.CaseRoleObj> caseRoleObjs) {
        if (CollectionUtils.isEmpty(caseRoleObjs)) {
            return null;
        }
        String caseRoleObjsStr = caseRoleObjs.stream().limit(Constants.CourtCase.CASE_ROLE_LIMIT).map(caseRoleObj -> {
            String showName = caseRoleObj.getShowName();
            if (!StringUtils.isContainChinese(showName)) {
                showName = StringUtils.upperCase(showName);
            }
            showName = StringUtils.replace(showName, "*", "⁕");
            return showName;
        }).collect(Collectors.joining("，"));
        if (caseRoleObjs.size() > Constants.CourtCase.CASE_ROLE_LIMIT) {
            caseRoleObjsStr += Constants.CourtCase.OVER_LIMIT_SUFFIX_CN;
        }
        return caseRoleObjsStr;
    }

    // 司法案件当前系列案件主体公司身份 added for v2.0.8 chenbl KNZT-5837
    private static List<String> getCaseRoleCodesOfCurr(String keyNo, List<String> prosecutorNames, List<String> defendantNames) {
        List<String> caseRolesOfCurr = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(prosecutorNames) && prosecutorNames.contains(keyNo)) {
            caseRolesOfCurr.add(LitigationTranslationUtil.RoleEnum.PLAINTIFF.getCode());
        }
        if (CollectionUtils.isNotEmpty(defendantNames) && defendantNames.contains(keyNo)) {
            caseRolesOfCurr.add(LitigationTranslationUtil.RoleEnum.DEFENDANT.getCode());
        }
        return caseRolesOfCurr;
    }

    // 司法案件身份映射 added for v2.0.8 chenbl KNZT-5837
    public static String getCaseRoleEn(String caseRole) {
        if (StringUtils.equalsAny(caseRole, "被告", "被告人")) {
            return LitigationTranslationUtil.RoleEnum.DEFENDANT.getEn();
        }
        if (StringUtils.equalsAny(caseRole, "原告", "原告人")) {
            return LitigationTranslationUtil.RoleEnum.PLAINTIFF.getEn();
        }
        return null;
    }

    // added for v1.9.9 chenbl KNZT-5067
    public static List<CorpLogoInfoTO> getCorpImageUrlByKeyNos(List<String> keyNos) {
        return getCorpImageUrlByKeyNos(keyNos, true);
    }

    // added for v1.9.9 chenbl KNZT-5067
    public static List<CorpLogoInfoTO> getCorpImageUrlByKeyNos(List<String> keyNos, boolean needCompanyKey) {
        if (CollectionUtils.isEmpty(keyNos)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNoList", keyNos);
        String param = JSON.toJSONString(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/getCorpImageUrlByKeyNos", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<CorpLogoInfoTO> resultListJo = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpLogoInfoTO>>() {
            });
            if (resultListJo != null) {
                return resultListJo.getResultList();
            }
        }
        return null;
    }

    /**
     * added for v1.8.6 KNZT-3822 企业三要素
     * 三码转换，根据code和codeType找到对应的 qccCode|keyNo|leiCode
     * code: keyNo|qcccode|leicode
     * type: keyno-公司ID|qcccode-企查查code|leicode-leicode编码
     * 注意 该方法只会返回三码转换的其他两种类型码值
     * 
     * @param qccCode
     * @param type
     * @return
     */
    public static QccEntDetailInfoTO getQccDetailByEntCode(String qccCode, String type) throws MessageException {
        String param = "code=" + qccCode + "&type=" + type;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/QccDetail/EntCode/Detail", "1", param, false);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        Integer status = null;
        if (jsonObject != null && (status = jsonObject.getInteger("Status")) != null && (status == 200 || status == 201)) {
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (CollectionUtils.isEmpty(resultJa)) {
                return null;
            }
            QccEntDetailInfoTO qccEntDetailInfoTO = new QccEntDetailInfoTO();
            for (Object result : jsonObject.getJSONArray("Result")) {
                JSONObject jsonObject1 = (JSONObject) result;
                if (jsonObject1 != null) {
                    if (StringUtils.equals(QccEntCodeTypeEnum.LEI_CODE.getCode(), jsonObject1.getString("Type"))) {
                        qccEntDetailInfoTO.setLeiCode(jsonObject1.getString("Code"));
                    }
                    if (StringUtils.equals(QccEntCodeTypeEnum.KEY_NO.getCode(), jsonObject1.getString("Type"))) {
                        qccEntDetailInfoTO.setKeyNo(jsonObject1.getString("Code"));
                    }
                    if (StringUtils.equals(QccEntCodeTypeEnum.QCC_CODE.getCode(), jsonObject1.getString("Type"))) {
                        qccEntDetailInfoTO.setQccCode(jsonObject1.getString("Code"));
                    }
                }
            }
            return qccEntDetailInfoTO;
        } else if ("openapi".equals(Global.getConfig("application.type"))) {
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
        } else {
            logger.warn("qccCode {} doesn't match company", qccCode);
            throw new MessageException("err.access");
        }
    }

    /**
     * added for v1.8.6 KNZT-3822
     * token生成
     * @param qccEntDetailTO
     * @return
     * @throws MessageException
     */
    public static String generateDataToken(QccEntDetailInfoTO qccEntDetailTO) throws MessageException {
        if (qccEntDetailTO != null) {
            long currentTimeMillis = System.currentTimeMillis();
            String jsonStr = JSONObject.toJSONString(qccEntDetailTO);
            logger.info("generateDataToken begin {}", jsonStr);
            String token = StringUtils.getSha256Token4Validate(qccEntDetailTO.getQccCode() + currentTimeMillis);
            if (StringUtils.isNotBlank(token)) {
                token = token.substring(0, token.length() / 2);
            }
            return qccEntDetailTO.getQccCode() + Constants.TokenSplit.DATA + currentTimeMillis + Constants.TokenSplit.DATA + token;
        }
        logger.error("generateDataToken err, qccEntDetailTO is null");
        throw new MessageException("err.access");
    }
    /**
     * added for v1.8.6 KNZT-3822
     * 校验token 有效期7天
     * @param qccCode
     * @param timeMillisStr
     * @param token
     * @return
     * @throws MessageException
     */
    public static boolean validateDataToken(String qccCode, String timeMillisStr, String token) throws MessageException {
        long currentTimeMillis = System.currentTimeMillis();
        long timeMillis = Long.parseLong(timeMillisStr);
        long diffHours = (currentTimeMillis - timeMillis) / (60 * 60 * 1000);
        if (diffHours >= 168) {
            throw new MessageException("err.data.token.tip");
        } else if (StringUtils.isNoneBlank(qccCode, timeMillisStr, token)) {
            String validateToken = StringUtils.getSha256Token4Validate(qccCode + timeMillisStr);
            if (StringUtils.isNotBlank(validateToken)) {
                validateToken = validateToken.substring(0, validateToken.length() / 2);
            }
            return StringUtils.equals(token, validateToken);
        } else {
            logger.error("generateDataToken err, qccEntDetailTO is null");
        }
        throw new MessageException("err.data.token.tip");
    }
    
    public static ShellRelateCompanyDetailResult getSimilarExecutiveList(ShellCompanySearchForm form, boolean needCompanyKey) {
        if (Objects.isNull(form) || Objects.isNull(form.getCondition()) || StringUtils.isBlank(form.getCondition().getKeyno())) {
            return null;
        }
        form.setId(null);
        String param = JSON.toJSONString(form);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/shellco/similarExecutiveList", "2", param, needCompanyKey);
        
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ShellRelateCompanyDetailResult result = jsonObject.toJavaObject(ShellRelateCompanyDetailResult.class);
            if (result != null &&CollectionUtils.isNotEmpty(result.getResultList())) {
                List<ShellRelateCompanyDetailTO> resultList = result.getResultList();
                Set<String> textSet = new HashSet<>();
                Set<String> keyNoSet = new HashSet<>();
                for (ShellRelateCompanyDetailTO shellRelateCompanyDetailTO : resultList) {
                    if (StringUtils.isContainChinese(shellRelateCompanyDetailTO.getReAddress())) {
                        textSet.add(shellRelateCompanyDetailTO.getReAddress());
                    }
                    if (StringUtils.isNotBlank(shellRelateCompanyDetailTO.getReOperkeyno()) && !StringUtils.startsWith(shellRelateCompanyDetailTO.getReOperkeyno(), Constants.CorpTypePrefix.PERSON_PREFIX)) {
                        keyNoSet.add(shellRelateCompanyDetailTO.getReOperkeyno());
                    } else {
                        shellRelateCompanyDetailTO.setReOpernameEn(PinyinUtils.chineseNameToPinyin(shellRelateCompanyDetailTO.getReOpername()));
                    }
                    String[] names = StringUtils.split(shellRelateCompanyDetailTO.getReNameCollect(), ",");
                    List<ShellRelateCompanyDetailTO.OperInfo> operInfoList = new ArrayList<>();
                    for (String name : names) {
                        if(!PinyinUtils.notConvert2Pinyin(name)){
                            ShellRelateCompanyDetailTO.OperInfo operInfo = new ShellRelateCompanyDetailTO.OperInfo();
                            operInfo.setReOpername(name);
                            operInfo.setReOpernameEn(PinyinUtils.chineseNameToPinyin(name));
                            operInfoList.add(operInfo);
                        }
                    }
                    shellRelateCompanyDetailTO.setReNameCollectList(operInfoList);
                }
                if (CollectionUtils.isNotEmpty(textSet)) {
                    Map<String, String> textCn2EnMap = translaterService.getEnglishTextBatchSimple(textSet, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                    for (ShellRelateCompanyDetailTO shellRelateCompanyDetailTO : resultList) {
                        String addressEn = textCn2EnMap.get(shellRelateCompanyDetailTO.getReAddress());
                        if (StringUtils.isNotBlank(addressEn)) {
                            shellRelateCompanyDetailTO.setReAddressEn(addressEn);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(keyNoSet)) {
                    Map<String, AdvanceSearchTO> keyNo2AdvanceSearchMap;
                    List<AdvanceSearchTO> advanceSearchTOS = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(new ArrayList<>(keyNoSet), needCompanyKey);
                    if (CollectionUtils.isNotEmpty(advanceSearchTOS)) {
                        keyNo2AdvanceSearchMap = advanceSearchTOS.stream().filter(to -> StringUtils.isNotBlank(to.getKeyNo())).collect(Collectors.toMap(AdvanceSearchTO::getKeyNo, Function.identity(), (k1, k2) -> k1));
                        for (ShellRelateCompanyDetailTO shellRelateCompanyDetailTO : resultList) {
                            AdvanceSearchTO advanceSearchTO = keyNo2AdvanceSearchMap.get(shellRelateCompanyDetailTO.getReOperkeyno());
                            if (advanceSearchTO != null) {
                                shellRelateCompanyDetailTO.setReOpernameEn(advanceSearchTO.getNameEn());
                            }
                        }
                    }
                }
                return result;
            }
        }
        return null;
    }

    public static ShellScCompanyRepeatAddressResult getSameTelAddressList(ShellCompanySearchForm form, boolean needCompanyKey) {
        if (Objects.isNull(form) || Objects.isNull(form.getCondition()) || StringUtils.isBlank(form.getCondition().getKeyno())) {
            return null;
        }
        form.setId(null);
        String param = JSON.toJSONString(form);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/shellco/sameTelAddressListV2", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            ShellScCompanyRepeatAddressResult result = jsonObject.toJavaObject(ShellScCompanyRepeatAddressResult.class);
            if (result != null && CollectionUtils.isNotEmpty(result.getResultList())) {
                List<ShellScCompanyRepeatAddressTO> resultList = result.getResultList();
                Set<String> textSet = new HashSet<>();
                Set<String> keyNoSet = new HashSet<>();
                for (ShellScCompanyRepeatAddressTO addressTO : resultList) {
                    if (StringUtils.isContainChinese(addressTO.getContent())) {
                        textSet.add(addressTO.getContent());
                    }
                    // updated for v2.0.3 KNZT-5431 fengsw【bug】空壳扫描，法定代表人为公司的未翻译（杭州湖妙科技有限公司）
                    if (StringUtils.isNotBlank(addressTO.getOperkeyno()) && !StringUtils.startsWith(addressTO.getOperkeyno(), Constants.CorpTypePrefix.PERSON_PREFIX)) {
                        keyNoSet.add(addressTO.getOperkeyno());
                    } else {
                        addressTO.setOpernameEn(PinyinUtils.chineseNameToPinyin(addressTO.getOpername()));
                    }
                }
                if (CollectionUtils.isNotEmpty(textSet)) {
                    Map<String, String> textCn2EnMap = translaterService.getEnglishTextBatchSimple(textSet, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                    for (ShellScCompanyRepeatAddressTO addressTO : resultList) {
                        String addressEn = textCn2EnMap.get(addressTO.getContent());
                        if (StringUtils.isNotBlank(addressEn)) {
                            addressTO.setAddressEn(addressEn);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(keyNoSet)) {
                    Map<String, AdvanceSearchTO> keyNo2AdvanceSearchMap;
                    List<AdvanceSearchTO> advanceSearchTOS = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(new ArrayList<>(keyNoSet), needCompanyKey);
                    if (CollectionUtils.isNotEmpty(advanceSearchTOS)) {
                        keyNo2AdvanceSearchMap = advanceSearchTOS.stream().filter(to -> StringUtils.isNotBlank(to.getKeyNo())).collect(Collectors.toMap(AdvanceSearchTO::getKeyNo, Function.identity(), (k1, k2) -> k1));
                        for (ShellScCompanyRepeatAddressTO addressTO : resultList) {
                            AdvanceSearchTO advanceSearchTO = keyNo2AdvanceSearchMap.get(addressTO.getOperkeyno());
                            if (advanceSearchTO != null) {
                                addressTO.setOpernameEn(advanceSearchTO.getNameEn());
                            }
                        }
                    }
                }
                return result;
            }
        }
        return null;
    }

    public static List<ShellCorpExceptionTO> getNoContactExceptionWithReasonByKeyNo(String keyNo, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/shellco/noContactException", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<ShellCorpExceptionTO> resultListJo = jsonObject.toJavaObject(new TypeReference<JsonResultList<ShellCorpExceptionTO>>() {
            });
            if (resultListJo != null && CollectionUtils.isNotEmpty(resultListJo.getResultList())) {
                Set<String> textSet = new HashSet<>();
                List<ShellCorpExceptionTO> resultList = resultListJo.getResultList();
                for (ShellCorpExceptionTO exceptionTO : resultList) {
                    if (StringUtils.isContainChinese(exceptionTO.getAddReason())) {
                        textSet.add(exceptionTO.getAddReason());
                    }
                    if (StringUtils.isContainChinese(exceptionTO.getRomoveReason())) {
                        textSet.add(exceptionTO.getRomoveReason());
                    }
                    String registrationAuthorityEn = translateRegistrationAuthority(exceptionTO.getDecisionOffice());
                    exceptionTO.setDecisionOfficeEn(registrationAuthorityEn);
                    String registrationAuthorityEn1 = translateRegistrationAuthority(exceptionTO.getRemoveDecisionOffice());
                    exceptionTO.setRemoveDecisionOfficeEn(registrationAuthorityEn1);
                }
                if (CollectionUtils.isNotEmpty(textSet)) {
                    Map<String, String> textCn2EnMap = translaterService.getEnglishTextBatchSimple(textSet, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                    for (ShellCorpExceptionTO exceptionTO : resultList) {
                        String addReasonEn = textCn2EnMap.get(exceptionTO.getAddReason());
                        String removeReasonEn = textCn2EnMap.get(exceptionTO.getRomoveReason());
                        if (StringUtils.isNotBlank(addReasonEn)) {
                            exceptionTO.setAddReasonEn(addReasonEn);
                        }
                        if (StringUtils.isNotBlank(removeReasonEn)) {
                            exceptionTO.setRomoveReasonEn(removeReasonEn);
                        }
                    }
                }
                return resultList;
            }
        }
        return null;
    }

    public static List<ShellScCompanyWnjTO> getCompanyWnjListByKeyNo(String keyNo, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        String param = "keyNo=" + keyNo;
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/shellco/annualReportNotPublish", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<ShellScCompanyWnjTO> resultListJo = jsonObject.toJavaObject(new TypeReference<JsonResultList<ShellScCompanyWnjTO>>() {
            });
            if (resultListJo != null && CollectionUtils.isNotEmpty(resultListJo.getResultList())) {
                List<ShellScCompanyWnjTO> resultList = resultListJo.getResultList();
                Set<String> textSet = new HashSet<>();
                for (ShellScCompanyWnjTO shellScCompanyWnjTO : resultList) {
                    if (StringUtils.isContainChinese(shellScCompanyWnjTO.getAddreason())) {
                        textSet.add(shellScCompanyWnjTO.getAddreason());
                    }
                    String registrationAuthorityEn = translateRegistrationAuthority(shellScCompanyWnjTO.getDecisionoffice());
                    shellScCompanyWnjTO.setDecisionofficeEn(registrationAuthorityEn);
                }
                if (CollectionUtils.isNotEmpty(textSet)) {
                    Map<String, String> textCn2EnMap = translaterService.getEnglishTextBatchSimple(textSet, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                    for (ShellScCompanyWnjTO shellScCompanyWnjTO : resultList) {
                        String addReasonEn = textCn2EnMap.get(shellScCompanyWnjTO.getAddreason());
                        if (StringUtils.isNotBlank(addReasonEn)) {
                            shellScCompanyWnjTO.setAddreasonEn(addReasonEn);
                        }
                    }
                }
                return resultList;
            }
        }
        return null;
    }

    /**
     * updated for v2.0.5 fengsw KNZT-5516
     * UBO链路的公共方法抽取
     * 
     * @param corpUBO4Unit
     */
    private static void handleCorpUBO4UnitInfo(StockResult4UnitTO corpUBO4Unit) {
        if (Objects.nonNull(corpUBO4Unit)) {
            if (CollectionUtils.isNotEmpty(corpUBO4Unit.getBreakThroughListReport())) {
                // updated for v1.9.9 KNZT-4985 获取中间控股公司 保证中间控股公司展示的顺序
                Map<String, StockIntermediateCompanyTO> intermediaCompanyMap = new LinkedHashMap<>();
                for (StockDetail4UnitTO stockDetail4UnitTO : corpUBO4Unit.getBreakThroughListReport()) {
                    // updated for v1.7.8 KNZT-3359 【优化】【国际版】UBO链路中补充企业中文名
                    handleUBOPathNameAndEnName(stockDetail4UnitTO, intermediaCompanyMap);
                    stockDetail4UnitTO.setRoleEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(stockDetail4UnitTO.getRole()));//updated for v1.1.9 KNZT-945
                    if (PinyinUtils.notConvert2Pinyin(stockDetail4UnitTO.getName())) {
                        stockDetail4UnitTO.setNameEn(stockDetail4UnitTO.getName());
                        stockDetail4UnitTO.setName("");
                    } else {
                        stockDetail4UnitTO.setNameEn(PinyinUtils.chineseNameToPinyin(stockDetail4UnitTO.getName()));
                    }
                    stockDetail4UnitTO.setBenifitTypeEn(mappingBenefitTypeEn(stockDetail4UnitTO.getBenifitType()));
                }
                corpUBO4Unit.setIntermediateCompanyList(new ArrayList<>(intermediaCompanyMap.values()));
            } else {
                corpUBO4Unit.setRemarkEn(UboRemarkEnum.getUboRemarkEnByUboRemarkCn(corpUBO4Unit.getRemark()));
                if (StringUtils.isNotBlank(corpUBO4Unit.getOperName())) {
                    String[] operNames = corpUBO4Unit.getOperName().split("、");
                    String operNameEns = Arrays.stream(operNames).map(operName -> {
                        if (PinyinUtils.notConvert2Pinyin(operName)) {
                            return "";
                        } else {
                            return PinyinUtils.chineseNameToPinyin(operName);
                        }
                    }).collect(Collectors.joining("、"));
                    corpUBO4Unit.setOperNameEn(operNameEns);
                }
                corpUBO4Unit.setPositionEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(corpUBO4Unit.getPosition()));//updated for v1.1.9 KNZT-945
                if (CollectionUtils.isNotEmpty(corpUBO4Unit.getExecutives())) {//广东文化长城集团股份有限公司
                    for (Executive executive : corpUBO4Unit.getExecutives()) {
                        if ("其他类型".equals(executive.getBenifitType())) {//added v1.1.7 KNZT-758
                            executive.setBenifitType("关键人员");
                        }
                        if (PinyinUtils.notConvert2Pinyin(executive.getName())) {
                            executive.setNameEn(executive.getName());
                            executive.setName("");
                        } else {
                            executive.setNameEn(PinyinUtils.chineseNameToPinyin(executive.getName()));
                        }
                        executive.setBenifitTypeEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(executive.getBenifitType()));//updated for v1.1.9 KNZT-945
                        executive.setPositionEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(executive.getPosition()));
                    }
                }
                if (CollectionUtils.isNotEmpty(corpUBO4Unit.getOtherBeneList())) {
                    for (OtherBeneTO otherBeneTO : corpUBO4Unit.getOtherBeneList()) {//山东高投毅达新动能创业投资合伙企业（有限合伙）
                        if ("其他类型".equals(otherBeneTO.getBenifitType())) {//added v1.1.7 KNZT-758
                            otherBeneTO.setBenifitType("关键人员");
                        }
                        if (PinyinUtils.notConvert2Pinyin(otherBeneTO.getName())) {
                            otherBeneTO.setNameEn(otherBeneTO.getName());
                            otherBeneTO.setName("");
                        } else {
                            otherBeneTO.setNameEn(convertSpecOtherBeneName2En(otherBeneTO.getName()));
                        }
                        otherBeneTO.setBenifitTypeEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(otherBeneTO.getBenifitType()));//updated for v1.1.9 KNZT-945
                        otherBeneTO.setRoleEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(otherBeneTO.getRole()));
                    }
                } else if (CollectionUtils.isNotEmpty(corpUBO4Unit.getHehuoPersonList())) {
                    for (OtherBeneTO otherBeneTO : corpUBO4Unit.getHehuoPersonList()) {
                        if ("其他类型".equals(otherBeneTO.getBenifitType())) {//added v1.1.7 KNZT-758
                            otherBeneTO.setBenifitType("关键人员");
                        }
                        if (PinyinUtils.notConvert2Pinyin(otherBeneTO.getName())) {
                            otherBeneTO.setNameEn(otherBeneTO.getName());
                            otherBeneTO.setName("");
                        } else {
                            otherBeneTO.setNameEn(PinyinUtils.chineseNameToPinyin(otherBeneTO.getName()));
                        }
                        otherBeneTO.setBenifitTypeEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(otherBeneTO.getBenifitType()));//updated for v1.1.9 KNZT-945
                        otherBeneTO.setRoleEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(otherBeneTO.getRole()));
                    }
                }
                if ("其他类型".equals(corpUBO4Unit.getBenifitType())) {//added v1.1.7 KNZT-758
                    corpUBO4Unit.setBenifitType("关键人员");
                }
                corpUBO4Unit.setBenifitTypeEn(PositionTraslationUtils.getPositionEnByPositionCnByExactSearch(corpUBO4Unit.getBenifitType()));//updated for v1.1.9 KNZT-945
            }
            // [10%,25%)的受益人信息处理
            if (CollectionUtils.isNotEmpty(corpUBO4Unit.getBreakThroughListReportBO())) {
                Map<String, StockIntermediateCompanyTO> intermediaCompanyMap = new LinkedHashMap<>();
                for (StockDetail4UnitTO stockDetail4UnitTO : corpUBO4Unit.getBreakThroughListReportBO()) {
                    handleUBOPathNameAndEnName(stockDetail4UnitTO, intermediaCompanyMap);
                    stockDetail4UnitTO.setRoleEn(PositionTraslationUtils.getPositionEnByPositionCnByExactAndFuzzyMatch(stockDetail4UnitTO.getRole()));//updated for v1.1.9 KNZT-945
                    if (PinyinUtils.notConvert2Pinyin(stockDetail4UnitTO.getName())) {
                        stockDetail4UnitTO.setNameEn(stockDetail4UnitTO.getName());
                        stockDetail4UnitTO.setName("");
                    } else {
                        stockDetail4UnitTO.setNameEn(PinyinUtils.chineseNameToPinyin(stockDetail4UnitTO.getName()));
                    }
                    stockDetail4UnitTO.setBenifitTypeEn(mappingBenefitTypeEn(stockDetail4UnitTO.getBenifitType()));
                }
            }
        }
    }

    private static String mappingBenefitTypeEn(String benefitType) {
        if (StringUtils.isBlank(benefitType)) return null;
        if (benefitType.contains("直接") && benefitType.contains("间接")) {
            return "Direct & Indirect";
        } else if (benefitType.contains("直接")) {
            return "Direct";
        } else if (benefitType.contains("间接")) {
            return "Indirect";
        }
        return null;
    }

    /**
     * added for v2.0.6 fengsw KNZT-5582 英文名来源处理
     * 获取企业英文别名，带来源
     *
     * @param keyNo
     * @param needCompanyKey
     * @return
     */
    public static CorpEnNameResult listCorpEnglishNameByKeyNo(String keyNo, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNoList", Lists.newArrayList(keyNo));
        String param = JSON.toJSONString(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/global/listQccCompanyEnglishName", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("resultList"))) {
            JsonResultList<CorpEnNameResult> resultListJo = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpEnNameResult>>() {
            });
            if (resultListJo != null && CollectionUtils.isNotEmpty(resultListJo.getResultList())) {
                return resultListJo.getResultList().stream().filter(item->StringUtils.equals(keyNo, item.getKeyNo())).findFirst().orElse(null);
            }
        }
        return null;
    }

    // 详情/五星图 added for v2.1.1 chenbl KNZT-6038
    public static GetCreditRateTO getCreditRate(String keyNo, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        String param = JSON.toJSONString(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/ECILocal/GetCreditRate", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JSONObject result = jsonObject.getJSONObject("Result");
            if (result != null) {
                GetCreditRateTO creditRateTO = result.toJavaObject(GetCreditRateTO.class);
                fill4GetCreditRateTO(creditRateTO);
                return creditRateTO;
            }
        }
        return null;
    }

    private static void fill4GetCreditRateTO(GetCreditRateTO getCreditRateTO) {
        if (getCreditRateTO == null) {
            return;
        }
        getCreditRateTO.setScoreDescEn(CreditScoreLevelEnum.getDescEnByCode(getCreditRateTO.getScoreLevel()));
        List<GetCreditRateTO.DimensionList> dimensionList = getCreditRateTO.getDimensionList();
        if (CollectionUtils.isNotEmpty(dimensionList)) {
            for (GetCreditRateTO.DimensionList dimension : dimensionList) {
                dimension.setTypeDescEn(CreditScoreDimensionEnum.getDescEnByCode(dimension.getType()));
            }
        }
        // added for v2.1.3 chenbl KNZT-6276
        GetCreditRateTO.IndustryAnalysis industryAnalysis = getCreditRateTO.getIndustryAnalysis();
        if (industryAnalysis != null && industryAnalysis.getRank() != null) {
            industryAnalysis.setRankDesc(NumberUtils.number2PercentileWithSuffix(industryAnalysis.getRank()));
        }
    }

    // 历史企查分 added for v2.1.1 chenbl KNZT-6038
    public static GetCreditRateTrendTO getCreditRateTrend(String keyNo, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        GetCreditRateTrendTO getCreditRateTrendTO = null;
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        Date startDate = DateUtils.addDays(new Date(), -90);
        paramJo.put("date", DateUtils.formatDate(startDate, DateUtils.DATE_FORMAT_YMD));
        String param = JSON.toJSONString(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/ECILocal/GetCreditRateTrend", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JSONObject result = jsonObject.getJSONObject("Result");
            if (result != null) {
                getCreditRateTrendTO = result.toJavaObject(GetCreditRateTrendTO.class);
            }
        }
        return getCreditRateTrendTO;
    }

    // 发票抬头 added for v2.1.1 chenbl KNZT-6038
    public static GetInvoiceDetailTO getInvoiceDetail(String keyNo, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        String param = StringUtils.toUrlParamStr(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/Invoice/GetInvoiceDetail", "1", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JSONObject result = jsonObject.getJSONObject("Result");
            if (result != null) {
                GetInvoiceDetailTO getInvoiceDetailTO = result.toJavaObject(GetInvoiceDetailTO.class);
                fill4GetInvoiceDetailTO(getInvoiceDetailTO);
                return getInvoiceDetailTO;
            }
        }
        return null;
    }

    private static void fill4GetInvoiceDetailTO(GetInvoiceDetailTO getInvoiceDetailTO) {
        if (getInvoiceDetailTO == null) {
            return;
        }
        Set<String> cnSet = new HashSet<>();
        if (StringUtils.isNotBlank(getInvoiceDetailTO.getAddress())) {
            cnSet.add(getInvoiceDetailTO.getAddress());
        }
        if (StringUtils.isNotBlank(getInvoiceDetailTO.getBank())) {
            cnSet.add(getInvoiceDetailTO.getBank());
        }
        if (StringUtils.isNotBlank(getInvoiceDetailTO.getSwiftName())) {
            cnSet.add(getInvoiceDetailTO.getSwiftName());
        }
        if (CollectionUtils.isNotEmpty(cnSet)) {
            Map<String, String> cn2enMap = translaterService.getEnglishTextBatchSimple(cnSet, getInvoiceDetailTO.getKeyno(), null, null);
            if (MapUtils.isNotEmpty(cn2enMap)) {
                getInvoiceDetailTO.setAddressEn(cn2enMap.get(getInvoiceDetailTO.getAddress()));
                getInvoiceDetailTO.setBankEn(cn2enMap.get(getInvoiceDetailTO.getBank()));
                getInvoiceDetailTO.setSwiftNameEn(cn2enMap.get(getInvoiceDetailTO.getSwiftName()));
            }
        }
    }

    // 纳税人资质 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<GeneralTaxPayerTO> listGeneralTaxPayer(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = JSON.toJSONString(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/QccSearch/List/GeneralTaxPayer", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<GeneralTaxPayerTO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(GeneralTaxPayerTO.class));
                fill4listGeneralTaxPayer(jsonResultList.getResultList());
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4listGeneralTaxPayer(List<GeneralTaxPayerTO> list) {
        TranslaterService.doMapping(list, GeneralTaxPayerTO::getTaxpayerType, GeneralTaxPayerTO::setTaxpayerTypeEn, GeneralTaxPayerTypeEnum::getDescEnByDesc);
        TranslaterService.doMapping(list, GeneralTaxPayerTO::getBelongOrg, GeneralTaxPayerTO::setBelongOrgEn, StateOrgansTranslationUtil::getStateOrgansEnByCn);
        TranslaterService.doMapping(list, GeneralTaxPayerTO::getDataStatusDesc, GeneralTaxPayerTO::setDataStatusDescEn, GeneralTaxPayerStatusEnum::getDescEnByDesc);
    }

    // 税务非正常户核查 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<GetTaxUnnormalsTO> getTaxUnnormals(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = JSON.toJSONString(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/Risk/GetTaxUnnormals", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<GetTaxUnnormalsTO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(GetTaxUnnormalsTO.class));
                fill4GetTaxUnnormalsTO(jsonResultList.getResultList());
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4GetTaxUnnormalsTO(List<GetTaxUnnormalsTO> list) {
        TranslaterService.doMapping(list, GetTaxUnnormalsTO::getExecuteGov, GetTaxUnnormalsTO::setExecuteGovEn, StateOrgansTranslationUtil::getStateOrgansEnByCn);
    }

    // 税收违法 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<GetIllegalListTO> getIllegalList(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = StringUtils.toUrlParamStr(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/Tax/GetIllegalList", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<GetIllegalListTO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(GetIllegalListTO.class));
                fill4GetIllegalListTO(jsonResultList.getResultList());
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4GetIllegalListTO(List<GetIllegalListTO> list) {
        Set<String> textSet2Translate = new HashSet<>();
        textSet2Translate.addAll(TranslaterService.getTextSet2Translate(list, GetIllegalListTO::getCaseNature));
        textSet2Translate.addAll(TranslaterService.getTextSet2Translate(list, GetIllegalListTO::getIllegalContent));
        textSet2Translate.addAll(TranslaterService.getTextSet2Translate(list, GetIllegalListTO::getPunishContent));
        if (CollectionUtils.isNotEmpty(textSet2Translate)) {
            Map<String, String> cn2EnMap = translaterService.getEnglishTextBatchSimple(textSet2Translate, null, null, null);
            if (MapUtils.isNotEmpty(cn2EnMap)) {
                TranslaterService.doMapping(list, GetIllegalListTO::getCaseNature, GetIllegalListTO::setCaseNatureEn, cn2EnMap::get);
                TranslaterService.doMapping(list, GetIllegalListTO::getIllegalContent, GetIllegalListTO::setIllegalContentEn, cn2EnMap::get);
                TranslaterService.doMapping(list, GetIllegalListTO::getPunishContent, GetIllegalListTO::setPunishContentEn, cn2EnMap::get);
            }
        }
        TranslaterService.doMapping(list, GetIllegalListTO::getTaxGov, GetIllegalListTO::setTaxGovEn, StateOrgansTranslationUtil::getStateOrgansEnByCn);
    }

    // 欠税公告 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<GetListOfOweNoticeNewTO> getListOfOweNoticeNew(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("searchKey", keyNo);
        paramJo.put("isValid", "1");
        paramJo.put("isCombin", "1");
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = StringUtils.toUrlParamStr(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/Tax/GetListOfOweNoticeNew", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<GetListOfOweNoticeNewTO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(GetListOfOweNoticeNewTO.class));
                fill4GetListOfOweNoticeNewTO(jsonResultList.getResultList());
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
                jsonResultList.setGroupItems(Lists.newArrayList(
                        new GroupItem("TaxBalanceAmount", "TaxBalanceAmount", pagingJo.getString("TaxBalanceAmount")),
                        new GroupItem("TaxBalanceCurrentAmount", "TaxBalanceCurrentAmount", pagingJo.getString("TaxBalanceCurrentAmount"))));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4GetListOfOweNoticeNewTO(List<GetListOfOweNoticeNewTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (GetListOfOweNoticeNewTO getListOfOweNoticeNewTO : list) {
            if (StringUtils.isNotBlank(getListOfOweNoticeNewTO.getTitle())) {
                List<String> splitList = StringUtils.split2ListV2(getListOfOweNoticeNewTO.getTitle(), "、", ",", "，");
                List<String> splitEnList = splitList.stream().map(TaxCollectionItemEnum::getEnByCn).collect(Collectors.toList());
                boolean allMatch = splitEnList.stream().allMatch(StringUtils::isNotBlank);
                if (allMatch) {
                    getListOfOweNoticeNewTO.setTitleEn(StringUtils.join(splitEnList, ", "));
                }
            }
        }
        List<GetListOfOweNoticeNewTO> listOfTitleNotTranslated = list.stream()
                .filter(getListOfOweNoticeNewTO -> StringUtils.isNotBlank(getListOfOweNoticeNewTO.getTitle()) && StringUtils.isBlank(getListOfOweNoticeNewTO.getTitleEn()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listOfTitleNotTranslated)) {
            translaterService.translateByApi(listOfTitleNotTranslated, GetListOfOweNoticeNewTO::getTitle, GetListOfOweNoticeNewTO::setTitleEn);
        }
        TranslaterService.doMapping(list, GetListOfOweNoticeNewTO::getIssuedBy, GetListOfOweNoticeNewTO::setIssuedByEn, StateOrgansTranslationUtil::getStateOrgansEnByCn);
    }

    // 进出口信用 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<IETO> listIE(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = StringUtils.toUrlParamStr(paramJo);
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("1", "/QccSearch/List/IE", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<IETO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(IETO.class));
                fill4IETO(jsonResultList.getResultList(), keyNo, !needCompanyKey);
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4IETO(List<IETO> list, String keyNo, boolean isReport) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> textSet2Translate = new HashSet<>();
        textSet2Translate.addAll(TranslaterService.getTextSet2Translate(list, IETO::getRegGov));
        Set<String> tradeTypeSet2Translate = new HashSet<>();
        Set<String> creditGradeSet2Translate = new HashSet<>();
        for (IETO ieto : list) {
            if (StringUtils.isNotBlank(ieto.getTradeType())) {
                tradeTypeSet2Translate.addAll(StringUtils.split2ListV2(ieto.getTradeType(), "、")
                        .stream().filter(tradeType -> IETradeTypeEnum.getByDesc(tradeType) == null)
                        .collect(Collectors.toSet()));
            }

            if (StringUtils.isNotBlank(ieto.getEbusinessType())) {
                tradeTypeSet2Translate.addAll(StringUtils.split2ListV2(ieto.getEbusinessType(), "、")
                        .stream().filter(tradeType -> IETradeTypeEnum.getByDesc(tradeType) == null)
                        .collect(Collectors.toSet()));
            }

            if (CollectionUtils.isNotEmpty(ieto.getRegCoInfos())) {
                tradeTypeSet2Translate.addAll(ieto.getRegCoInfos().stream().map(IETO.RegCoInfos::getTradeTypeName)
                        .filter(tradeType -> IETradeTypeEnum.getByDesc(tradeType) == null)
                        .collect(Collectors.toSet()));
            }

            if (CollectionUtils.isNotEmpty(ieto.getCreditGrade())) {
                creditGradeSet2Translate.addAll(ieto.getCreditGrade().stream().map(IETO.CreditGrade::getGrade)
                        .filter(creditGrade -> IECreditGradeEnum.getByDesc(creditGrade) == null)
                        .collect(Collectors.toSet()));
            }
        }
        textSet2Translate.addAll(tradeTypeSet2Translate);
        textSet2Translate.addAll(creditGradeSet2Translate);
        Map<String, String> cn2EnMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(textSet2Translate)) {
            cn2EnMap.putAll(translaterService.getEnglishTextBatchSimple(textSet2Translate, null, null, null));
        }
        for (IETO ieto : list) {
            if (StringUtils.isNotBlank(ieto.getRegGov())) {
                ieto.setRegGovEn(cn2EnMap.get(ieto.getRegGov()));
            }

            if (StringUtils.isNotBlank(ieto.getTradeType())) {
                ieto.setTradeTypeEn(StringUtils.split2ListV2(ieto.getTradeType(), "、")
                        .stream().map(tradeType -> {
                            String descEnByDesc = IETradeTypeEnum.getDescEnByDesc(tradeType);
                            if (StringUtils.isNotBlank(descEnByDesc)) {
                                return descEnByDesc;
                            }
                            return cn2EnMap.get(tradeType);
                        }).filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", ")));
            }

            if (StringUtils.isNotBlank(ieto.getEbusinessType())) {
                ieto.setEbusinessTypeEn(StringUtils.split2ListV2(ieto.getEbusinessType(), "、")
                        .stream().map(tradeType -> {
                            String descEnByDesc = IETradeTypeEnum.getDescEnByDesc(tradeType);
                            if (StringUtils.isNotBlank(descEnByDesc)) {
                                return descEnByDesc;
                            }
                            return cn2EnMap.get(tradeType);
                        }).filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", ")));
            }

            if (CollectionUtils.isNotEmpty(ieto.getRegCoInfos())) {
                for (IETO.RegCoInfos regCoInfo : ieto.getRegCoInfos()) {
                    String descEnByDesc = IETradeTypeEnum.getDescEnByDesc(regCoInfo.getTradeTypeName());
                    if (StringUtils.isNotBlank(descEnByDesc)) {
                        regCoInfo.setTradeTypeNameEn(descEnByDesc);
                    } else {
                        regCoInfo.setTradeTypeNameEn(cn2EnMap.get(regCoInfo.getTradeTypeName()));
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(ieto.getCreditGrade())) {
                for (IETO.CreditGrade creditGrade : ieto.getCreditGrade()) {
                    String descEnByDesc = IECreditGradeEnum.getDescEnByDesc(creditGrade.getGrade());
                    if (StringUtils.isNotBlank(descEnByDesc)) {
                        creditGrade.setGradeEn(descEnByDesc);
                    } else {
                        creditGrade.setGradeEn(cn2EnMap.get(creditGrade.getGrade()));
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(tradeTypeSet2Translate) || CollectionUtils.isNotEmpty(creditGradeSet2Translate)) {
            if (CollectionUtils.isNotEmpty(tradeTypeSet2Translate)) {
                logger.error("海关资质存在未映射的项目: " + StringUtils.join(tradeTypeSet2Translate,","));
            }
            if (CollectionUtils.isNotEmpty(creditGradeSet2Translate)) {
                logger.error("信用等级存在未映射的项目: " + StringUtils.join(creditGradeSet2Translate,","));
            }
            if (isReport) {
                // 报告调用才发送钉钉提醒，避免页面频繁提醒
                sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.MERCHANT_ITEM_EN_NO_MAPPING, keyNo);
            }

        }
    }

    // 荣誉 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<HonorCertificationV2TO> listHonorCertificationV2(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("isValid", 1);
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = paramJo.toJSONString();
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/QccSearch/List/HonorCertificationV2", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<HonorCertificationV2TO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(HonorCertificationV2TO.class));
                fill4HonorCertificationV2TO(jsonResultList.getResultList(), needCompanyKey);
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4HonorCertificationV2TO(List<HonorCertificationV2TO> list, boolean needCompanyKey) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> textSet2Translate = new HashSet<>();
        textSet2Translate.addAll(TranslaterService.getTextSet2Translate(list, HonorCertificationV2TO::getDirectoryName));
        TranslaterService.doMapping(list, HonorCertificationV2TO::getKindDesc, HonorCertificationV2TO::setDirectoryKindEn, HonorDirectoryKindEnum::getDescEnByDesc);
        TranslaterService.doMapping(list, HonorCertificationV2TO::getApproveClassDesc, HonorCertificationV2TO::setApproveClassDescEn, HonorApproveClassEnum::getDescEnByDesc);
        List<HonorCertificationV2TO.PublishOfficeJson> publishOfficeJsons = list.stream().map(HonorCertificationV2TO::getPublishOfficeJson).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        List<HonorCertificationV2TO.PublishOfficeJson> publishOfficeJsons4ApiTranslate = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(publishOfficeJsons)) {
            Set<String> keyNos = publishOfficeJsons.stream().map(HonorCertificationV2TO.PublishOfficeJson::getKeyNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(keyNos)) {
                Map<String, String> corpKeyNo2EnMap = GlobalCompanyDetailsInterface.batchGetCorpKeyNo2EnMap(keyNos, needCompanyKey);
                TranslaterService.doMapping(publishOfficeJsons, HonorCertificationV2TO.PublishOfficeJson::getKeyNo, HonorCertificationV2TO.PublishOfficeJson::setNameEn, corpKeyNo2EnMap::get);
            }
            publishOfficeJsons4ApiTranslate.addAll(publishOfficeJsons.stream().filter(publishOfficeJson -> StringUtils.isBlank(publishOfficeJson.getNameEn())).collect(Collectors.toList()));
            textSet2Translate.addAll(TranslaterService.getTextSet2Translate(publishOfficeJsons4ApiTranslate, HonorCertificationV2TO.PublishOfficeJson::getName));
        }
        if(CollectionUtils.isNotEmpty(textSet2Translate)){
            Map<String, String> cn2EnMap = translaterService.getEnglishTextBatchSimple(textSet2Translate, null, null, null);
            if(MapUtils.isNotEmpty(cn2EnMap)){
                TranslaterService.doMapping(list, HonorCertificationV2TO::getDirectoryName, HonorCertificationV2TO::setDirectoryNameEn, cn2EnMap::get);
                for (HonorCertificationV2TO honorCertificationV2TO : list) {
                    honorCertificationV2TO.setDirectoryNameEn(StringUtils.upperFirst(honorCertificationV2TO.getDirectoryNameEn()));
                }
                TranslaterService.doMapping(publishOfficeJsons4ApiTranslate, HonorCertificationV2TO.PublishOfficeJson::getName, HonorCertificationV2TO.PublishOfficeJson::setNameEn, cn2EnMap::get);
            }
        }
    }

    // 商标信息 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<TrademarkTO> listTrademark(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("aggFields", new String[]{"trademarkstatus", "intclass", "regnoticeyear", "firsttrialnoticeyear", "trademarkname", "applyyear", "applicant", "agency"});
        paramJo.put("isValid", 1);
        paramJo.put("sortField", "applydate");
        paramJo.put("isSortAsc", "false");
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = paramJo.toJSONString();
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/QccSearch/IPRSearch/Trademark", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<TrademarkTO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(TrademarkTO.class));
                fill4TrademarkTO(jsonResultList.getResultList());
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4TrademarkTO(List<TrademarkTO> list) {
        TranslaterService.doMapping(list, TrademarkTO::getIntClass, TrademarkTO::setIntClsDescEn, TrademarkIntClsEnum::getDescEnByDesc);
        TranslaterService.doMapping(list, TrademarkTO::getLegalStatus, TrademarkTO::setLegalStatusEn, TrademarkLegalStatusEnum::getDescEnByDesc);
    }

    // 专利信息 added for v2.1.1 chenbl KNZT-6038
    public static JsonResultList<PatentTO> listPatent(String keyNo, String pageIndex, String pageSize, boolean needCompanyKey) {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        JSONObject paramJo = new JSONObject();
        paramJo.put("keyNo", keyNo);
        paramJo.put("searchType", "1");
        paramJo.put("aggFields", new String[]{"kindfourcode", "legalstatus", "assigneelist", "publicationyear", "applicationyear", "agency", "status", "pubdatekind", "appdatekind", "loclist", "ipclist"});
        paramJo.put("sortField", new String[]{"publicationdate"});
        paramJo.put("isSortAsc", false);
        paramJo.put("pageIndex", pageIndex);
        paramJo.put("pageSize", pageSize);
        String param = paramJo.toJSONString();
        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/QccSearch/IPRSearch/Patent", "2", param, needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("Status"), Constants.Result.SUCCESS_STR)) {
            JsonResultList<PatentTO> jsonResultList = new JsonResultList<>();
            JSONArray resultJa = jsonObject.getJSONArray("Result");
            if (resultJa != null) {
                jsonResultList.setResultList(resultJa.toJavaList(PatentTO.class));
                fill4PatentTO(jsonResultList.getResultList());
            }
            JSONObject pagingJo = jsonObject.getJSONObject("Paging");
            if (pagingJo != null) {
                jsonResultList.setTotalCount(pagingJo.getLongValue("TotalRecords"));
            }
            return jsonResultList;
        }
        return null;
    }

    private static void fill4PatentTO(List<PatentTO> list) {
        translaterService.translateByApi(list, PatentTO::getTitle, PatentTO::setTitleEn);
        TranslaterService.doMapping(list, PatentTO::getKindCodeDesc, PatentTO::setKindCodeDescEn, PatentKindCodeEnum::getDescEnByDesc);
        TranslaterService.doMapping(list, PatentTO::getSimpleStatus, PatentTO::setSimpleStatusDescEn, PatentLegalStatusEnum::getDescEnByCode);
    }
}
