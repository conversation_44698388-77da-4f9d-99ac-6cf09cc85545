package com.backend.common.modules.delivery.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.entity.SysTemplate;
import com.backend.common.modules.api.entity.TblCompApiChargeUnit;
import com.backend.common.modules.api.entity.TblCompApiOrder;
import com.backend.common.modules.api.service.CommTblCompApiChargeUnitService;
import com.backend.common.modules.common.model.DictItem;
import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.condition.TransactionPageCondition;
import com.backend.common.modules.delivery.entity.*;
import com.backend.common.modules.delivery.form.FinanceConfirmForm;
import com.backend.common.modules.delivery.model.*;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.condition.CompanyChargeHistoryCondition;
import com.backend.common.openapi.KzzApiInterface;
import com.backend.common.openapi.model.KzzContractCreateRequestTO;
import com.backend.common.openapi.model.KzzContractDetailTO;
import com.backend.common.openapi.model.KzzContractPaymentSheet;
import com.backend.common.service.CommSysCompInfoExtService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.CommSysTemplateService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.service.*;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.*;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.model.CompanyAddressTO;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.QccMailSenderService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * added for v1.8.8 KNZT-3324
 * 交易综合service
 *
 * <AUTHOR>
 * @datetime 2024/7/3 12:24
 */
@Service
public class TransactionBusinessService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommTblContractDeliveryProdAccService contractDeliveryProdAccService;
    @Autowired
    private CommTblContractDeliveryTransactionService contractDeliveryTransactionService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private CommTblContractDeliveryTransactionBillMonthlyService contractDeliveryTransactionBillMonthlyService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private CommTblContractDeliveryProdService prodService;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblCompStatementMonthlyService commTblCompStatementMonthlyService;
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private CommSysTemplateService commSysTemplateService;
    @Autowired
    private CommSysCompInfoFuncCountService funcCountService;
    @Autowired
    private QccMailSenderService qccMailSenderService;
    @Autowired
    private CommCrmCompTrackingRecordService commCrmCompTrackingRecordService;
    @Autowired
    private CommTblContractDeliveryProdPeriodService prodPeriodService;
    @Autowired
    private SysConfigCacheService sysConfigCacheService;
    @Autowired
    private CommSysCompInfoExtService commSysCompInfoExtService;
    @Autowired
    private CommTblReportChargeUnitService commTblReportChargeUnitService;
    @Autowired
    private CommTblCompApiChargeUnitService commTblCompApiChargeUnitService;
    @Autowired
    private CommTblSettlementContractHistService commTblSettlementContractHistService;
    @Autowired
    private CommTblSettlementContractRevenueService commTblSettlementContractRevenueService;
    @Autowired
    private CommTblContractDeliveryChargeUnitService commTblContractDeliveryChargeUnitService;
    @Autowired
    private UserService userService;
    @Autowired
    private CommCompUserService commCompUserService;

    public Page<FinanceConfirmMonthBillTO> listMonthBill(FinanceConfirmForm form) {
        List<TblContractDeliveryTransactionBillMonthly> list = contractDeliveryTransactionBillMonthlyService.pageByCondition(form.getCondition());
        Page<FinanceConfirmMonthBillTO> page = new Page<>();
        if(CollectionUtils.isEmpty(list)) return page;
        List<TblContractDelivery> contractDeliveryList = contractDeliveryService.getByContractNoList(
            list.stream().map(TblContractDeliveryTransactionBillMonthly::getContractNo).collect(Collectors.toList()));

        Map<String, TblContractDelivery> contractDeliveryMap = contractDeliveryList.stream().collect(Collectors.toMap(TblContractDelivery::getContractNo, Function.identity(), (a, b) -> a));

        List<User> userList = userService.listMainUserByCompanyId(contractDeliveryList.stream().map(TblContractDelivery::getCompanyId).collect(Collectors.toList()));
        Map<String, String> userMap = buildUserMap(contractDeliveryList, userList);
        List<FinanceConfirmMonthBillTO> billList = list.stream().map(bill -> {
            TblContractDelivery contractDelivery = contractDeliveryMap.get(bill.getContractNo());
            String loginName = userMap.get(bill.getContractNo());
            return FinanceConfirmMonthBillTO.build(contractDelivery, bill, loginName);
        }).collect(Collectors.toList());

        BeanUtils.copyProperties(form.getCondition().getPage(), page);
        page.setList(billList);
        return page;
    }

    /**
     * 构建合同编号到用户登录名的映射
     */
    public static Map<String, String> buildUserMap(List<TblContractDelivery> contractDeliveryList, List<User> userList) {
        Map<String, String> userMap = new HashMap<>();
        // 按合同编号分组合同交付信息
        Map<String, List<TblContractDelivery>> deliveryMap = contractDeliveryList.stream()
                .collect(Collectors.groupingBy(TblContractDelivery::getContractNo));
        deliveryMap.forEach((contractNo, deliveries) -> {
            List<String> companyIds = deliveries.stream()
                    .map(TblContractDelivery::getCompanyId)
                    .collect(Collectors.toList());
            String userLoginName = userList.stream()
                    .filter(user -> companyIds.contains(user.getCompanyId()))
                    .map(User::getLoginName)
                    .collect(Collectors.joining(","));
            userMap.put(contractNo, userLoginName);
        });
        return userMap;
    }


    // updated for v2.3.1 fengsw KNZT-7904 财税API 退款流程修改
    public void createTransaction4Refund(String oriOrderId, String companyId, BigDecimal totalUnit, TransactionTypeEnum refundTransactionType, String oriTransactionType) throws MessageException {
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");
        // added for v1.9.8 KNZT-4538
        SysCompInfoFuncCount funcCount = funcCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(funcCount);
        funcCountService.consumeFuncCount(funcCount, totalUnit.negate());

        if (CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
            logger.info("公司类型是试用，不生成退款流水，companyId:{}", companyId);
            return;
        }

        List<TblContractDeliveryProdAcc> accList = contractDeliveryProdAccService.lockValidAccByCompanyId(companyId);

        List<TblContractDeliveryTransaction> oriTransactionList = contractDeliveryTransactionService.getByRelId(oriOrderId, oriTransactionType);
        if (CollectionUtils.isEmpty(oriTransactionList)) {
            // 问题场景
            // 1.试用期下的订单，在转签约后进行退款
            // 2.问题数据，缺少订单流水
            logger.error("退款订单流水记录没有找到，检查数据，oriOrderId:{}", oriOrderId);
            return;
        }

        // 一笔订单可能生成多笔流水
        for (TblContractDeliveryTransaction oriTransaction : oriTransactionList) {
            BigDecimal oriTransactionChangedUnit = oriTransaction.getChangedUnit();

            TblContractDeliveryProdAcc oriAcc = accList.stream().filter(acc -> Objects.equals(acc.getId(), oriTransaction.getContractDeliveryProdAccId())).findAny().orElse(null);
            // 如果退款账户有效，则退回到原账户
            if (Objects.nonNull(oriAcc)) {
                TblContractDeliveryTransaction refundTransaction = TblContractDeliveryTransaction
                        .init(companyId, refundTransactionType.getCode(), funcCount.calRemainCount())
                        .injectRelId(oriOrderId)
                        .injectChangedUnit(oriTransactionChangedUnit.negate());
                this.saveTransactionAndChangeAccUnit(refundTransaction, oriAcc);
            } else {
                 logger.info("退款账户不生效，不生成退款流水，oriOrderId:{}", oriOrderId);
            }
        }
    }


    public void consumeTransaction(String companyId, String relId, String chargeUnitContractDeliveryId,
                                   BigDecimal totalUnit, Date orderCreateDate, String transactionType,
                                   String highPriorityProdAccId) throws MessageException {
        if (BigDecimal.ZERO.compareTo(totalUnit) == 0) {
            logger.info("credits为0，不生成流水，relId:{}", relId);
            return;
        }
        MsgExceptionUtils.checkIsNull(companyId, "msg:缺少companyId");
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");

        // added for v1.9.8 KNZT-4538
        SysCompInfoFuncCount funcCount = funcCountService.lockByCompanyId(companyId);
        funcCountService.consumeFuncCount(funcCount, totalUnit);

        if (CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
            logger.info("公司类型为试用，不生成流水，companyId:{}", companyId);
            return;
        }

        List<TblContractDeliveryProdAcc> accList = contractDeliveryProdAccService.lockValidAccByCompanyId(companyId);

        MsgExceptionUtils.checkIsNull(accList, "err.amount.insufficient");
        // 根据规则排序
        Collections.sort(accList);
        // 如果存在高优先级账户，将其移到列表首位
        if (StringUtils.isNotBlank(highPriorityProdAccId)) {
            accList.stream()
            .filter(acc -> Objects.equals(acc.getId(), highPriorityProdAccId))
            .findFirst()
                .ifPresent(acc -> {
                    accList.remove(acc);
                    accList.add(0, acc);
                });
        }
        // 递归找到产品账户生成流水
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.init(companyId, transactionType, funcCount.calRemainCount())
                .injectRelId(relId)
                .injectChargeUnitContractDeliveryId(chargeUnitContractDeliveryId)
                .injectDateInt(orderCreateDate);
        boolean generateResult = recursionGenerateTransaction(accList, 0, totalUnit, transaction);
        // 如果递归扣减没有成功，则说明没有足够的余额
        MsgExceptionUtils.failBuild(!generateResult, "err.amount.insufficient");
    }

    /**
     * 创建B端订单流水
     *
     * @param order
     * @param chargeUnitContractDeliveryId
     * @return
     */
    public void payOrderAndCreateTransaction(TblCompReportOrder order, String chargeUnitContractDeliveryId, String highPriorityProdAccId) throws MessageException {
        this.consumeTransaction(order.getCompanyId(), order.getId(), chargeUnitContractDeliveryId,
                order.getTotalUnit(), order.getCreateDate(), TransactionTypeEnum.ORDER.getCode(), highPriorityProdAccId);
        order.setPayStatus(PayStatusEnum.PAID.getCode());
        order.setPayResponseTime(new Date());
        commTblCompReportOrderService.save(order);
    }

    public void payOrderAndCreateTransaction(TblCompReportOrder order, String chargeUnitContractDeliveryId) throws MessageException {
        this.payOrderAndCreateTransaction(order, chargeUnitContractDeliveryId, null);
    }
/*
    *//**
     * 创建自助账户流水
     * added for v1.9.4 KNZT-4324
     *
     * @param order
     * @return
     *//*
    public void createTransaction4SelfAccountOrder(TblCompReportOrder order) throws MessageException {
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.init(order.getCompanyId(), TransactionTypeEnum.ORDER.getCode())
                .injectRelId(order.getId())
                .injectChargeUnitContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID)
                .injectDateInt(order.getCreateDate())
                .injectChangedUnit(order.getTotalUnit());
        transaction.setContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID);
        transaction.setContractDeliveryProdAccId(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID);
        transaction.setBeforeRemainUnit(Constants.MAX_UNIT);
        contractDeliveryTransactionService.save(transaction);
        logger.info("saveTransactionAndChangeAccUnit 生成自助账户订单流水, companyId:{}, changedUnit:{}",
                transaction.getCompanyId(), transaction.getChangedUnit());
    }*/

    /**
     * 创建api订单流水
     * added for v1.9.7 KNZT-4708
     *
     * @param order
     * @param chargeUnitContractDeliveryId
     * @return
     */
    public void createTransaction4ApiOrder(TblCompApiOrder order, String chargeUnitContractDeliveryId) throws MessageException {
        this.consumeTransaction(order.getCompanyId(), order.getId(), chargeUnitContractDeliveryId,
                order.getTotalUnit(), order.getCreateDate(), TransactionTypeEnum.API_ORDER.getCode(), null);
    }


/*    public void processProdAccTransactionDaily(TblContractDeliveryProdAcc prodAcc, LocalDate date) {
        // 计算一天内的流水总和
        Integer dateInt = DateUtils.formatDateToInt(date);
        BigDecimal sumChangedUnit = contractDeliveryTransactionService.sumChangedUnitByProAccAndDate(prodAcc.getId(), dateInt);

        TblContractDeliveryTransactionBillDaily bill = contractDeliveryTransactionBillDailyService.getByProdAccAndDate(prodAcc.getId(), dateInt);
        if (Objects.isNull(bill)) {
            bill = new TblContractDeliveryTransactionBillDaily();
            bill.setContractDeliveryProdAccId(prodAcc.getId());
            bill.setContractDeliveryId(prodAcc.getContractDeliveryId());
            bill.setCompanyId(prodAcc.getCompanyId());
            bill.setDateInt(dateInt);
        }
        bill.setConsumedUnitDay(sumChangedUnit);
        BigDecimal latestTotalConsumedUnit = contractDeliveryTransactionBillDailyService.getLatestTotalConsumedUnitBeforeDate(prodAcc.getId(), dateInt);
        bill.setTotalConsumedUnit(latestTotalConsumedUnit.add(sumChangedUnit));
        bill.setRemainUnit(prodAcc.getTotalUnit().subtract(bill.getTotalConsumedUnit()));

        contractDeliveryTransactionBillDailyService.save(bill);
        logger.info("processProdAccTransactionDaily calculate done, date:{}, contractDeliveryId:{}, consumedUnitDay:{}"
                , date, bill.getContractDeliveryProdAccId(), bill.getConsumedUnitDay());

    }*/

    /**
     * 自助账户流水日账计算
     * added for v1.9.4 KNZT-4324
     *
     * @param date
     * @return
     */
/*    public void processProdAccTransactionDaily4SelfAccountOrder(LocalDate date) {
        Integer dateInt = DateUtils.formatDateToInt(date);
        BigDecimal sumChangedUnit = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID, dateInt, dateInt);

        TblContractDeliveryTransactionBillDaily bill = contractDeliveryTransactionBillDailyService.getByProdAccAndDate(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID, dateInt);
        if (Objects.isNull(bill)) {
            bill = new TblContractDeliveryTransactionBillDaily();
            bill.setContractDeliveryProdAccId(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID);
            bill.setContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID);
            bill.setCompanyId(Constants.DEFAULT_COMPANY_ID);
            bill.setDateInt(dateInt);
        }
        bill.setConsumedUnitDay(sumChangedUnit);
        BigDecimal latestTotalConsumedUnit = contractDeliveryTransactionBillDailyService.getLatestTotalConsumedUnitBeforeDate(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID, dateInt);
        bill.setTotalConsumedUnit(latestTotalConsumedUnit.add(sumChangedUnit));
        bill.setRemainUnit(Constants.MAX_UNIT);

        contractDeliveryTransactionBillDailyService.save(bill);
        logger.info("processProdAccTransactionDaily4SelfAccountOrder calculate done, date:{}, contractDeliveryId:{}, consumedUnitDay:{}"
                , date, bill.getContractDeliveryProdAccId(), bill.getConsumedUnitDay());
    }*/

    // updated for lvcy v2.0.8 KNZT-5828
    public void processContractTransactionMonthly(String contractNo, BigDecimal actualDiscountAmount, YearMonth yearMonth) {
        BigDecimal prodTotalAmount = prodService.sumProdTotalAmountByContractNo(contractNo);
        prodTotalAmount = Objects.isNull(prodTotalAmount)
                || prodTotalAmount.compareTo(BigDecimal.ZERO) == 0
                || prodTotalAmount.compareTo(Constants.MAX_UNIT) > 0
                ? Constants.MAX_UNIT : prodTotalAmount;
        BigDecimal prodTotalUnit = prodService.sumProdTotalUnitByContractNo(contractNo);
        prodTotalUnit = Objects.isNull(prodTotalUnit)
                || prodTotalUnit.compareTo(BigDecimal.ZERO) == 0
                || prodTotalUnit.compareTo(Constants.MAX_UNIT) > 0
                ? Constants.MAX_UNIT : prodTotalUnit;
        logger.info("processContractTransactionMonthly contractNo:{}, actualDiscountAmount:{}, yearMonth:{}", contractNo, actualDiscountAmount, yearMonth);

        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate endDayOfMonth = yearMonth.atEndOfMonth();
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);

        BigDecimal beforeConsumedUnitMonth = contractDeliveryTransactionBillMonthlyService
                .getLatestTotalConsumedUnitBeforeYearMonthByContractNo(contractNo, yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode());
        // 本期消耗单元数 = 月中每日的日账总和
        BigDecimal consumedUnitMonth = contractDeliveryTransactionService.sumChangedUnitByContractNoAndDateRange(
                contractNo, DateUtils.formatDateToInt(firstDayOfMonth), DateUtils.formatDateToInt(endDayOfMonth));
        // 累计消耗单元数 = 上期月账累计消耗单元数 + 本期消耗单元数
        BigDecimal totalConsumedUnit = beforeConsumedUnitMonth.add(consumedUnitMonth);
        // 本期消耗金额 = 本期消耗单元数 * 产品实际单价
        BigDecimal consumedAmountMonth = consumedUnitMonth.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        // 累计消耗金额 = 累计消耗单元数 * 产品实际单价
        BigDecimal totalConsumedAmount = totalConsumedUnit.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        // 如果单元数已经消耗完，对数据作修正 （避免小数点误差问题、避免数据问题导致的对不齐问题）
        if (BigDecimal.ZERO.compareTo(prodTotalUnit) != 0 && totalConsumedUnit.compareTo(prodTotalUnit) >= 0) {
            // 本期消耗单元数 = 产品总单元数 - 上期累计消耗单元数
            consumedUnitMonth = prodTotalUnit.subtract(beforeConsumedUnitMonth);
            // 累计消耗单元数 = 产品总单元数
            totalConsumedUnit = prodTotalUnit;
            // 本期消耗金额 = 产品金额 - 上期累计消耗金额
            BigDecimal beforeTotalConsumedAmount = beforeConsumedUnitMonth.multiply(actualDiscountAmount)
                    .setScale(2, RoundingMode.HALF_UP);
            consumedAmountMonth = prodTotalAmount.subtract(beforeTotalConsumedAmount);
            // 累计消耗金额 = 产品金额
            totalConsumedAmount = prodTotalAmount;
        }
        BigDecimal remainUnit = prodTotalUnit.subtract(totalConsumedUnit);
        BigDecimal remainAmount = prodTotalAmount.subtract(totalConsumedAmount);

        logger.info("processContractTransactionMonthly contractNo:{}, yearMonth:{}, consumedUnitMonth:{}, totalConsumedUnit:{}, remainUnit:{}, consumedAmountMonth:{}, totalConsumedAmount:{}, remainAmount:{}",
                contractNo, yearMonth, consumedUnitMonth, totalConsumedUnit, remainUnit, consumedAmountMonth, totalConsumedAmount, remainAmount);
        TblContractDeliveryTransactionBillMonthly bill = TblContractDeliveryTransactionBillMonthly
                .build(contractNo, yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode())
                .injectUnit(consumedUnitMonth, totalConsumedUnit, remainUnit)
                .injectAmount(consumedAmountMonth, totalConsumedAmount, remainAmount);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(bill);

        // 本期 订单、api订单涉及中国的单元数
        BigDecimal cnConsumedUnitMonth = contractDeliveryTransactionService.sumCnChangedUnitByContractNoAndDateRange(contractNo,
                DateUtils.formatDateToInt(firstDayOfMonth), DateUtils.formatDateToInt(endDayOfMonth));
        // QCC 本期消耗单元数 = 订单、api订单涉及中国的单元数 * 0.2 
        BigDecimal qccConsumedUnitMonth = cnConsumedUnitMonth.multiply(BigDecimal.valueOf(0.2)).setScale(2, RoundingMode.HALF_UP);
        // QCC 本期消耗金额 = QCC 本期消耗单元数 * 产品实际单价
        BigDecimal qccConsumedAmountMonth = qccConsumedUnitMonth.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        // QCC 上期月账累计消耗单元数
        BigDecimal cnBeforeTotalConsumedUnitMonth = contractDeliveryTransactionBillMonthlyService.getLatestTotalConsumedUnitBeforeYearMonthByContractNo(
            contractNo, yearMonthInt, ContractSubjectEnum.QCC.getCode());
        // QCC 累计消耗单元数 = QCC 上期月账累计消耗单元数 + QCC 本期消耗单元数
        BigDecimal qccTotalConsumedUnit = cnBeforeTotalConsumedUnitMonth.add(qccConsumedUnitMonth); 
        // QCC 累计消耗金额 = QCC 累计消耗单元数 * 产品实际单价
        BigDecimal qccTotalConsumedAmount = qccTotalConsumedUnit.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        logger.info("processContractTransactionMonthly qccBill contractNo:{}, yearMonth:{}, qccConsumedUnitMonth:{}, qccTotalConsumedUnit:{}, qccConsumedAmountMonth:{}, qccTotalConsumedAmount:{}",
                contractNo, yearMonth, qccConsumedUnitMonth, qccTotalConsumedUnit, qccConsumedAmountMonth, qccTotalConsumedAmount);
        TblContractDeliveryTransactionBillMonthly qccBill = TblContractDeliveryTransactionBillMonthly
                .build(contractNo, yearMonthInt, ContractSubjectEnum.QCC.getCode())
                .injectUnit(qccConsumedUnitMonth , qccTotalConsumedUnit, BigDecimal.ZERO)
                .injectAmount(qccConsumedAmountMonth, qccTotalConsumedAmount, BigDecimal.ZERO);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(qccBill);
        logger.info("processProdAccTransactionMonthly calculate done, contractNo:{}, yearMonth:{}", contractNo, yearMonth);
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 手动触发账单推送
     *
     * updated for v2.1.1 fengsw KNZT-6123
     * update for v2.1.2 fengsw KNZT-6211 推送合同并保存推送记录，推送成功之后，生成收入结算的产品统计信息
     * @param contractNoList
     * @param yearMonth
     */
    public void pushConfirmAmountContract2KzzByManual(List<String> contractNoList, YearMonth yearMonth) {
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);
        List<String> errorContractNoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(contractNoList)) {
            for (String contractNo : contractNoList) {
                // updated for v2.3.0 fengsw KNZT-8045 过滤增加内部测试账号数据
                TblContractDeliveryTransactionBillMonthly monthBill = getMonthBillExcludeTestAccount(contractNo, yearMonthInt);
                String result = pushConfirmAmountContract2KzzFirstDayPerMonth(monthBill, yearMonth);
                if (StringUtils.isNotBlank(result)) {
                    errorContractNoList.add(contractNo);
                }
            }
        }
        
        String contractNo = pushSelfConfirmAmountContract2KzzByManual(yearMonth);
        if (StringUtils.isNotBlank(contractNo)) {
            errorContractNoList.add(contractNo);
        }
        if (CollectionUtils.isNotEmpty(errorContractNoList)) {// 同步出错数据需要钉钉提醒
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk("收入确认合同推送异常", AlarmTypeEnum.PUSH_KZZ_CRATE_CONTRACT_ERROR, String.join(",", errorContractNoList), String.valueOf(yearMonthInt), DateUtils.getDate());
        }
    }

    /**
     * 排除测试账号数据
     * 
     * @param yearMonth
     * @return
     */
    public String pushSelfConfirmAmountContract2KzzByManual(YearMonth yearMonth) {
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);
        TblContractDeliveryTransactionBillMonthly monthBill = getSelfAccMonthBill(yearMonthInt);
        monthBill.setContractNo("KYCOL");
        String result = pushConfirmAmountContract2KzzFirstDayPerMonth(monthBill, yearMonth);
        if (StringUtils.isNotBlank(result)) {
            return monthBill.getContractNo();
        }
        return null;
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * <p>
     * 根据金额，构建合同信息，推送合同数据到客找找，如果有问题，需要打印错误日志，并钉钉提醒，
     * 固定主框架合同配置数据，项目缓存初始化的时候，加载到缓存对象中
     * 客户合同信息 调用接口获取合同信息内容 组装部分合同数据
     * 根据确认收入金额，填充回款计划、本次推送合同金额
     * 推送合同到客找找，打印输出日志，异常处理
     * 自助账号 合同签订人、创建人、回款计划跟进人取默认内置用户
     * 合同编号生成规则；正常合同 合同编号-年月；
     * 注：当前每月金额为0，负数都是要推送到客找找的
     * 
     * @param monthlyBill
     * @param yearMonth
     */
    public String pushConfirmAmountContract2KzzFirstDayPerMonth(TblContractDeliveryTransactionBillMonthly monthlyBill, YearMonth yearMonth) {
        if (monthlyBill == null || StringUtils.isBlank(monthlyBill.getContractNo()) || monthlyBill.getSubjectConsumedAmountMonth() == null || yearMonth == null) {
            return null;
        }

        String yearMonthStr = yearMonth.toString();
        // 已经同步成功过的数据，不用进行处理
        String kzzContractNo = StringUtils.startsWith(monthlyBill.getContractNo(), "KYCOL") ? "KYCOL" + monthlyBill.getYearMonthInt() : monthlyBill.getContractNo() + "-" + monthlyBill.getYearMonthInt();
        TblSettlementContractHist taskRecord = commTblSettlementContractHistService.getByContractNoAndYearMonth(kzzContractNo, yearMonthStr);
        if (taskRecord != null && StringUtils.equals("S", taskRecord.getSyncStatus())) {
            logger.info("contractNo:{}, has pushed kzz", monthlyBill.getContractNo());
            return null;
        }

        // 生产上的测试数据，不能推送到客找找
        if (StringUtils.contains(monthlyBill.getContractNo(), "TEST")) {
            logger.info("prod test contractNo:{}, doesn't need push kzz", monthlyBill.getContractNo());
            return null;
        }

        logger.info("contractNo: {}, yearMonth:{}, confirm amount: {} push start", monthlyBill.getContractNo(), monthlyBill.getYearMonthInt(), monthlyBill.getSubjectConsumedAmountMonth());

        if (Objects.isNull(taskRecord)) {
            taskRecord = new TblSettlementContractHist();
            taskRecord.setYearMonth(yearMonthStr);
        }

        try {
            BigDecimal amount = monthlyBill.getSubjectConsumedAmountMonth();
            KzzContractCreateRequestTO createRequestTO = createContractRequest(monthlyBill.getContractNo(), amount, monthlyBill.getYearMonthInt(), yearMonth);
            String requestJSONStr = JSON.toJSONString(createRequestTO);
            String responseStr = KzzApiInterface.createContract("/openapi/kzz/contract/v2/add", requestJSONStr);
            taskRecord.setContractNo(createRequestTO.getCode());
            taskRecord.setReqJson(requestJSONStr);
            taskRecord.setRespJson(responseStr);
            JSONObject jsonObj = JsonUtils.parseObject(responseStr);
            if (jsonObj == null) {
                throw new MessageException("接口响应内容为空");
            }
            if (!"OK".equals(jsonObj.getString("status"))) {
                throw new MessageException(jsonObj.getString("error"));
            }
            taskRecord.setSyncStatus("S");
            commTblSettlementContractHistService.save(taskRecord);
            // 保存收入确认数据, 先查一遍 如果没有则插入，有则更新
            List<TblSettlementContractRevenue> tblSettlementContractRevenueList = commTblSettlementContractRevenueService.getByContractNoAndYearMonth(kzzContractNo, yearMonthStr);
            List<TblContractDelivery> contractInfoList = contractDeliveryService.getByContractNo(monthlyBill.getContractNo());
            String currency = CollectionUtils.isNotEmpty(contractInfoList) ? contractInfoList.get(0).getAmountStd() : "USD";
            TblSettlementContractRevenue tblSettlementContractRevenue = null;
            if (CollectionUtils.isNotEmpty(tblSettlementContractRevenueList)) {
                tblSettlementContractRevenue = tblSettlementContractRevenueList.get(0);// 目前收入确认就一个产品数据
            }
            tblSettlementContractRevenue = buildConfirmContractProductInfo(tblSettlementContractRevenue, createRequestTO, yearMonthStr, currency);
            commTblSettlementContractRevenueService.save(tblSettlementContractRevenue);
            logger.info("contractNo: {}, relate kzz contractNo: {}, year Month:{}, confirm amount: {} push end", monthlyBill.getContractNo(), createRequestTO.getCode(), monthlyBill.getYearMonthInt(), monthlyBill.getSubjectConsumedAmountMonth());
        } catch (Exception e) {
            //除了重复合同以外的创建失败情况都需要关注
            logger.error("客找找收入确认合同：{}，推送失败：{}，请检查", monthlyBill.getContractNo(), e.getMessage());
            if (StringUtils.contains(e.getMessage(), "重复")) {
                return null;
            } else {
                taskRecord.setSyncStatus("F");
                commTblSettlementContractHistService.save(taskRecord);
                return e.getMessage();
            }
        }
        return null;
    }

    public static TblSettlementContractRevenue buildConfirmContractProductInfo(TblSettlementContractRevenue contractRevenue, KzzContractCreateRequestTO createRequestTO, String yearMonth, String currency) {
        if(Objects.isNull(contractRevenue)) {
            contractRevenue = new TblSettlementContractRevenue();
        }
        contractRevenue.setContractNo(createRequestTO.getCode());
        contractRevenue.setCustomerName(ContractSubjectEnum.QCC_GLOBAL.getDesc());
        contractRevenue.setBillMonth(yearMonth);
        contractRevenue.setCurrency(currency);
        contractRevenue.setContractAmount(createRequestTO.getAmount());
        JSONArray jsonArray = createRequestTO.getCustomObjectValues().getJSONArray("CFLD202208300061");
        if (jsonArray != null && jsonArray.size() > 0) {
            contractRevenue.setProduct(jsonArray.getString(0));
            contractRevenue.setChildProduct(jsonArray.getString(0));
        }
        contractRevenue.setSignedDate(DateUtils.parseDate(createRequestTO.getSignerDate(), DateUtils.DATE_FORMAT));
        contractRevenue.setContractStatus("签约");// 默认值
        contractRevenue.setSigner("系统内置用户");// 默认值
        contractRevenue.setInvoicedAmount(BigDecimal.ZERO);
        contractRevenue.setReceivedAmount(BigDecimal.ZERO);
        contractRevenue.setServiceStartDate(DateUtils.parseDate(createRequestTO.getBeginDate(), DateUtils.DATE_FORMAT));
        contractRevenue.setServiceEndDate(DateUtils.parseDate(createRequestTO.getExpiredDate(), DateUtils.DATE_FORMAT));
        contractRevenue.setServicePeriod(1);// 默认固定为 1个月
        return contractRevenue;
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 根据合同和年月信息获取确认收入金额
     * updated for v2.3.0 fengsw KNZT_8045 增加内部测试账号数据过滤
     *
     * @param contractNo
     * @return
     */
    public TblContractDeliveryTransactionBillMonthly getMonthBillExcludeTestAccount(String contractNo, Integer yearMonthInt) {
        return contractDeliveryTransactionBillMonthlyService.getByContractNoAndSubjectAndYearMonthExcludeTestAccount(contractNo, ContractSubjectEnum.QCC.getCode(), yearMonthInt);
    }

    public TblContractDeliveryTransactionBillMonthly getSelfAccMonthBill(Integer yearMonthInt) {
        return contractDeliveryTransactionBillMonthlyService.getSelfAccMonthBillByContractNoAndSubjectAndYearMonth(ContractSubjectEnum.QCC.getCode(), yearMonthInt);
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 构建创建合同请求
     *
     * @param contractNo
     * @return
     */
    private KzzContractCreateRequestTO createContractRequest(String contractNo, BigDecimal amount, Integer yearMonthInt, YearMonth yearMonth) throws MessageException {
        KzzContractCreateRequestTO createRequestTO = new KzzContractCreateRequestTO();
        // 根据合同编号判断是否为自助或者签约合同账号
        JSONObject customObjectValues = new JSONObject();// 合同自定义字段
        String contractNoKzz;
        // updated by v2.0.9 fengsw KNZT-5929 合同签订人、跟进人取默认内置用户
        String singerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("singerIdSelf");
        MsgExceptionUtils.checkIsNull(singerId1, "签订人信息缺失，请检查");
        long singerId = Long.parseLong(singerId1);
        long currencyId;
        if (contractNo.startsWith("KYCOL")) {// 自助账号合同签订人  // 测试 100740 生产 239729 合同编号 年月日
            contractNoKzz = "KYCOL" + yearMonthInt;
            String currencyIdSelf = sysConfigCacheService.getCreateContractConfigValueByKey("currencyIdSelf");
            MsgExceptionUtils.checkIsNull(currencyIdSelf, "货币单位缺失，请检查");
            currencyId = Long.parseLong(currencyIdSelf);
        } else {
            contractNoKzz = contractNo + "-" + yearMonthInt;
            String dbContractCurrencyId = contractDeliveryTransactionBillMonthlyService.getCurrencyIdByContractNo(contractNo);
            MsgExceptionUtils.checkIsNull(dbContractCurrencyId, "合同货币信息缺失，请检查");
            currencyId = Long.parseLong(dbContractCurrencyId);
        }
        
        long parentContractId;// 关联主合同id  测试  162114 生产92949
        String parentContractId1 = sysConfigCacheService.getCreateContractConfigValueByKey("parentContractId");
        MsgExceptionUtils.checkIsNull(parentContractId1, "关联主合同缺失，请检查");
        parentContractId = Long.parseLong(parentContractId1);
        
        long contactId; //联系人 1154585 测试 生产 3108863
        String contactId1 = sysConfigCacheService.getCreateContractConfigValueByKey("contactId");
        MsgExceptionUtils.checkIsNull(contactId1, "联系人缺失，请检查");
        contactId = Long.parseLong(contactId1);

        long customerId;// 客户id 测试 5216091 生产 9815343
        String customerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("customerId");
        MsgExceptionUtils.checkIsNull(customerId1, "客户id缺失，请检查");
        customerId = Long.parseLong(customerId1);

        createRequestTO.setCode(contractNoKzz);// 合同编号
        createRequestTO.setCustomerId(customerId);// 客户id
        createRequestTO.setRenewType(1);// 续约类型 一次性合同
        createRequestTO.setContractType(3);//类型-框架子合同
        createRequestTO.setSkipApproval(1);// 默认跳过审批，否则客找找无法保存指定的开始日期和到期日期

        int serviceLimit;// 服务年限
        String serviceLimit1 = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023042500004");
        MsgExceptionUtils.checkIsNull(serviceLimit1, "服务年限缺失，请检查");
        serviceLimit = Integer.parseInt(serviceLimit1);
        customObjectValues.put("CFLD2023042500004", serviceLimit);//服务年限

        //开始时间 推送周期当月1⽇
        //结束时间 推送周期当月最后⼀天
        LocalDate beginDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth(); // 这个时间同时应用到签订时间 +" 23:59:59"
        createRequestTO.setBeginDate(DateUtils.formatDate(beginDate));
        createRequestTO.setExpiredDate(DateUtils.formatDate(endDate));

        //主产品-QCC与企查查三⽅合同的结算产品
        String mainProduct = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300061");
        MsgExceptionUtils.checkIsNull(mainProduct, "主产品缺失，请检查");
        customObjectValues.put("CFLD202208300061", Lists.newArrayList(mainProduct));

        // 关联产品填充
        KzzContractCreateRequestTO.ContractProduct contractProduct = new KzzContractCreateRequestTO.ContractProduct();
        int mainProductId; // 主产品id 测试 21741；生产 23104
        String mainProductId1 = sysConfigCacheService.getCreateContractConfigValueByKey("mainProductId");
        MsgExceptionUtils.checkIsNull(mainProductId1, "主产品id缺失，请检查");
        mainProductId = Integer.parseInt(mainProductId1);
        contractProduct.setProductId(mainProductId);
        contractProduct.setPeriod(1);
        contractProduct.setTimeLimits(1);
        contractProduct.setPrice(amount);
        contractProduct.setDiscountPrice(amount);
        contractProduct.setDiscountRate(new BigDecimal("100"));
        contractProduct.setQuantity(1);
        contractProduct.setTotalPrice(amount);
        createRequestTO.setProducts(Lists.newArrayList(contractProduct));

        createRequestTO.setAmount(amount);//合同金额
        createRequestTO.setTotal(amount);//合同原金额
        createRequestTO.setOrderDiscountRate(new BigDecimal("100"));// 整单折扣率
        createRequestTO.setComDiscountRate(new BigDecimal("100"));// 综合折扣率

        //开票⽅式-先票后款
        String billType = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023033100006");
        MsgExceptionUtils.checkIsNull(billType, "开票⽅式缺失，请检查");
        customObjectValues.put("CFLD2023033100006", Lists.newArrayList(billType));

        //回款⽅式-后付费
        String paymentType = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023033100004");
        MsgExceptionUtils.checkIsNull(paymentType, "回款⽅式缺失，请检查");
        customObjectValues.put("CFLD2023033100004", Lists.newArrayList(paymentType));

        //结算周期-半年度结算
        String chargeType = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023033100002");
        MsgExceptionUtils.checkIsNull(chargeType, "结算周期缺失，请检查");
        customObjectValues.put("CFLD2023033100002", Lists.newArrayList(chargeType));

        // 回款计划填充
        KzzContractCreateRequestTO.ContractPayment contractPayment = new KzzContractCreateRequestTO.ContractPayment();
        contractPayment.setLeaderId(singerId);
        contractPayment.setCreateBy(singerId);
        contractPayment.setCode(contractNoKzz + ".1");
        contractPayment.setAmount(amount);
        contractPayment.setType(2);// 回款类型，常规

        YearMonth now = YearMonth.now();
        LocalDate localDate = now.atEndOfMonth();
        contractPayment.setPaymentDate(DateUtils.toDate(localDate));// 预计回款时间为推送当前时间的当月30天
        JSONObject obj = new JSONObject();

        // 回款条件-开票之日起
        String paymentConditions = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032100002");
        MsgExceptionUtils.checkIsNull(paymentConditions, "回款条件缺失，请检查");
        obj.put("CFLD2023032100002", Lists.newArrayList(paymentConditions));

        //账龄天数-30
        String agingDays = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202111050008");
        MsgExceptionUtils.checkIsNull(agingDays, "账龄天数缺失，请检查");
        obj.put("CFLD202111050008", Integer.valueOf(agingDays));

        contractPayment.setCustomObjectValues(obj);
        createRequestTO.setPayments(Lists.newArrayList(contractPayment));

        // 收款⽅式-⽹银转账
        String paymentMethod = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300081");
        MsgExceptionUtils.checkIsNull(paymentMethod, "收款⽅式缺失，请检查");
        customObjectValues.put("CFLD202208300081", Lists.newArrayList(paymentMethod));

        //客户需要合同-不需要合同
        String customerNeedContract = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300054");
        MsgExceptionUtils.checkIsNull(customerNeedContract, "客户需要合同配置项缺失，请检查");
        customObjectValues.put("CFLD202208300054", Lists.newArrayList(customerNeedContract));

        //客户是否先⽤印-否
        String needSign = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032900009");
        MsgExceptionUtils.checkIsNull(needSign, "客户是否先⽤印配置项缺失，请检查");
        customObjectValues.put("CFLD2023032900009", Lists.newArrayList(needSign));

        //是否标准模板-否
        String standardTemplate = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300057");
        MsgExceptionUtils.checkIsNull(standardTemplate, "是否标准模板配置项缺失，请检查");
        customObjectValues.put("CFLD202208300057", Lists.newArrayList(standardTemplate));

        createRequestTO.setContactId(contactId);// 联系人

        //我司签约主体（⼄⽅）- 企查查科技股份有限公司
        String ourSigningParty = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300069");
        MsgExceptionUtils.checkIsNull(ourSigningParty, "我司签约主体（⼄⽅）配置项缺失，请检查");
        customObjectValues.put("CFLD202208300069", Lists.newArrayList(ourSigningParty));

        // 客户开票名称
        String customerInvoicingName = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300090");
        MsgExceptionUtils.checkIsNull(customerInvoicingName, "客户开票名称配置项缺失，请检查");
        customObjectValues.put("CFLD202208300090", customerInvoicingName);

        // 收件⼈姓名
        String recipientName = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300098");
        MsgExceptionUtils.checkIsNull(recipientName, "收件⼈姓名配置项缺失，请检查");
        customObjectValues.put("CFLD202208300098", recipientName);

        // 收件人电话
        String recipientPhone = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300101");
        MsgExceptionUtils.checkIsNull(recipientPhone, "收件人电话配置项缺失，请检查");
        customObjectValues.put("CFLD202208300101", recipientPhone);

        // 收件人地址
        String recipientAddress = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300100");
        MsgExceptionUtils.checkIsNull(recipientAddress, "收件人地址配置项缺失，请检查");
        customObjectValues.put("CFLD202208300100", recipientAddress);

        //合同名称 KYC内部结算
        String contractName = sysConfigCacheService.getCreateContractConfigValueByKey("contractName");
        MsgExceptionUtils.checkIsNull(contractName, "合同名称配置项缺失，请检查");
        createRequestTO.setName(contractName);

        // 合同来源-线下合同
        String contractSource = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023040400002");
        MsgExceptionUtils.checkIsNull(contractSource, "合同来源配置项缺失，请检查");
        customObjectValues.put("CFLD2023040400002", Lists.newArrayList(contractSource));

        createRequestTO.setCustomObjectValues(customObjectValues);
        createRequestTO.setParentContractId(parentContractId);// 关联主合同
        createRequestTO.setSignerId(singerId);// 签订人
        createRequestTO.setCreateBy(singerId);// 创建人
        createRequestTO.setCurrencyId(currencyId);
        // updated for v2.3.5 fengsw KNZT-8451 签订时间取当前账单月份的最后一天 +23:59:59 作为合同时间
        createRequestTO.setSignerDate(createRequestTO.getExpiredDate() + " 23:59:59");// 签订时间-当前时间,精确到时分秒
        createRequestTO.setCreateDate(DateUtils.getDate(DateUtils.DATETIME_FORMAT));// 创建时间-当前时间
        
        //合同状态-签约
        String contractStatus = sysConfigCacheService.getCreateContractConfigValueByKey("contractStatus");
        MsgExceptionUtils.checkIsNull(contractStatus, "合同状态配置项缺失，请检查");
        createRequestTO.setStatus(Integer.parseInt(contractStatus));
        return createRequestTO;
    }

/*    *//**
     * 计算自助账户月账
     * added for v1.9.4 KNZT-4324
     *
     * @param yearMonth
     * @return
     *//*
    @Deprecated
    public void processSelfAccountProdAccTransactionMonthly(YearMonth yearMonth) {
        String selfAccountContractDeliveryId = Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID;
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);

        BigDecimal totalConsumed = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(selfAccountContractDeliveryId);
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate endDayOfMonth = yearMonth.atEndOfMonth();
        BigDecimal consumedMonth = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(selfAccountContractDeliveryId,
                DateUtils.formatDateToInt(firstDayOfMonth), DateUtils.formatDateToInt(endDayOfMonth));
        TblContractDeliveryTransactionBillMonthly bill = new TblContractDeliveryTransactionBillMonthly();
        bill.setYearMonthInt(yearMonthInt);
        bill.setSubject(ContractSubjectEnum.QCC_GLOBAL.getCode());
        bill.setProdName("自助账户");
        bill.injectUnitAndAmount4SelfAccount(consumedMonth, totalConsumed);
        bill.setContractNo(Constants.Delivery.SELF_ACCOUNT_CONTRACT_NO);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(bill);
        logger.info("processSelfAccountProdAccTransactionMonthly totalConsumed:{}, consumedMonth:{}", totalConsumed, consumedMonth);
    }*/

    public void processPeriodProdMonthly(String contractNo, YearMonth yearMonth) {
        TblContractDeliveryProdPeriod prodPeriod = prodPeriodService.getProdPeriodByContractNo(contractNo);
        logger.info("processProdAccTransactionMonthly start, prodAccId:{}, yearMonth:{}", prodPeriod.getContractDeliveryProdAccId(), yearMonth);

        TblContractDelivery contractDelivery = contractDeliveryService.get(prodPeriod.getContractDeliveryId());
        if (Objects.isNull(contractDelivery)) {
            logger.error("contractDelivery is null, contractDeliveryId:{}", prodPeriod.getContractDeliveryId());
            return;
        }

        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);

        BigDecimal totalConsumedAmountBefore = contractDeliveryTransactionBillMonthlyService.getLatestTotalConsumedAmountBeforeYearMonthByContractNo(
                contractDelivery.getContractNo(), yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode());
        if (totalConsumedAmountBefore.compareTo(prodPeriod.getTotalAmount()) >= 0) {
            logger.info("processPeriodProdMonthly periodProd totalAmount has done, totalConsumedUnitBefore:{}, totalAmount:{}",
                    totalConsumedAmountBefore, prodPeriod.getTotalAmount());
            return;
        }

        BigDecimal termAmount = prodPeriod.getTermAmount();
        BigDecimal totalConsumedAmount = totalConsumedAmountBefore.add(termAmount);
        BigDecimal remainAmount = prodPeriod.getTotalAmount().subtract(totalConsumedAmount);

        TblContractDeliveryTransactionBillMonthly bill = TblContractDeliveryTransactionBillMonthly
                .build(contractDelivery.getContractNo(), prodPeriod.getProdName(), yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode())
                .injectUnit(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO)
                .injectAmount(termAmount, totalConsumedAmount, remainAmount);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(bill);
        logger.info("processPeriodProdMonthly calculate done, prodAccId:{}, yearMonth:{}",
                prodPeriod.getContractDeliveryProdAccId(), yearMonth);
    }

    /**
     * 递归找产品账户进行扣减，生成流水
     *
     * @param accList         产品账户集合
     * @param accIndex        使用的账户index
     * @param unit            扣减的unit
     * @param baseTransaction 基本的流水对象
     * @return
     */
    private boolean recursionGenerateTransaction(List<TblContractDeliveryProdAcc> accList, int accIndex, BigDecimal unit,
                                                 TblContractDeliveryTransaction baseTransaction) throws MessageException {
        logger.info("recursionGenerateTransaction companyId:{}, accIndex:{}, unit:{}", baseTransaction.getCompanyId(), accIndex, unit);
        if (unit.compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("recursionGenerateTransaction 扣减完成，companyId:{}", baseTransaction.getCompanyId());
            return true;
        }
        if (accIndex >= accList.size()) {
            logger.info("recursionGenerateTransaction 所有的产品账户没有足够的余额进行扣减流水，companyId:{}", baseTransaction.getCompanyId());
            return false;
        }
        TblContractDeliveryProdAcc acc = accList.get(accIndex);
        BigDecimal remainUnit = acc.calTotalRemainUnit();
        BigDecimal changedUnit = remainUnit.compareTo(unit) >= 0 ? unit : remainUnit;

        if (changedUnit.compareTo(BigDecimal.ZERO) > 0) {
            TblContractDeliveryTransaction transaction = new TblContractDeliveryTransaction();
            BeanUtils.copyProperties(baseTransaction, transaction);
            transaction.setIsNewRecord(false);
            transaction.injectChangedUnit(changedUnit);
            this.saveTransactionAndChangeAccUnit(transaction, acc);
            unit = unit.subtract(changedUnit);
        }

        return recursionGenerateTransaction(accList, accIndex + 1, unit, baseTransaction);
    }



    public void saveTransactionAndChangeAccUnit(TblContractDeliveryTransaction transaction, TblContractDeliveryProdAcc prodAcc) throws MessageException {
        // 保存流水
        transaction.injectAccInfo(prodAcc);
        contractDeliveryTransactionService.save(transaction);
        logger.info("saveTransactionAndChangeAccUnit 生成订单流水, companyId:{}, prodAccId:{}, changedUnit:{}",
                transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), transaction.getChangedUnit());

        // 处理产品账户额度单元
        BigDecimal oriTotalConsumedUnit = prodAcc.getTotalConsumedUnit();
        BigDecimal afterTotalConsumedUnit = oriTotalConsumedUnit.add(transaction.getChangedUnit());
        int accUpdated = contractDeliveryProdAccService.updateConsumedUnit(
                transaction.getContractDeliveryProdAccId(), afterTotalConsumedUnit, oriTotalConsumedUnit);
        MsgExceptionUtils.failBuild(accUpdated <= 0, "err.amount.insufficient");
        logger.info("saveTransactionAndChangeAccUnit 扣减账户余额, companyId:{},  prodAccId:{}, afterTotalConsumedUnit:{}, oriTotalConsumedUnit:{}",
                transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), afterTotalConsumedUnit, oriTotalConsumedUnit);
    }

    /**
     * 计算月度报表
     *
     * @param companyId
     * @param targetYearMonth
     * @param isPreview
     * @return
     */
    public TblCompStatementMonthly calStatementMonthly(String companyId, YearMonth targetYearMonth, boolean isPreview) throws MessageException, IOException {
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "公司不存在");
        TblCompStatementMonthly statementMonthly = null;
        if (CompTypeEnum.SIGN.getCode().equals(company.getType())) {
            statementMonthly = this.calStatementMonthly4KA(company, targetYearMonth);
        } else if (CompTypeEnum.CLIENT.getCode().equals(company.getType())) {
            statementMonthly = this.calStatementMonthly4SelfAccount(company, targetYearMonth);
        }
        if(statementMonthly == null) {
            logger.info("calStatementMonthly companyId:{}, un support invoice", company.getId());
            return null;
        }

        if (!isPreview) {
            YearMonth now = YearMonth.now();
            if (now.isAfter(targetYearMonth)) {
                commTblCompStatementMonthlyService.saveOrUpdateStatement(statementMonthly);
                logger.info("calStatementMonthly companyId:{}, save to db", company.getId());
            }
        }
        return statementMonthly;
    }


    /**
     * 计算月度报表
     * added for v1.9.5 KNZT-4117
     * updated for lvcy v2.0.6 KNZT-5499 支持后付费账单
     *
     * @param company
     * @param targetYearMonth
     * @return
     */
    public TblCompStatementMonthly calStatementMonthly4KA(Company company, YearMonth targetYearMonth) throws MessageException, IOException {
        String companyId = company.getId();
        LocalDate firstDayOfMonth = targetYearMonth.atDay(1);
        LocalDate endDayOfMonth = targetYearMonth.atEndOfMonth();
        YearMonth lastYearMonth = targetYearMonth.minusMonths(1);
        int beginDateInt = DateUtils.getDateInt(firstDayOfMonth);
        int endDateInt = DateUtils.getDateInt(endDayOfMonth);

        // 数据准备
        List<TblContractDeliveryProdAcc> prodAccList = contractDeliveryProdAccService.getByCompanyId(companyId);
        List<TblContractDeliveryProdAcc> deliveryProdAccList = prodAccList.stream()
                .filter(k -> DeliveryContracProdAccTypeEnum.getDeliveryType().contains(k.getType()))
                .collect(Collectors.toList());


        AtomicReference<String> amtStdRef = new AtomicReference<>();
        AtomicReference<String> latestContractDeliveryIdRef = new AtomicReference<>();
        deliveryProdAccList.stream()
                .max(Comparator.comparing(TblContractDeliveryProdAcc::getCreateDate))
                .map(TblContractDeliveryProdAcc::getContractDeliveryId)
                .ifPresent(k -> {
            TblContractDelivery contractDelivery = contractDeliveryService.get(k);
            if (Objects.nonNull(contractDelivery)) {
                amtStdRef.set(contractDelivery.getAmountStd());
                latestContractDeliveryIdRef.set(contractDelivery.getId());
            }
        });
        if (StringUtils.isBlank(amtStdRef.get()) || StringUtils.isBlank(latestContractDeliveryIdRef.get())) {
            logger.info("calStatementMonthly not exist delivery contract, companyId:{}", companyId);
            return null;
        }
        String symbol = AmtStdEnum.getSymbol(amtStdRef.get());
        String bankAccount = AmtStdEnum.getBankAccount(amtStdRef.get());

        List<TblContractDeliveryProdAcc> firstDayProdAccList = deliveryProdAccList
                .stream().filter(acc -> acc.ifInEffectRange(firstDayOfMonth)).collect(Collectors.toList());
        BigDecimal startCredit = BigDecimal.ZERO;
        BigDecimal startAmount = BigDecimal.ZERO;
        for (TblContractDeliveryProdAcc prodAcc : firstDayProdAccList) {
            BigDecimal totalUnit = prodAcc.getTotalUnit();
            BigDecimal consumedUnitUntilLastMonth = contractDeliveryTransactionService.sumChangedUnitByProAccAndDateRange(
                prodAcc.getId(), DateUtils.getDateInt(prodAcc.getBeginDate()), DateUtils.getDateInt(lastYearMonth.atEndOfMonth()));
            BigDecimal remainCredit = totalUnit.subtract(consumedUnitUntilLastMonth);
            BigDecimal remainAmount = remainCredit.multiply(prodAcc.getActualDiscountAmount());
            startCredit = startCredit.add(remainCredit);
            startAmount = startAmount.add(remainAmount);
            logger.info("companyId:{}, remainCredit:{} = totalUnit:{} - consumedUnitUntilLastMonth:{}",
                    companyId, remainCredit, totalUnit, consumedUnitUntilLastMonth);
        }

        List<TblContractDeliveryProdAcc> endDayProdAccList = deliveryProdAccList
                .stream().filter(acc -> acc.ifInEffectRange(endDayOfMonth)).collect(Collectors.toList());
        BigDecimal endCredit = BigDecimal.ZERO;
        BigDecimal endAmount = BigDecimal.ZERO;
        for (TblContractDeliveryProdAcc prodAcc : endDayProdAccList) {
            BigDecimal totalUnit = prodAcc.getTotalUnit();
            BigDecimal changedTotalUnitUntilThisMonth = contractDeliveryTransactionService.sumChangedUnitByProAccAndDateRange(
                prodAcc.getId(), DateUtils.getDateInt(prodAcc.getBeginDate()), DateUtils.getDateInt(endDayOfMonth));
            BigDecimal remainCredit = totalUnit.subtract(changedTotalUnitUntilThisMonth);
            BigDecimal remainAmount = remainCredit.multiply(prodAcc.getActualDiscountAmount());
            endCredit = endCredit.add(remainCredit);
            endAmount = endAmount.add(remainAmount);
            logger.info("companyId:{}, remainCredit:{} = totalUnit:{} - changedTotalUnitUnitThisMonth:{}",
                    companyId, remainCredit, totalUnit, changedTotalUnitUntilThisMonth);
        }

        // 计算所有剩余赠送额度余额
        BigDecimal endGiftedCredit = prodAccList.stream()
                .filter(k -> DeliveryContracProdAccTypeEnum.getGiveTypeList().contains(k.getType()))
                .map(TblContractDeliveryProdAcc::calTotalRemainUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算在本周期内充值的单元
        BigDecimal toppedUpCredit = deliveryProdAccList
                .stream().filter(acc -> {
                    LocalDate beginDate = DateUtils.toLocalDate(acc.getBeginDate());
                    return DateUtils.checkInDateRange(beginDate, firstDayOfMonth, endDayOfMonth);
                })
                .map(TblContractDeliveryProdAcc::getTotalUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算所有赠送的单元
        BigDecimal giftedCredit = BigDecimal.ZERO;
        List<StatementGiftedCreditDetailTO> giftedCreditDetailList = Lists.newArrayList();
        for (TblContractDeliveryProdAcc prodAcc : prodAccList) {
            if (DeliveryContracProdAccTypeEnum.getGiveTypeList().contains(prodAcc.getType())) {
                giftedCredit = giftedCredit.add(prodAcc.getTotalUnit());
                giftedCreditDetailList.add(new StatementGiftedCreditDetailTO(prodAcc.getTotalUnit(), prodAcc.getCreateDate()));
            }
        }

        // 计算在本周期内过期的单元
        BigDecimal expiredCredit = BigDecimal.ZERO;
        List<TblContractDeliveryProdAcc> expiredProdAccList = deliveryProdAccList
                .stream().filter(acc -> {
                    LocalDate beginDate = DateUtils.toLocalDate(acc.getEndDate());
                    return DateUtils.checkInDateRange(beginDate, firstDayOfMonth, endDayOfMonth);
                }).collect(Collectors.toList());
        for (TblContractDeliveryProdAcc prodAcc : expiredProdAccList) {
            expiredCredit = expiredCredit.add(prodAcc.calTotalRemainUnit());
            logger.info("companyId:{}, expiredCredit:{}", companyId, expiredCredit);
            if (BigDecimal.ZERO.compareTo(expiredCredit) == 0) {
                // 当过期金额为0时，查是否有确认收入的流水
                expiredCredit = contractDeliveryTransactionService.sumChangedUnitByContractDeliveryWithType(
                        prodAcc.getContractDeliveryId(), beginDateInt, endDateInt, Lists.newArrayList(TransactionTypeEnum.CONFIRM.getCode()));
                logger.info("companyId:{}, expiredCredit in confirm transaction:{}", companyId, expiredCredit);
            }
        }

        // 计算调整流水的总和
        BigDecimal adjustedCredit = BigDecimal.ZERO;
        // 计算消耗订单流水的总和
        BigDecimal usedCredit = BigDecimal.ZERO;
        BigDecimal usedAmount = BigDecimal.ZERO;
        // 计算退款订单流水的总和
        BigDecimal refundedCredit = BigDecimal.ZERO;
        Map<String, TransactionOrderUserConsumeTO> userConsumeMap = Maps.newHashMap();
        // 计算最终变化额度
        BigDecimal totalChangedCredit = BigDecimal.ZERO;
        BigDecimal totalChangedAmount = BigDecimal.ZERO;
        for (TblContractDeliveryProdAcc prodAcc : deliveryProdAccList) {
            String contractDeliveryId = prodAcc.getContractDeliveryId();
            BigDecimal actualDiscountAmount = prodAcc.getActualDiscountAmount();
            // 计算调整流水的总和
            BigDecimal partAdjustedCredit = BigDecimal.ZERO;
            BigDecimal partAdjustedAmount = BigDecimal.ZERO;
            if (CompPayTypeEnum.AFTER.getCode().equals(company.getPayType())) {
                partAdjustedCredit = contractDeliveryTransactionService.sumCorrectedUnitReleatedOrderTransaction(
                    contractDeliveryId, beginDateInt, endDateInt);
                adjustedCredit = adjustedCredit.add(partAdjustedCredit);
                partAdjustedAmount = partAdjustedCredit.multiply(actualDiscountAmount);

                // 根据用户汇总消耗
                List<String> withoutCorrectTransactionList = Lists.newArrayList(TransactionTypeEnum.ORDER.getCode(), TransactionTypeEnum.REFUND_ORDER.getCode());
                List<TransactionOrderUserConsumeTO> userConsumeList = contractDeliveryTransactionService.getChangedSumOrderUnitGroupByUser(
                        contractDeliveryId, beginDateInt, endDateInt, withoutCorrectTransactionList);
                updateUserConsumeMap(userConsumeList, actualDiscountAmount, userConsumeMap);

                List<TransactionOrderUserConsumeTO> userAdjustedConsumeList = contractDeliveryTransactionService.getCorrectedUnitRelatedOrderTransactionByUser(
                        contractDeliveryId, beginDateInt, endDateInt);
                updateUserConsumeMap(userAdjustedConsumeList, actualDiscountAmount, userConsumeMap);

            } else if (CompPayTypeEnum.PREPAID.getCode().equals(company.getPayType())) {
                partAdjustedCredit = contractDeliveryTransactionService.sumChangedUnitByContractDeliveryWithType(
                    contractDeliveryId, beginDateInt, endDateInt, Lists.newArrayList(TransactionTypeEnum.CORRECT_ORD.getCode()));
                adjustedCredit = adjustedCredit.add(partAdjustedCredit);
                partAdjustedAmount = partAdjustedCredit.multiply(actualDiscountAmount);

                // 根据用户汇总消耗
                List<String> withoutCorrectTransactionList = Lists.newArrayList(TransactionTypeEnum.ORDER.getCode(), TransactionTypeEnum.REFUND_ORDER.getCode(), TransactionTypeEnum.CORRECT_ORD.getCode());
                List<TransactionOrderUserConsumeTO> userConsumeList = contractDeliveryTransactionService.getChangedSumOrderUnitGroupByUser(
                        contractDeliveryId, beginDateInt, endDateInt, withoutCorrectTransactionList);
                updateUserConsumeMap(userConsumeList, actualDiscountAmount, userConsumeMap);
            }
          

            // 计算消耗订单流水的总和
            BigDecimal partUsedCredit = contractDeliveryTransactionService.sumChangedUnitByContractDeliveryWithType(
                    contractDeliveryId, beginDateInt, endDateInt, Lists.newArrayList(TransactionTypeEnum.ORDER.getCode()));
            BigDecimal partUsedAmount = partUsedCredit.multiply(actualDiscountAmount);
            usedCredit = usedCredit.add(partUsedCredit);
            usedAmount = usedAmount.add(partUsedAmount);
            // 计算退款订单流水的总和
            BigDecimal partRefundedCredit = contractDeliveryTransactionService.sumChangedUnitByContractDeliveryWithType(
                    contractDeliveryId, beginDateInt, endDateInt, Lists.newArrayList(TransactionTypeEnum.REFUND_ORDER.getCode()));
            refundedCredit = refundedCredit.add(partRefundedCredit.abs());
            BigDecimal partRefundedAmount = partRefundedCredit.multiply(actualDiscountAmount);

            // 计算最终变化额度
            totalChangedCredit = totalChangedCredit.add(partUsedCredit).add(partAdjustedCredit).add(partRefundedCredit);
            totalChangedAmount = totalChangedAmount.add(partUsedAmount).add(partAdjustedAmount).add(partRefundedAmount);
        }

        // 计算总计amount消耗
        BigDecimal userTotalAmount = BigDecimal.ZERO;
        if (!userConsumeMap.isEmpty()) {
            userTotalAmount = userConsumeMap.values().stream()
                    .map(TransactionOrderUserConsumeTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 根据reportType、changedUnit group by,  后期接入api订单的item名称是否需要调整
//        List<TransactionItemConsumeTO> itemConsumeList = contractDeliveryTransactionService.getCompanyConsumeItemGroupByOrderTypeUnit(
//                companyId, symbol, beginDateInt, endDateInt, refundedCredit, adjustedCredit);

        List<String> serviceDescList = commTblContractDeliveryChargeUnitService.getServiceDescList4Bill(companyId, latestContractDeliveryIdRef.get());


        TblCompStatementMonthly statementMonthly = new TblCompStatementMonthly();
        String invoiceNo = commTblCompStatementMonthlyService.generateInvoiceNo(companyId, targetYearMonth);
        statementMonthly.setInvoiceNo(invoiceNo);
        statementMonthly.setCompanyId(companyId);
        statementMonthly.setStatementMonth(DateUtils.formatYearMonthInt(targetYearMonth));
        statementMonthly.setStartCredit(startCredit);
        statementMonthly.setStartAmount(startAmount);
        statementMonthly.setEndCredit(endCredit);
        statementMonthly.setEndAmount(endAmount);
        statementMonthly.setCreditsToppedUp(toppedUpCredit);
        statementMonthly.setCreditsGifted(giftedCredit);
        statementMonthly.setCreditsExpired(expiredCredit);
        statementMonthly.setCreditsUsed(usedCredit);
        statementMonthly.setAmountUsed(usedAmount);
        statementMonthly.setCreditsRefunded(refundedCredit);
        statementMonthly.setEndGiftedCredit(endGiftedCredit);
        LocalDate date = firstDayOfMonth.plusMonths(1L);
        statementMonthly.setIssueDate(DateUtils.toDate(date));
        statementMonthly.setTotalCreditDue(totalChangedCredit);
        statementMonthly.setTotalAmountDue(totalChangedAmount);
        if (CompPayTypeEnum.AFTER.getCode().equals(company.getPayType())) {
            statementMonthly.setReturnStatus(totalChangedAmount.compareTo(BigDecimal.ZERO) == 0 ? ReturnStatusEnum.RETURNED.getCode() : ReturnStatusEnum.PENDING_CONFIRM.getCode());
        }


        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("statementMonthly", statementMonthly);
        paramMap.put("invoiceNo", invoiceNo);
        paramMap.put("giftedCreditDetailList", giftedCreditDetailList);
        paramMap.put("userConsumeList", Lists.newArrayList(userConsumeMap.values()));
        paramMap.put("firstDayOfMonth", DateUtils.formatDateEn(firstDayOfMonth, DateUtils.DATE_FORMAT_DDMMMYYYY));
        paramMap.put("endDayOfMonth", DateUtils.formatDateEn(endDayOfMonth, DateUtils.DATE_FORMAT_DDMMMYYYY));
        paramMap.put("amtStd", amtStdRef.get());
        paramMap.put("amtStdSymbol", symbol);
        paramMap.put("companyId", maskCompanyId(companyId));
        paramMap.put("companyType", company.getType());
        paramMap.put("issueDate", DateUtils.formatDateForSg(statementMonthly.getIssueDate(), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH));
        paramMap.put("to", company.getShortName());
        paramMap.put("adjustedCredit", adjustedCredit);
        paramMap.put("totalChangedCredit", totalChangedCredit);
        paramMap.put("totalChangedAmountDesc", CommTblContractDeliveryTransactionService.formatBillAmountWithSymbol(symbol, totalChangedAmount));
        paramMap.put("userTotalAmountDesc", CommTblContractDeliveryTransactionService.formatBillAmountWithSymbol(symbol, userTotalAmount));
        paramMap.put("payType", StringUtils.isNotBlank(company.getPayType()) ? company.getPayType() : CompPayTypeEnum.PREPAID.getCode());
//        paramMap.put("itemConsumeList", itemConsumeList);
        paramMap.put("bankAccount", bankAccount);
        paramMap.put("serviceDescList", serviceDescList);
        CompanyAddressTO companyAddress = commCompUserService.getCompanyAddress(companyId);
        if (companyAddress != null) {
            List<String> addressInfoList = new ArrayList<>();
            CollectionUtils.addIgnoreNull(addressInfoList, companyAddress.getAddressLine1());
            CollectionUtils.addIgnoreNull(addressInfoList, companyAddress.getAddressLine2());
            CollectionUtils.addIgnoreNull(addressInfoList, companyAddress.getCity());
            CollectionUtils.addIgnoreNull(addressInfoList,
                    Lists.newArrayList(companyAddress.getProvince(), companyAddress.getZipCode()).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(" ")));
            CollectionUtils.addIgnoreNull(addressInfoList, GlobalAreaEnum.getShortNameEnByNameCode(companyAddress.getCountry()));
            String addressInfoStr = addressInfoList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
            if (StringUtils.isNotBlank(addressInfoStr)) {
                paramMap.put("address", addressInfoStr);
            }
        }
//        List<String> addressList = commSysCompInfoExtService.retrieveInfoByCompanyIdAndType(company.getId(), Constants.CompInfoColExt.ADDRESS);
//        if (addressList != null && !addressList.isEmpty()) {
//            paramMap.put("address", addressList.get(0));
//        }
//        List<String> nationalityList = commSysCompInfoExtService.retrieveInfoByCompanyIdAndType(company.getId(), Constants.CompInfoColExt.NATIONALITY);
//        if (nationalityList != null && !nationalityList.isEmpty()) {
//            paramMap.put("nationality", nationalityList.get(0));
//        }

        String url = generateStatementPdf(company, statementMonthly.getStatementMonth(), paramMap);
        logger.info("calStatementMonthly companyId:{}, statement url:{}", companyId, url);
        statementMonthly.setUrl(url);
        return statementMonthly;
    }

    /**
     * 更新用户消耗统计
     * @param userConsumeList
     * @param actualDiscountAmount
     * @param userConsumeMap
     */
    private static void updateUserConsumeMap(List<TransactionOrderUserConsumeTO> userConsumeList, BigDecimal actualDiscountAmount, Map<String, TransactionOrderUserConsumeTO> userConsumeMap) {
        for (TransactionOrderUserConsumeTO userConsumeTO : userConsumeList) {
            String loginName = userConsumeTO.getLoginName();
            BigDecimal credits = userConsumeTO.getCredits();
            BigDecimal amount = credits.multiply(actualDiscountAmount);
            TransactionOrderUserConsumeTO existUserConsumeTO = userConsumeMap.get(loginName);
            if (existUserConsumeTO == null) {
                userConsumeTO.setAmount(amount);
                userConsumeMap.put(loginName, userConsumeTO);
            } else {
                existUserConsumeTO.setCredits(existUserConsumeTO.getCredits().add(credits));
                existUserConsumeTO.setAmount(existUserConsumeTO.getAmount().add(amount));
                userConsumeMap.put(loginName, existUserConsumeTO);
            }
        }
    }


    /**
     * 计算月度报表 for 自助账户
     * added for v1.9.5 KNZT-4117
     *
     * @param company
     * @param targetYearMonth
     * @return
     */
    public TblCompStatementMonthly calStatementMonthly4SelfAccount(Company company, YearMonth targetYearMonth) throws MessageException, IOException {
        LocalDate firstDayOfMonth = targetYearMonth.atDay(1);
        LocalDate endDayOfMonth = targetYearMonth.atEndOfMonth();
        List<TblContractDeliveryTransaction> transactionList = contractDeliveryTransactionService.getChangedByCompanyIdAdnDateRange(
                company.getId(), DateUtils.getDateInt(firstDayOfMonth), DateUtils.getDateInt(endDayOfMonth));

        BigDecimal usedCredit = transactionList.stream()
                .filter(k -> TransactionTypeEnum.ORDER.getCode().equals(k.getTransactionType())
                        && k.getChangedUnit().compareTo(BigDecimal.ZERO) > 0)
                .map(TblContractDeliveryTransaction::getChangedUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Credit pack purchase：统计当月1日至月底的TOP-线上充值的金额
        BigDecimal topUpAmount = contractDeliveryTransactionService.sumChangeUnitByCompanyIdAndDateRange(
                company.getId(), DateUtils.getDateInt(firstDayOfMonth), DateUtils.getDateInt(endDayOfMonth), Lists.newArrayList(TransactionTypeEnum.TOP_UP.getCode()))
                .abs();
        // Direct purchase：统计当月1日至月底的TOP_C-线上直销的金额
        BigDecimal topUpConsumedAmount = contractDeliveryTransactionService.sumChangeUnitByCompanyIdAndDateRange(
                company.getId(), DateUtils.getDateInt(firstDayOfMonth), DateUtils.getDateInt(endDayOfMonth), Lists.newArrayList(TransactionTypeEnum.TOP_UP_CONSUMED.getCode()))
                .abs();
        // Total Amount Due：当月的Credit pack purchase+Direct purchase总金额
        BigDecimal totalAmountDue = topUpAmount.add(topUpConsumedAmount)
                .abs();

        TblCompStatementMonthly statementMonthly = new TblCompStatementMonthly();
        String invoiceNo = commTblCompStatementMonthlyService.generateInvoiceNo(company.getId(), targetYearMonth);
        statementMonthly.setInvoiceNo(invoiceNo);
        statementMonthly.setCompanyId(company.getId());
        statementMonthly.setStatementMonth(DateUtils.formatYearMonthInt(targetYearMonth));
        statementMonthly.setStartCredit(BigDecimal.ZERO);
        statementMonthly.setStartAmount(BigDecimal.ZERO);
        statementMonthly.setEndCredit(BigDecimal.ZERO);
        statementMonthly.setEndAmount(BigDecimal.ZERO);
        statementMonthly.setCreditsToppedUp(BigDecimal.ZERO);
        statementMonthly.setCreditsGifted(BigDecimal.ZERO);
        statementMonthly.setCreditsExpired(BigDecimal.ZERO);
        statementMonthly.setCreditsUsed(usedCredit);
        statementMonthly.setAmountUsed(usedCredit);
        statementMonthly.setCreditsRefunded(BigDecimal.ZERO);
        statementMonthly.setEndGiftedCredit(BigDecimal.ZERO);
        LocalDate date = firstDayOfMonth.plusMonths(1L);
        statementMonthly.setIssueDate(DateUtils.toDate(date));

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("invoiceNo", invoiceNo);
        paramMap.put("statementMonthly", statementMonthly);
        paramMap.put("firstDayOfMonth", DateUtils.formatDateEn(firstDayOfMonth, DateUtils.DATE_FORMAT_DDMMMYYYY));
        paramMap.put("endDayOfMonth", DateUtils.formatDateEn(endDayOfMonth, DateUtils.DATE_FORMAT_DDMMMYYYY));
        paramMap.put("amtStd", "USD");
        paramMap.put("companyId", maskCompanyId(company.getId()));
        paramMap.put("companyType", company.getType());
        paramMap.put("issueDate", DateUtils.formatDateForSg(statementMonthly.getIssueDate(), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH));
        // updated for v2.1.7 chenbl KNZT-6681
        paramMap.put("to", company.getShortName());
        CompanyAddressTO companyAddress = commCompUserService.getCompanyAddress(company.getId());
        if (companyAddress != null) {
            List<String> addressInfoList = new ArrayList<>();
            CollectionUtils.addIgnoreNull(addressInfoList, companyAddress.getAddressLine1());
            CollectionUtils.addIgnoreNull(addressInfoList, companyAddress.getAddressLine2());
            CollectionUtils.addIgnoreNull(addressInfoList, companyAddress.getCity());
            CollectionUtils.addIgnoreNull(addressInfoList,
                    Lists.newArrayList(companyAddress.getProvince(), companyAddress.getZipCode()).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(" ")));
            CollectionUtils.addIgnoreNull(addressInfoList, GlobalAreaEnum.getShortNameEnByNameCode(companyAddress.getCountry()));
            String addressInfoStr = addressInfoList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
            if (StringUtils.isNotBlank(addressInfoStr)) {
                paramMap.put("address", addressInfoStr);
            }
        }
        paramMap.put("payType", CompPayTypeEnum.REAL_TIME.getCode());
        paramMap.put("bankAccount", AmtStdEnum.USD.getBankAccount());
        if (topUpAmount.compareTo(BigDecimal.ZERO) != 0) {
            paramMap.put("topUpAmount", CommTblContractDeliveryTransactionService.formatBillAmountWithSymbol(AmtStdEnum.USD.getSymbol(), topUpAmount));
        }
        if (topUpConsumedAmount.compareTo(BigDecimal.ZERO) != 0) {
            paramMap.put("topUpConsumedAmount", CommTblContractDeliveryTransactionService.formatBillAmountWithSymbol(AmtStdEnum.USD.getSymbol(), topUpConsumedAmount));
        }
        paramMap.put("totalAmountDue", CommTblContractDeliveryTransactionService.formatBillAmountWithSymbol(AmtStdEnum.USD.getSymbol(), totalAmountDue));

        String url = generateStatementPdf(company, statementMonthly.getStatementMonth(), paramMap);
        logger.info("calStatementMonthly4SelfAccount companyId:{}, statement url:{}", company.getId(), url);
        statementMonthly.setUrl(url);
        return statementMonthly;
    }


    /**
     * 校准订单收入账单
     * added for lvcy v2.0.6 KNZT-5499
     *
     * @param orderId
     * @param trackingContent
     * @return
     */
    public void correctMonthBillByOrder(String orderId, String trackingContent) throws MessageException, IOException {
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(orderId), "err.email.invalid");

        TblCompReportOrder order = commTblCompReportOrderService.get(orderId);
        MsgExceptionUtils.checkIsNull(order, "msg:对应订单不存在");
        MsgExceptionUtils.failBuild(OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus()), "msg:该订单类型不支持调整收入");
        String companyId = order.getCompanyId();

        // 手动调整退款
        order.transitionStatus(OrderStatusEnum.REFUNDED);
        commTblCompReportOrderService.save(order);
        createTransaction4Refund(order.getId(), order.getCompanyId(), order.getTotalUnit(), TransactionTypeEnum.CORRECT_ORD, TransactionTypeEnum.ORDER.getCode());

        // 同步额度
        funcCountService.updateFuncCountByValidProdAcc(companyId);

        // 保存跟踪记录
        commCrmCompTrackingRecordService.save(order.getCompanyId(), trackingContent, CompTrackingRecordSourceEnum.CORRECT_ORDER.getCode());
    }

    /**
     * 获取订单概要信息 for 调整收入
     * added for lvcy v2.0.6 KNZT-5499
     *
     * @param orderId
     * @return OrderInfo4CorrectTO
     */
    public OrderInfo4CorrectTO getOrderInfo4Correct(String orderId) throws MessageException {
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(orderId), "err.param.invalid");
        TblCompReportOrder order = commTblCompReportOrderService.get(orderId);
        MsgExceptionUtils.checkIsNull(order, "msg:订单不存在");
        MsgExceptionUtils.failBuild(OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus()), "msg:退款订单不允许调整");
        List<TblContractDeliveryTransaction> transactionList = contractDeliveryTransactionService.getByRelId(orderId, TransactionTypeEnum.ORDER.getCode());
        MsgExceptionUtils.checkIsNull(transactionList, "msg:非正式账号的订单");
        TblContractDeliveryTransaction orderTransaction = transactionList.get(0);
        Company company = commSysCompanyService.get(order.getCompanyId());
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");
        // 构建出参
        LocalDate transactionDate = DateUtils.toLocalDate(orderTransaction.getDateInt());
        YearMonth transactionYearMonth = YearMonth.from(transactionDate);
        String yearMonthStr = transactionYearMonth.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        return OrderInfo4CorrectTO.from(order, null, company.getName(), yearMonthStr);
    }


    /**
     * 生成月度报表pdf
     * added for v1.9.5 KNZT-4117
     * updated for lvcy v2.0.6 KNZT-5499
     *
     * @param company
     * @param month
     * @param paramMap
     * @return String
     */
    private String generateStatementPdf(Company company, int month, Map<String, Object> paramMap) throws IOException {
        String fileName = "Monthly Statement " + month;
        String md5Id = IdGenUtil.uuid();
        String route = String.format("statement/%s/%s.pdf", md5Id, fileName);

        SysTemplate template = commSysTemplateService.getByTemplateName("pdf_statement_monthly");

        File pdf = PdfUtils.createPdf("pdf_statement_monthly_id", template.getTemplateContent(), paramMap);

//        sendStatementEmail(company, pdf, fileName); // added for v1.9.8 KNZT-4538

        try {
            return HuaweiObsServUtils.getInstance().putObject(route, pdf);
        } catch (Exception e) {
            logger.error("", e);
            throw e;
        } finally {
            if (Objects.nonNull(pdf)) {
                FileUtils.deleteFile(pdf.getPath());
            }
        }
    }

    // removed for lvcy v2.0.8 KNZT-5811
  /*  private void sendStatementEmail(Company company, File file, String fileName) {
        List<String> checkCompanyIdList = ConfigUtils.listValueByTypeAndKey("month_statement_check_list", "company_id");
        if (!checkCompanyIdList.contains(company.getId())) {
            return;
        }
        logger.info("Statement monthly check, companyId:{}", company.getId());
        String sendTo = DictUtils.getDictValue("email_po", "system_config", "<EMAIL>");
        List<String> toList = StringUtils.split2List(sendTo, ",");
        List<String> ccList = null;
        if (toList.size() > 1) {
            ccList = toList.subList(1, toList.size());
        }
        ArrayList<EmailAttachmentFile> attachFileList = new ArrayList<>(2);
        EmailAttachmentFile attachmentFile = new EmailAttachmentFile();
        attachmentFile.setFile(file);
        fileName = company.getName() + " - " + fileName;
        attachmentFile.setFileName(fileName + ".pdf");
        attachFileList.add(attachmentFile);
        qccMailSenderService.send(toList.get(0), ccList, null, fileName, "", false, attachFileList);
    }*/

    /**
     * 掩码公司ID,保留前6位和后4位
     * added for v1.9.5 KNZT-4117
     *
     * @param companyId
     * @return String
     */
    private static String maskCompanyId(String companyId) {
        // 检查公司ID长度是否符合要求
        if (companyId == null || companyId.length() < 10) {
            return companyId;
        }
        // 保留前面的6位
        String prefix = companyId.substring(0, 6);
        // 保留后面的4位
        String suffix = companyId.substring(companyId.length() - 4);
        // 中间用4个星号替换
        return prefix + "****" + suffix;
    }

    /**
     * 查询交易流水详情
     * added for lvcy v2.1.2 KNZT-4565
     * @param condition 查询条件
     * @return 分页结果
    * @throws MessageException 
    */
    public List<TransactionListTO> pageTransaction(TransactionPageCondition condition) throws MessageException {
        // 如果交易流水ID、合同编号、订单编号都为空，则需要校验开始日期和结束日期
        if (Objects.isNull(condition.getTransactionId()) && Objects.isNull(condition.getContractNo()) && Objects.isNull(condition.getOrderNo())) {
            MsgExceptionUtils.failBuild(Objects.isNull(condition.getBeginDate()) || Objects.isNull(condition.getEndDate()), "msg:beginDate endDate不可为null");
            MsgExceptionUtils.failBuild(DateUtils.getDaysBetween(condition.getBeginDate(), condition.getEndDate()) > 366, "msg:时间间隔不能超过1年");
        }
      
        List<TransactionListTO> transactionList = contractDeliveryTransactionService.pageTransaction(condition);
        transactionList.forEach(item -> {
            item.setTransactionTypeDesc(TransactionTypeEnum.getCodeDesc(item.getTransactionType()));

            if (TransactionTypeEnum.getOrderTypeWithoutApiList().contains(item.getTransactionType())) {
                item.setOrderTypeDesc(ReportTypeEnum.getDesc(item.getOrderType()));
            } else if (TransactionTypeEnum.getOrderTypeApiList().contains(item.getTransactionType())) {
                item.setOrderTypeDesc(ApiTypeEnum.getDesc4TransactionPage(item.getOrderType()));
            }
            
            item.setUnitGroupDesc(UnitGroupEnum.getNameByUnitGroup(item.getUnitGroup()));

        
            if (Objects.nonNull(item.getConfirmAmount())) {
                item.setConfirmAmountStr(NumberUtils.formatRptUnitWithSign(item.getConfirmAmount()));
            }

            if (Objects.nonNull(item.getChangedUnit())) {
                item.setChangedUnitStr(NumberUtils.formatRptUnitWithSign(item.getChangedUnit()));
            }

            if (Objects.nonNull(item.getConfirmAmount()) && UnitGroupEnum.CN_UNIT.getGroup().equals(item.getUnitGroup())) {
                item.setQccConfirmAmount(item.getConfirmAmount().multiply(BigDecimal.valueOf(0.2)));
                item.setQccConfirmAmountStr(NumberUtils.formatRptUnitWithSign(item.getQccConfirmAmount()));
            } else {
                item.setQccConfirmAmount(BigDecimal.ZERO);
                item.setQccConfirmAmountStr("0");
            }

            if (Objects.nonNull(item.getDateInt())) {
                item.setDateStr(DateUtils.formatDateInt(item.getDateInt()));
            }
        });
        return transactionList;
    }

    /**
     * 获取交易流水详情字典
     * added for lvcy v2.1.2 KNZT-4565
     * @return TransactionListDictTO
     */
    public TransactionOrderDictTO getTransactionOrderDict() {
        List<DictItem> transactionDict = Arrays.stream(TransactionTypeEnum.values()).map(item -> new DictItem(item.getCode(), item.getCodeDesc(), item.getTransactionCategory().getCode())).collect(Collectors.toList());

        List<TblReportChargeUnit> chargeUnits = commTblReportChargeUnitService.listChargeUnitByCompanyId(Constants.DEFAULT_COMPANY_ID);
        List<DictItem> orderReportDict = commTblCompReportOrderService.chargeUnitToItemList(chargeUnits);

        List<TblCompApiChargeUnit> apiChargeUnits = commTblCompApiChargeUnitService.getByCompanyId(Constants.DEFAULT_COMPANY_ID);
        List<DictItem> apiOrderTypeDict = commTblCompApiChargeUnitService.apiChargeUnitToItemList(apiChargeUnits);

        TransactionOrderDictTO dictTO = new TransactionOrderDictTO();
        dictTO.setTransactionTypeDict(transactionDict);
        dictTO.setOrderReportTypeDict(orderReportDict);
        dictTO.setApiOrderTypeDict(apiOrderTypeDict);
        return dictTO;
    }

    /**
     * 用户查询全量流水信息
     *
     * @param condition
     * @return
     */
    public List<TransactionListV2TO> pageTransaction4UserComp(TransactionPageCondition condition) throws MessageException {
        List<TransactionBO> transactionBOs = contractDeliveryTransactionService.pageTransaction4UserComp(condition);
        // 定义操作人是公司主账号的类型
        List<String> companyTransactionTypeList = Lists.newArrayList(TransactionTypeEnum.API_ORDER.getCode(), TransactionTypeEnum.API_REFUND_ORDER.getCode(), TransactionTypeEnum.TOP_UP.getCode(), TransactionTypeEnum.TOP_UP_CONSUMED.getCode());
        // 定义操作人是System的类型
        List<String> systemTransactionTypeList = Lists.newArrayList(TransactionTypeEnum.PREPAID.getCode(), TransactionTypeEnum.GIVEN.getCode(), TransactionTypeEnum.CORRECT_ORD.getCode(), TransactionTypeEnum.CONFIRM.getCode(), TransactionTypeEnum.RETURN.getCode(), TransactionTypeEnum.BENEFIT.getCode());
        // 查询主账号邮箱
        Company company = UserUtils.getUser().getCompany();
        User mainUser = userService.get(company.getMainUserId());
        String mainUserLoginName = Objects.nonNull(mainUser) ? mainUser.getLoginName() : null;
        return transactionBOs.stream().map(bo -> {
            TransactionListV2TO vo = new TransactionListV2TO();
            vo.setTransactionId(bo.getId());
            String transactionType = bo.getTransactionType();
            vo.setTransactionType(transactionType);
            vo.setTransactionTypeDesc(TransactionTypeEnum.getDescEn(transactionType));
            BigDecimal changedUnit = bo.getChangedUnit();
            vo.setChangedUnit(changedUnit);
            BigDecimal balance = bo.getCompanyRemainUnit();
            vo.setBalance(balance);
            vo.setCreateDate(bo.getCreateDate());
            vo.setDateStr(DateUtils.formatDateForSg(vo.getCreateDate(), DateUtils.DATETIME_FORMAT_2, Locale.ENGLISH));

            String orderNo = null;
            if (TransactionTypeEnum.getOrderTypeWithoutApiList().contains(transactionType)) {
                String reportType = Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getReportType).orElse(null);
                vo.setOrderType(reportType);
                vo.setOrderTypeDesc(ReportTypeEnum.getDesc(reportType));
                orderNo = Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getOrderNo).orElse(null);
            } else if (TransactionTypeEnum.getOrderTypeApiList().contains(transactionType)) {
                String apiType = Optional.ofNullable(bo.getCompApiOrder()).map(TransactionBO.CompApiOrderBO::getApiType).orElse(null);
                vo.setOrderType(apiType);
                vo.setOrderTypeDesc(ApiTypeEnum.getDesc4TransactionPage(apiType));
                orderNo = Optional.ofNullable(bo.getCompApiOrder()).map(TransactionBO.CompApiOrderBO::getOrderNo).orElse(null);
            } else if (TransactionTypeEnum.BENEFIT.getCode().equals(transactionType)) {
                String benefitType = Optional.ofNullable(bo.getBenefitDelivery()).map(TransactionBO.BenefitDeliveryBO::getBenefitType).orElse(null);
                vo.setOrderType(benefitType);
                vo.setOrderTypeDesc(BenefitTypeEnum.getNameByCode(benefitType));
            }
            if (TransactionTypeEnum.getOrderTypeWithoutApiList().contains(transactionType)) {
                vo.setUser(Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getLoginName).orElse(null));
            }
            if (companyTransactionTypeList.contains(transactionType)) {
                vo.setUser(mainUserLoginName);
            } else if (systemTransactionTypeList.contains(transactionType)) {
                vo.setUser("System");
            }

            // 设置默认值
            vo.setChangedUnitStr("0");
            vo.setCreditUsageUnitStr("0");
            vo.setBalanceStr("0");
            if (Objects.nonNull(changedUnit)) {
                if (TransactionTypeEnum.getDeductionTypeList().contains(transactionType) || TransactionTypeEnum.getExpiredTypeList().contains(transactionType)) {
                    vo.setChangedUnitStr(NumberUtils.formatRptUnitWithSign(changedUnit.negate()));
                } else if (TransactionTypeEnum.getTopUpTypeList().contains(transactionType) || TransactionTypeEnum.getRefundTypeList().contains(transactionType) || TransactionTypeEnum.getPromotionTypeList().contains(transactionType)) {
                    vo.setChangedUnitStr(NumberUtils.formatRptUnitWithSign(changedUnit.abs()));
                }
                vo.setCreditUsageUnitStr(NumberUtils.formatRptUnit(changedUnit.abs()));
            }
            String prodAccType = Optional.ofNullable(bo.getContractDeliveryProdAcc()).map(TransactionBO.ContractDeliveryProdAccBO::getType).orElse(null);
            if (Objects.nonNull(balance)) {
                if (DeliveryContracProdAccTypeEnum.AFTER_INF.getCode().equals(prodAccType)) {
                    vo.setBalance(balance.subtract(Constants.MAX_UNIT));
                }
                vo.setBalanceStr(NumberUtils.formatRptUnit(vo.getBalance()));
            }
            String corpCombinedName = null;
            if (bo.getCompReportOrder() != null) {
                TransactionBO.CompReportOrderBO compReportOrder = bo.getCompReportOrder();
                if ("P".equals(compReportOrder.getKeyNoType())) {
                    corpCombinedName = StringUtils.joinIgnoreNull(Lists.newArrayList(compReportOrder.getPersNameEn(), compReportOrder.getPersName()),
                            "\n");
                } else if ("C".equals(compReportOrder.getKeyNoType())) {
                    corpCombinedName = StringUtils.joinIgnoreNull(Lists.newArrayList(compReportOrder.getCorpNameEn(), compReportOrder.getCorpName()),
                            "\n");
                }
            }
            vo.setCorpCombinedName(corpCombinedName);

            String contractNo = Optional.ofNullable(bo.getContractDelivery()).map(TransactionBO.ContractDeliveryBO::getContractNo).orElse(null);
            Date contractDeliveryBeginDate = Optional.ofNullable(bo.getContractDelivery()).map(TransactionBO.ContractDeliveryBO::getBeginDate).orElse(null);
            Date contractDeliveryEndDate = Optional.ofNullable(bo.getContractDelivery()).map(TransactionBO.ContractDeliveryBO::getEndDate).orElse(null);
            Date contractDeliveryProdAccBeginDate = Optional.ofNullable(bo.getContractDeliveryProdAcc()).map(TransactionBO.ContractDeliveryProdAccBO::getBeginDate).orElse(null);
            Date contractDeliveryProdAccEndDate = Optional.ofNullable(bo.getContractDeliveryProdAcc()).map(TransactionBO.ContractDeliveryProdAccBO::getEndDate).orElse(null);
            Date beginDate = ObjectUtils.getNotNull(contractDeliveryBeginDate, contractDeliveryProdAccBeginDate);
            Date endDate = ObjectUtils.getNotNull(contractDeliveryEndDate, contractDeliveryProdAccEndDate);
            String beginDateFormat = DateUtils.formatDateForSg(beginDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
            String endDateFormat = DateUtils.formatDateForSg(endDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
            // 描述处理
            if (TransactionTypeEnum.getDeductionTypeList().contains(transactionType) || TransactionTypeEnum.getRefundTypeList().contains(transactionType)) {// 正常扣费
                if (TransactionTypeEnum.BENEFIT.getCode().equals(transactionType)) {
                    TransactionBO.BenefitDeliveryBO benefitDelivery = bo.getBenefitDelivery();
                    if (benefitDelivery != null) {
                        Integer poolCount = benefitDelivery.getPoolCount();
                        Date benefitBeginDate = benefitDelivery.getBeginDate();
                        Date benefitEndDate = benefitDelivery.getEndDate();
                        vo.setDescription(String.format("Redeemed %s monitor slots valid from %s to %s",
                                poolCount,
                                DateUtils.formatDateForSg(benefitBeginDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH),
                                DateUtils.formatDateForSg(benefitEndDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH)));
                    }
                } else {
                    vo.setDescription("Transaction No. : " + orderNo);
                }
            } else {
                if (TransactionTypeEnum.TOP_UP.getCode().equals(transactionType)) {
                    vo.setDescription(String.format("Top-up No. : %s\nCredits valid from %s to %s", contractNo, beginDateFormat, endDateFormat));
                } else if (TransactionTypeEnum.PREPAID.getCode().equals(transactionType)) { // 预付费充值 只展示合同号
                    vo.setDescription(String.format("Contract No. : %s\nCredits valid from %s to %s", contractNo, beginDateFormat, endDateFormat));
                } else if (TransactionTypeEnum.TOP_UP_CONSUMED.getCode().equals(transactionType) || TransactionTypeEnum.getExpiredTypeList().contains(transactionType)) {
                    if (TransactionTypeEnum.GIVEN.getCode().equals(prodAccType)) {
                        vo.setDescription("Promotional credit has expired."); // 过期流水来自赠送操作，则给出默认文案
                    } else {
                        vo.setDescription("Top-up No. : " + contractNo);
                    }
                } else if (TransactionTypeEnum.getPromotionTypeList().contains(transactionType)) {
                    vo.setDescription(String.format("Credits valid from %s to %s", beginDateFormat, endDateFormat));
                } else if (TransactionTypeEnum.RETURN.getCode().equals(transactionType)) {
                    String statementMonth = Optional.ofNullable(bo.getCompStatementMonthly()).map(TransactionBO.CompStatementMonthlyBO::getStatementMonth).orElse(null);
                    if (StringUtils.isNotBlank(statementMonth)) {
                        YearMonth yearMonth = YearMonth.parse(statementMonth, DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
                        String yearMonthEn = DateUtils.formatYearMonthEn(yearMonth, DateUtils.DATE_FORMATMMMYYYY);
                        vo.setDescription(String.format("Payment to %s Invoice", yearMonthEn));
                    }
                }
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 充值记录分页查询
     *
     * @param condition
     * @return
     */
    public Page<ChargeRecordsInfoListV2TO> pageChargeRecords(CompanyChargeHistoryCondition condition) {
        if (CollectionUtils.isEmpty(condition.getSortedFiledList())) {
            condition.setSortedFiledList(Lists.newArrayList(new SortedFiled("createDate", "desc")));
        }
        List<ChargeRecordsInfoListV2TO> chargeRecordsInfoListTOS = contractDeliveryService.pageChargeRecords(condition);
        if (CollectionUtils.isNotEmpty(chargeRecordsInfoListTOS)) {
            chargeRecordsInfoListTOS.forEach(item -> {
                item.setContractSubject(ContractSubjectEnum.QCC_GLOBAL.getDesc());
                if (Objects.nonNull(item.getContractAmount())) {
                    item.setContractAmountStr(NumberUtils.formatRptUnit(item.getContractAmount()));
                }
                if (Objects.nonNull(item.getConsumedAmount())) {
                    item.setConsumedAmountStr(NumberUtils.formatRptUnit(item.getConsumedAmount()));
                }
                if (Objects.nonNull(item.getContractCredits())) {
                    item.setContractCreditsStr(NumberUtils.formatRptUnit(item.getContractCredits()));
                }
                if (Objects.nonNull(item.getConsumedCredits())) {
                    item.setConsumedCreditsStr(NumberUtils.formatRptUnit(item.getConsumedCredits()));
                }
                if (Objects.nonNull(item.getBeginDate())) {
                    item.setBeginDateStr(DateUtils.formatDate(item.getBeginDate()));
                }
                if (Objects.nonNull(item.getEndDate())) {
                    item.setEndDateStr(DateUtils.formatDate(item.getEndDate()));
                }
            });
        }
        condition.getPage().setList(chargeRecordsInfoListTOS);
        return condition.getPage();
    }

    /**
     * 处理过期额度账户，生成过期流水
     * 
     * @param account 过期的额度账户列表
     * @throws MessageException 如果处理过程中出现错误
     */
    public void processExpiredAccounts(TblContractDeliveryProdAcc account) throws MessageException {
        String companyId = account.getCompanyId();
        BigDecimal remainUnit = account.getTotalUnit().subtract(account.getTotalConsumedUnit());

        // 获取当前公司的功能计数对象
        SysCompInfoFuncCount funcCount = funcCountService.updateFuncCountByValidProdAcc(companyId);

        // 生成过期流水
        TblContractDeliveryTransaction expireTransaction = TblContractDeliveryTransaction
                .init(companyId, TransactionTypeEnum.CONFIRM.getCode(), funcCount.calRemainCount())
                .injectAccInfo(account)
                .injectChangedUnit(remainUnit);
        expireTransaction.injectDateInt(account.getEndDate());

        // 保存流水并更新账户
        this.saveTransactionAndChangeAccUnit(expireTransaction, account);

        logger.info("处理过期额度账户成功，companyId: {}, accountId: {}, expiredUnit: {}",
                companyId, account.getId(), remainUnit);

    }


    /**
     * 生成回款流水，并同步更新额度
     * @param statementMonthly
     * @throws MessageException
     */
    public void createTransaction4Return(TblCompStatementMonthly statementMonthly) throws MessageException {
        String companyId = statementMonthly.getCompanyId();
        BigDecimal totalAmountDue = statementMonthly.getTotalCreditDue();
        Integer statementMonth = statementMonthly.getStatementMonth();
        if (Objects.isNull(totalAmountDue) || totalAmountDue.compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("totalAmountDue is null or zero, companyId: {}, statementMonth: {}", companyId, statementMonth);
            return;
        }

        List<TblContractDeliveryProdAcc> prodAccList = contractDeliveryProdAccService.lockValidAccByCompanyId(companyId);
        TblContractDeliveryProdAcc prodAcc = prodAccList.stream()
                .filter(acc -> DeliveryContracProdAccTypeEnum.AFTER_INF.getCode().equals(acc.getType()))
                .max(Comparator.comparing(TblContractDeliveryProdAcc::getBeginDate))
                .orElse(null);
        if (Objects.isNull(prodAcc)) {
            logger.error("prodAcc is null, companyId: {}, statementMonth: {}", companyId, statementMonth);
            return;
        }
        prodAcc.setTotalUnit(prodAcc.getTotalUnit().add(totalAmountDue));
        contractDeliveryProdAccService.save(prodAcc);
        logger.info("update prodAcc totalUnit, companyId: {}, totalUnit: {}", companyId, prodAcc.getTotalUnit());

        SysCompInfoFuncCount funcCount = funcCountService.updateFuncCountByValidProdAcc(companyId);

        // 生成回款流水
        TblContractDeliveryTransaction returnTransaction = TblContractDeliveryTransaction
                .init(companyId, TransactionTypeEnum.RETURN.getCode(), funcCount.calRemainCount())
                .injectAccInfo(prodAcc)
                .injectRelId(statementMonthly.getId())
                .injectChangedUnit(totalAmountDue.negate());
        contractDeliveryTransactionService.save(returnTransaction);
    }

    /**
     * 月度账单管理分页查询
     * @param condition 查询条件
     * @return 分页结果
     */
    public Page<StatementMonthly4ManagementTO> pageStatement4Management(StatementMonthly4ManagementCondition condition) {
        // 查询数据
        List<StatementMonthly4ManagementTO> list = commTblCompStatementMonthlyService.page4Management(condition);

        List<String> companyIds = list.stream().map(StatementMonthly4ManagementTO::getCompanyId).distinct().collect(Collectors.toList());
        Map<String, String> companyAmountStdMap = contractDeliveryService.getCompanyAmountStdMap(companyIds);

        // 处理数据，设置描述信息
        for (StatementMonthly4ManagementTO to : list) {
            // 设置账号类型描述
            to.setCompanyTypeDesc(CompTypeEnum.getDesc(to.getCompanyType()));
            // 设置付费类型描述
            to.setPayTypeDesc(CompPayTypeEnum.getDescCnByCode(to.getPayType()));
            // 设置是否可以下载
            to.setCanDownload(StringUtils.isNotBlank(to.getUrl()));
            // 设置货币单位
            to.setAmountStd(companyAmountStdMap.getOrDefault(to.getCompanyId(), "USD"));

            if (to.getStatementMonth() != null) {
                // 设置账单月份描述
                YearMonth yearMonth = YearMonth.parse(to.getStatementMonth().toString(), DateTimeFormatter.ofPattern("yyyyMM"));
                to.setStatementMonthDesc(DateUtils.formatYearMonth(yearMonth));
            }

            if (to.getReturnStatus() != null) {
                // 设置回款状态描述
                to.setReturnStatusDesc(ReturnStatusEnum.getDescCnByCode(to.getReturnStatus()));
                if (CompPayTypeEnum.AFTER.getCode().equals(to.getPayType())) {
                    // 设置是否可以确认 - 待确认状态可以确认
                    to.setCanConfirm(ReturnStatusEnum.PENDING_CONFIRM.getCode().equals(to.getReturnStatus()));
                    // 设置是否可以重新生成 - 待回款状态可以重新生成
                    to.setCanReGenerate(ReturnStatusEnum.PENDING_RETURN.getCode().equals(to.getReturnStatus()));
                }
            }
        }

        Page<StatementMonthly4ManagementTO> page = new Page<>();
        // 复制分页信息
        if (condition.getPage() != null) {
            page.setPageNo(condition.getPage().getPageNo());
            page.setPageSize(condition.getPage().getPageSize());
            page.setOrderBy(condition.getPage().getOrderBy());
            page.setCount(condition.getPage().getCount());
        }
        page.setList(list);
        return page;
    }


    /**
     * 预览invoice
     * @param statementId 发票号
     * @return 预览结果
     * @throws MessageException 异常
     * @throws IOException 异常
     */
    public String previewStatementMonthly(String statementId) throws MessageException, IOException {
        TblCompStatementMonthly statement = commTblCompStatementMonthlyService.get(statementId);
        MsgExceptionUtils.checkIsNull(statement, "msg:invoice不存在");
        YearMonth ym = YearMonth.parse(statement.getStatementMonth().toString(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        TblCompStatementMonthly statementMonthly = this.calStatementMonthly(statement.getCompanyId(), ym, true);
        return Objects.isNull(statementMonthly) ? null : statementMonthly.getUrl();
    }

    /**
     * 重新生成月度报表 并推送客找找
     * @param statementId
     * @throws MessageException
     * @throws IOException
     */
    public void reGenerateStatementMonthly(String statementId) throws MessageException, IOException {
        TblCompStatementMonthly statement = commTblCompStatementMonthlyService.get(statementId);
        MsgExceptionUtils.checkIsNull(statement, "msg:invoice不存在");
        MsgExceptionUtils.failBuild(ReturnStatusEnum.RETURNED.getCode().equals(statement.getReturnStatus()), "msg:已回款，不允许重新生成");
        YearMonth ym = YearMonth.parse(statement.getStatementMonth().toString(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        TblCompStatementMonthly statementMonthly = this.calStatementMonthly(statement.getCompanyId(), ym, false);
        // 触发推送账单到客找找，出错则钉钉邮件提醒
        pushAfterConfirmContract2Kzz(statementMonthly);
    }


    /**
     * 推送失败，钉钉提醒
     *
     * @param errorInvoiceNoList
     */
    private void sendPushErrorRemainMsg(List<String> errorInvoiceNoList) {
        if (CollectionUtils.isNotEmpty(errorInvoiceNoList)) {
            String invoiceNoStr = String.join(",", errorInvoiceNoList);
            logger.error("pushAfterConfirmContract2Kzz error, invoiceNo: {}", invoiceNoStr);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.PUSH_AFTER_KZZ_CRATE_CONTRACT_ERROR, invoiceNoStr, DateUtils.getDate());
        }
    }


    /**
     * 推送合同信息
     * @param tblCompStatementMonthly
     * @return
     */
    public void pushAfterConfirmContract2Kzz(TblCompStatementMonthly tblCompStatementMonthly) throws MessageException {
        try {
            //金额为0直接 设置为已回款 且不推送到客找找 updated for v2.2.5 fengsw KNZT-7570【优化】【后付费合同】金额为0的Invoice不推送收入数据
            if (BigDecimal.ZERO.compareTo(tblCompStatementMonthly.getTotalAmountDue()) == 0) {
                tblCompStatementMonthly.setReturnStatus(ReturnStatusEnum.RETURNED.getCode());
            } else {
                String contractNo = getContractNo4StatementByCompanyId(tblCompStatementMonthly.getCompanyId());
                MsgExceptionUtils.checkIsNull(contractNo, "msg:未找到对应的交付合同编号信息", tblCompStatementMonthly.getCompanyId());
                logger.info("push afterConfirmContract2Kzz, contractNo:{}, invoiceNo:{}, dateInt:{} start", contractNo, tblCompStatementMonthly.getInvoiceNo(), tblCompStatementMonthly.getStatementMonth());
                KzzContractDetailTO kzzContractDetail = KzzApiInterface.getContractDetail(contractNo);
                MsgExceptionUtils.checkIsNull(kzzContractDetail, "msg:未找到对应的交付合同信息", contractNo);
                StatementPushDetailTO statementPushDetailTO = StatementPushDetailTO.build(contractNo, tblCompStatementMonthly);
                KzzContractCreateRequestTO createRequestTO = buildAfterAccountContractInfo(statementPushDetailTO, kzzContractDetail);
                String requestJSONStr = JSON.toJSONString(createRequestTO);
                String responseStr = KzzApiInterface.createContract("/openapi/kzz/contract/v2/add", requestJSONStr);
                JSONObject jsonObj = JsonUtils.parseObject(responseStr);
                MsgExceptionUtils.checkIsNull(jsonObj, "接口响应内容为空");
                MsgExceptionUtils.failBuild(!"OK".equals(jsonObj.getString("status")), jsonObj.getString("error"));
                tblCompStatementMonthly.setKzzContractNo(createRequestTO.getCode());
                tblCompStatementMonthly.setReturnStatus(ReturnStatusEnum.PENDING_RETURN.getCode());
            }
            commTblCompStatementMonthlyService.saveOrUpdateStatement(tblCompStatementMonthly);
        } catch (MessageException e) {
            logger.error("push contract error{}, invoiceNo:{}, dateInt:{}", e, tblCompStatementMonthly.getInvoiceNo(), tblCompStatementMonthly.getStatementMonth());
            throw new MessageException(e.getMessage());
        }
    }


    /**
     * 获取companyId当前生效的交付合同编号
     * 
     * @param companyId
     * @return
     */
    public String getContractNo4StatementByCompanyId(String companyId) {
        TblContractDelivery delivery = contractDeliveryService.getLatestDataByCompanyId(companyId);
        if (delivery != null) {
            return delivery.getContractNo();
        }
        return null;
    }

    /**
     * 根据invoice构建创建合同请求
     *
     * @return
     */
    private KzzContractCreateRequestTO buildAfterAccountContractInfo(StatementPushDetailTO statementPushDetailTO, KzzContractDetailTO kzzContractDetail) throws MessageException {
        KzzContractCreateRequestTO createRequestTO = new KzzContractCreateRequestTO();
        YearMonth yearMonth = YearMonth.parse(statementPushDetailTO.getStatementMonth() + "", DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        checkKzzContractInfoValid(kzzContractDetail);
        // updated for v2.2.7 fengsw KNZT-7684
        String contractNoKzz = statementPushDetailTO.getContractNo() + "-" + "H" + statementPushDetailTO.getInvoiceNo().replace("INV","");
        buildPushContractBaseInfo(contractNoKzz, statementPushDetailTO.getTotalAmountDue(), yearMonth, kzzContractDetail, createRequestTO);
        // 构建推送产品信息
        buildPushContractProductInfo(createRequestTO, kzzContractDetail);
        // 构建回款信息
        buildPushContractPaymentInfo(createRequestTO, kzzContractDetail, contractNoKzz, statementPushDetailTO.getTotalAmountDue(), statementPushDetailTO.getInvoiceNo());
        // 构建自定义信息
        buildPushContractCustomerInfo(createRequestTO, kzzContractDetail);
        return createRequestTO;
    }

    private void buildPushContractCustomerInfo(KzzContractCreateRequestTO createRequestTO, KzzContractDetailTO kzzContractDetail) throws MessageException {
        JSONObject customObjectValues = new JSONObject();// 合同自定义字段
        //开票⽅式-先票后款
        String billType = kzzContractDetail.getInvoiceMethod();
        if (StringUtils.isNotBlank(billType)) {
            customObjectValues.put("CFLD2023033100006", Lists.newArrayList(billType));
        }
        //回款⽅式-后付费
        String paymentType = kzzContractDetail.getPayType();
        if (StringUtils.isNotBlank(paymentType)) {
            customObjectValues.put("CFLD2023033100004", Lists.newArrayList(paymentType));
        }
        //结算周期-月结
        String statementTerm = sysConfigCacheService.getCreateContractConfigValueByKey("afterPaymentType");
        MsgExceptionUtils.checkIsNull(statementTerm, "结算周期缺失，请检查");  
        customObjectValues.put("CFLD2023033100002", Lists.newArrayList(statementTerm));
        
        // 收款⽅式-⽹银转账
        String paymentMethod = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300081");
        MsgExceptionUtils.checkIsNull(paymentMethod, "收款⽅式缺失，请检查");
        customObjectValues.put("CFLD202208300081", Lists.newArrayList(paymentMethod));
        //客户需要合同-不需要合同
        String customerNeedContract = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300054");
        MsgExceptionUtils.checkIsNull(customerNeedContract, "客户需要合同配置项缺失，请检查");
        customObjectValues.put("CFLD202208300054", Lists.newArrayList(customerNeedContract));
        //客户是否先⽤印-否
        String needSign = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032900009");
        MsgExceptionUtils.checkIsNull(needSign, "客户是否先⽤印配置项缺失，请检查");
        customObjectValues.put("CFLD2023032900009", Lists.newArrayList(needSign));
        //是否标准模板-否
        String standardTemplate = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300057");
        MsgExceptionUtils.checkIsNull(standardTemplate, "是否标准模板配置项缺失，请检查");
        customObjectValues.put("CFLD202208300057", Lists.newArrayList(standardTemplate));
        //我司签约主体
        String ourSigningParty = kzzContractDetail.getSigningParty();
        if (StringUtils.isNotBlank(ourSigningParty)) {
            customObjectValues.put("CFLD202208300069", Lists.newArrayList(ourSigningParty));
        }
        // 客户开票名称
        String customerInvoicingName = kzzContractDetail.getInvoiceName();
        if (StringUtils.isNotBlank(customerInvoicingName)) {
            customObjectValues.put("CFLD202208300090", customerInvoicingName);
        }
        // 收件⼈姓名
        String recipientName = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300098");
        MsgExceptionUtils.checkIsNull(recipientName, "收件⼈姓名配置项缺失，请检查");
        customObjectValues.put("CFLD202208300098", recipientName);
        // 收件人电话
        String recipientPhone = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300101");
        MsgExceptionUtils.checkIsNull(recipientPhone, "收件人电话配置项缺失，请检查");
        customObjectValues.put("CFLD202208300101", recipientPhone);
        // 收件人地址
        String recipientAddress = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300100");
        MsgExceptionUtils.checkIsNull(recipientAddress, "收件人地址配置项缺失，请检查");
        customObjectValues.put("CFLD202208300100", recipientAddress);
        // 合同来源-线下合同
        String contractSource = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023040400002");
        MsgExceptionUtils.checkIsNull(contractSource, "合同来源配置项缺失，请检查");
        customObjectValues.put("CFLD2023040400002", Lists.newArrayList(contractSource));
        //主产品-KYC国际业务
        customObjectValues.put("CFLD202208300061", Lists.newArrayList("KYC国际业务"));
        customObjectValues.put("CFLD2023042500004", kzzContractDetail.getServiceYear());//服务年限
        createRequestTO.setCustomObjectValues(customObjectValues);
    }

    private void buildPushContractBaseInfo(String contractNoKzz, BigDecimal amount, YearMonth yearMonth, KzzContractDetailTO kzzContractDetail, KzzContractCreateRequestTO createRequestTO) throws MessageException {
        createRequestTO.setCode(contractNoKzz);// 合同编号
        createRequestTO.setCustomerId(kzzContractDetail.getCustomerId());// 客户id
        createRequestTO.setContactId(kzzContractDetail.getContactId());// 联系人
        createRequestTO.setRenewType(1);// 续约类型 一次性合同
        createRequestTO.setContractType(3);//类型-框架子合同
        createRequestTO.setSkipApproval(1);// 默认跳过审批，否则客找找无法保存指定的开始日期和到期日期
        createRequestTO.setAmount(amount);//合同金额
        createRequestTO.setTotal(amount);//合同原金额
        createRequestTO.setOrderDiscountRate(new BigDecimal("100"));// 整单折扣率
        createRequestTO.setComDiscountRate(new BigDecimal("100"));// 综合折扣率
        //开始时间：推送周期当月1⽇；结束日期：推送周期当月最后⼀天
        LocalDate beginDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        createRequestTO.setBeginDate(DateUtils.formatDate(beginDate));
        createRequestTO.setExpiredDate(DateUtils.formatDate(endDate));
        //合同名称 【2024/1/1-2024/1/31】 【客户名称】消耗账单
        String contractName = DateUtils.formatDate(beginDate, "yyyy/MM/dd") + "-" + DateUtils.formatDate(endDate, "yyyy/MM/dd") + kzzContractDetail.getCustomerName() + "消耗账单";
        createRequestTO.setName(contractName);
        createRequestTO.setParentContractId(kzzContractDetail.getContractId());// 关联主合同
        createRequestTO.setCurrencyId(kzzContractDetail.getCurrencyId());
        createRequestTO.setSignerDate(DateUtils.getDate(DateUtils.DATETIME_FORMAT));// 签订时间-当前时间,精确到时分秒
        createRequestTO.setCreateDate(createRequestTO.getSignerDate());// 创建时间-当前时间
        //合同状态-签约
        String contractStatus = sysConfigCacheService.getCreateContractConfigValueByKey("contractStatus");
        MsgExceptionUtils.checkIsNull(contractStatus, "合同状态配置项缺失，请检查");
        createRequestTO.setStatus(Integer.parseInt(contractStatus));
        String singerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("singerIdSelf");
        MsgExceptionUtils.checkIsNull(singerId1, "签订人信息缺失，请检查");
        long singerId = Long.parseLong(singerId1);
        // updated for v2.2.7 fengsw KNZT-7687 跟进人 取主合同的客户负责人id
        createRequestTO.setSignerId(Objects.nonNull(kzzContractDetail.getCustomerUserId()) ? kzzContractDetail.getCustomerUserId() : singerId);
        createRequestTO.setCreateBy(singerId);// 创建人 取默认内置用户
    }

    /**
     * 检查同基本信息是否存在缺失
     * 
     * @param kzzContractDetail
     * @throws MessageException
     */
    private void checkKzzContractInfoValid(KzzContractDetailTO kzzContractDetail) throws MessageException {
        MsgExceptionUtils.failBuild(kzzContractDetail.getSignerId() == 0L, "签订人信息缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getCurrencyId() == 0L, "合同货币信息缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getContractId() == 0L, "关联主合同缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getContactId() == 0L, "联系人缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getCustomerId() == 0L, "客户id缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getServiceYear() <= 0, "服务年限缺失，请检查");
    }

    /**
     * 回款计划填充
     * 
     * @param createRequestTO
     * @param kzzContractDetailTO
     * @param contractNoKzz
     * @param amount
     * @param note
     * @throws MessageException
     */
    private void buildPushContractPaymentInfo(KzzContractCreateRequestTO createRequestTO, KzzContractDetailTO kzzContractDetailTO, String contractNoKzz, BigDecimal amount, String note) throws MessageException {
        KzzContractCreateRequestTO.ContractPayment contractPayment = new KzzContractCreateRequestTO.ContractPayment();
        contractPayment.setCode(contractNoKzz + ".1");
        contractPayment.setAmount(amount);
        contractPayment.setType(2);// 回款类型，常规
        contractPayment.setNote(note);// 备注
        YearMonth now = YearMonth.now();
        LocalDate localDate = now.atEndOfMonth();
        contractPayment.setPaymentDate(DateUtils.toDate(localDate));// 预计回款时间为推送当前时间的当月30天
        String singerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("singerIdSelf");
        MsgExceptionUtils.checkIsNull(singerId1, "签订人信息缺失，请检查");
        long singerId = Long.parseLong(singerId1);
        // updated for v2.2.7 fengsw KNZT-7687 跟进人 取主合同的客户负责人id
        contractPayment.setLeaderId(Objects.nonNull(kzzContractDetailTO.getCustomerUserId()) ? kzzContractDetailTO.getCustomerUserId() : singerId);
        contractPayment.setCreateBy(singerId);// 创建人 取默认内置用户
        JSONObject obj = new JSONObject();// 自定义字段
        // 回款条件-开票之日起
        String paymentConditions = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032100002");
        MsgExceptionUtils.checkIsNull(paymentConditions, "回款条件缺失，请检查");
        obj.put("CFLD2023032100002", Lists.newArrayList(paymentConditions));
        //账龄天数-30
        String agingDays = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202111050008");
        MsgExceptionUtils.checkIsNull(agingDays, "账龄天数缺失，请检查");
        obj.put("CFLD202111050008", Integer.valueOf(agingDays));
        contractPayment.setCustomObjectValues(obj);
        createRequestTO.setPayments(Lists.newArrayList(contractPayment));
    }


    /**
     * added for v2.1.9 fengsw KNZT-7061
     * 构建产品信息时，需要给主产品 设置数量、总金额 通过消耗的总金额，除以产品单价计算得出
     * 
     * @param createRequestTO
     * @param kzzContractDetail
     */
    private void buildPushContractProductInfo(KzzContractCreateRequestTO createRequestTO, KzzContractDetailTO kzzContractDetail) {
        if (CollectionUtils.isNotEmpty(kzzContractDetail.getContractProducts())) {
            String afterMainProductCodeHK = sysConfigCacheService.getCreateContractConfigValueByKey("afterMainProductCodeHK");
            String afterMainProductCodeCN = sysConfigCacheService.getCreateContractConfigValueByKey("afterMainProductCodeCN");
            String afterMainProductCode = sysConfigCacheService.getCreateContractConfigValueByKey("afterMainProductCode");
            Integer mainProductCNIndex = null;
            Integer mainProductHKIndex = null;
            Integer mainProductIndex = null;
            List<KzzContractCreateRequestTO.ContractProduct> contractProductList = new ArrayList<>();
            for (int i = 0; i < kzzContractDetail.getContractProducts().size(); i++) {
                KzzContractDetailTO.ContractProduct kzzContractProduct = kzzContractDetail.getContractProducts().get(i);
                KzzContractCreateRequestTO.ContractProduct contractProduct = new KzzContractCreateRequestTO.ContractProduct();
                contractProduct.setProductId(kzzContractProduct.getProductId());
                contractProduct.setPeriod(kzzContractProduct.getPeriod());
                contractProduct.setTimeLimits(kzzContractProduct.getTimeLimits());
                contractProduct.setPrice(kzzContractProduct.getPrice());
                contractProduct.setDiscountPrice(kzzContractProduct.getDiscountPrice());
                contractProduct.setDiscountRate(kzzContractProduct.getDiscountRate());
                contractProduct.setQuantity(kzzContractProduct.getQuantity());
                contractProduct.setTotalPrice(kzzContractProduct.getTotalPrice());
                String productCode = kzzContractProduct.getProductCode();
                if (afterMainProductCode.equals(productCode)) {
                    mainProductIndex = i;
                } else if (afterMainProductCodeCN.equals(productCode)) {
                    mainProductCNIndex = i;
                } else if (afterMainProductCodeHK.equals(productCode)) {
                    mainProductHKIndex = i;
                }
                contractProductList.add(contractProduct);
            }
            // 确定目标主产品索引
            Integer targetIndex = null;
            if (mainProductIndex != null) {
                targetIndex = mainProductIndex;
            } else if (mainProductCNIndex != null) {
                targetIndex = mainProductCNIndex;
            } else if (mainProductHKIndex != null) {
                targetIndex = mainProductHKIndex;
            }

            if (targetIndex != null) {
                KzzContractCreateRequestTO.ContractProduct mainProduct = contractProductList.get(targetIndex);
                if (BigDecimal.ZERO.compareTo(mainProduct.getDiscountPrice()) != 0) {
                    // 计算数量和总价
                    BigDecimal divide = createRequestTO.getAmount().divide(mainProduct.getDiscountPrice(), 2, RoundingMode.HALF_UP);
                    mainProduct.setQuantity(divide.intValue());
                    mainProduct.setTotalPrice(createRequestTO.getAmount());
                } else {
                    logger.error("main product price is zero, contractNo:{}", createRequestTO.getCode());
                }
            }
            createRequestTO.setProducts(contractProductList);
        }
    }

    /**
     * 检查invoice 是否已经回款
     * 回款逻辑判断，根据回款类型=已核销的回款单的总金额计总 比较账号金额是否相等，如果相等则代表已经回款，更新invoice的回款状态，并给用户增加额度
     */
    public void checkInvoicePaymentStatus() {
        List<TblCompStatementMonthly> statementMonthlyList = commTblCompStatementMonthlyService.getUnfinishedPayments();
        for (TblCompStatementMonthly statementMonthly : statementMonthlyList) {
            try {
                KzzContractDetailTO contractDetail = KzzApiInterface.getContractDetail(statementMonthly.getKzzContractNo());
                MsgExceptionUtils.checkIsNull(contractDetail, "msg:合同信息未找到", statementMonthly.getKzzContractNo());
                if (CollectionUtils.isNotEmpty(contractDetail.getPaymentSheets())) {
                    for (KzzContractDetailTO.PaymentSheet kzzPaymentSheet : contractDetail.getPaymentSheets()) {
                        KzzContractPaymentSheet paymentSheet = KzzApiInterface.getPaymentSheet(kzzPaymentSheet.getCode());
                        if (Objects.nonNull(paymentSheet) && Objects.equals(paymentSheet.getType(), 1) && paymentSheet.getAmount().compareTo(statementMonthly.getTotalAmountDue()) == 0) {
                            statementMonthly.setReturnStatus(ReturnStatusEnum.RETURNED.getCode());
                            commTblCompStatementMonthlyService.saveOrUpdateStatement(statementMonthly);
                            createTransaction4Return(statementMonthly);
                        }
                    }
                }
            } catch (MessageException e) {
                logger.error("check payment status failed, dateInt:{}, invoiceNo:{}, contractNo:{}, error:{}", statementMonthly.getStatementMonth(), statementMonthly.getInvoiceNo(), statementMonthly.getKzzContractNo(), e);
            }
        }
    }
}
