package com.backend.common.modules.delivery.service;

import com.backend.common.entity.mapping.CompanyConsumedInfoTO;
import com.backend.common.modules.delivery.model.TransactionBO;
import com.backend.common.modules.delivery.model.TransactionItemConsumeTO;
import com.backend.common.modules.delivery.model.TransactionListTO;
import com.backend.common.modules.delivery.model.TransactionOrderUserConsumeTO;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.ApiTypeEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.backend.common.modules.delivery.condition.TransactionPageCondition;
import com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction;
import com.backend.common.modules.delivery.mapper.TblContractDeliveryTransactionDao;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.jee.commons.service.CrudService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * added for v1.8.8 KNZT-3324
 * 
 * <AUTHOR>
 * @datetime 2024/6/26 10:18
 */
@Service
public class CommTblContractDeliveryTransactionService
        extends CrudService<TblContractDeliveryTransactionDao, TblContractDeliveryTransaction> {
    @Autowired
    private CommTblReportChargeUnitService chargeUnitService;

    public List<TblContractDeliveryTransaction> getByRelId(String relId, String transactionType) {
        return dao.getByRelId(relId, transactionType);
    }


    public BigDecimal sumChangedUnitByProAccAndDate(String contractDeliveryProdAccId, Integer dateInt) {
        BigDecimal res = dao.sumChangedUnitByProAccAndDate(contractDeliveryProdAccId, dateInt);
        return Objects.isNull(res) ? BigDecimal.ZERO : res;
    }

    public BigDecimal sumChangedUnitByProAccAndDateRange(String contractDeliveryProdAccId, Integer beginDateInt,
            Integer endDateInt) {
        BigDecimal res = dao.sumChangedUnitByProAccAndDateRange(contractDeliveryProdAccId, beginDateInt, endDateInt);
        return Objects.isNull(res) ? BigDecimal.ZERO : res;
    }

    // added for v1.9.4 KNZT-4324
    public BigDecimal sumChangedUnitByContractDelivery(String contractDeliveryId, Integer beginDateInt,
            Integer endDateInt) {
        BigDecimal res = dao.sumChangedUnitByContractDeliveryAndDate(contractDeliveryId, beginDateInt, endDateInt);
        return Objects.isNull(res) ? BigDecimal.ZERO : res;
    }

    // added for v1.9.4 KNZT-4324
    public BigDecimal sumChangedUnitByContractDelivery(String contractDeliveryId) {
        return sumChangedUnitByContractDelivery(contractDeliveryId, 0, 99999999);
    }

    // added for lvcy v2.0.6 KNZT-5499
    public BigDecimal sumChangedUnitByContractDeliveryWithType(String contractDeliveryId, Integer beginDateInt,
            Integer endDateInt, List<String> transactionTypeList) {
        BigDecimal res = dao.sumChangedUnitByContractDeliveryWithType(contractDeliveryId, beginDateInt, endDateInt,
                transactionTypeList);
        return Objects.isNull(res) ? BigDecimal.ZERO : res;
    }

    // added for lvcy v2.0.6 KNZT-5499
    public BigDecimal sumCorrectedUnitReleatedOrderTransaction(String contractDeliveryId, Integer beginDateInt,
                                                               Integer endDateInt) {
        BigDecimal res = dao.sumCorrectOrderUnitByContractDeliveryAndDate(contractDeliveryId, beginDateInt, endDateInt);
        return Objects.isNull(res) ? BigDecimal.ZERO : res;
    }

    /**
     * 根据公司Id和时间周期查询公司下所有交易记录
     * added for v1.9.5 KNZT-4117
     *
     * @param companyId
     * @param beginDateInt
     * @param endDateInt
     * @return List<TblContractDeliveryTransaction>
     */
    public List<TblContractDeliveryTransaction> getChangedByCompanyIdAdnDateRange(String companyId, Integer beginDateInt,
                                                                                  Integer endDateInt) {
        return dao.getChangedByCompanyIdAdnDateRange(companyId, beginDateInt, endDateInt);
    }

    /**
     * 根据公司ID查询所有流水记录
     * @param companyId 公司ID
     * @return 流水记录列表
     */
    public List<TblContractDeliveryTransaction> getByCompanyId(String companyId) {
        return dao.findByCompanyId(companyId);
    }

    public void updateProdAccIdByProdAccId(String beforeProdAccId, String afterProdAccId) {
        dao.updateProdAccIdByProdAccId(beforeProdAccId, afterProdAccId);
    }

    public void correctSelfOrderTransaction(String contractDeliveryId, String prodAccId, String relId, BigDecimal beforeRemainUnit) {
        dao.correctSelfOrderTransaction(contractDeliveryId, prodAccId, relId, beforeRemainUnit);
    }

    // added for lvcy v2.0.6 KNZT-5499
    public List<TransactionOrderUserConsumeTO> getChangedSumOrderUnitGroupByUser(String contractDeliveryId, int beginDateInt,
                                                                                 int endDateInt, List<String> transactionList) {
        return dao.getChangedSumOrderUnitGroupByUser(contractDeliveryId, beginDateInt, endDateInt, transactionList);
    }

    /**
     * 计算公司在指定时间范围内的订单额度消耗（按用户分组）
     *
     * @param companyId    公司ID
     * @param userIdList   用户ID列表
     * @param ym 指定月份
     * @return 订单额度消耗列表
     */
    public List<TransactionOrderUserConsumeTO> calcOrderConsumeMonthlyGroupByUser(String companyId, List<String> userIdList, YearMonth ym) {
        LocalDate firstDayOfMonth = ym.atDay(1);
        LocalDate endDayOfMonth = ym.atEndOfMonth();
        int beginDateInt = DateUtils.getDateInt(firstDayOfMonth);
        int endDateInt = DateUtils.getDateInt(endDayOfMonth);
        return dao.calcTransactionConsumeGroupByUser(companyId, userIdList, beginDateInt, endDateInt, TransactionTypeEnum.getOrderTypeWithoutApiList());
    }

    /**
     * 计算公司在指定时间范围内的总额度消耗（不按用户分组）
     *
     * @param companyId    公司ID
     * @param beginDateInt 开始日期
     * @param endDateInt   结束日期
     * @return 总额度消耗
     */
    public BigDecimal calcCompanyTotalConsumeByDateRange(String companyId, int beginDateInt, int endDateInt) {
        return dao.calcCompanyTotalConsumeByDateRange(companyId, beginDateInt, endDateInt, TransactionTypeEnum.getOrderTypeWithoutApiList());
    }

    // added for lvcy v2.0.8 KNZT-5828
    public BigDecimal sumChangedUnitByContractNoAndDateRange(String contractNo, Integer beginDateInt,
            Integer endDateInt) {
        return dao.sumChangedUnitByProAccAndDateRangeByContractNo(contractNo, beginDateInt, endDateInt);
    }

    // added for lvcy v2.0.8 KNZT-5828
    public BigDecimal sumCnChangedUnitByContractNoAndDateRange(String contractNo, Integer beginDateInt,
            Integer endDateInt) {
        List<String> transactionTypeList = TransactionTypeEnum.getOrderTypeList(); // 如果此类型增加新的枚举，需要check sql 中的
                                                                                   // transaction_type 是否需要增加
        return dao.sumCnChangedUnitByContractNoAndDateRange(contractNo, beginDateInt, endDateInt, transactionTypeList);
    }

    // added for lvcy v2.1.2 KNZT-4565
    public List<TransactionListTO> pageTransaction(TransactionPageCondition condition) {
        return dao.pageTransaction(condition);
    }

    public List<TransactionBO> pageTransaction4UserComp(TransactionPageCondition condition) {
        return dao.pageTransaction4UserComp(condition);
    }

    // added for lvcy v2.1.3 KNZT-6174
    public List<TransactionItemConsumeTO> getCompanyConsumeItemGroupByOrderTypeUnit(String companyId, String symbol, int beginDateInt, int endDateInt,
                                                                                    BigDecimal refundedAmount, BigDecimal adjustedAmount) {
        List<TransactionItemConsumeTO> itemConsumeList = dao.getCompanyConsumeItemGroupByOrderTypeUnit(companyId, beginDateInt, endDateInt, Lists.newArrayList(TransactionTypeEnum.ORDER.getCode()));
        for (TransactionItemConsumeTO itemConsumeTO : itemConsumeList) {
            String orderType = itemConsumeTO.getOrderType();
            String orderTypeStr = ReportTypeEnum.getDesc(orderType);
            if (StringUtils.isBlank(orderTypeStr)) {
                orderTypeStr = ApiTypeEnum.getDesc(orderType);
                if (StringUtils.isBlank(orderTypeStr)) {
                    orderTypeStr = "Other";
                }
            }
            itemConsumeTO.setOrderTypeDesc(orderTypeStr);
            String formatAmountStr = formatBillAmountWithSymbol(symbol, itemConsumeTO.getTotalAmount());
            itemConsumeTO.setTotalAmountDesc(formatAmountStr);
            String formatSingleUnitAmountStr = formatBillAmountWithSymbol(symbol, itemConsumeTO.getSingleUnitAmount());
            itemConsumeTO.setSingleUnitAmountDesc(formatSingleUnitAmountStr);
        }
        List<TblReportChargeUnit> chargeUnitList = chargeUnitService.listChargeUnitByCompanyId(Constants.DEFAULT_COMPANY_ID);
        
        // 根据chargeUnitList中的seq排序
        Map<String, Integer> orderTypeSeqMap = chargeUnitList.stream()
            .collect(Collectors.toMap(TblReportChargeUnit::getReportType, TblReportChargeUnit::getSeq, (a, b) -> a));
            
        itemConsumeList.sort((a, b) -> {
            Integer seqA = orderTypeSeqMap.getOrDefault(a.getOrderType(), Integer.MAX_VALUE);
            Integer seqB = orderTypeSeqMap.getOrDefault(b.getOrderType(), Integer.MAX_VALUE);
            return seqA.compareTo(seqB);
        });
        if (refundedAmount != null && refundedAmount.compareTo(BigDecimal.ZERO) != 0) {
            TransactionItemConsumeTO refundItemConsumeTO = new TransactionItemConsumeTO();
            refundItemConsumeTO.setOrderTypeDesc("Refund");
            refundItemConsumeTO.setTotalAmountDesc(formatBillAmountWithSymbol(symbol, refundedAmount));
            refundItemConsumeTO.setSingleUnitAmountDesc("-");
            refundItemConsumeTO.setOrderCount("-");
            itemConsumeList.add(refundItemConsumeTO);
        }
        if (adjustedAmount != null && adjustedAmount.compareTo(BigDecimal.ZERO) != 0) {
            TransactionItemConsumeTO adjustedItemConsumeTO = new TransactionItemConsumeTO();
            adjustedItemConsumeTO.setOrderTypeDesc("Adjustment");
            adjustedItemConsumeTO.setTotalAmountDesc(formatBillAmountWithSymbol(symbol, adjustedAmount));
            adjustedItemConsumeTO.setSingleUnitAmountDesc("-");
            adjustedItemConsumeTO.setOrderCount("-");
            itemConsumeList.add(adjustedItemConsumeTO);
        }
        return itemConsumeList;
    }

    /**
     * 格式化金额，带符号
     * added for lvcy v2.1.3 KNZT-6174
     * 
     * @param symbol
     * @param totalAmount
     * @return
     */
    public static String formatBillAmountWithSymbol(String symbol, BigDecimal totalAmount) {
        symbol = StringUtils.isBlank(symbol) ? "" : symbol + " ";
        if (totalAmount.compareTo(BigDecimal.ZERO) < 0) {
            return "-" + symbol + NumberUtils.formatRptUnit2c(totalAmount.abs());
        } else {
            return symbol + NumberUtils.formatRptUnit2c(totalAmount);
        }
    }

    /**
     * added for v2.1.3 fengsw KNZT-6263
     * 获取时间范围内的公司账号额度消耗情况
     *
     * @param companyIdList
     * @param startDate
     * @param endDate
     * @return
     */
    public List<CompanyConsumedInfoTO> listCompanyConsumedInfoWithDateRange(List<String> companyIdList, Date startDate, Date endDate) {
        return dao.listCompanyConsumedInfoWithDateRange(companyIdList, startDate, endDate);
    }

    /**
     * 根据公司ID和时间范围查询公司账号额度消耗情况
     * added for v2.1.7 lvcy KNZT-6583
     * 
     * @param companyId
     * @param beginDateInt
     * @param endDateInt
     * @param transactionTypeList
     * @return
     */
    public BigDecimal sumChangeUnitByCompanyIdAndDateRange(String companyId, int beginDateInt, int endDateInt, List<String> transactionTypeList) {
        return dao.sumChangeUnitByCompanyIdAndDateRange(companyId, beginDateInt, endDateInt, transactionTypeList);
    }

    public void insert(TblContractDeliveryTransaction transaction) {
        dao.insert(transaction);
    }


    /**
     * 根据合同ID和时间范围查询的订单流水关联的调整流水，根据用户汇总
     * 
     * @param contractDeliveryId 合同ID
     * @param beginDateInt 开始日期
     * @param endDateInt 结束日期
     * @return 调整流水的总和
     */
    public List<TransactionOrderUserConsumeTO> getCorrectedUnitRelatedOrderTransactionByUser(String contractDeliveryId,
                                                                                             int beginDateInt, int endDateInt) {
        return dao.getCorrectedUnitRelateOrderTransactionByUser(contractDeliveryId, beginDateInt, endDateInt);
    }

    /**
     * added for v.2.2.7 fengsw KNZT-7785
     * 根据充值类型和流水类型查询公司账号的额度情况
     * 
     * @param companyId
     * @param category
     * @param typeList
     * @return
     */
    public BigDecimal sumChangeUnitByCategoryAndType(String companyId, String category, List<String> typeList) {
        return dao.sumChangeUnitByCategoryAndType(companyId, category, typeList);
    }
}
