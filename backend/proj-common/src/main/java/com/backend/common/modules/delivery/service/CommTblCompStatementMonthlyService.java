package com.backend.common.modules.delivery.service;

import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.model.StatementMonthlyListTO;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.backend.common.modules.delivery.condition.TblCompStatementMonthlyCondition;
import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.backend.common.modules.delivery.mapper.TblCompStatementMonthlyDao;
import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.model.StatementMonthly4ManagementTO;

import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;

import java.net.URLDecoder;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
public class CommTblCompStatementMonthlyService extends CrudService<TblCompStatementMonthlyDao, TblCompStatementMonthly> {
    @Autowired
    private CommTblContractDeliveryService deliveryService;

    /**
     * 生成唯一的发票号
     * @param companyId 公司id
     * @param yearMonth 年月
     * @return 发票号
     * @throws MessageException 异常
     */
    public String generateInvoiceNo(String companyId, YearMonth yearMonth) throws MessageException {
        String baseInvoiceNo = generateBaseInvoiceNo(companyId, yearMonth, 0);
        // 检查是否已存在，最多尝试26次（A-Z）
        for (int i = 0; i < 26; i++) {
            TblCompStatementMonthly exist = dao.getByInvoiceNo(baseInvoiceNo);
            if (exist == null || companyId.equals(exist.getCompanyId())) {
                return baseInvoiceNo;
            }
            // 已存在且属于其他公司，生成新的号码
            baseInvoiceNo = generateBaseInvoiceNo(companyId, yearMonth, i + 1);
        }
        throw new MessageException("无法生成唯一的发票号");
    }

    /**
     * 生成基础发票号
     * @param companyId 公司id
     * @param yearMonth 年月
     * @param extraSeed 额外种子
     * @return 基础发票号
     */
    private static String generateBaseInvoiceNo(String companyId, YearMonth yearMonth, int extraSeed) {
        // 使用companyId和年月的组合计算hash值
        int hash = (companyId + yearMonth.toString() + extraSeed).hashCode();
        StringBuilder randomStr = new StringBuilder();
        // 确保为正数
        hash = Math.abs(hash);
        // 从hash值中提取3个0-25的数字，转换为A-Z
        for (int i = 0; i < 3; i++) {
            randomStr.append((char) ('A' + (hash % 26)));
            hash = hash / 26;
        }
        return "INV" + companyId.substring(0, 6).toUpperCase() + randomStr + DateUtils.formatYearMonthInt(yearMonth);
    }

    /**
     * 更新保存月度报表
     * added for v1.9.5 KNZT-4117
     *
     * @param statement
     * @return
     */
    public void saveOrUpdateStatement(TblCompStatementMonthly statement) {
        TblCompStatementMonthly existStatement = getByCompanyAndYearMonth(
                statement.getCompanyId(), statement.getStatementMonth());
        if (Objects.isNull(existStatement)) {
            save(statement);
        } else {
            statement.setId(existStatement.getId());
            save(statement);
            // 删除不可用的obs文件
            if (StringUtils.isNotEmpty(existStatement.getUrl())
                    && !StringUtils.equals(existStatement.getUrl(), statement.getUrl())) {
                deleteDiscardObsFile(existStatement.getUrl());
            }
        }
    }


    private void deleteDiscardObsFile(String url) {
        try {
            String decodedUrl = URLDecoder.decode(url, "UTF-8");
            int startIndex = decodedUrl.indexOf("statement");
            String obsObjectName = decodedUrl.substring(startIndex);
            HuaweiObsServUtils.getInstance().deleteObject(obsObjectName);
        } catch (Exception e) {
            logger.error("delete obs file error", e);
        }
    }


    /**
     * 根据公司id查指定月度报告
     * added for v1.9.5 KNZT-4117
     *
     * @param companyId
     * @param statementMonth
     * @return TblCompStatementMonthly
     */
    public TblCompStatementMonthly getByCompanyAndYearMonth(String companyId, Integer statementMonth) {
        return dao.getByCompanyAndYearMonth(companyId, statementMonth);
    }

    /**
     * 分页查询
     * added for v1.9.5 KNZT-4117
     *
     * @param condition
     * @return Page<StatementMonthlyListTO>
     */
    public Page<StatementMonthlyListTO> listByCondition(TblCompStatementMonthlyCondition condition) {
        condition.setCompanyId(UserUtils.getUserCompanyId());
        Page<TblCompStatementMonthly> page = this.findListByCondtion(condition);
        String amountStd = null;
        List<TblContractDelivery> deliveryList = deliveryService.getByCompanyId(UserUtils.getUserCompanyId());
        deliveryList.sort(Comparator.comparing(TblContractDelivery::getCreateDate).reversed());
        if (CollectionUtils.isNotEmpty(deliveryList)) {
            amountStd = deliveryList.get(deliveryList.size() - 1).getAmountStd();
        } else {
            amountStd = "USD";
        }

        Page<StatementMonthlyListTO> statementMonthlyListTOPage = new Page<>();
        statementMonthlyListTOPage.setList(StatementMonthlyListTO.build(page.getList(), amountStd));
        statementMonthlyListTOPage.setCount(page.getCount());
        statementMonthlyListTOPage.setPageNo(page.getPageNo());
        statementMonthlyListTOPage.setPageSize(page.getPageSize());


        return statementMonthlyListTOPage;
    }

    /**
     * 月度账单管理分页查询
     * @param condition 查询条件
     * @return 分页结果
     */
    public List<StatementMonthly4ManagementTO> page4Management(StatementMonthly4ManagementCondition condition) {
        return dao.page4Management(condition);
    }


    /**
     * 根据发票号查询
     * @param invoiceNo 发票号
     * @return 月度报表
     */
    public TblCompStatementMonthly getByInvoiceNo(String invoiceNo) {
        return dao.getByInvoiceNo(invoiceNo);
    }
    public List<TblCompStatementMonthly> listByIds(List<String> statementMonthlyIdList) {
        return dao.listByIds(statementMonthlyIdList);
    }

    /**
     * 查询未回款的账单据
     * 
     * @return
     */
    public List<TblCompStatementMonthly> getUnfinishedPayments() {
        return dao.getUnfinishedPayments();
    }
}
