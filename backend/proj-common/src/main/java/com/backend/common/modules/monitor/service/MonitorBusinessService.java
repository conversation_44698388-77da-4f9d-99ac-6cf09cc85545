package com.backend.common.modules.monitor.service;

import com.backend.common.entity.SysTemplate;
import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.global.gateway.event.MonitorEventInterface;
import com.backend.common.global.gateway.event.form.EntityStatisticsForm;
import com.backend.common.global.gateway.event.form.EventBaseForm;
import com.backend.common.global.gateway.event.form.EventOverviewForm;
import com.backend.common.global.gateway.event.form.EventStatisticsRealtimeForm;
import com.backend.common.global.gateway.event.form.ListEventForm;
import com.backend.common.global.gateway.event.form.MonitorNewsDetailForm;
import com.backend.common.global.gateway.event.form.PortfolioStatisticsForm;
import com.backend.common.global.gateway.event.form.StatisticsLastRefreshTimeForm;
import com.backend.common.global.gateway.event.model.EventEntityTO;
import com.backend.common.global.gateway.event.model.EventNewsDetailTO;
import com.backend.common.global.gateway.event.model.EventOverviewTO;
import com.backend.common.global.gateway.event.model.MonitorEventListTO;
import com.backend.common.global.gateway.event.model.StatisticsLastRefreshTimeTO;
import com.backend.common.global.gateway.event.model.statistics.EntityStatisticsTO;
import com.backend.common.global.gateway.event.model.statistics.PortfolioStatisticsRealtimeTO;
import com.backend.common.global.gateway.event.model.statistics.PortfolioStatisticsTO;
import com.backend.common.modules.benefit.entity.TblBenefitPool;
import com.backend.common.modules.benefit.entity.TblBenefitPoolChangeRecord;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolChangeRecordService;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolService;
import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.monitor.entity.TblMonitorEntityCompany;
import com.backend.common.modules.monitor.entity.TblMonitorEntityCompanyUser;
import com.backend.common.modules.monitor.entity.TblMonitorEntityCompanyUserHis;
import com.backend.common.modules.monitor.entity.TblMonitorUserPortfolio;
import com.backend.common.modules.monitor.entity.TblMonitorUserPortfolioNotifySetting;
import com.backend.common.modules.monitor.entity.TblMonitorUserPortfolioNotifySignal;
import com.backend.common.modules.monitor.form.EntityItem;
import com.backend.common.modules.monitor.form.ListMonitorFollowForm;
import com.backend.common.modules.monitor.form.MonitorBatchFollowForm;
import com.backend.common.modules.monitor.form.MonitorEntityUserListForm;
import com.backend.common.modules.monitor.form.MonitorFollowForm;
import com.backend.common.modules.monitor.form.MonitorFollowUpdateBO;
import com.backend.common.modules.monitor.form.MonitorUnfollowForm;
import com.backend.common.modules.monitor.form.UserPortfolioSaveForm;
import com.backend.common.modules.monitor.model.CheckInOtherPortfolioTO;
import com.backend.common.modules.monitor.model.ListNeedAddEntityCompanyTO;
import com.backend.common.modules.monitor.model.ListNeedRemoveEntityCompanyTO;
import com.backend.common.modules.monitor.model.MonitorEntityUserListTO;
import com.backend.common.modules.monitor.model.MonitorFollowInfoTO;
import com.backend.common.modules.monitor.model.MonitorNotifySettingTO;
import com.backend.common.modules.monitor.model.MonitorServiceUpdateTO;
import com.backend.common.modules.monitor.model.PortfolioEntityCountTO;
import com.backend.common.modules.monitor.model.UserPortfolioListTO;
import com.backend.common.service.CommSysTemplateService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.form.PageReq;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.exception.MessageExceptionEnum;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.FreemarkerUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.SysUserExt;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.QccMailSenderService;
import com.qcc.frame.jee.modules.sys.service.SysUserExtService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.qcc.frame.commons.Constants.CorpTypePrefix.FOREIGN_CORPORATE_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.PERSON_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.TAIWAN_CORPORATE_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.US_STOCKS;

@Service
public class MonitorBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(MonitorBusinessService.class);
    @Autowired
    private CommTblMonitorUserPortfolioService monitorUserPortfolioService;
    @Autowired
    private CommTblMonitorEntityCompanyUserService monitorEntityCompanyUserService;
    @Autowired
    private CommTblMonitorEntityCompanyService monitorEntityCompanyService;
    @Autowired
    private QccMailSenderService qccMailSenderService;
    @Autowired
    private CommTblMonitorEntityCompanyUserService userMonitorPortfolioService;
    @Autowired
    private CommTblMonitorUserPortfolioNotifySignalService portfolioNotifySignalService;
    @Autowired
    private CommTblMonitorUserPortfolioNotifySettingService providerNotifySettingService;
    @Autowired
    private CommSysTemplateService commSysTemplateService;
    @Autowired
    private SysUserExtService sysUserExtService;
    @Autowired
    private CommTblBenefitPoolChangeRecordService commTblBenefitPoolChangeRecordService;
    @Autowired
    private CommTblMonitorEntityCompanyUserHisService commTblMonitorEntityCompanyUserHisService;
    @Autowired
    private CommTblBenefitPoolService commTblBenefitPoolService;
    @Autowired
    private CommonDataService commonDataService;

    // updated for v2.2.8 fengsw KNZT-7845【优化】【codereview】监控推送时，校验用户监控权限逻辑优化
    public void checkMonitorPermission(List<String> withBenefitTypes) throws MessageException {
        String userCompanyId = UserUtils.getUserCompanyId();
        boolean serviceEnabled = checkMonitorPermissionByCompanyId(userCompanyId, withBenefitTypes);
        MsgExceptionUtils.failBuild(!serviceEnabled, MessageExceptionEnum.MONITOR_SERVICE_NOT_ENABLED);
    }

    private boolean checkMonitorPermissionByCompanyId(String userCompanyId, List<String> withBenefitTypes) {
        // 检查监控维度, 数据接口需要传入对应的维度，这里需要检查维度是否可见
        TblBenefitPool effectBenefitPool = commTblBenefitPoolService.getEffectMonitorBenefitPoolByCompanyId(userCompanyId);
        return Objects.nonNull(effectBenefitPool) && withBenefitTypes.contains(effectBenefitPool.getBenefitType());
    }

    public List<CheckInOtherPortfolioTO> checkInOtherPortfolio(UserPortfolioSaveForm form) throws MessageException {
        if (form == null || CollectionUtils.isEmpty(form.getEntityItems())) {
            return new ArrayList<>();
        }
        Set<String> keyNos = form.getEntityItems().stream().map(EntityItem::getKeyNo).collect(Collectors.toSet());
        return monitorEntityCompanyUserService.checkInOtherPortfolio(UserUtils.getUserId(), form.getId(), keyNos);
    }

    public void saveUserPortfolio(UserPortfolioSaveForm form) throws MessageException {
        // 保存或更新监控组
        String userId = UserUtils.getUserId();
        String companyId = UserUtils.getUserCompanyId();
        String portfolioId = monitorUserPortfolioService.saveUserPortfolio(userId, form.getId(), form.getName());

        // 保存用户监控池
        MonitorFollowUpdateBO monitorFollowUpdateBO = this.buildMonitorFollowUpdateBO4SaveUserPortfolio(companyId, userId, portfolioId, form.getEntityItems());
        this.doUpdateMonitorUserFollow(monitorFollowUpdateBO);
    }

    // 批量更新分组内关注实体（表单相对DB多则插入，少则删除）
    private MonitorFollowUpdateBO buildMonitorFollowUpdateBO4SaveUserPortfolio(String companyId, String userId, String portfolioId, List<EntityItem> entityItems) throws MessageException {
        if (entityItems == null) {
            entityItems = new ArrayList<>();
        }
        MonitorFollowUpdateBO monitorFollowUpdateBO = new MonitorFollowUpdateBO();
        Set<String> keyNosForm = entityItems.stream().map(EntityItem::getKeyNo).collect(Collectors.toSet());
        List<TblMonitorEntityCompanyUser> tblMonitorEntityCompanyUsersDb = monitorEntityCompanyUserService.listByUserPortfolioId(userId, null);

        for (EntityItem entityItem : entityItems) {
            monitorFollowUpdateBO.getFollowItems().add(MonitorFollowUpdateBO.EntityFollowItem.build(entityItem.getKeyNoType(), entityItem.getKeyNo(), portfolioId));
        }
        // 本分组内需要删除的
        monitorFollowUpdateBO.getUnfollowKeyNos().addAll(tblMonitorEntityCompanyUsersDb.stream()
                .filter(tblMonitorEntityCompanyUser -> StringUtils.equals(portfolioId, tblMonitorEntityCompanyUser.getPortfolioId())
                        && !keyNosForm.contains(tblMonitorEntityCompanyUser.getKeyNo())).map(TblMonitorEntityCompanyUser::getKeyNo).collect(Collectors.toSet()));
        return monitorFollowUpdateBO;
    }

    public void insertMonitorEntityCompanyUserWithCheck(List<TblMonitorEntityCompanyUser> list) throws MessageException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String configValue = ConfigUtils.getConfigValueByTypeAndKey("monitor", "portfolio_limit", "2000");
        int portfolioLimit = Integer.parseInt(configValue);
        String userId = list.get(0).getUserId();
        Map<String, Long> portfolioId2InsertCountMap = list.stream().collect(Collectors.groupingBy(TblMonitorEntityCompanyUser::getPortfolioId, Collectors.counting()));
        List<PortfolioEntityCountTO> portfolioEntityCountTOS = monitorEntityCompanyUserService.listPortfolioEntityCount(userId);
        Map<String, Integer> portfolioId2DbCountMap = MappingUtils.getMap(portfolioEntityCountTOS, PortfolioEntityCountTO::getPortfolioId, PortfolioEntityCountTO::getEntityCount);
        for (Map.Entry<String, Long> entry : portfolioId2InsertCountMap.entrySet()) {
            String portfolioId = entry.getKey();
            Long insertCount = entry.getValue();
            Integer dbCount = portfolioId2DbCountMap.getOrDefault(portfolioId, 0);
            MsgExceptionUtils.failBuild(dbCount + insertCount > portfolioLimit, MessageExceptionEnum.WARN_MONITOR_OVER_PORTFOLIO_LIMIT, NumberUtils.format(portfolioLimit, "#,##0"));
        }
        monitorEntityCompanyUserService.batchPartialInsert(list);
    }

    public List<MonitorEntityUserListTO> listEntityUser(MonitorEntityUserListForm form) {
        return monitorEntityCompanyUserService.listEntityUser(UserUtils.getUserId(), form.getPortfolioId());
    }

    public MonitorFollowInfoTO getFollowInfo(EntityItem form) {
        return monitorEntityCompanyUserService.getMonitorFollowInfo(UserUtils.getUserId(), form.getKeyNo());
    }

    public List<MonitorFollowInfoTO> listFollowInfo(ListMonitorFollowForm form) {
        if (CollectionUtils.isEmpty(form.getKeyNos())) {
            return Collections.emptyList();
        }
        List<String> supportedKycKeyNos = CorpGatewayInterface.listSupportedKycKeyNos(form.getKeyNos());
        List<MonitorFollowInfoTO> monitorFollowInfoTOS = monitorEntityCompanyUserService.listMonitorFollowInfo(UserUtils.getUserId(), form.getKeyNos());
        Map<String, MonitorFollowInfoTO> keyNo2FollowInfoMap = MappingUtils.getMap(monitorFollowInfoTOS, MonitorFollowInfoTO::getKeyNo, Function.identity());
        return form.getKeyNos()
                .stream().map(keyNo -> {
                    MonitorFollowInfoTO to = new MonitorFollowInfoTO();
                    to.setKeyNo(keyNo);
                    to.setKeyNoType(StringUtils.startsWith(keyNo, PERSON_PREFIX) ? "P" : "C");
                    to.setSupportKyc(supportedKycKeyNos.contains(keyNo));
                    to.setSupportMonitor(!StringUtils.startsWithAny(keyNo, FOREIGN_CORPORATE_PREFIX, HONGKONG_CORPORATE_PREFIX, TAIWAN_CORPORATE_PREFIX, US_STOCKS, PERSON_PREFIX));
                    MonitorFollowInfoTO monitorFollowInfoTODB = keyNo2FollowInfoMap.get(keyNo);
                    if (monitorFollowInfoTODB != null) {
                        to.setMonitorId(monitorFollowInfoTODB.getMonitorId());
                        to.setPortfolioId(monitorFollowInfoTODB.getPortfolioId());
                        to.setPortfolioName(monitorFollowInfoTODB.getPortfolioName());
                        to.setFollowTime(monitorFollowInfoTODB.getFollowTime());
                    }
                    return to;
                }).collect(Collectors.toList());
    }

    public MonitorServiceUpdateTO followPreview(MonitorBatchFollowForm form) throws MessageException {
        String companyId = UserUtils.getUserCompanyId();
        List<String> formKeyNos = form.getKeyNos();
        MsgExceptionUtils.failBuild(CollectionUtils.isEmpty(formKeyNos));
        TblBenefitPool effectBenefitPool = commTblBenefitPoolService.getEffectMonitorBenefitPoolByCompanyId(companyId);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(effectBenefitPool), logger, "没有有效监控权限, companyId: " + companyId);
        MonitorServiceUpdateTO updateTO = new MonitorServiceUpdateTO();
        updateTO.setBeforeCount(effectBenefitPool.getTotalCount() - effectBenefitPool.getConsumedCount());
        Set<String> keyNos = monitorEntityCompanyService.listMonitoredKeyNos4Follow(companyId, formKeyNos);
        int thisConsumedCount = formKeyNos.size() - keyNos.size();
        updateTO.setAfterCount(updateTO.getBeforeCount() - thisConsumedCount);
        return updateTO;
    }

    public void follow(MonitorFollowForm form) throws MessageException {
        String userId = UserUtils.getUserId();
        String companyId = UserUtils.getUserCompanyId();
        monitorUserPortfolioService.batchSavePortfolios(userId, form.getNewPortfolioNames(), form.getUpdatePortfolios());
        TblMonitorUserPortfolio toUserPortfolio = monitorUserPortfolioService.getByName(userId, form.getToPortfolioName());
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(toUserPortfolio), logger, "监控组名不存在，" + form.getToPortfolioName());

        MonitorFollowUpdateBO monitorFollowUpdateBO = new MonitorFollowUpdateBO();
        monitorFollowUpdateBO.getFollowItems().add(MonitorFollowUpdateBO.EntityFollowItem.build(form.getKeyNoType(), form.getKeyNo(), toUserPortfolio.getId()));
        this.doUpdateMonitorUserFollow(monitorFollowUpdateBO);
    }

    public MonitorServiceUpdateTO batchFollow(MonitorBatchFollowForm form) throws MessageException {
        String userId = UserUtils.getUserId();
        String companyId = UserUtils.getUserCompanyId();
        monitorUserPortfolioService.batchSavePortfolios(userId, form.getNewPortfolioNames(), form.getUpdatePortfolios());
        TblMonitorUserPortfolio toUserPortfolio = monitorUserPortfolioService.getByName(userId, form.getToPortfolioName());
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(toUserPortfolio), logger, "监控组名不存在，" + form.getToPortfolioName());

        MonitorFollowUpdateBO monitorFollowUpdateBO = new MonitorFollowUpdateBO();

        monitorFollowUpdateBO.getFollowItems().addAll(form.getKeyNos().stream().map(keyNo ->
                MonitorFollowUpdateBO.EntityFollowItem.build("C", keyNo, toUserPortfolio.getId())
        ).collect(Collectors.toList()));
        return this.doUpdateMonitorUserFollow(monitorFollowUpdateBO);
    }

    public void unfollow(MonitorUnfollowForm form) throws MessageException {
        MonitorFollowUpdateBO monitorFollowUpdateBO = new MonitorFollowUpdateBO();
        monitorFollowUpdateBO.getUnfollowKeyNos().add(form.getKeyNo());
        this.doUpdateMonitorUserFollow(monitorFollowUpdateBO);
    }

    /**
     * 作为用户关注/取关企业的统一入口
     *
     * @param monitorFollowUpdateBO
     * @throws MessageException
     */
    public MonitorServiceUpdateTO doUpdateMonitorUserFollow(MonitorFollowUpdateBO monitorFollowUpdateBO) throws MessageException {
        String userId = UserUtils.getUserId();
        String companyId = UserUtils.getUserCompanyId();
        TblBenefitPool effectBenefitPool = commTblBenefitPoolService.getEffectMonitorBenefitPoolByCompanyId(companyId);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(effectBenefitPool), logger, "没有有效监控权限, companyId: " + companyId);
        commTblBenefitPoolService.lockBenefitPoolById(effectBenefitPool.getId());
        effectBenefitPool = commTblBenefitPoolService.get(effectBenefitPool.getId());
        MonitorServiceUpdateTO updateTO = new MonitorServiceUpdateTO();
        updateTO.setBeforeCount(effectBenefitPool.getTotalCount() - effectBenefitPool.getConsumedCount());
        this.updateMonitorUser(companyId, userId, monitorFollowUpdateBO, effectBenefitPool);
        this.updateMonitorCompany(companyId, userId, effectBenefitPool);
        this.updateBenefitPool(companyId, userId, effectBenefitPool);
        effectBenefitPool = commTblBenefitPoolService.get(effectBenefitPool.getId());
        updateTO.setAfterCount(effectBenefitPool.getTotalCount() - effectBenefitPool.getConsumedCount());
        return updateTO;
    }

    private void updateMonitorUser(String companyId, String userId, MonitorFollowUpdateBO monitorFollowUpdateBO, TblBenefitPool effectBenefitPool) throws MessageException {
        List<EntityItem> newFollowEntityItems = new ArrayList<>(); // 本次新关注的实体
        List<EntityItem> newUnfollowEntityItems = new ArrayList<>(); // 本次取关的实体
        List<String> monitorEntityCompanyUserId2Delete = new ArrayList<>(); // 待删除的关注关系记录
        List<TblMonitorEntityCompanyUser> monitorEntityCompanyUsers2Insert = new ArrayList<>(); // 待新增的关注关系记录
        if (CollectionUtils.isNotEmpty(monitorFollowUpdateBO.getUnfollowKeyNos())) {
            List<TblMonitorEntityCompanyUser> monitorFollowInfosDb = monitorEntityCompanyUserService.listByKeyNos(userId, monitorFollowUpdateBO.getUnfollowKeyNos());
            if (CollectionUtils.isNotEmpty(monitorFollowInfosDb)) {
                monitorEntityCompanyUserId2Delete.addAll(monitorFollowInfosDb.stream().map(TblMonitorEntityCompanyUser::getId).filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
                newUnfollowEntityItems.addAll(monitorFollowInfosDb.stream().map(monitorFollowInfoTO ->
                        EntityItem.build(monitorFollowInfoTO.getKeyNoType(), monitorFollowInfoTO.getKeyNo())).collect(Collectors.toList()));
            }
        }
        List<MonitorFollowUpdateBO.EntityFollowItem> followItemsForm = monitorFollowUpdateBO.getFollowItems();
        if (CollectionUtils.isNotEmpty(followItemsForm)) {
            Set<String> keyNos = followItemsForm.stream().map(EntityItem::getKeyNo).collect(Collectors.toSet());
            List<MonitorFollowInfoTO> monitorFollowInfosDb = monitorEntityCompanyUserService.listMonitorFollowInfo(userId, keyNos);
            Map<String, MonitorFollowInfoTO> keyNo2FollowInfoDbMap = MappingUtils.getMap(monitorFollowInfosDb, MonitorFollowInfoTO::getKeyNo, Function.identity());
            for (MonitorFollowUpdateBO.EntityFollowItem followItem : followItemsForm) {
                MonitorFollowInfoTO monitorFollowInfoDb = keyNo2FollowInfoDbMap.get(followItem.getKeyNo());
                if (monitorFollowInfoDb == null) {
                    monitorEntityCompanyUsers2Insert.add(TblMonitorEntityCompanyUser.build(companyId, userId, followItem.getPortfolioId(), followItem.getKeyNoType(), followItem.getKeyNo()));
                    newFollowEntityItems.add(EntityItem.build(followItem.getKeyNoType(), followItem.getKeyNo()));
                } else {
                    if (!StringUtils.equals(followItem.getPortfolioId(), monitorFollowInfoDb.getPortfolioId())) {
                        monitorEntityCompanyUserId2Delete.add(monitorFollowInfoDb.getMonitorId());
                        monitorEntityCompanyUsers2Insert.add(TblMonitorEntityCompanyUser.build(companyId, userId, followItem.getPortfolioId(), followItem.getKeyNoType(), followItem.getKeyNo()));
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(monitorEntityCompanyUserId2Delete)) {
            monitorEntityCompanyUserService.batchDelete(monitorEntityCompanyUserId2Delete, userId);
        }
        if (CollectionUtils.isNotEmpty(monitorEntityCompanyUsers2Insert)) {
            this.insertMonitorEntityCompanyUserWithCheck(monitorEntityCompanyUsers2Insert);
        }
        this.updateMonitorUserHist(companyId, userId, effectBenefitPool.getBenefitType(), newFollowEntityItems, newUnfollowEntityItems);
    }

    /**
     * 更新用户关注历史
     *
     * @param companyId
     * @param userId
     * @param monitorType            监控类型
     * @param newFollowEntityItems   新关注的实体
     * @param newUnfollowEntityItems 取关的实体
     */
    private void updateMonitorUserHist(String companyId, String userId, String monitorType, List<EntityItem> newFollowEntityItems, List<EntityItem> newUnfollowEntityItems) {
        if (CollectionUtils.isNotEmpty(newFollowEntityItems)) {
            List<TblMonitorEntityCompanyUserHis> monitorUserHisList = newFollowEntityItems.stream().map(entityItem -> {
                TblMonitorEntityCompanyUserHis monitorUserHis = new TblMonitorEntityCompanyUserHis();
                monitorUserHis.setCompanyId(companyId);
                monitorUserHis.setUserId(userId);
                monitorUserHis.setKeyNoType(entityItem.getKeyNoType());
                monitorUserHis.setKeyNo(entityItem.getKeyNo());
                monitorUserHis.setMonitorStatus(Constants.YES);
                monitorUserHis.setMonitorType(monitorType);
                monitorUserHis.setBeginDate(new Date());
                return monitorUserHis;
            }).collect(Collectors.toList());
            commTblMonitorEntityCompanyUserHisService.batchPartialInsert(monitorUserHisList);
        }
        if (CollectionUtils.isNotEmpty(newUnfollowEntityItems)) {
            Set<String> unfollowKeyNos = newUnfollowEntityItems.stream().map(EntityItem::getKeyNo).collect(Collectors.toSet());
            commTblMonitorEntityCompanyUserHisService.updateEndDate(userId, unfollowKeyNos, new Date());
        }
    }

    /**
     * 更新用户关注信息后置处理，更新公司监控池
     *
     * @param companyId
     * @param userId
     * @throws MessageException
     */
    private void updateMonitorCompany(String companyId, String userId, TblBenefitPool effectBenefitPool) throws MessageException {
        // 刷新公司监控池(公司监控池里的实体无用户关注则移除，有用户关注但公司监控池里无则插入)
        String benefitPoolId = effectBenefitPool.getId();

        AtomicReference<Integer> monitorPoolSeatRemainCount = new AtomicReference<>(effectBenefitPool.getTotalCount() - effectBenefitPool.getConsumedCount()); // 监控权益余量
        List<String> monitorEntityCompanyIds2Delete = new ArrayList<>();
        List<TblMonitorEntityCompany> monitorEntityCompanyList2Save = new ArrayList<>();
        List<TblBenefitPoolChangeRecord> changeRecords = new ArrayList<>();
        List<ListNeedRemoveEntityCompanyTO> listNeedRemoveEntityCompanyTOS = monitorEntityCompanyUserService.listNeedRemoveEntityCompany(companyId);
        if (CollectionUtils.isNotEmpty(listNeedRemoveEntityCompanyTOS)) {
            monitorEntityCompanyIds2Delete.addAll(listNeedRemoveEntityCompanyTOS.stream().map(ListNeedRemoveEntityCompanyTO::getMonitorEntityCompanyId).collect(Collectors.toSet()));
            List<TblMonitorEntityCompany> tblMonitorEntityCompanies = monitorEntityCompanyService.listByIds(monitorEntityCompanyIds2Delete);
            List<TblBenefitPoolChangeRecord> releaseChangeRecords = tblMonitorEntityCompanies.stream().map(tblMonitorEntityCompany ->
                            TblBenefitPoolChangeRecord.build4releaseOneSeat(companyId, userId, benefitPoolId, monitorPoolSeatRemainCount.getAndAccumulate(1, Integer::sum), tblMonitorEntityCompany.getId(), tblMonitorEntityCompany.getKeyNo()))
                    .collect(Collectors.toList());
            changeRecords.addAll(releaseChangeRecords);
        }

        List<ListNeedAddEntityCompanyTO> listNeedAddEntityCompanyTOS = monitorEntityCompanyUserService.listNeedAddEntityCompany(companyId);
        if (CollectionUtils.isNotEmpty(listNeedAddEntityCompanyTOS)) {
            monitorEntityCompanyList2Save.addAll(listNeedAddEntityCompanyTOS.stream().map(listNeedAddEntityCompanyTO -> TblMonitorEntityCompany.build(companyId, listNeedAddEntityCompanyTO.getKeyNoType(), listNeedAddEntityCompanyTO.getKeyNo()))
                    .collect(Collectors.toList()));

            List<TblBenefitPoolChangeRecord> releaseChangeRecords = monitorEntityCompanyList2Save.stream().map(tblMonitorEntityCompany ->
                            TblBenefitPoolChangeRecord.build4allocateOneSeat(companyId, userId, benefitPoolId, monitorPoolSeatRemainCount.getAndAccumulate(1, (prev, x) -> prev - x), tblMonitorEntityCompany.getId(), tblMonitorEntityCompany.getKeyNo()))
                    .collect(Collectors.toList());
            changeRecords.addAll(releaseChangeRecords);
        }

        MsgExceptionUtils.failBuild(monitorPoolSeatRemainCount.get() < 0, MessageExceptionEnum.WARN_MONITOR_OVER_COMPANY_LIMIT);
        if (CollectionUtils.isNotEmpty(monitorEntityCompanyIds2Delete)) {
            monitorEntityCompanyService.batchDelete(monitorEntityCompanyIds2Delete, userId);
        }
        if (CollectionUtils.isNotEmpty(monitorEntityCompanyList2Save)) {
            monitorEntityCompanyService.batchPartialInsert(monitorEntityCompanyList2Save);
        }
        if (CollectionUtils.isNotEmpty(changeRecords)) {
            commTblBenefitPoolChangeRecordService.batchPartialInsert(changeRecords);
        }
    }

    private void updateBenefitPool(String companyId, String userId, TblBenefitPool effectBenefitPool) {
        Integer monitoredEntityCount = monitorEntityCompanyService.monitoredEntityCount(companyId);
        commTblBenefitPoolService.updateConsumedCount(userId, effectBenefitPool.getId(), monitoredEntityCount);
    }

    public JsonResultList<MonitorEventListTO> pageEvent(ListEventForm form) throws MessageException {
        JsonResultList<MonitorEventListTO> resultList = MonitorEventInterface.pageEvent(form);
        List<MonitorEventListTO> monitorEventListTOS = resultList.getResultList();
        if (CollectionUtils.isNotEmpty(monitorEventListTOS)) {
            List<EventEntityTO> eventEntityTOS = monitorEventListTOS.stream().map(MonitorEventListTO::getSourceEntity).filter(Objects::nonNull).collect(Collectors.toList());
            eventEntityTOS.addAll(MappingUtils.flattenAndGetList(monitorEventListTOS, MonitorEventListTO::getMentionedEntityList));
            this.fill4EventEntityList(form.getUserId(), eventEntityTOS);
            monitorEventListTOS.forEach(monitorEventListTO -> monitorEventListTO.setCreateDateEn(DateUtils.formatDateForSg(monitorEventListTO.getCreateDate(), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH)));
        }
        return resultList;
    }

    public EventNewsDetailTO getNewsDetail(MonitorNewsDetailForm form) {
        EventNewsDetailTO eventNewsDetailTO = MonitorEventInterface.getNewsDetail(form);
        if (eventNewsDetailTO != null) {
            List<EventEntityTO> relatedEntityList = eventNewsDetailTO.getRelatedEntityList();
            if (CollectionUtils.isNotEmpty(relatedEntityList)) {
                fill4EventEntityList(form.getUserId(), relatedEntityList);
                relatedEntityList.forEach(t -> {
                    t.setSourceEntity(StringUtils.equals(t.getKeyNo(), form.getSourceKeyNo()));
                });
                // 事件来源实体放在第一个
                CollectionUtils.sort(relatedEntityList, EventEntityTO::getSourceEntity, false);
            }
        }
        return eventNewsDetailTO;
    }

    private void fill4EventEntityList(String userId, Collection<EventEntityTO> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        Set<String> keyNos = MappingUtils.getSet(entityList, EventEntityTO::getKeyNo);
        List<MonitorFollowInfoTO> monitorFollowInfoTOS = monitorEntityCompanyUserService.listMonitorFollowInfo(userId, keyNos);
        Map<String, MonitorFollowInfoTO> keyNo2FollowInfoMap = MappingUtils.getMap(monitorFollowInfoTOS, MonitorFollowInfoTO::getKeyNo, Function.identity());
        MappingUtils.doMapping(entityList, EventEntityTO::getKeyNo, (e, v) -> {
            e.setPortfolioId(v.getPortfolioId());
            e.setPortfolioName(v.getPortfolioName());
            e.setFollowTime(v.getFollowTime());
        }, keyNo2FollowInfoMap::get);
        entityList.forEach(eventEntityTO -> {
            if (eventEntityTO.getSupportKyc() == null || !eventEntityTO.getSupportKyc()) {
                eventEntityTO.setKeyNo(null);
            }
        });

        // 实体英文名处理
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
        if (CollectionUtils.isNotEmpty(entityList)) {
            entityList.forEach(obj -> transWrapper.buildAndAddEntry(obj, EventEntityTO::setName, EventEntityTO::setNameEn, EventEntityTO::getName, EventEntityTO::getNameEn).withEntity(EventEntityTO::getKeyNo, TransWrapper.ENTRY_TYPE_CORP));
        }
        commonDataService.enPostProcessor(transWrapper);
    }

    public EventOverviewTO getEventOverview(EventOverviewForm form) {
        return MonitorEventInterface.getEventOverview(form);
    }

    // 后端假分页
    public JsonResultList<PortfolioStatisticsTO> pagePortfolioStatistics(PortfolioStatisticsForm form) throws MessageException {
        form.setCompanyId(UserUtils.getUserCompanyId());
        form.setUserId(UserUtils.getUserId());
        if (form.getPage() == null) {
            form.setPage(new PageReq());
        }
        PageReq originalPage = form.getPage();
        List<UserPortfolioListTO> userPortfolioListTOS = monitorUserPortfolioService.listUserPortfolio(form.getPortfolioName());
        if (CollectionUtils.isEmpty(userPortfolioListTOS)) {
            return JsonResultList.buildSuccess(new ArrayList<>());
        }
        List<String> portfolioIds = userPortfolioListTOS.stream().map(UserPortfolioListTO::getId).collect(Collectors.toList());
        form.setPortfolioIds(portfolioIds);
        form.setPage(PageReq.build4List());
        JsonResultList<PortfolioStatisticsTO> jsonResultList = MonitorEventInterface.pagePortfolioStatistics(form);
        List<PortfolioStatisticsTO> resultList = jsonResultList.getResultList();
        Map<String, PortfolioStatisticsTO> portfolioId2StatisticsMap = MappingUtils.getMap(resultList, PortfolioStatisticsTO::getPortfolioId, Function.identity());
        List<PortfolioStatisticsTO> pageList = userPortfolioListTOS.stream().map(userPortfolioListTO -> {
            PortfolioStatisticsTO portfolioStatisticsTO = new PortfolioStatisticsTO();
            String portfolioId = userPortfolioListTO.getId();
            portfolioStatisticsTO.setPortfolioId(portfolioId);
            portfolioStatisticsTO.setPortfolioName(userPortfolioListTO.getName());
            portfolioStatisticsTO.setCreateDate(userPortfolioListTO.getCreateDate());
            portfolioStatisticsTO.setPortfolioEntityCount(userPortfolioListTO.getEntityCount());
            PortfolioStatisticsTO portfolioStatisticsTOAPI = portfolioId2StatisticsMap.get(portfolioId);
            portfolioStatisticsTO.setEntityCount(0);
            portfolioStatisticsTO.setEventCount(0);
            portfolioStatisticsTO.setNeutralCount(0);
            portfolioStatisticsTO.setPositiveCount(0);
            portfolioStatisticsTO.setNegativeCount(0);
            portfolioStatisticsTO.setNoticeCount(0);
            portfolioStatisticsTO.setWarningCount(0);
            portfolioStatisticsTO.setRiskCount(0);
            portfolioStatisticsTO.setHighRiskCount(0);
            if (portfolioStatisticsTOAPI != null) {
                portfolioStatisticsTO.setEntityCount(portfolioStatisticsTOAPI.getEntityCount());
                portfolioStatisticsTO.setEventCount(portfolioStatisticsTOAPI.getEventCount());
                portfolioStatisticsTO.setNeutralCount(portfolioStatisticsTOAPI.getNeutralCount());
                portfolioStatisticsTO.setPositiveCount(portfolioStatisticsTOAPI.getPositiveCount());
                portfolioStatisticsTO.setNegativeCount(portfolioStatisticsTOAPI.getNegativeCount());
                portfolioStatisticsTO.setNoticeCount(portfolioStatisticsTOAPI.getNoticeCount());
                portfolioStatisticsTO.setWarningCount(portfolioStatisticsTOAPI.getWarningCount());
                portfolioStatisticsTO.setRiskCount(portfolioStatisticsTOAPI.getRiskCount());
                portfolioStatisticsTO.setHighRiskCount(portfolioStatisticsTOAPI.getHighRiskCount());
                portfolioStatisticsTO.setEventLastUpdatedOn(portfolioStatisticsTOAPI.getEventLastUpdatedOn());
            }
            return portfolioStatisticsTO;
        }).collect(Collectors.toList());
        // 排序处理
        sort4PortfolioStatistics(originalPage, pageList);

        // 分页处理
        pageList = pageList.stream().skip((long) (originalPage.getPageNo() - 1) * originalPage.getPageSize())
                .limit(originalPage.getPageSize()).collect(Collectors.toList());
        return JsonResultList.buildSuccess(pageList, userPortfolioListTOS.size());
    }

    private static void sort4PortfolioStatistics(PageReq originalPage, List<PortfolioStatisticsTO> pageList) {
        PageReq.Order order = null;
        if (CollectionUtils.isNotEmpty(originalPage.getOrders())) {
            order = originalPage.getOrders().get(0);
        }
        if (order == null) {
            CollectionUtils.sort(pageList, PortfolioStatisticsTO::getCreateDate, false);
        } else {
            String property = order.getProperty();
            boolean ascending = order.isAscending();
            switch (property) {
                case "PortfolioEntityCount":
                    CollectionUtils.sort(pageList, PortfolioStatisticsTO::getPortfolioEntityCount, ascending);
                    break;
                case "EntityCount":
                    CollectionUtils.sort(pageList, PortfolioStatisticsTO::getEntityCount, ascending);
                    break;
                case "EventCount":
                    CollectionUtils.sort(pageList, PortfolioStatisticsTO::getEventCount, ascending);
                    break;
                case "EventLastUpdatedOn":
                    CollectionUtils.sort(pageList, PortfolioStatisticsTO::getEventLastUpdatedOn, ascending);
                    break;
                case "CreateDate":
                    CollectionUtils.sort(pageList, PortfolioStatisticsTO::getCreateDate, ascending);
                    break;
                case "PortfolioName":
                    CollectionUtils.sort(pageList, PortfolioStatisticsTO::getPortfolioName, ascending, true);
                    break;
                default:
                    logger.error("不支持的Property: " + property);
                    break;
            }
        }
    }

    public JsonResultList<EntityStatisticsTO> pageEntityStatistics(EntityStatisticsForm form) throws MessageException {
        form.setCompanyId(UserUtils.getUserCompanyId());
        form.setUserId(UserUtils.getUserId());
        return MonitorEventInterface.pageEntityStatistics(form);
    }

    public void refreshStatistics() {
        EventBaseForm form = new EventBaseForm();
        form.setCompanyId(UserUtils.getUserCompanyId());
        form.setUserId(UserUtils.getUserId());
        MonitorEventInterface.refreshStatistics(form);
    }

    public Boolean getStatisticsRefreshStatus() {
        EventBaseForm form = new EventBaseForm();
        form.setCompanyId(UserUtils.getUserCompanyId());
        form.setUserId(UserUtils.getUserId());
        return MonitorEventInterface.getStatisticsRefreshStatus(form);
    }

    public StatisticsLastRefreshTimeTO getStatisticsLastRefreshTime(StatisticsLastRefreshTimeForm form) {
        StatisticsLastRefreshTimeTO to = new StatisticsLastRefreshTimeTO();
        String userId = UserUtils.getUserId();
        int monitorEntityCount = monitorEntityCompanyUserService.monitorEntityCount(userId, null, StringUtils.isNotBlank(form.getPortfolioId()) ? Lists.newArrayList(form.getPortfolioId()) : null);
        to.setEntityCount(monitorEntityCount);
        EventBaseForm eventBaseForm = new EventBaseForm();
        eventBaseForm.setCompanyId(UserUtils.getUserCompanyId());
        eventBaseForm.setUserId(userId);
        Date statisticsLastRefreshTime = MonitorEventInterface.getStatisticsLastRefreshTime(eventBaseForm);
        to.setLastRefreshTime(statisticsLastRefreshTime);
        return to;
    }

    public void monitorChangeEventNotify(List<User> userList, Date beginDate, Date endDate) {
        if (CollectionUtils.isEmpty(userList) || beginDate == null || endDate == null) return;
        Map<String, List<User>> companyUserMap = userList.stream().collect(Collectors.groupingBy(User::getCompanyId));
        for (Map.Entry<String, List<User>> entry : companyUserMap.entrySet()) {
            String companyId = entry.getKey();
            List<User> userInfoList = entry.getValue();
            try {
                if (checkMonitorPermissionByCompanyId(companyId, BenefitTypeEnum.getMonitorBenefitTypeList())) {
                    for (User user : userInfoList) {
                        patchChangeEventsNotify(user, beginDate, endDate);
                    }
                } else {
                    logger.info("monitor permission not found, userCompanyId:{}", companyId);
                }
            } catch (Exception e) {
                logger.error("push monitor change events error, userCompanyId:{}", companyId, e);
            }
        }
    }

    private void patchChangeEventsNotify(User user, Date beginDate, Date endDate) {
        try {
            long startTime = System.currentTimeMillis();
            logger.info("push change events to users to start:{}", user.getId());
            List<PortfolioEntityCountTO> portfolioEntityCountTOS = userMonitorPortfolioService.listEffectivePortfolioInfo(user.getId());

            // 合并过滤 并分组操作 取存在监控企业的分组
            Map<String, PortfolioEntityCountTO> portfolioId2CountMap = MappingUtils.getMap(portfolioEntityCountTOS, PortfolioEntityCountTO::getPortfolioId, Function.identity());
            if (portfolioId2CountMap.isEmpty()) {
                return;
            }
            List<String> portfolioIdList = new ArrayList<>(portfolioId2CountMap.keySet());
            // 获取监控和维度、情感标签数据（任一为空则退出）
            Map<String, List<Integer>> portfolioId2RiskLevelsMap = getPortfolioId2RiskLevelsMap(user.getId(), portfolioIdList);
            Map<String, List<String>> portfolioId2TagsMap = getPortfolioId2TagsMap(user.getId(), portfolioIdList);
            if (MapUtils.isEmpty(portfolioId2RiskLevelsMap) || MapUtils.isEmpty(portfolioId2TagsMap)) {
                return;
            }
            // 交集处理保证数据一致性
            Collection<String> commonPortfolioIds = CollectionUtils.intersection2(portfolioId2RiskLevelsMap.keySet(), portfolioId2TagsMap.keySet());
            if (CollectionUtils.isEmpty(commonPortfolioIds)) {
                return;
            }
            List<PortfolioStatisticsRealtimeTO> portfolioStatisticsTOs = getPortfolioStatistics(user, beginDate, endDate, new HashSet<>(commonPortfolioIds),
                    portfolioId2TagsMap, portfolioId2RiskLevelsMap, portfolioId2CountMap);
            if (CollectionUtils.isEmpty(portfolioStatisticsTOs)) {
                return;
            }
            // 计算事件总数量
            int eventCount = portfolioStatisticsTOs.stream()
                    .map(PortfolioStatisticsRealtimeTO::getEventCount)
                    .filter(Objects::nonNull)
                    .reduce(Integer::sum)
                    .orElse(0);
            if (eventCount == 0) {
                return;
            }
            sendNotificationEmail(user, portfolioStatisticsTOs, portfolioId2TagsMap, portfolioId2RiskLevelsMap, beginDate, endDate);
            logger.info("push change events to users to end:{}, took {} ms", user.getId(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            logger.error("PushChangeEventsNotifyJob processTask error, userId:{}", user.getId(), e);
        }
    }

    // 提取查询用户监控的情感配置逻辑
    private Map<String, List<Integer>> getPortfolioId2RiskLevelsMap(String userId, List<String> portfolioIdList) {
        List<TblMonitorUserPortfolioNotifySignal> userPortfolioSignalList = portfolioNotifySignalService.listPortfolioSignalByUserId(userId, portfolioIdList);
        return MappingUtils.groupByKeyAndMapValues(userPortfolioSignalList,
                TblMonitorUserPortfolioNotifySignal::getPortfolioId,
                e -> Integer.parseInt(e.getSignalLevel()));
    }

    // 提取查询用户监控的维度配置逻辑
    private Map<String, List<String>> getPortfolioId2TagsMap(String userId, List<String> portfolioIdList) {
        List<TblMonitorUserPortfolioNotifySetting> userPortfolioNotifySettingList = providerNotifySettingService.listPortfolioNotifyCategory(userId, portfolioIdList);
        return MappingUtils.groupByKeyAndMapValues(userPortfolioNotifySettingList,
                TblMonitorUserPortfolioNotifySetting::getPortfolioId,
                TblMonitorUserPortfolioNotifySetting::getMonitorDimensionCode);
    }

    // 提取处理企业变动事件的逻辑
    private List<PortfolioStatisticsRealtimeTO> getPortfolioStatistics(User user,
                                                                       Date beginDate,
                                                                       Date endDate,
                                                                       Set<String> portfolioIds,
                                                                       Map<String, List<String>> portfolioId2TagsMap,
                                                                       Map<String, List<Integer>> portfolioId2RiskLevelsMap,
                                                                       Map<String, PortfolioEntityCountTO> portfolioId2Map) {
        return portfolioIds.stream()
                .map(portfolioId -> {
                    try {
                        PortfolioEntityCountTO portfolioEntityCountTO = portfolioId2Map.get(portfolioId);
                        if (portfolioEntityCountTO == null) {
                            return null;
                        }
                        String portfolioName = portfolioEntityCountTO.getPortfolioName();
                        List<String> tags = portfolioId2TagsMap.get(portfolioId);
                        List<Integer> riskLevels = portfolioId2RiskLevelsMap.get(portfolioId);
                        if (CollectionUtils.isEmpty(tags) || CollectionUtils.isEmpty(riskLevels)) {
                            logger.warn("Portfolio configuration missing required parameters, portfolioId:{}", portfolioId);
                            return null;
                        }
                        EventStatisticsRealtimeForm realtimeForm = new EventStatisticsRealtimeForm();
                        realtimeForm.setCompanyId(user.getCompanyId());
                        realtimeForm.setUserId(user.getId());
                        realtimeForm.setEventBeginTime(beginDate);
                        realtimeForm.setEventEndTime(endDate);
                        realtimeForm.setPortfolioId(portfolioId);
                        realtimeForm.setTags(tags);
                        realtimeForm.setRiskLevels(riskLevels);
                        PortfolioStatisticsRealtimeTO realtimeTO = MonitorEventInterface.getEventStatisticsRealTime(realtimeForm);
                        if (realtimeTO != null) {
                            realtimeTO.setPortfolioId(portfolioId);
                            realtimeTO.setPortfolioName(portfolioName);
                        }
                        return realtimeTO;
                    } catch (Exception e) {
                        logger.error("Process portfolio statistics failed, portfolioId:{}", portfolioId, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // 提取发送通知邮件的逻辑
    private void sendNotificationEmail(User user,
                                       List<PortfolioStatisticsRealtimeTO> portfolioStatisticsTOs,
                                       Map<String, List<String>> portfolioId2TagsMap,
                                       Map<String, List<Integer>> portfolioId2RiskLevelsMap,
                                       Date beginDate,
                                       Date endDate) throws MessageException {
        // 计算事件总数量并获取前10条记录
        long eventCount = portfolioStatisticsTOs.stream()
                .filter(Objects::nonNull)
                .mapToLong(PortfolioStatisticsRealtimeTO::getEventCount)
                .sum();
        List<PortfolioStatisticsRealtimeTO> top10PortfolioStatistics = portfolioStatisticsTOs.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(PortfolioStatisticsRealtimeTO::getEventCount).reversed())
                .limit(10)
                .collect(Collectors.toList());

        // 获取邮件内容类型配置
        SysUserExt emailContentTypeConfig = sysUserExtService.getByUserIdAndType(user.getId(), Constants.UserExtType.MONITOR_NOTIFY_EMAIL_CONTENT);
        if (emailContentTypeConfig == null) {
            logger.warn("用户：{}，监控通知邮件内容类型未找到，请检查配置", user.getId());
            return;
        }

        // 获取邮件模板
        SysTemplate emailTemplate = commSysTemplateService.getByTemplateName("u_monitor_companies_notify");
        if (emailTemplate == null) {
            logger.warn("监控通知模板未找到，请检查配置");
            return;
        }

        // 准备模板参数
        String subject = String.format("【QCC MONITOR】【%s】New Updates on Monitored Companies", eventCount);
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("top10PortfolioStatistics", top10PortfolioStatistics);

        // 根据内容类型处理不同的邮件内容
        String contentType = emailContentTypeConfig.getValue();
        if ("Basic".equals(contentType)) {
            templateParams.put("top10PortfolioStatistics", null);
            templateParams.put("contentType", contentType);
        } else if ("Detail".equals(contentType)) {
            List<MonitorEventListTO> recentEvents = fetchRecentEvents(user, top10PortfolioStatistics, portfolioId2TagsMap, portfolioId2RiskLevelsMap, beginDate, endDate, 5);
            templateParams.put("changeEventList", recentEvents);
        }

        // 解析模板
        String emailContent = FreemarkerUtils.parseTemplate(emailTemplate.getId(), emailTemplate.getTemplateContent(), templateParams);

        // 获取通知邮件配置
        SysUserExt notifyEmailConfig = sysUserExtService.getByUserIdAndType(user.getId(), Constants.UserExtType.MONITOR_NOTIFY_EMAIL);

        // 根据配置发送邮件
        if (notifyEmailConfig == null) {
            qccMailSenderService.send(user.getLoginName(), subject, emailContent);
        } else {
            List<String> ccAddresses = MonitorNotifySettingTO.parseEmailList(notifyEmailConfig.getValue()).stream()
                    .filter(item -> Constants.YES.equals(item.getChecked()))
                    .map(MonitorNotifySettingTO.NotifyEmail::getEmail)
                    .distinct()
                    .collect(Collectors.toList());
            qccMailSenderService.send(user.getLoginName(), ccAddresses, subject, emailContent);
        }
    }

    /**
     * 根据分组统计结果，查询限定数量的事件信息
     *
     * @param user
     * @param top10PortfolioStatistics
     * @param portfolioId2TagsMap
     * @param portfolioId2RiskLevelsMap
     * @param beginDate
     * @param endDate
     * @param topRecordsSize
     * @return
     * @throws MessageException
     */
    private List<MonitorEventListTO> fetchRecentEvents(User user,
                                                       List<PortfolioStatisticsRealtimeTO> top10PortfolioStatistics,
                                                       Map<String, List<String>> portfolioId2TagsMap,
                                                       Map<String, List<Integer>> portfolioId2RiskLevelsMap,
                                                       Date beginDate,
                                                       Date endDate,
                                                       int topRecordsSize) throws MessageException {
        List<MonitorEventListTO> recentEvents = new ArrayList<>();
        for (PortfolioStatisticsRealtimeTO portfolioStatisticsTO : top10PortfolioStatistics) {
            String portfolioId = portfolioStatisticsTO.getPortfolioId();
            // 构建查询条件
            ListEventForm queryForm = new ListEventForm();
            queryForm.setCompanyId(user.getCompanyId());
            queryForm.setUserId(user.getId());
            queryForm.setPortfolioIds(Collections.singletonList(portfolioId));
            queryForm.setTags(portfolioId2TagsMap.get(portfolioId));
            queryForm.setRiskLevels(portfolioId2RiskLevelsMap.get(portfolioId));
            queryForm.setEventBeginTime(beginDate);
            queryForm.setEventEndTime(endDate);
            queryForm.setPage(new PageReq(1, topRecordsSize));
            // 查询事件
            JsonResultList<MonitorEventListTO> eventResult = this.pageEvent(queryForm);
            recentEvents.addAll(eventResult.getResultList());
            // 达到额定条数事件后停止查询
            if (recentEvents.size() >= topRecordsSize) {
                break;
            }
        }
        // 按事件时间排序并取前X条额定数量数据
        CollectionUtils.sort(recentEvents, MonitorEventListTO::getCreateDate, false);
        return recentEvents.stream().limit(topRecordsSize).collect(Collectors.toList());
    }
}
