package com.backend.common.modules.delivery.model;

import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

public class StatementPushDetailTO {
    private String contractNo;
    private Integer statementMonth;
    private Date issueDate;
    private String invoiceNo;
    private BigDecimal amountUsed;
    private BigDecimal totalAmountDue;

    public static StatementPushDetailTO build(String contractNo, TblCompStatementMonthly statementMonthly) {
        StatementPushDetailTO statementPushDetailTO = new StatementPushDetailTO();
        BeanUtils.copyProperties(statementMonthly, statementPushDetailTO);
        statementPushDetailTO.setContractNo(contractNo);
        return statementPushDetailTO;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public Integer getStatementMonth() {
        return statementMonth;
    }

    public void setStatementMonth(Integer statementMonth) {
        this.statementMonth = statementMonth;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public BigDecimal getAmountUsed() {
        return amountUsed;
    }

    public void setAmountUsed(BigDecimal amountUsed) {
        this.amountUsed = amountUsed;
    }

    public BigDecimal getTotalAmountDue() {
        return totalAmountDue;
    }

    public void setTotalAmountDue(BigDecimal totalAmountDue) {
        this.totalAmountDue = totalAmountDue;
    }
}
