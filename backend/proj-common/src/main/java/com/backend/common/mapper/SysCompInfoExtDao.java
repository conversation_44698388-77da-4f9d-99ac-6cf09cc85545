package com.backend.common.mapper;

import com.backend.common.entity.CompInfoExt;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface SysCompInfoExtDao extends CrudDao<CompInfoExt> {

	List<String> retrieveCompanyIdByType(@Param("type")String type);
	List<String> retrieveCompanyIdByTypeAndValue(@Param("type")String type, @Param("value")String value);
	List<String> retrieveInfoByCompanyIdAndType(@Param("companyId")String companyId, @Param("type")String type);
	public void physicallyDeleteByCompanyId(@Param("company_id") String companyId);
	List<CompInfoExt> retrieveCompInfoExtInfo(@Param("companyId")String companyId, @Param("type")String type);

	
	//执行逻辑删除
	public void logicDeleteByCompanyIdAndType(@Param("company_id")String companyId, @Param("type")String type);

	// added for v2.0.7 chenbl KNZT-6595
	void deleteValue(@Param("companyId") String companyId,
					 @Param("type") String type,
					 @Param("value") String value,
					 @Param("updateBy") String updateBy);
}
