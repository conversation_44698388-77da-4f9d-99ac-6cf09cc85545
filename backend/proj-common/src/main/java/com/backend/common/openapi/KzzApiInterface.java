package com.backend.common.openapi;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.openapi.model.KzzContractDetailTO;
import com.backend.common.openapi.model.KzzContractPaymentSheet;
import com.backend.common.openapi.model.KzzWorkFlowDetailTO;
import com.backend.common.openapi.model.KzzBenefitWorkFlowDetailTO;
import com.google.common.collect.Lists;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.HttpUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * added for v1.8.8 KNZT-3324
 *
 * <AUTHOR>
 * @datetime 2024/6/26 18:01
 */
public class KzzApiInterface {
    protected static Logger logger = LoggerFactory.getLogger(KzzApiInterface.class);


    public static KzzContractDetailTO getContractDetail(String contractNo) throws MessageException {
        if (StringUtils.isBlank(contractNo)) {
            return null;
        }
        String respStr = get("/openapi/kzz/contract/v2/code/" + contractNo, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject contractJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(contractJsonObj)) {
                    continue;
                }
                KzzContractDetailTO contract = contractJsonObj.toJavaObject(KzzContractDetailTO.class);

                JSONObject customerJsonObj = contractJsonObj.getJSONObject("customer");
                if (Objects.nonNull(customerJsonObj)) {
                    String customerName = customerJsonObj.getString("name");
                    contract.setCustomerName(customerName);
                }
                JSONObject currencyEsModelJsonObj = contractJsonObj.getJSONObject("currencyEsModel");
                if (Objects.nonNull(currencyEsModelJsonObj)) {
                    String abbreviation = currencyEsModelJsonObj.getString("abbreviation");
                    contract.setAbbreviation(abbreviation);
                    String currencyId = currencyEsModelJsonObj.getString("currencyId");
                    if (StringUtils.isNotBlank(currencyId)) {
                        contract.setCurrencyId(Long.parseLong(currencyId));
                    }
                }
                JSONObject quotationJsonObj = contractJsonObj.getJSONObject("quotation");
                if (Objects.nonNull(quotationJsonObj)) {
                    String quotationCode = quotationJsonObj.getString("code");
                    contract.setQuotationCode(quotationCode);
                }

                List<KzzContractDetailTO.ContractProduct> contractProducts = Lists.newArrayList();
                JSONArray contractProductJsonArray = contractJsonObj.getJSONArray("contractProducts");
                if (Objects.nonNull(contractProductJsonArray)) {
                    for (int j = 0; j < contractProductJsonArray.size(); j++) {
                        JSONObject contractProductJsonObj = contractProductJsonArray.getJSONObject(j);
                        if (Objects.nonNull(contractProductJsonObj)) {
                            KzzContractDetailTO.ContractProduct contractProduct = contractProductJsonObj.toJavaObject(KzzContractDetailTO.ContractProduct.class);
                            JSONObject productJsonObj = contractProductJsonObj.getJSONObject("product");
                            if (Objects.nonNull(productJsonObj)) {
                                contractProduct.setProductCode(productJsonObj.getString("code"));
                                contractProduct.setProductName(productJsonObj.getString("name"));
                            }
                            contractProducts.add(contractProduct);
                        }
                    }
                }
                contract.setContractProducts(contractProducts);

                JSONObject customShowFieldsJsonObj = contractJsonObj.getJSONObject("customShowFields");
                if (Objects.nonNull(customShowFieldsJsonObj)) {
                    JSONArray payTypeArray = customShowFieldsJsonObj.getJSONArray("CFLD2023033100004");
                    if (Objects.nonNull(payTypeArray) && !payTypeArray.isEmpty()) {
                        contract.setPayType(payTypeArray.getString(0));
                    }
                    int serviceYear = customShowFieldsJsonObj.getIntValue("CFLD2023042500004");
                    contract.setServiceYear(serviceYear);
                    JSONArray invoiceMethodArray = customShowFieldsJsonObj.getJSONArray("CFLD2023033100006");
                    if (Objects.nonNull(invoiceMethodArray) && !invoiceMethodArray.isEmpty()) {
                        contract.setInvoiceMethod(invoiceMethodArray.getString(0));
                    }
                    JSONArray signingPartyArray = customShowFieldsJsonObj.getJSONArray("CFLD202208300069");
                    if (Objects.nonNull(signingPartyArray) && !signingPartyArray.isEmpty()) {
                        contract.setSigningParty(signingPartyArray.getString(0));
                    }
                    String invoiceName = customShowFieldsJsonObj.getString("CFLD202208300090");
                    if (StringUtils.isNotBlank(invoiceName)) {
                        contract.setInvoiceName(invoiceName);
                    }
                    JSONArray statementTypeArray = customShowFieldsJsonObj.getJSONArray("CFLD2023033100002");
                    if (Objects.nonNull(statementTypeArray) && !statementTypeArray.isEmpty()) {
                        contract.setStatementType(statementTypeArray.getString(0));
                    }
                }
                // added for fengsw v2.2.7 KNZT-7687 获取合同的客户负责人id
                JSONArray userCustomerArray = contractJsonObj.getJSONArray("userCustomers");
                if (Objects.nonNull(userCustomerArray)) {
                    for (int j = 0; j < userCustomerArray.size(); j++) {
                        JSONObject jsonObject = userCustomerArray.getJSONObject(j);
                        if (Objects.nonNull(jsonObject)) {
                            int roleId = jsonObject.getIntValue("roleId");
                            if (roleId == 4) {
                                contract.setCustomerUserId(jsonObject.getLong("userId"));
                                break;
                            }
                        }
                    }
                }

                return contract;
            }
        }
        return null;
    }

    public static KzzWorkFlowDetailTO getWorkFlowDetail(String workFlowNo) throws MessageException {
        String respStr = get("/openapi/kzz/workflow/code/" + workFlowNo, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject workFlowJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(workFlowJsonObj)) {
                    continue;
                }
                String rootRouteBaseId = workFlowJsonObj.getString("rootRouteBaseId");
                if (!StringUtils.equalsAny(rootRouteBaseId, "3466", "4673")) {
                    logger.info("工单类型不符合要求, workFlowNo:{}, rootRouteBaseId:{}", workFlowNo, rootRouteBaseId);
                    continue;
                }
                KzzWorkFlowDetailTO workFlow = workFlowJsonObj.toJavaObject(KzzWorkFlowDetailTO.class);

                JSONObject contractJsonObj = workFlowJsonObj.getJSONObject("contract");
                if (Objects.nonNull(contractJsonObj)) {
                    String contractNo = contractJsonObj.getString("code");
                    workFlow.setContractNo(contractNo);
                }

                JSONObject createUserJsonObj = workFlowJsonObj.getJSONObject("createUser");
                if (Objects.nonNull(createUserJsonObj)) {
                    String name = createUserJsonObj.getString("name");
                    workFlow.setSubmitName(name);
                }
                JSONArray customFieldValues = workFlowJsonObj.getJSONArray("customFieldValues");
                for (Object customFieldValue : customFieldValues) {
                    JSONObject customShowFieldsJsonObj = (JSONObject) customFieldValue;
                    if("CFLD2024070100023".equals(customShowFieldsJsonObj.getString("kzzFieldKey"))){
                        Date date = customShowFieldsJsonObj.getDate("dateValue");
                        if (date != null) {
                            workFlow.setBeginDate(date);
                        }
                    }
                    if("CFLD2024070100025".equals(customShowFieldsJsonObj.getString("kzzFieldKey"))){
                        Date date = customShowFieldsJsonObj.getDate("dateValue");
                        if (date != null) {
                            workFlow.setEndDate(date);
                        }
                    }
                }
               return workFlow;
            }
        }
        return null;
    }

    /**
     * 获取回款单数据
     * 
     * @param paymentSheetCode
     * @return
     * @throws MessageException
     */
    public static KzzContractPaymentSheet getPaymentSheet(String paymentSheetCode) throws MessageException {
        String respStr = get("/openapi/kzz/payment/sheet/v2/code/" + paymentSheetCode, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject paymentSheetJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(paymentSheetJsonObj)) {
                    continue;
                }
                return paymentSheetJsonObj.toJavaObject(KzzContractPaymentSheet.class);
            }
        }
        return null;
    }
    
    public static KzzContractDetailTO getContractDetailByWorkFlowNo(String workFlowNo) throws MessageException {
        KzzWorkFlowDetailTO workFlowDetail = KzzApiInterface.getWorkFlowDetail(workFlowNo);
        if (Objects.nonNull(workFlowDetail) && workFlowDetail.isPass()) {
            return KzzApiInterface.getContractDetail(workFlowDetail.getContractNo());
        }
        return null;
    }


    public static String get(String url, Map<String, String> paramMap) throws MessageException {
        try {
            String accessKey = Global.getConfig("kzz.api.access.key");
            String secretKey = Global.getConfig("kzz.api.secret.key");
            Map<String, String> headerMap = new HashMap<>();
            long timestamp = System.currentTimeMillis();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String sign = Base64.encodeBase64String(sha256_HMAC.doFinal((url + "\n" + "GET" + "\n" + timestamp).getBytes()));// 重点
            headerMap.put("x-kzz-sign", sign);
            headerMap.put("x-kzz-timestamp", String.valueOf(timestamp));
            headerMap.put("x-kzz-access-key", accessKey);
            logger.info("call kzz url:{}, param:{}", url, JSONUtils.toJSONString(paramMap));
            String config = Global.getConfig("kzz.api.domain");
            String resp = HttpUtils.get(config + url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_300000, headerMap);
            logger.info("call kzz url:{}, resp:{}", url, resp);
            return resp;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            logger.error("KzzApiInterface.get error", e);
        }
        return null;
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 推送客找找创建合同信息
     * 
     * @param url
     * @param contractDetailStr
     * @return
     * @throws MessageException
     */
    public static String createContract(String url, String contractDetailStr) throws MessageException {
        try {
            String accessKey = Global.getConfig("kzz.api.access.key");
            String secretKey = Global.getConfig("kzz.api.secret.key");
            Map<String, String> headerMap = new HashMap<>();
            long timestamp = System.currentTimeMillis();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String sign = Base64.encodeBase64String(sha256_HMAC.doFinal((url + "\n" + "POST" + "\n" + timestamp).getBytes()));
            headerMap.put("x-kzz-sign", sign);
            headerMap.put("x-kzz-timestamp", String.valueOf(timestamp));
            headerMap.put("x-kzz-access-key", accessKey);
            headerMap.put("content-type", "application/json");
            logger.info("call kzz url:{}, param:{}", url, contractDetailStr);
            String config = Global.getConfig("kzz.api.domain");
            String resp = HttpUtils.postJson(config + url, contractDetailStr, headerMap, HttpUtils.HTTP_CLIENT_TIMEOUT_65000);
            logger.info("call kzz url:{}, resp:{}", url, resp);
            return resp;
        } catch (Exception e) {
            logger.error("KzzApiInterface create contract error", e);
            throw new MessageException("KzzApiInterface create contract error", e.getMessage());
        }
    }

    /**
     * 获取额度划扣工单详情
     * @param workFlowNo 工单编号
     * @return KzzBenefitWorkFlowDetailTO 额度划扣工单详情
     * @throws MessageException 消息异常
     */
    public static KzzBenefitWorkFlowDetailTO getBenefitWorkFlowDetail(String workFlowNo) throws MessageException {
        String respStr = get("/openapi/kzz/workflow/code/" + workFlowNo, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject workFlowJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(workFlowJsonObj)) {
                    continue;
                }
                
                KzzBenefitWorkFlowDetailTO benefitWorkFlow = workFlowJsonObj.toJavaObject(KzzBenefitWorkFlowDetailTO.class);

                // 设置关联合同号
                JSONObject contractJsonObj = workFlowJsonObj.getJSONObject("contract");
                if (Objects.nonNull(contractJsonObj)) {
                    String contractNo = contractJsonObj.getString("code");
                    benefitWorkFlow.setContractNo(contractNo);
                }
                
                // 解析自定义字段值
                JSONArray customFieldValues = workFlowJsonObj.getJSONArray("customFieldValues");
                if (Objects.nonNull(customFieldValues)) {
                    for (Object customFieldValue : customFieldValues) {
                        JSONObject customFieldJsonObj = (JSONObject) customFieldValue;
                        String fieldKey = customFieldJsonObj.getString("kzzFieldKey");
                        
                        // 解析credits
                        if ("CFLD2025050700007".equals(fieldKey) || "CFLD2025051900005".equals(fieldKey)) {
                            Integer initialCredits = customFieldJsonObj.getInteger("numberValue");
                            if (initialCredits != null) {
                                benefitWorkFlow.setConsumedCredits(initialCredits);
                            }
                        }
                        
                        // 解析交付产品(产品编号)
                        if ("CFLD2025050700005".equals(fieldKey) || "CFLD2025051900003".equals(fieldKey)) {
                            String productCode = customFieldJsonObj.getString("strValue");
                            if (productCode != null) {
                                benefitWorkFlow.setProductCode(productCode);
                            }
                        }
                        
                        // 解析开始时间
                        if ("CFLD2025050700002".equals(fieldKey) || "CFLD2025051900002".equals(fieldKey)) {
                            Date beginDate = customFieldJsonObj.getDate("dateValue");
                            if (beginDate != null) {
                                benefitWorkFlow.setBeginDate(beginDate);
                            }
                        }
                        
                        // 解析交付数量
                        if ("CFLD2025050700009".equals(fieldKey) || "CFLD2025051900007".equals(fieldKey)) {
                            Integer deliveryQuantity = customFieldJsonObj.getInteger("numberValue");
                            if (deliveryQuantity != null) {
                                benefitWorkFlow.setDeliveryQuantity(deliveryQuantity);
                            }
                        }
                    }
                }
                
                return benefitWorkFlow;
            }
        }
        return null;
    }
}