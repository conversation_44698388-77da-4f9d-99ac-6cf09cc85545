package com.backend.common.modules.delivery.model;

import java.math.BigDecimal;

/**
 * added for v1.9.5 KNZT-4117
 * <AUTHOR>
 * @datetime 2024/8/30 10:09
 */
public class TransactionOrderUserConsumeTO {
    private String userId;
    private String loginName;
    private BigDecimal credits;
    private BigDecimal amount;

    public TransactionOrderUserConsumeTO(String loginName, BigDecimal credits, BigDecimal amount) {
        this.loginName = loginName;
        this.credits = credits;
        this.amount = amount;
    }

    public TransactionOrderUserConsumeTO() {

    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public BigDecimal getCredits() {
        return credits;
    }

    public void setCredits(BigDecimal credits) {
        this.credits = credits;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
