package com.backend.common.modules.report.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.global.gateway.map.DataMapInterface;
import com.backend.common.modules.benefit.entity.TblBenefitPool;
import com.backend.common.modules.benefit.model.PaymentStrategyTO;
import com.backend.common.modules.benefit.service.CommTblBenefitDeliveryService;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolChangeRecordService;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolService;
import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommTblCompOpIpLogService;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.industry.entity.TblGlobalIndustry;
import com.backend.common.modules.industry.service.CommTblGlobalIndustryService;
import com.backend.common.modules.person_vrfy.form.EncryptedForm;
import com.backend.common.modules.report.entity.TblCompReportCart;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblCompReportOrderHkDoc;
import com.backend.common.modules.report.entity.TblCompReportOrderMap;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.form.BatchScanOrderSubmitForm;
import com.backend.common.modules.report.form.MapReportOrderSubmitForm;
import com.backend.common.modules.report.form.ReportOrderSubmitForm;
import com.backend.common.modules.report.form.TblCompReportOrderDuplicateForm;
import com.backend.common.modules.report.model.HongKongAnnouncementTO;
import com.backend.common.modules.report.model.InfoBeforeSubmitV2TO;
import com.backend.common.modules.report.model.OrderDistributionResultTO;
import com.backend.common.modules.report.model.OrderDuplicateCheckResultTO;
import com.backend.common.modules.report.model.OrderGenerateTimeTO;
import com.backend.common.modules.report.model.OrderSubmitHkDocParamTO;
import com.backend.common.modules.report.model.OrderSubmitTO;
import com.backend.common.modules.report.model.QccOvsBasicInfoCommTO;
import com.backend.common.modules.report.model.ReportOrderInfoOfBillTO;
import com.backend.common.modules.report.model.order.OrderSubmitCheckBusinessTO;
import com.backend.common.modules.report.model.order.OrderSubmitCheckErrorTO;
import com.backend.common.modules.report.model.order.OrderSubmitResult;
import com.backend.common.modules.report.model.sg.SgBasicTO;
import com.backend.common.modules.report.service.report.order.custom.SgReportOrderCustomService;
import com.backend.common.modules.rule.model.UserAccessScopeTO;
import com.backend.common.modules.rule.service.RuleBusinessService;
import com.backend.common.modules.scan.entity.TblCompReportOrderScan;
import com.backend.common.modules.scan.form.ScanOrderSubmitForm;
import com.backend.common.modules.scan.service.CommTblCompReportOrderScanService;
import com.backend.common.modules.scan.service.CommTblCompScanListRuleService;
import com.backend.common.modules.search.HkIrdService;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.modules.setting.model.CompChargeUnitV2TO;
import com.backend.common.modules.setting.model.CompChargeUnitWithGroupTO;
import com.backend.common.mq.OverseaKafkaService;
import com.backend.common.overseamongo.entity.QccOvsBasic;
import com.backend.common.overseamongo.entity.QccOvsBasicInfo;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.backend.common.stripeapi.StripePaymentInterface;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.ECILocalInterface;
import com.backend.common.yunjuapi.GlobalCompanyDetailsInterface;
import com.backend.common.yunjuapi.IntranetInterface;
import com.backend.common.yunjuapi.QccEntDetailInfoTO;
import com.backend.common.yunjuapi.SgCompanyDetailsInterface;
import com.backend.common.yunjuapi.model.ApiGlobalCorpDetailTO;
import com.backend.common.yunjuapi.model.HkCorpBasicDetailTO;
import com.backend.common.yunjuapi.model.IndustryTO;
import com.backend.common.yunjuapi.model.OverseaMaintainStatusTO;
import com.backend.common.yunjuapi.model.PersonDetailInfoTO;
import com.backend.common.yunjuapi.model.QccOvsBasicIndustryInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.CommDelayedTaskTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.GenderEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.HkCompanyTypeEnum;
import com.qcc.frame.commons.ienum.OrderHkDocRelTypeEnum;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.PayTypeEnum;
import com.qcc.frame.commons.ienum.QccEntCodeTypeEnum;
import com.qcc.frame.commons.ienum.ReportGroupMappingEnum;
import com.qcc.frame.commons.ienum.ReportPurchaseGroupEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.commons.ienum.ScanListEnum;
import com.qcc.frame.commons.ienum.UnitGroupEnum;
import com.qcc.frame.commons.ienum.corp.company.type.OverseaCompanyTypeEnum;
import com.qcc.frame.commons.ienum.oversea.MaintainGoodsIdEnum;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import com.qcc.frame.jee.commons.thread.ThreadPoolHolder;
import com.qcc.frame.jee.commons.thread.ThreadPoolManager;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.stripe.model.PaymentIntent;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.qcc.frame.commons.Constants.CorpTypePrefix.FOREIGN_CORPORATE_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.PERSON_PREFIX;
import static com.qcc.frame.commons.Constants.CorpTypePrefix.TAIWAN_CORPORATE_PREFIX;

/**
 * 订单综合处理类
 * added for lvcy v2.1.5 KNZT-6254
 * <AUTHOR>
 * @datetime 6/2/2025 2:38 下午
 */
@Service
public class OrderBusinessService {
    private static final Logger log = LoggerFactory.getLogger(OrderBusinessService.class);
    @Autowired
    private CommTblCompReportOrderService orderService;
    @Autowired
    private CommTblReportChargeUnitService chargeUnitService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private SgReportOrderCustomService sgReportOrderCustomService;
    @Autowired
    private CommTblCompOpIpLogService commTblCompOpIpLogService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private CommTblCompReportCartService commTblCompReportCartService;
    @Autowired
    private CommTblGlobalIndustryService commTblGlobalIndustryService;
    @Autowired
    private OvsQccOvsBasicService qccOvsBasicService;
    @Autowired
    private TranslaterService translaterService;
    @Autowired
    private CommTblCompReportOrderMapService commTblCompReportOrderMapService;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblCompScanListRuleService commTblCompScanListRuleService;
    @Autowired
    private CommTblCompReportOrderScanService commTblCompReportOrderScanService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DataMapBusinessService dataMapBusinessService;
    @Autowired
    private OvsQccOvsBasicService ovsQccOvsBasicService;
    @Autowired
    private OverseaKafkaService overseaKafkaService;
    @Autowired
    private CommTblCompReportOrderHkDocService hkDocService;
    @Autowired
    private SysConfigCacheService sysConfigCacheService;
    @Autowired
    private RuleBusinessService ruleBusinessService;
    @Autowired
    private CommonDataService commonDataService;
    @Autowired
    private CommTblBenefitPoolService benefitPoolService;
    @Autowired
    private CommTblBenefitPoolChangeRecordService benefitPoolChangeRecordService;
    @Autowired
    private HkIrdService hkIrdService;
    @Autowired
    private CommTblBenefitDeliveryService commTblBenefitDeliveryService;

    private static ThreadPoolHolder threadPoolHolder = ThreadPoolManager.initPool("order-submit-pool", 20);

    /**
     * 提交订单
     * added for lvcy v2.1.5 KNZT-6254
     * @param form
     * @return
     * @throws MessageException
     */
    public OrderSubmitResult submitOrder(ReportOrderSubmitForm form) throws MessageException {
        // 1.参数校验
        checkParamOrderSubmitForm(form);
        // 2.业务校验，包含两种校验：1.抛出MessageException，程序终止；2.返回不允许下单的订单信息，返回给前端
        OrderSubmitCheckBusinessTO checkBusinessTO = checkBusinessOrderSubmitForm(form.getOrderInfoList(), form.getCreditsToChargeByProdAcc());
        if (!checkBusinessTO.getCheckPass()) {
            return OrderSubmitResult.build4CheckFail(checkBusinessTO.getCheckFailList());
        }
        // 3.保存数据
        List<TblCompReportOrder> savedOrderList = saveOrderData(form.getOrderInfoList(), checkBusinessTO);

        // 4.扣费执行后续逻辑
        return payAndExecuteAfterSave(checkBusinessTO, savedOrderList);
    }


    /**
     * 提交扫描订单
     * added for lvcy v2.1.7 KNZT-6487
     * @param form
     * @return
     * @throws MessageException
     */
    public OrderSubmitResult submitScanOrder(BatchScanOrderSubmitForm form) throws MessageException {
        // 1.参数校验
        checkParamScanOrderSubmitForm(form);
        // 2.业务校验
        OrderSubmitCheckBusinessTO checkBusinessTO = checkBusinessScanOrderSubmitForm(form.getOrderInfoList(), form.getCreditsToChargeByProdAcc());
        if (!checkBusinessTO.getCheckPass()) {
            return OrderSubmitResult.build4CheckFail(checkBusinessTO.getCheckFailList());
        }
        // 3.保存数据
        List<TblCompReportOrder> savedOrderList = saveScanOrder(form.getOrderInfoList(), checkBusinessTO.getReportTypeChargeUnitMap().get(ReportTypeEnum.SCAN.getCode()), checkBusinessTO.getNeedPayOnline());

        // 4.扣费执行后续逻辑
        return payAndExecuteAfterSave(checkBusinessTO, savedOrderList);
    }

    /**
     * 通过购物车提交订单
     * added for lvcy v2.1.5 KNZT-6254
     * @return
     * @throws MessageException
     */
    public OrderSubmitResult submitOrder4Cart(ReportOrderSubmitForm form) throws MessageException {
        // 构建提交订单参数
        List<TblCompReportCart> cartList = commTblCompReportCartService.getCartByUserId(UserUtils.getUserId());
        MsgExceptionUtils.checkIsNull(cartList, "err.param.invalid");
        List<OrderSubmitTO> orderSubmitTOList = OrderSubmitTO.build(cartList);
        form.setOrderInfoList(orderSubmitTOList);
        // 提交订单
        OrderSubmitResult orderSubmitResult = submitOrder(form);
        if (orderSubmitResult.getCheckPass()) {
            // 清空购物车
            commTblCompReportCartService.clearByUserId(UserUtils.getUserId());
        }

        return orderSubmitResult;
    }

    /**
     * 记录图谱消耗，用credits支付或者用权益抵扣
     * @param form
     * @return
     * @throws MessageException
     */
    public JsonResult<OrderSubmitResult> submitOrder4Map(MapReportOrderSubmitForm form) throws MessageException {
        // 如果提交的mapNodeNo都已经存在对应的订单，则直接返回订单信息
        List<TblCompReportOrderMap> existMapList = checkMapOrderSubmitForm(form);
        if (CollectionUtils.isNotEmpty(existMapList)) {
            List<String> existOrderNoList = existMapList.stream().map(TblCompReportOrderMap::getReportOrderNo).collect(Collectors.toList());
            log.info("submitOrder4Map all mapNodeNo is exist, matchOrderNoList:{}", existOrderNoList);
            List<TblCompReportOrder> orderList = orderService.getByOrderNoList(existOrderNoList);
            return JsonResult.buildSuccess(OrderSubmitResult.build4Success(orderList));
        }
        // 获取权益池支付策略
        String reportType = form.getOrderInfoList().get(0).getReportType();
        String rootKeyNo = form.getRootKeyNo();
        PaymentStrategyTO strategy = benefitPoolService.determinePaymentStrategyByKeyNo(reportType, rootKeyNo);
        TblBenefitPool pool = strategy.getPool();
        if (Objects.nonNull(pool)) {
            benefitPoolService.lockBenefitPoolById(pool.getId());
        }

        // 注入内部参数
        for (OrderSubmitTO orderSubmitTO : form.getOrderInfoList()) {
            orderSubmitTO.setUseBenefit(strategy.isUseBenefit());
        }
        // 提交订单
        OrderSubmitResult submitResult = submitOrder(form);
        // 订单提交失败直接抛出异常
        if (!submitResult.getCheckPass()) {
            throw new MessageException(submitResult.getCheckFailList().get(0).getErrorMsg());
        }
        // 使用credits支付图谱，不允许使用stripe支付(更新图谱功能未适配，前台交互未适配，留意map表数据留存)
        MsgExceptionUtils.failBuild(StringUtils.isNotBlank(submitResult.getPayRelId()), "msg:Stripe payment is not supported for data map.");

        // 不在权益池中，进行计次
        if (strategy.isUseBenefit() && !strategy.isInPool() && pool.getTotalCount() > pool.getConsumedCount()) {
            String successOrderId = submitResult.getSubmitSuccessList().get(0).getOrderId();
            log.info("submitOrder4Map benefit pool usage recorded, orderId:{}, poolId:{}", successOrderId, pool.getId());
            benefitPoolService.perUse(pool, successOrderId, rootKeyNo, UserUtils.getUserId());
        }

        // 订单完成后执行
        return executeAfterMapOrderSubmit(form, submitResult);
    }


    /**
     * 扣费执行后续逻辑
     * added for lvcy v2.1.7 KNZT-6487
     * @param checkBusinessTO
     * @param savedOrderList
     * @return
     * @throws MessageException
     */
    private OrderSubmitResult payAndExecuteAfterSave(OrderSubmitCheckBusinessTO checkBusinessTO, List<TblCompReportOrder> savedOrderList) throws MessageException {
        // 如果需要stripe支付
        if (UserUtils.isSelf() && checkBusinessTO.getNeedPayOnline()) {
            // 4.发起支付，需要等待支付完成回调继续流程
            PaymentIntent paymentIntent = createPaymentIntent(savedOrderList, checkBusinessTO.getOnlinePayCredits());
            return OrderSubmitResult.build4Success(savedOrderList, paymentIntent);
        } else {
            // 不需要stripe支付，直接使用额度账号付款
            for (TblCompReportOrder order : savedOrderList) {
                // 4.发起支付，同步完成
                TblReportChargeUnit chargeUnit = checkBusinessTO.getReportTypeChargeUnitMap().get(order.getReportType());
                transactionBusinessService.payOrderAndCreateTransaction(order, chargeUnit.getContractDeliveryId());
                // 5.执行支付后流程
                orderService.executeAfterOrderPaid(order);
            }
            return OrderSubmitResult.build4Success(savedOrderList);
        }
    }

    /**
     * 校验订单提交参数
     * added for lvcy v2.1.5 KNZT-6254
     * @param form
     * @throws MessageException
     */
    private void checkParamOrderSubmitForm(ReportOrderSubmitForm form) throws MessageException {
        MsgExceptionUtils.checkIsNull(form.getOrderInfoList(), "err.param.invalid");
        for (OrderSubmitTO orderInfo : form.getOrderInfoList()) { 
            if (StringUtils.isNotBlank(orderInfo.getCorpKeyNo())) {
                MsgExceptionUtils.failBuild(StringUtils.isBlank(orderInfo.getCorpName()) && StringUtils.isBlank(orderInfo.getCorpNameEn()), "err.param.invalid");
            } else if (StringUtils.isNotBlank(orderInfo.getPersKeyNo())) {
                MsgExceptionUtils.failBuild(StringUtils.isBlank(orderInfo.getPersName()), "err.param.invalid");
            } else if (StringUtils.isNotBlank(orderInfo.getPersName())) {
                // 核验订单数时，必传参参数
                MsgExceptionUtils.checkIsNull(orderInfo.getEncryptedVerifyForm(), "err.param.invalid");
            } else {
                throw new MessageException("err.param.invalid");
            }
            MsgExceptionUtils.checkIsNull(orderInfo.getReportType(), "err.param.invalid");
            // 校验香港文档参数
            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(orderInfo.getReportType())) {
                MsgExceptionUtils.checkIsNull(orderInfo.getHkDocNumber(), "err.param.invalid");
            }
        }
    }

    /**
     * 校验订单提交业务
     * added for lvcy v2.1.5 KNZT-6254
     * @param orderParamList
     * @return
     * @throws MessageException
     */
    private OrderSubmitCheckBusinessTO checkBusinessOrderSubmitForm(List<OrderSubmitTO> orderParamList, BigDecimal creditsToChargeByProdAcc) throws MessageException {
        log.info("checkBusinessOrderSubmitForm start");
        List<TblReportChargeUnit> chargeUnitList = chargeUnitService.listEnableChargeUnitByCompany(UserUtils.getUserCompanyId());
        Map<String, TblReportChargeUnit> chargeUnitMap = chargeUnitList.stream().collect(Collectors.toMap(TblReportChargeUnit::getReportType, Function.identity(), (a, b) -> a));

        BigDecimal totalUnit = orderParamList.stream()
                .filter(k -> !Boolean.TRUE.equals(k.getUseBenefit())) // 减去使用权益的(使用credits时，这个参数可能是false，也可能是null)
                .map(k -> chargeUnitMap.get(k.getReportType()))
                .filter(Objects::nonNull)
                .map(TblReportChargeUnit::getUnit)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        // 校验额度
        if (Objects.isNull(creditsToChargeByProdAcc)) {
            creditsToChargeByProdAcc = totalUnit;
        }
        MsgExceptionUtils.failBuild(creditsToChargeByProdAcc.compareTo(BigDecimal.ZERO) < 0, "err.param.invalid");
        MsgExceptionUtils.failBuild(creditsToChargeByProdAcc.compareTo(totalUnit) > 0, "err.param.invalid");
        MsgExceptionUtils.failBuild(!UserUtils.isSelf() && creditsToChargeByProdAcc.compareTo(totalUnit) != 0, "err.param.invalid");
        boolean needCheck4ActualCost = CompTypeEnum.isTrial(UserUtils.getUserCommInfo().getType())
                && orderParamList.stream().anyMatch(order -> ReportTypeEnum.actualCostTypeList().contains(order.getReportType()));
        orderService.checkFuncCount(creditsToChargeByProdAcc, needCheck4ActualCost);

        // 单独选择original document或仅选择original document与Financial & Tax，不允许提交订单
        List<String> reportTypeList = orderParamList.stream().map(OrderSubmitTO::getReportType).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reportTypeList)) {
            if (reportTypeList.size() == 1 && ReportTypeEnum.LEGAL.getCode().equals(reportTypeList.get(0))) {
                throw new MessageException("msg:The original document is only available when purchased with other reports.");
            }
            if (reportTypeList.size() == 2
                    && reportTypeList.contains(ReportTypeEnum.LEGAL.getCode())
                    && reportTypeList.contains(ReportTypeEnum.FIN_TAX.getCode())) {
                throw new MessageException("The original document cannot be ordered together with Financial & Tax.");
            }
        }

        // 校验订单是否允许提交，会校验所有订单，不会因为订单校验不通过而抛出异常
        List<OrderSubmitCheckErrorTO> errorList = checkOrderCanSubmitWithoutException(orderParamList, chargeUnitMap);

        // 校验通过, 组装数据
        if (CollectionUtils.isEmpty(errorList)) {
            Boolean needPayOnline = totalUnit.compareTo(creditsToChargeByProdAcc) > 0;
            BigDecimal onlinePayCredits = needPayOnline ? totalUnit.subtract(creditsToChargeByProdAcc) : BigDecimal.ZERO;
            return OrderSubmitCheckBusinessTO.buildPass(chargeUnitMap, needPayOnline, onlinePayCredits);
        } else {
            log.info("checkOrderCanSubmitWithoutException order submit is not allowed, errorList: {}", JSON.toJSONString(errorList));
            return OrderSubmitCheckBusinessTO.buildFail(errorList);
        }
    }

    /**
     * 校验订单是否允许提交，不会抛出异常，每个都会校验到
     * added for lvcy v2.1.5 KNZT-6254
     * @param orderParamList
     * @param chargeUnitMap
     * @return
     * @throws MessageException
     */ 
    public List<OrderSubmitCheckErrorTO> checkOrderCanSubmitWithoutException(List<OrderSubmitTO> orderParamList, Map<String, TblReportChargeUnit> chargeUnitMap) throws MessageException {
        List<OrderSubmitCheckErrorTO> errorList = new ArrayList<>();
        for (OrderSubmitTO order : orderParamList) {
            TblReportChargeUnit chargeUnit = chargeUnitMap.get(order.getReportType());
            if (Objects.isNull(chargeUnit)) {
                errorList.add(OrderSubmitCheckErrorTO.build(order, "err.account.no.service"));
            } else {
                if (Objects.nonNull(order.getUnit()) && !(chargeUnit.getUnit().compareTo(order.getUnit()) == 0)) {
                    errorList.add(OrderSubmitCheckErrorTO.build(order, "err.service.unit.change", chargeUnit.getUnit()));
                }
            }
        }
        // 如果存在original document订单，则需要进行国内工商官网校验
        List<OrderSubmitTO> documentOrderParamList = orderParamList.stream().filter(k -> ReportTypeEnum.LEGAL.getCode().equals(k.getReportType())).collect(Collectors.toList());
        boolean hasOriginalDocument = CollectionUtils.isNotEmpty(documentOrderParamList);
        if (hasOriginalDocument) {
            String originalDocumentActiveFlag = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfigType.SERVICE_ACTIVE, ReportTypeEnum.LEGAL.getCode(), "1");
            if (StringUtils.equals(originalDocumentActiveFlag, "0")) {
                for (OrderSubmitTO k : documentOrderParamList) {
                    errorList.add(OrderSubmitCheckErrorTO.build(k, "err.original.website.unavailable"));
                }
            }
        }

        // 香港需要额外校验查册人信息
        List<OrderSubmitTO> hkOrderParamList = orderParamList.stream()
                .filter(orderSubmitTO -> ReportTypeEnum.hkCorpList().contains(orderSubmitTO.getReportType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hkOrderParamList)) {
            boolean validateHkSearcherInfo = commSysCompanyService.validateHkSearcherInfo(UserUtils.getUserCompanyId());
            if (!validateHkSearcherInfo) {
                errorList.addAll(OrderSubmitCheckErrorTO.build(hkOrderParamList, Constants.MessageExceptionKey.HK_SEARCHER_INFO_MISSING));
            }
        }

        // 校验海外国家官网维护状态
        Set<String> reportTypes = MappingUtils.getSet(orderParamList, OrderSubmitTO::getReportType);
        Map<String, Boolean> reportType2MaintainStatusMap = getReportType2MaintainStatusMap(reportTypes);
        for (OrderSubmitTO orderSubmitTO : orderParamList) {
            Boolean maintainStatus = reportType2MaintainStatusMap.get(orderSubmitTO.getReportType());
            if (BooleanUtils.isTrue(maintainStatus)) {
                errorList.add(OrderSubmitCheckErrorTO.build(orderSubmitTO, "err.hk.maintenance"));
            }
        }

        // checkOrderParamList 根据corpKeyNo 进行分组
        Map<String, List<OrderSubmitTO>> orderParamMap = orderParamList.stream()
                .filter(k -> Objects.nonNull(k.getCorpKeyNo()) && Objects.isNull(k.getPersKeyNo()))
                .filter(k -> !errorList.contains(OrderSubmitCheckErrorTO.build(k, null)))
                .collect(Collectors.groupingBy(OrderSubmitTO::getCorpKeyNo));

        List<CompletableFuture<List<OrderSubmitCheckErrorTO>>> futures = new ArrayList<>();
        
        for (Map.Entry<String, List<OrderSubmitTO>> entry : orderParamMap.entrySet()) {
            String keyNo = entry.getKey();
            List<OrderSubmitTO> orderList = entry.getValue();
            String reportType = orderList.get(0).getReportType();
            UnitGroupEnum unitGroupEnum = ReportTypeEnum.getUnitGroupEnum(reportType);
            if (Objects.isNull(unitGroupEnum)) {
                log.info("unitGroupEnum is null, reportType: {}", reportType);
                continue;
            }
            
            CompletableFuture<List<OrderSubmitCheckErrorTO>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    List<OrderSubmitCheckErrorTO> subErrorList = new ArrayList<>();
                    if (UnitGroupEnum.CN_UNIT.equals(unitGroupEnum)) {
                        // 国内企业
                        ApiGlobalCorpDetailTO corp = CompanyDetailsInterface.getECIInfoVerifyInfo4Global(keyNo);
                        for (OrderSubmitTO order : orderList) {
                            if (Objects.isNull(corp)) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.access"));
                                continue;
                            }
                            if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType())) {
                                if (!StringUtils.equals(corp.getEntType(), "General") || StringUtils.isBlank(corp.getCreditCode())) {
                                    subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.fin.tax.forbid"));
                                    continue;
                                }
                            }
                            order.correct(corp);
                        }
                    } else if (UnitGroupEnum.HK_UNIT.equals(unitGroupEnum)) {
                        // 香港企业
                        // 处理IRD商品
                        List<OrderSubmitTO> hkIrdOrderList = orderList.stream()
                                .filter(orderSubmitTO -> ReportTypeEnum.hkIrdCorpList().contains(orderSubmitTO.getReportType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(hkIrdOrderList)) {
                            QccOvsBasicInfoCommTO qccOvsBasicInfo = qccOvsBasicService.getQccOvsBasicInfoCommTO(keyNo);
                            Map<String, Boolean> reportType2CanOrderMapFromGoods = hkIrdService.doGetReportType2CanOrderMapFromGoods(qccOvsBasicInfo, false);
                            for (OrderSubmitTO order : hkIrdOrderList) {
                                Boolean canOrderFromGoods = reportType2CanOrderMapFromGoods.get(order.getReportType());
                                if (canOrderFromGoods == null || !canOrderFromGoods) {
                                    subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.hk.not.supported"));
                                }
                                order.correct4HkIrd(qccOvsBasicInfo);
                            }
                        }

                        // 处理ICRIS商品
                        List<OrderSubmitTO> hkIcrisOrderList = orderList.stream()
                                .filter(orderSubmitTO -> ReportTypeEnum.hkIcrisCorpList().contains(orderSubmitTO.getReportType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(hkIcrisOrderList)) {
                            HkCorpBasicDetailTO corp = qccOvsBasicService.getHkCorpBasicDetailTO(keyNo);
                            for (OrderSubmitTO order : hkIcrisOrderList) {
                                if (Objects.isNull(corp)) {
                                    subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.access"));
                                    continue;
                                }

                                if (ReportTypeEnum.hkReportList().contains(order.getReportType())
                                        || ReportTypeEnum.MAP_OWNERSHIP_HK.getCode().equals(order.getReportType())
                                        || ReportTypeEnum.MAP_NETWORK_HK.getCode().equals(order.getReportType())) {
                                    JsonResultList<HongKongAnnouncementTO> hkAnnouncementPage = IntranetInterface.getHkAnnouncementPage(keyNo, "1", "1000");
                                    List<HongKongAnnouncementTO> hkAnnouncements = hkAnnouncementPage.getResultList();
                                    boolean companyCanOrder = CommTblCompReportOrderService.companyCanOrder(hkAnnouncements, corp);
                                    if (!companyCanOrder) {
                                        subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.hk.not.supported"));
                                        continue;
                                    }
                                    if (ReportTypeEnum.HK_BAS_AR.getCode().equals(order.getReportType())) {
                                        Set<String> anTypeList = IntranetInterface.listAnnualOrApplicationType(hkAnnouncements);
                                        if (Objects.isNull(corp.getCompanyTypeEn())
                                                || HkCompanyTypeEnum.COMPANY_TYPE7.getCompanyTypeEn().equals(corp.getCompanyTypeEn())
                                                || Objects.isNull(anTypeList)) {
                                            subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.hk.not.supported"));
                                            continue;
                                        }
                                    }
                                }
                                order.correct(corp);
                            }
                        }
                    } else if (UnitGroupEnum.SG_UNIT.equals(unitGroupEnum)) {
                        // 新加坡企业
                        SgBasicTO basic = sgReportOrderCustomService.getBasic(keyNo);
                        for (OrderSubmitTO order : orderList) {
                            if (basic == null || basic.getCanBuyInGeneral() == null || !basic.getCanBuyInGeneral()) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.sg.forbid"));
                                continue;
                            }
                            order.correct(basic);
                        }
                    } else if (UnitGroupEnum.MY_UNIT.equals(unitGroupEnum)) {
                        // 马来西亚企业
                        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
                        for (OrderSubmitTO order : orderList) {
                            if (Objects.isNull(qccOvsBasic) || Objects.isNull(qccOvsBasic.getBasicInfo())) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.access"));
                                continue;
                            }
                            QccOvsBasicInfo basicInfo = qccOvsBasic.getBasicInfo();
                            if (StringUtils.andBlank(basicInfo.getCompNo(), basicInfo.getRegNo())) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.access"));
                                continue;
                            }
                            order.correct4My(basicInfo);
                        }
                    } else if (UnitGroupEnum.NZ_UNIT.equals(unitGroupEnum)) {
                        // 新西兰企业
                        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
                        for (OrderSubmitTO order : orderList) {
                            if (Objects.isNull(qccOvsBasic) || Objects.isNull(qccOvsBasic.getBasicInfo())) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "err.access"));
                                continue;
                            }
                            QccOvsBasicInfo basicInfo = qccOvsBasic.getBasicInfo();
                            if (StringUtils.andBlank(basicInfo.getCompNo())) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "msg:This report is not available"));
                                continue;
                            }
                            order.correct4Nz(basicInfo);
                        }
                    } else if (UnitGroupEnum.TW_UNIT.equals(unitGroupEnum)) {
                        // 台湾企业
                        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
                        QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
                        boolean companyCanOrder = CommTblCompReportOrderService.companyCanOrder4Tw(qccOvsBasic);
                        for (OrderSubmitTO order : orderList) {
                            if (!companyCanOrder) {
                                subErrorList.add(OrderSubmitCheckErrorTO.build(order, "msg:This report is not available"));
                                continue;
                            }
                            order.correct4Tw(basicInfo);
                        }
                    }
                    return subErrorList;
                } catch (MessageException e) {
                    throw new CompletionException(e);
                }
            }, threadPoolHolder.getPoolExecutor());
            futures.add(future);
        }

        if (CollectionUtils.isNotEmpty(futures)) {
            try {
                // 等待所有Future完成并收集结果
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                futures.stream()
                        .map(CompletableFuture::join)
                        .forEach(errorList::addAll);
            } catch (CompletionException e) {
                log.error("checkBusinessOrderSubmitForm run sync error, e", e);
                throw new MessageException("err.access");
            }
        }
        log.info("checkBusinessOrderSubmitForm end");
        return errorList;
    }


    /**
     * 保存订单数据
     * added for lvcy v2.1.5 KNZT-6254
     * @param orderParamList
     * @param checkBusinessTO
     * @return
     */
    private List<TblCompReportOrder> saveOrderData(List<OrderSubmitTO> orderParamList, OrderSubmitCheckBusinessTO checkBusinessTO) throws MessageException {
        Map<String, TblReportChargeUnit> chargeUnitMap = checkBusinessTO.getReportTypeChargeUnitMap();
        List<TblCompReportOrder> orderToSaveList = new ArrayList<>();
        for (OrderSubmitTO orderSubmitTO : orderParamList) {
            TblCompReportOrder order = orderService.buildOrderFromParam(orderSubmitTO, chargeUnitMap.get(orderSubmitTO.getReportType()));
            if (checkBusinessTO.getNeedPayOnline()) {
                order.setPayType(order.getTotalUnit().compareTo(checkBusinessTO.getOnlinePayCredits()) == 0 ?
                        PayTypeEnum.ONLINE.getCode() : PayTypeEnum.COMBINE.getCode());
            } else {
                order.setPayType(PayTypeEnum.UNIT.getCode());
            }
            orderToSaveList.add(order);
            // 保存verify订单的核验数据
            if (ReportTypeEnum.getVerifyList().contains(order.getReportType())) {
                saveEncryptedVerifyForm(order.getOrderNo(), orderSubmitTO.getEncryptedVerifyForm());
            }
        }
        // 保存订单数据
        orderService.batchPartialInsertWithIncreaseCreateDate(orderToSaveList);
        // 批量保存香港文档数据
        List<OrderSubmitHkDocParamTO> hkDocToSaveList = orderToSaveList.stream()
                .filter(k -> ReportTypeEnum.HK_DOCUMENT.getCode().equals(k.getReportType()))
                .map(k -> OrderSubmitHkDocParamTO.build(k.getId(), OrderHkDocRelTypeEnum.ORDER.getCode(), k.getKeyNo(), k.getExtraInfo1()))
                .collect(Collectors.toList());
        hkDocService.batchSave(hkDocToSaveList);
        // 批量保存操作IP日志
//        List<String> orderIdList = orderToSaveList.stream().map(TblCompReportOrder::getId).collect(Collectors.toList());
//        commTblCompOpIpLogService.batchSaveIpLog(UserUtils.getUserCompanyId(), UserUtils.getUserId(), OpIpLogTypeEnum.ORDER.getCode(), orderIdList);
        return orderToSaveList;
    }


    /**
     * 校验扫描订单参数
     * added for lvcy v2.1.7 KNZT-6487
     * @param form
     * @throws MessageException
     */
    private void checkParamScanOrderSubmitForm(BatchScanOrderSubmitForm form) throws MessageException {
        MsgExceptionUtils.checkIsNull(form.getOrderInfoList(), "err.param.invalid");
        for (ScanOrderSubmitForm order : form.getOrderInfoList()) {
            if (StringUtils.isNotBlank(order.getEntityCountryCode())) {
                GlobalAreaEnum areaEnum = GlobalAreaEnum.getByNameCode(order.getEntityCountryCode());
                MsgExceptionUtils.checkIsNull(areaEnum, "err.param.invalid");
            }
    
            // 人员参数校验
            if (Constants.Report.KEY_NO_TYPE_PERS.equals(order.getEntityType())) {
                // 校验姓名必填规则:要么填First Name + Last Name,要么填Script Name
                boolean hasFirstLastName = StringUtils.isNotBlank(order.getPersFirstName()) && StringUtils.isNotBlank(order.getPersLastName());
                boolean hasScriptName = StringUtils.isNotBlank(order.getPersScriptName());
                MsgExceptionUtils.failBuild(!hasFirstLastName && !hasScriptName, 
                    "msg:Please provide either First Name and Last Name, or Script Name");
    
                // 校验名字长度和格式
                if (StringUtils.isNotBlank(order.getPersFirstName())) {
                    MsgExceptionUtils.failBuild(order.getPersFirstName().length() > 200, "msg:First Name cannot exceed 200 characters");
                }
                if (StringUtils.isNotBlank(order.getPersMiddleName())) {
                    MsgExceptionUtils.failBuild(order.getPersMiddleName().length() > 200, "msg:Middle Name cannot exceed 200 characters");
                }
                if (StringUtils.isNotBlank(order.getPersLastName())) {
                    MsgExceptionUtils.failBuild(order.getPersLastName().length() > 200, "msg:Last Name cannot exceed 200 characters");
                }
                if (StringUtils.isNotBlank(order.getPersScriptName())) {
                    MsgExceptionUtils.failBuild(order.getPersScriptName().length() > 200, "msg:Script Name cannot exceed 200 characters");
                }
                
                // 校验生日格式
                if (Objects.nonNull(order.getPersBirthday())) {
                    validateBirthday(order.getPersBirthday());
                }
    
                // 校验性别 Gender
                if (StringUtils.isNotBlank(order.getPersGender())) {
                    GenderEnum genderEnum = GenderEnum.getByCode(order.getPersGender());
                    MsgExceptionUtils.checkIsNull(genderEnum, "msg:Invalid gender value. Must be M or F");
                }
    
                // 校验个人ID信息 (National ID, Passport, etc.)
                if (StringUtils.isNotBlank(order.getPersNo())) {
                    MsgExceptionUtils.failBuild(order.getPersNo().length() > 200,
                        "msg:ID No. cannot exceed 200 characters");
                }
    
                // 校验关联组织名称
                if (StringUtils.isNotBlank(order.getPersOrgScriptName())) {
                    MsgExceptionUtils.failBuild(order.getPersOrgScriptName().length() > 200,
                        "Related Organization Name cannot exceed 200 characters");
                }
    
                // 校验关联组织注册号
                if (StringUtils.isNotBlank(order.getPersOrgNo())) {
                    MsgExceptionUtils.failBuild(order.getPersOrgNo().length() > 200,
                        "msg:Registration Number of Associated Organization cannot exceed 200 characters");
                }
            } else if (Constants.Report.KEY_NO_TYPE_CORP.equals(order.getEntityType())){ // 企业参数校验
                // 企业名称必填一个
                MsgExceptionUtils.checkIsNull(StringUtils.isBlank(order.getOrgName()) && StringUtils.isBlank(order.getOrgScriptName()), "Organization name is required");
                // 企业参数校验
                if (StringUtils.isNotBlank(order.getOrgName())) {
                    MsgExceptionUtils.failBuild(order.getOrgName().length() > 200, "msg:Organization name cannot exceed 200 characters");
                }
                if (StringUtils.isNotBlank(order.getOrgScriptName())) {
                    MsgExceptionUtils.failBuild(order.getOrgScriptName().length() > 200, "msg:Organization Script Name cannot exceed 200 characters");
                }
                // 校验注册号
                if (StringUtils.isNotBlank(order.getOrgNo())) {
                    MsgExceptionUtils.failBuild(order.getOrgNo().length() > 200, "msg:Registration Number cannot exceed 200 characters");
                }
            } else {
                throw new MessageException("err.param.invalid");
            }
    
        }
    }

    /**
     * 扫描订单业务校验
     * added for lvcy v2.1.7 KNZT-6487
     * @param orderParamList
     * @param creditsToChargeByProdAcc
     * @return
     * @throws MessageException
     */
    private OrderSubmitCheckBusinessTO checkBusinessScanOrderSubmitForm(List<ScanOrderSubmitForm> orderParamList, BigDecimal creditsToChargeByProdAcc) throws MessageException {
        TblReportChargeUnit rptUnit = chargeUnitService.getChargeUnitByUniqueKey(UserUtils.getUserCompanyId(), 
            ReportGroupMappingEnum.SCAN_G.getRptGroup(), ReportTypeEnum.SCAN.getCode());
        MsgExceptionUtils.failBuild(Objects.isNull(rptUnit) || Constants.NO.equals(rptUnit.getEnabled()), "err.account.no.service");

        // 非C端客户校验额度
        BigDecimal totalUnit = orderParamList.stream()
                .map(k -> rptUnit.getUnit())
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        if (Objects.isNull(creditsToChargeByProdAcc)) {
            creditsToChargeByProdAcc = totalUnit;
        } else {
            MsgExceptionUtils.failBuild(creditsToChargeByProdAcc.compareTo(totalUnit) > 0, "err.param.invalid");
        }
        orderService.checkFuncCount(creditsToChargeByProdAcc, false);

        Map<String, TblReportChargeUnit> chargeUnitMap = Maps.newHashMap();
        chargeUnitMap.put(rptUnit.getReportType(), rptUnit);
        Boolean needPayOnline = totalUnit.compareTo(creditsToChargeByProdAcc) > 0;
        BigDecimal onlinePayCredits = needPayOnline ? totalUnit.subtract(creditsToChargeByProdAcc) : BigDecimal.ZERO;
        return OrderSubmitCheckBusinessTO.buildPass(chargeUnitMap, needPayOnline, onlinePayCredits);
    }

    /**
     * 保存扫描订单
     * added for lvcy v2.1.7 KNZT-6487
     * @param orderParamList
     * @param reportChargeUnit
     * @param needPayOnline
     * @return
     */
    private List<TblCompReportOrder> saveScanOrder(List<ScanOrderSubmitForm> orderParamList, TblReportChargeUnit reportChargeUnit, Boolean needPayOnline) throws MessageException {
        List<TblCompReportOrder> orderToSaveList = new ArrayList<>();
        List<TblCompReportOrderScan> scanToSaveList = new ArrayList<>();
        for (ScanOrderSubmitForm orderSubmitTO : orderParamList) {
            TblCompReportOrder order = new TblCompReportOrder();
            order.setReportType(reportChargeUnit.getReportType());
            order.setReportGroup(reportChargeUnit.getReportGroup());
            order.setReportName(reportChargeUnit.getReportName());
            order.setUnit(reportChargeUnit.getUnit());
            order.setTotalUnit(reportChargeUnit.getUnit());
            order.setLoginName(UserUtils.getUserLoginName());
            order.setUserId(UserUtils.getUserId());
            order.setCompanyId(UserUtils.getUserCompanyId());
            order.setOrderNo(orderService.generateOrderNo(order.getReportType()));// updated for v2.1.6 fengsw KNZT-6513
            order.setKeyNoType(orderSubmitTO.getEntityType());
            order.setDataResource(Constants.OrderDataResource.GLOBAL);
            order.setRptStatus(OrderStatusEnum.NONE.getCode());
            if (needPayOnline) {
                order.setPayType(PayTypeEnum.ONLINE.getCode());
            } else {
                order.setPayType(PayTypeEnum.UNIT.getCode());
            }

            if (Constants.Report.KEY_NO_TYPE_PERS.equals(orderSubmitTO.getEntityType())) {
                order.setPersName(orderSubmitTO.getPersScriptName());
                // 拼接英文名,过滤空值并用空格连接
                String persNameEn = Stream.of(orderSubmitTO.getPersFirstName(), orderSubmitTO.getPersMiddleName(), orderSubmitTO.getPersLastName())
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(" "));
                order.setPersNameEn(persNameEn);
            } else {
                order.setCorpName(orderSubmitTO.getOrgScriptName());
                order.setCorpNameEn(orderSubmitTO.getOrgName());
            }
            orderToSaveList.add(order);

            TblCompReportOrderScan scan = new TblCompReportOrderScan();
            BeanUtils.copyProperties(orderSubmitTO, scan);
            scan.setReportOrderNo(order.getOrderNo());
            scan.setCompanyId(UserUtils.getUserCompanyId());
            scan.setUserId(UserUtils.getUserId());
            List<ScanListEnum> scanListEnumList = commTblCompScanListRuleService.getCompScanListEnumList(UserUtils.getUserCompanyId());
            scan.setScanGroupCodeList(ScanListEnum.generateScanListStr(scanListEnumList));
            scanToSaveList.add(scan);
        }
        // 保存订单数据
        orderService.batchPartialInsertWithIncreaseCreateDate(orderToSaveList);
        // 保存扫描数据
        commTblCompReportOrderScanService.batchInsert(scanToSaveList);
        // 批量保存操作IP日志
//        List<String> orderIdList = orderToSaveList.stream().map(TblCompReportOrder::getId).collect(Collectors.toList());
//        commTblCompOpIpLogService.batchSaveIpLog(UserUtils.getUserCompanyId(), UserUtils.getUserId(), OpIpLogTypeEnum.ORDER.getCode(), orderIdList);

        return orderToSaveList;
    }

    /**
     * 创建支付意图
     * added for lvcy v2.1.5 KNZT-6254
     * @param savedOrderList
     * @return
     */
    private PaymentIntent createPaymentIntent(List<TblCompReportOrder> savedOrderList, BigDecimal onlinePayCredits) throws MessageException {
        PaymentIntent paymentIntent = null;
        try {
            log.info("PaymentIntent create start");
            paymentIntent = StripePaymentInterface.createPaymentIntent4CartOrder(savedOrderList, onlinePayCredits, contractDeliveryService.generateTopUpNo());
            MsgExceptionUtils.checkIsNull(paymentIntent, "err.access");
            List<String> orderIdList = savedOrderList.stream().map(TblCompReportOrder::getId).collect(Collectors.toList());
            orderService.updatePayRelId(orderIdList, paymentIntent.getId());
            // stripe 超时取消
            redisService.addDelayedCommTask(CommDelayedTaskTypeEnum.PAYMENT_INTENT_CANCEL, paymentIntent.getId(), 30 * 60);
            log.info("PaymentIntent create success,paymentIntent:{}", JSONObject.toJSONString(paymentIntent));
            return paymentIntent;
        } catch (Exception e) {
            log.error("saveOrderAndCreatePaymentIntent4Cart error, ", e);
            if (Objects.nonNull(paymentIntent)) {
                paymentIntent = StripePaymentInterface.cancelPaymentIntent(paymentIntent);
                log.info("PaymentIntent cancel,paymentIntent:{}", JSONObject.toJSONString(paymentIntent));
            }
            throw e;
        }
    }

    /**
     * 校验图谱订单提交参数
     * added for lvcy v2.1.5 KNZT-6384
     * @param form
     * @return 返回已经提交的订单编号列表
     * @throws MessageException
     */
    private List<TblCompReportOrderMap> checkMapOrderSubmitForm(MapReportOrderSubmitForm form) throws MessageException {
        String reportType = form.getOrderInfoList().get(0).getReportType();
        if (ReportTypeEnum.isRelationMap(reportType)) {
            MsgExceptionUtils.failBuild(form.getRelationNodeKeyNoList().size() < 2,
                    "err.param.invalid", "relation node must be greater than 2");
            MsgExceptionUtils.failBuild(ReportTypeEnum.MAP_RELATION.getCode().equals(reportType) && form.getRelationNodeKeyNoList().size() > 10,
                "err.param.invalid", "relation node must be less than 10");
            MsgExceptionUtils.failBuild(ReportTypeEnum.MAP_RELATION_3.getCode().equals(reportType) && form.getRelationNodeKeyNoList().size() > 3,
                "err.param.invalid", "relation node must be less than 3");
            return Lists.newArrayList();
        }
        MsgExceptionUtils.checkIsNull(form.getOrderInfoList(), "err.param.invalid");
        MsgExceptionUtils.checkIsNull(form.getRootKeyNo(), "err.param.invalid");

        List<String> nodeNoList = form.getOrderInfoList().stream().map(OrderSubmitTO::getMapNodeNo)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        MsgExceptionUtils.checkIsNull(nodeNoList, "err.param.invalid");
        // 校验入参中的nodeNoList是否有重复的
        MsgExceptionUtils.failBuild(nodeNoList.size() != form.getOrderInfoList().size(), "err.param.invalid", "duplicate node no is not allowed.");
        // 校验mapNo是否都一致
        long mapNoDistinctCount = form.getOrderInfoList().stream().map(OrderSubmitTO::getMapNo).distinct().count();
        MsgExceptionUtils.failBuild(mapNoDistinctCount != 1, "err.param.invalid");
        // 校验根节点和子节点是否在一个图中
        List<String> nodekeyNoList = form.getOrderInfoList().stream().map(OrderSubmitTO::getKeyNo)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (StringUtils.isNotBlank(form.getMapId())) {
            if (!nodekeyNoList.contains(form.getRootKeyNo())) {
                nodekeyNoList.add(form.getRootKeyNo());
            }
            Map<String, Boolean> nodeInMap = DataMapInterface.checkNodeInMap(form.getMapId(), nodekeyNoList);
            boolean isAllInMap = nodekeyNoList.stream().allMatch(k -> nodeInMap.getOrDefault(k, false));
            MsgExceptionUtils.failBuild(!isAllInMap, "err.param.invalid", "node not in map");
        } else {
            MsgExceptionUtils.failBuild(nodekeyNoList.size() != 1, "err.param.invalid", "node key no list size must be 1");
            MsgExceptionUtils.failBuild(!nodekeyNoList.get(0).equals(form.getRootKeyNo()), "err.param.invalid", "node key no must be root node key no");
        }

        // 校验根节点是否在更新后的图中
        if (StringUtils.isNotBlank(form.getUpdateMapId())) {
            boolean isRootNode = DataMapInterface.checkRootNode(form.getUpdateMapId(), form.getRootKeyNo());
            MsgExceptionUtils.failBuild(!isRootNode, "err.param.invalid", "root node not in map");
        }

        // 校验节点是否都已经购买过订单，如果是则返回给上层
        String mapNo = form.getOrderInfoList().get(0).getMapNo();
        List<TblCompReportOrderMap> mapList = commTblCompReportOrderMapService.getMapListByMapNodeNo(UserUtils.getUserId(), mapNo, nodeNoList);
        if (CollectionUtils.isEmpty(mapList)) {
            return Lists.newArrayList();
        }
        Set<String> existNodeNoList = mapList.stream().map(TblCompReportOrderMap::getMapNodeNo).collect(Collectors.toSet());
        boolean allInExistNodeNoList = existNodeNoList.containsAll(nodeNoList);
        if (allInExistNodeNoList) {
            return mapList;
        } else {
            throw new MessageException("err.param.invalid");
        }
    }


    /**
     * added for v2.1.4 fengsw KNZT-6360
     * 查询基本详情
     *
     * @return
     * @throws MessageException
     */
    public InfoBeforeSubmitV2TO geBasicDetailsBeforeSubmit(String keyNo) throws MessageException {
        if (StringUtils.isBlank(keyNo)) {
            return null;
        }
        String overseaKeyNoRegex = "^(" + FOREIGN_CORPORATE_PREFIX + "|" + HONGKONG_CORPORATE_PREFIX + "|" + TAIWAN_CORPORATE_PREFIX + ").*";
        String personKeyNoRegex = "^" + PERSON_PREFIX + ".*";
        boolean isOverseaCorp = keyNo.matches(overseaKeyNoRegex);
        boolean isPerson = keyNo.matches(personKeyNoRegex);
        InfoBeforeSubmitV2TO rtn = null;
        ReportGroupMappingEnum reportGroupMappingEnum = null;
        TransWrapper transWrapper = null;
        if (isOverseaCorp) {
            QccOvsBasicInfoCommTO qccOvsBasic = qccOvsBasicService.getQccOvsBasicInfoCommTO(keyNo);
            rtn = InfoBeforeSubmitV2TO.buildOverseasCorpDetail(qccOvsBasic);
            if (Objects.nonNull(rtn)) {
                GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getByNameCode(rtn.getNationCode());
                transWrapper = TransWrapper.buildWrapper(globalAreaEnum)
                        .add4Entity(rtn, InfoBeforeSubmitV2TO::setCompanyName, InfoBeforeSubmitV2TO::setCompanyNameEn, InfoBeforeSubmitV2TO::getCompanyName, InfoBeforeSubmitV2TO::getCompanyNameEn, InfoBeforeSubmitV2TO::getCorpKeyNo, TransWrapper.ENTRY_TYPE_CORP)
                        .batchAdd4NonEntity(rtn.getIndustry(), QccOvsBasicIndustryInfo::setDesc, QccOvsBasicIndustryInfo::setDescEn, QccOvsBasicIndustryInfo::getDesc, QccOvsBasicIndustryInfo::getDescEn);
                if (StringUtils.isNotBlank(rtn.getLegalForm())) {
                    String legalFormByEnum = OverseaCompanyTypeEnum.getTypeEnByType(rtn.getLegalForm(), globalAreaEnum);
                    if (StringUtils.isNotBlank(legalFormByEnum)) {
                        rtn.setLegalForm(legalFormByEnum);
                    } else {
                        transWrapper.add4NonEntityWithOverwrite(rtn, InfoBeforeSubmitV2TO::setLegalForm, InfoBeforeSubmitV2TO::getLegalForm);
                    }
                }
                if (GlobalAreaEnum.HK == globalAreaEnum) {
                    rtn.setImageUrl(SgCompanyDetailsInterface.getGlobalCorpImageUrlInfo(keyNo));// added for v2.1.4 fengsw KNZT-6416 imageUrl
                }
            }
        } else {
            transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN);
            if (isPerson) {
                PersonDetailInfoTO personDetailInfoTO = ECILocalInterface.getPersonDetail(keyNo);
                rtn = InfoBeforeSubmitV2TO.buildPersonDetail(personDetailInfoTO);
                if (rtn != null) {
                    transWrapper.add4Entity(rtn, InfoBeforeSubmitV2TO::setPersName, InfoBeforeSubmitV2TO::setPersNameEn, InfoBeforeSubmitV2TO::getPersName, InfoBeforeSubmitV2TO::getPersNameEn, InfoBeforeSubmitV2TO::getKeyNo, TransWrapper.ENTRY_TYPE_PERSON)
                            .add4Entity(rtn, InfoBeforeSubmitV2TO::setCompanyName, InfoBeforeSubmitV2TO::setCompanyNameEn, InfoBeforeSubmitV2TO::getCompanyName, InfoBeforeSubmitV2TO::getCompanyNameEn, InfoBeforeSubmitV2TO::getCorpKeyNo, TransWrapper.ENTRY_TYPE_CORP);
                }
            } else {
                // 大陆企业
                ApiGlobalCorpDetailTO apiGlobalCorpDetailTO = CompanyDetailsInterface.getECIInfoVerifyInfo4Global(keyNo);
                if (Objects.nonNull(apiGlobalCorpDetailTO)) {
                    rtn = InfoBeforeSubmitV2TO.buildCnCorpDetail(apiGlobalCorpDetailTO);
                    if (rtn != null) {
                        rtn.setNationCode(GlobalAreaEnum.CN.getNameCode());
                        transWrapper.add4Entity(rtn, InfoBeforeSubmitV2TO::setCompanyName, InfoBeforeSubmitV2TO::setCompanyNameEn, InfoBeforeSubmitV2TO::getCompanyName, InfoBeforeSubmitV2TO::getCompanyNameEn, InfoBeforeSubmitV2TO::getCorpKeyNo, TransWrapper.ENTRY_TYPE_CORP);
                        IndustryTO industryInfo = apiGlobalCorpDetailTO.getIndustryInfo();
                        if (industryInfo != null) {
                            String indCode = StringUtils.getNotBlankStr(industryInfo.getSmallCategoryCode(), industryInfo.getMiddleCategoryCode(), industryInfo.getSubIndustryCode(), industryInfo.getIndustryCode());
                            if (StringUtils.isNotBlank(indCode)) {
                                ArrayList<String> industryCodeList = Lists.newArrayList(indCode);
                                Map<String, TblGlobalIndustry> code2Industry4CnMapping = new HashMap<>(commTblGlobalIndustryService.getCode2Industry4CnMapping(industryCodeList));
                                TblGlobalIndustry tblGlobalIndustry = code2Industry4CnMapping.get(indCode);
                                if (tblGlobalIndustry != null) {
                                    rtn.setIndustry(Lists.newArrayList(new QccOvsBasicIndustryInfo(indCode.equals(tblGlobalIndustry.getInd1Code()) ? indCode : tblGlobalIndustry.getInd1Code() + indCode, StringUtils.getNotBlankStr(tblGlobalIndustry.getInd4Name(), tblGlobalIndustry.getInd3Name(), tblGlobalIndustry.getInd2Name(), tblGlobalIndustry.getInd1Name()), StringUtils.getNotBlankStr(tblGlobalIndustry.getInd4NameEn(), tblGlobalIndustry.getInd3NameEn(), tblGlobalIndustry.getInd2NameEn(), tblGlobalIndustry.getInd1NameEn()))));
                                }
                            }
                        }
                    }
                }
            }
        }
        if (rtn == null) {
            return null;
        }
        commonDataService.enPostProcessor(transWrapper);
        // 填充leiCode、qccCode
        if (isPerson) {
            reportGroupMappingEnum = ReportGroupMappingEnum.PERS_G;
        } else {
            reportGroupMappingEnum = ReportGroupMappingEnum.getByRegionCode4Corp(rtn.getNationCode(), rtn.getRegPlace());
            // 高管详情不需要查询leiCode、qccCode
            QccEntDetailInfoTO entDetailInfoTO = CompanyDetailsInterface.getQccDetailByEntCode(keyNo, QccEntCodeTypeEnum.KEY_NO.getCode());
            if (Objects.nonNull(entDetailInfoTO)) {
                rtn.setLeiCode(entDetailInfoTO.getLeiCode());
                if (StringUtils.isBlank(rtn.getQccCode())) {
                    rtn.setQccCode(entDetailInfoTO.getQccCode());
                }
            }
        }
        rtn.setReportGroup(reportGroupMappingEnum != null ? reportGroupMappingEnum.getRptGroup() : null);
        rtn.setStatus(null);

        boolean hasKYC = false;
        List<String> kycReportList = ReportTypeEnum.getKYCReportList();
        if (ReportGroupMappingEnum.PERS_G == reportGroupMappingEnum) {
            hasKYC = CollectionUtils.containsAny(kycReportList, ReportTypeEnum.persTypeList());
        } else {
            if (reportGroupMappingEnum != null) {
                for (String reportType : kycReportList) {
                    ReportTypeEnum reportTypeEnum = ReportTypeEnum.getEnumByCode(reportType);
                    if (reportTypeEnum != null && reportTypeEnum.getUnitGroupEnum() == reportGroupMappingEnum.getUnitGroupEnum()) {
                        hasKYC = true;
                        break;
                    }
                }
            }
        }
        rtn.setHasKYC(hasKYC);
        return rtn;
    }

    /**
     * added for v2.1.4 fengsw KNZT-6360
     * 获取当前系统用户的服务产品配置(过滤无效产品)
     * @return
     */
    public List<CompChargeUnitV2TO> listCompChargeUnits() {
        List<CompChargeUnitTO> allEnabledChargeUnits = chargeUnitService.getCompChargeUnits(UserUtils.getUserCompanyId());
        CommTblReportChargeUnitService.removeCreditIfNecessary4UnitTO(allEnabledChargeUnits);
        List<CompChargeUnitV2TO> result = allEnabledChargeUnits.stream().map(CompChargeUnitV2TO::buildNewCompChargeUnitTO).collect(Collectors.toList());
        // 判断是否进入workspace
        boolean workspace = commTblBenefitDeliveryService.getByCompanyId(UserUtils.getUserCompanyId()).stream()
                .anyMatch(k -> BenefitTypeEnum.getMapBenefitTypeList().contains(k.getBenefitType()));
        if (workspace) {
            CompChargeUnitV2TO workspaceTO = new CompChargeUnitV2TO();
            workspaceTO.setId("WORKSPACE");
            result.add(workspaceTO);
        }
        return result;
    }


    /**
     * added for v2.1.4 fengsw KNZT-6360
     * 根据产品Group获取产品列表（标记服务是可用、是否可下单）
     * 
     * @param keyNo
     * @param reportGroup
     * @return
     * @throws MessageException
     */
    public List<CompChargeUnitWithGroupTO> listReportPurchases(String companyId, String keyNo, String reportGroup) throws MessageException {
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(keyNo, reportGroup), "err.param.invalid");
        ReportGroupMappingEnum reportGroupMappingEnum = ReportGroupMappingEnum.getReportGroupByCode(reportGroup);
        MsgExceptionUtils.checkIsNull(reportGroupMappingEnum, "err.param.invalid");
        UnitGroupEnum unitGroupEnum = reportGroupMappingEnum.getUnitGroupEnum();

        // 获取当前启用的服务列表
        List<CompChargeUnitTO> chargeUnitTOList = chargeUnitService.getCompChargeUnits(companyId, reportGroup);
        if (CollectionUtils.isEmpty(chargeUnitTOList)) {
            return new ArrayList<>();
        }

        // 过滤KYC Report
        List<CompChargeUnitV2TO> compChargeUnits = chargeUnitTOList.stream()
                .filter(k -> ReportTypeEnum.getKYCReportList().contains(k.getReportType()))
                .map(CompChargeUnitV2TO::buildNewCompChargeUnitTO)
                .collect(Collectors.toList());

        // 填充数据来源网站维护状态, 并分组
        Set<String> reportTypes = MappingUtils.getSet(compChargeUnits, CompChargeUnitV2TO::getReportType);
        Map<String, Boolean> reportType2MaintainStatusMap = getReportType2MaintainStatusMap(reportTypes);
        List<CompChargeUnitWithGroupTO> serviceWithGroupList = buildServiceWithGroupList(compChargeUnits, reportType2MaintainStatusMap);

        // 填充服务是否可下单等
        if (UnitGroupEnum.HK_UNIT == unitGroupEnum) {
            handleServiceWithGroup4Hk(keyNo, serviceWithGroupList);
        } else if (UnitGroupEnum.MY_UNIT == unitGroupEnum) {
            handleServiceWithGroup4My(keyNo, serviceWithGroupList);
        } else if (UnitGroupEnum.SG_UNIT == unitGroupEnum) {
            handleServiceWithGroup4Sg(keyNo, serviceWithGroupList);
        } else if (UnitGroupEnum.NZ_UNIT == unitGroupEnum) {
            // do nothing
        } else if (UnitGroupEnum.TW_UNIT == unitGroupEnum) {
            handleServiceWithGroup4Tw(keyNo, serviceWithGroupList);
        } else if(UnitGroupEnum.CN_UNIT == unitGroupEnum){
            handleServiceWithGroup4Cn(keyNo, serviceWithGroupList);
        }
        return serviceWithGroupList;
    }

    private List<CompChargeUnitWithGroupTO> buildServiceWithGroupList(List<CompChargeUnitV2TO> compChargeUnits, Map<String, Boolean> reportType2MaintainStatusMap) {
        return compChargeUnits.stream()
                .filter(compChargeUnitV2TO -> StringUtils.isNotBlank(compChargeUnitV2TO.getGroupName()))
                .collect(Collectors.groupingBy(CompChargeUnitV2TO::getGroupName))
                .entrySet()
                .stream()
                .map(entry -> {
                    List<CompChargeUnitV2TO> serviceList = entry.getValue();
                    boolean hasMaintain = serviceList.stream().anyMatch(compChargeUnitV2TO -> reportType2MaintainStatusMap.getOrDefault(compChargeUnitV2TO.getReportType(), false));
                    return new CompChargeUnitWithGroupTO(entry.getKey(), hasMaintain ? CompChargeUnitWithGroupTO.SERVICE_N : CompChargeUnitWithGroupTO.SERVICE_Y, serviceList);
                })
                .sorted(Comparator.comparingInt(o -> {
                    ReportPurchaseGroupEnum enumItem = ReportPurchaseGroupEnum.getByName(o.getServiceGroup());
                    return enumItem != null ? enumItem.ordinal() : Integer.MAX_VALUE;
                }))
                .collect(Collectors.toList());
    }

    private Map<String, Boolean> getReportType2MaintainStatusMap(Collection<String> reportTypes) {
        if (CollectionUtils.isEmpty(reportTypes)) {
            return new HashMap<>();
        }
        Map<String, String> reportType2GoodsIdMap = new HashMap<>();
        reportTypes.forEach(reportType -> reportType2GoodsIdMap.put(reportType, MaintainGoodsIdEnum.getGoodsIdByReportType(reportType)));
        Set<String> goodsIds = reportType2GoodsIdMap.values().stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, Boolean> goodsId2MaintainStatus = new HashMap<>();
        if (CollectionUtils.isNotEmpty(goodsIds)) {
            goodsIds.forEach(goodsId -> {
                OverseaMaintainStatusTO overseaTainStatus = GlobalCompanyDetailsInterface.getOverseaTainStatus(goodsId);
                goodsId2MaintainStatus.put(goodsId, MappingUtils.getValue(overseaTainStatus, OverseaMaintainStatusTO::getMainTain));
            });
        }

        Map<String, Boolean> reportType2MaintainStatusMap = new HashMap<>();
        reportType2GoodsIdMap.forEach((reportType, goodsId) -> {
            reportType2MaintainStatusMap.put(reportType, goodsId2MaintainStatus.getOrDefault(goodsId, false));
        });
        return reportType2MaintainStatusMap;
    }

    private void handleServiceWithGroup4Hk(String keyNo, List<CompChargeUnitWithGroupTO> serviceWithGroupList) {
        try {
            List<CompChargeUnitWithGroupTO> serviceWithGroupAndServiceYList = serviceWithGroupList.stream()
                    .filter(serviceWithGroup -> CompChargeUnitWithGroupTO.SERVICE_Y.equals(serviceWithGroup.getCanOrder()))
                    .collect(Collectors.toList());
            List<CompChargeUnitV2TO> compChargeUnits = MappingUtils.flattenAndGetList(serviceWithGroupAndServiceYList, CompChargeUnitWithGroupTO::getServiceList);
            if (CollectionUtils.isEmpty(compChargeUnits)) {
                return;
            }
            boolean notInWorkingHours = sysConfigCacheService.isNotInWorkingHours(LocalDateTime.now());

            // HkIrd的商品 begin
            List<CompChargeUnitV2TO> compChargeUnitsOfHkIrd = compChargeUnits.stream().filter(compChargeUnitV2TO -> ReportTypeEnum.hkIrdCorpList().contains(compChargeUnitV2TO.getReportType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(compChargeUnitsOfHkIrd)) {
                compChargeUnitsOfHkIrd.forEach(compChargeUnitV2TO -> {
                    compChargeUnitV2TO.setDisabledTips("This report is not available on the Inland Revenue Department.");
                    compChargeUnitV2TO.setCanOrder(CompChargeUnitWithGroupTO.COMPANY_P);

                    if (ReportTypeEnum.hkIrdCorpList().contains(compChargeUnitV2TO.getReportType()) && notInWorkingHours) {
                        compChargeUnitV2TO.setProvideTime("Delivery at 9 AM (UTC+8)");
                        compChargeUnitV2TO.setWorkdayFlag(Constants.NO);
                    }
                });
                // 触发商品刷新
                hkIrdService.refreshIrdGoods(keyNo);
            }
            // HkIrd的商品 end

            // HkIcris商品处理 begin
            List<CompChargeUnitV2TO> compChargeUnitsOfHkIcris = compChargeUnits.stream().filter(compChargeUnitV2TO -> ReportTypeEnum.hkIcrisCorpList().contains(compChargeUnitV2TO.getReportType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(compChargeUnitsOfHkIcris)) {
                HkCorpBasicDetailTO detail = qccOvsBasicService.getHkCorpBasicDetailTO(keyNo);
                if (detail == null) {
                    serviceWithGroupList.removeIf(next ->
                            StringUtils.equalsAny(next.getServiceGroup(), ReportPurchaseGroupEnum.KNOW_YOUR_CUSTOMER.getName(), ReportPurchaseGroupEnum.OFFICIAL_DOCUMENT.getName()));
                    return;
                }
                JsonResultList<HongKongAnnouncementTO> hkAnnouncementPage = IntranetInterface.getHkAnnouncementPage(keyNo, "1", "1000");
                List<HongKongAnnouncementTO> hkAnnouncements = hkAnnouncementPage.getResultList();
                boolean companyCanOrder = CommTblCompReportOrderService.companyCanOrder(hkAnnouncements, detail);
                String companyTypeEn = detail.getCompanyTypeEn();
                Set<String> anTypeList = IntranetInterface.listAnnualOrApplicationType(hkAnnouncements);
                log.info(String.format("keyNo:%s, 公告类型有：%s", keyNo, StringUtils.join(anTypeList, ",")));
                boolean basicArFlag = StringUtils.isNotBlank(companyTypeEn) && !HkCompanyTypeEnum.COMPANY_TYPE7.getCompanyTypeEn().equals(companyTypeEn) && CollectionUtils.isNotEmpty(anTypeList);
                for (CompChargeUnitV2TO chargeUnitNewVersionTO : compChargeUnitsOfHkIcris) {// added for v2.2.2 fengsw KNZT-6921 香港文档下单不需要做多余判断，直接是可用的
                    chargeUnitNewVersionTO.setDisabledTips("This report is not available on the company registry.");
                    if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(chargeUnitNewVersionTO.getReportType())) {
                        chargeUnitNewVersionTO.setCanOrder("companyY");
                        continue;
                    }
                    chargeUnitNewVersionTO.setCanOrder(companyCanOrder ? CompChargeUnitWithGroupTO.COMPANY_Y : CompChargeUnitWithGroupTO.COMPANY_N);
                    if (!companyCanOrder || (!basicArFlag && ReportTypeEnum.HK_BAS_AR.getCode().equals(chargeUnitNewVersionTO.getReportType()))) {
                        chargeUnitNewVersionTO.setCanOrder(CompChargeUnitWithGroupTO.COMPANY_N);
                    }
                    if (ReportTypeEnum.HK_BAS_AR.getCode().equals(chargeUnitNewVersionTO.getReportType()) && notInWorkingHours) {
                        chargeUnitNewVersionTO.setProvideTime("Delivery at 9 AM (UTC+8)");
                        chargeUnitNewVersionTO.setWorkdayFlag(Constants.NO);
                    }
                }
            }
            // HkIcris商品处理 end
        } catch (Exception e) {
            log.error("Error occurred while handling Hong Kong report", e);
        }
    }

    private void handleServiceWithGroup4My(String keyNo, List<CompChargeUnitWithGroupTO> serviceWithGroupList) {
        try {
            List<CompChargeUnitWithGroupTO> serviceWithGroupAndServiceYList = serviceWithGroupList.stream()
                    .filter(serviceWithGroup -> CompChargeUnitWithGroupTO.SERVICE_Y.equals(serviceWithGroup.getCanOrder()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serviceWithGroupAndServiceYList)) {
                boolean companyCanOrder = orderService.companyCanOrder4My(keyNo);
                MappingUtils.flattenAndGetList(serviceWithGroupAndServiceYList, CompChargeUnitWithGroupTO::getServiceList).forEach(chargeUnit -> {
                    chargeUnit.setCanOrder(companyCanOrder ? CompChargeUnitWithGroupTO.COMPANY_Y : CompChargeUnitWithGroupTO.COMPANY_N);
                    chargeUnit.setDisabledTips("This report is not available");
                });
            }
        } catch (Exception e) {
            log.error("Error occurred in handleServiceWithGroup", e);
        }
    }

    private void handleServiceWithGroup4Sg(String keyNo, List<CompChargeUnitWithGroupTO> serviceWithGroupList) {
        try {
            List<CompChargeUnitWithGroupTO> serviceWithGroupAndServiceYList = serviceWithGroupList.stream()
                    .filter(serviceWithGroup -> CompChargeUnitWithGroupTO.SERVICE_Y.equals(serviceWithGroup.getCanOrder()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serviceWithGroupAndServiceYList)) {
                boolean companyCanOrder = orderService.companyCanOrder4Sg(keyNo);
                boolean notInWorkingHours = sysConfigCacheService.isNotInWorkingHours4Sg(LocalDateTime.now());// added for v2.3.3 fengsw KNZT-8350
                MappingUtils.flattenAndGetList(serviceWithGroupAndServiceYList, CompChargeUnitWithGroupTO::getServiceList).forEach(chargeUnit -> {
                    chargeUnit.setCanOrder(companyCanOrder ? CompChargeUnitWithGroupTO.COMPANY_Y : CompChargeUnitWithGroupTO.COMPANY_N);
                    chargeUnit.setDisabledTips("This report is not available on the Accounting and Corporate Regulatory Authority");
                    if (notInWorkingHours) {
                        chargeUnit.setProvideTime("Delivery after 9:00 AM (UTC+8) on the next business day");
                        chargeUnit.setWorkdayFlag(Constants.NO);
                    }
                });
            }
        } catch (Exception e) {
            log.error("Error occurred in handleServiceWithGroup", e);
        }
    }

    private void handleServiceWithGroup4Tw(String keyNo, List<CompChargeUnitWithGroupTO> serviceWithGroupList) {
        try {
            List<CompChargeUnitWithGroupTO> serviceWithGroupAndServiceYList = serviceWithGroupList.stream()
                    .filter(serviceWithGroup -> CompChargeUnitWithGroupTO.SERVICE_Y.equals(serviceWithGroup.getCanOrder()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serviceWithGroupAndServiceYList)) {
                QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
                boolean companyCanOrder = orderService.companyCanOrder4Tw(qccOvsBasic);
                MappingUtils.flattenAndGetList(serviceWithGroupAndServiceYList, CompChargeUnitWithGroupTO::getServiceList).forEach(chargeUnit -> {
                    chargeUnit.setCanOrder(companyCanOrder ? CompChargeUnitWithGroupTO.COMPANY_Y : CompChargeUnitWithGroupTO.COMPANY_N);
                    chargeUnit.setDisabledTips("This report is not available");

                    QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
                    String compClassDetail = MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompClassDetail);
                    if (chargeUnit.isActiveFlag()) {
                        if (StringUtils.equals(compClassDetail, "Company")) {
                            chargeUnit.setIntro(Lists.newArrayList("Company | Firmographics", "Contact Information", "Shareholding Structure (Only Disclosed Significant Shareholders)", "Officers", "Stock Information", "History Information"));
                        } else if (StringUtils.equals(compClassDetail, "Business")) {
                            chargeUnit.setIntro(Lists.newArrayList("Business | Firmographics", "Contact Information", "Shareholding Structure (Only Disclosed Significant Shareholders)", "Officers"));
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.error("Error occurred in handleServiceWithGroup", e);
        }
    }

    private void handleServiceWithGroup4Cn(String keyNo, List<CompChargeUnitWithGroupTO> serviceWithGroupList) {
        try {
            MappingUtils.flattenAndGetList(serviceWithGroupList, CompChargeUnitWithGroupTO::getServiceList)
                    .stream().filter(compChargeUnitV2TO -> ReportTypeEnum.FIN_TAX.getCode().equals(compChargeUnitV2TO.getReportType()))
                    .findFirst().ifPresent(compChargeUnitV2TO -> {
                        try {
                            ApiGlobalCorpDetailTO source = CompanyDetailsInterface.getECIInfoVerifyInfo4Global(keyNo);
                            if (Objects.nonNull(source) && StringUtils.equals(source.getEntType(), "General")
                                    && StringUtils.isNotBlank(source.getCreditCode())) {
                                compChargeUnitV2TO.setCanOrder(CompChargeUnitWithGroupTO.COMPANY_Y);
                            } else {
                                compChargeUnitV2TO.setCanOrder(CompChargeUnitWithGroupTO.COMPANY_N);
                                compChargeUnitV2TO.setDisabledTips("The Financial & Tax report of this entity you currently selected is not available. Please select other types of reports.");
                            }
                        } catch (Exception e) {
                            log.error("Error occurred in handleServiceWithGroup", e);
                        }
                    });
        } catch (Exception e) {
            log.error("Error occurred in handleServiceWithGroup", e);
        }
    }

    /**
     * 校验生日格式
     * 1. 年份格式 YYYY 
     * 2. 完整日期格式 YYYYMMDD
     * added for lvcy v2.0.9 KNZT-5362
     * 
     * @param birthday
     * @throws MessageException
     */
    private void validateBirthday(Integer birthday) throws MessageException {
        String birthdayStr = String.valueOf(birthday);
        
        // 校验年份格式 YYYY
        if (birthdayStr.length() == 4) {
            int year = Integer.parseInt(birthdayStr);
            MsgExceptionUtils.failBuild(year > Calendar.getInstance().get(Calendar.YEAR), "msg:Please enter the correct format");
            return;
        }
        
        // 校验完整日期格式 YYYYMMDD
        if (birthdayStr.length() == 8) {
            try {
                int year = Integer.parseInt(birthdayStr.substring(0,4));
                int month = Integer.parseInt(birthdayStr.substring(4,6));
                int day = Integer.parseInt(birthdayStr.substring(6));
                
                Calendar cal = Calendar.getInstance();
                cal.setLenient(false);
                cal.set(year, month-1, day);
                cal.getTime(); // 会抛出异常如果日期无效
                return;
            } catch (Exception e) {
                log.info("birthday format failed, str:{}, ex:{}", birthday, e);
                throw new MessageException("msg:Please enter the correct format");
            }
        }
        
        throw new MessageException("msg:Please enter the correct format");
    }

    /**
     * 判断是否存在查册人信息
     * 
     * @return
     */
    public Boolean checkHkSearcherInfoExists() {
        return commSysCompanyService.existsExtColByType(Constants.CompInfoColExt.HkSearcherInfo.EXT_TYPE);
    }

    /**
     * added for v2.1.6 fengsw KNZT-6513
     * 将订单（只有状态为退款-RE、已取消-F）置为不可见（用户端逻辑删除）      *
     * @param orderNoList
     */
    public void updateOrderInvisible4User(List<String> orderNoList) {
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            String userId = UserUtils.getUserId();
            log.info("user:{} logic delete orders: {}", userId, String.join(",", orderNoList));
            orderService.updateOrderInvisible4User(userId, orderNoList);
        }
    }

    /**
     * 重复订单校验 added for v2.1.8 fengsw KNZT-6879
     * 
     * @param orderList
     * @return
     * @throws MessageException
     */
    public List<OrderDuplicateCheckResultTO> checkDuplicateOrder(List<OrderDuplicateCheckResultTO> orderList) throws MessageException {
        List<OrderDuplicateCheckResultTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderList)) {
            return result;
        }

        // 获取公司信息
        String companyId = UserUtils.getUserCompanyId();
        Company company = commSysCompanyService.get(companyId);
        if (company == null) {
            log.warn("invalid param companyId id:" + companyId);
            throw new MessageException("err.param.invalid");
        }

        // 检查是否开启重复订单提醒配置 未开启默认不提醒
        boolean duplicateOrderRemind = commSysCompanyService.getConfigDuplicateOrderRemind(companyId);
        if (!duplicateOrderRemind) {
            return new ArrayList<>();
        }


        // 获取用户可见范围
        UserAccessScopeTO userAccessScope = ruleBusinessService.getUserAccessScope(UserUtils.getUserId());

        // 计算30天前的时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DATE, -30);
        Date minOrderCreatedDate = cal.getTime();

        // 将订单按个人和企业分组
        Map<Boolean, List<OrderDuplicateCheckResultTO>> orderMap = orderList.stream()
                .collect(Collectors.groupingBy(item -> ReportGroupMappingEnum.PERS_G.getRptGroup().equals(item.getReportGroup())));

        // 提取个人和企业的KeyNo集合
        Set<String> keyNoSet = Stream.concat(
                Optional.ofNullable(orderMap.get(true)).orElse(Collections.emptyList()).stream().map(OrderDuplicateCheckResultTO::getPersKeyNo),
                Optional.ofNullable(orderMap.get(false)).orElse(Collections.emptyList()).stream().map(OrderDuplicateCheckResultTO::getCorpKeyNo)
        ).collect(Collectors.toSet());

        // 查询重复订单
        List<OrderDistributionResultTO> queryResultList = orderService.listDuplicateOrder(companyId, new ArrayList<>(keyNoSet), userAccessScope.getUserIdList(), minOrderCreatedDate, new Date());

        // 构建查询结果映射，方便快速查找
        Map<String, List<OrderDistributionResultTO>> queryResultMap = queryResultList.stream()
                .collect(Collectors.groupingBy(OrderDistributionResultTO::getKeyNo, Collectors.toList()));

        // 检查每个订单是否重复，并设置结果
        for (OrderDuplicateCheckResultTO order : orderList) {
            String keyNoType = ReportGroupMappingEnum.getKeyNoTypeByReportGroup(order.getReportGroup());
            String keyNo = Constants.Report.KEY_NO_TYPE_CORP.equals(keyNoType) ? order.getCorpKeyNo() : order.getPersKeyNo();
            if (queryResultMap.containsKey(keyNo) && isDuplicateOrder(order.getReportType(), order.getDocNumber(), queryResultMap.get(keyNo))) {
                order.setKeyNoType(keyNoType);
                result.add(order);
            }
        }
        return result;
    }


    /**
     * 范围仅限于KYC报告，不支持制裁名单扫描与监控池等其他模块
     * 用户下财税时，30天内下过财税，则需要重复提醒，如重复需要给出提醒；
     * 用户下非财税报告时，30天内下过非财税报告，则需要重复提醒
     * 一、购买Official Documents商品 校验如下：
     * Official Documents中完全一样的文档（文档编号一致）
     * KYC Basic + Annual Return/Incorporation Form中完全一样的文档（文档编号一致）
     * 二、购买KYC Basic 校验如下：
     * ▪KYC Basic
     * ▪KYC Basic + Annual Return/Incorporation Form
     * 三、购买KYC Basic + Annual Return/Incorporation Form 校验如下：
     * ▪KYC Basic
     * ▪KYC Basic + Annual Return/Incorporation Form
     * ▪Official Documents中完全一样的文档（文档编号一致）
     * 其他类型报告，则判断是否存在除香港报告以外类型的数据
     *
     * @param reportType
     * @param docNumber
     * @param reportTypes
     * @return
     */
    private boolean isDuplicateOrder(String reportType, String docNumber, List<OrderDistributionResultTO> reportTypes) {
        boolean isFinTax = ReportTypeEnum.FIN_TAX.getCode().equals(reportType);
        boolean isKYC = ReportTypeEnum.getKYCReportList().contains(reportType);
        if (isFinTax) { // 如果当前是财税报告 则判断库里是否存在财税报告
            return reportTypes.stream().anyMatch(item -> reportType.equals(item.getReportType()));
        } else if (isKYC) { // 如果是除财税以外的报告，则判断库里是否存在 非财税类型的报告 且不是图谱产品
            // 如果是basic|basic + AR 则判断库里是否有basic+ar|basic 的订单
            // 如果是hk_docment 则判断库里是否有docNumber一致的订单
            List<OrderDistributionResultTO> nonFinTaxReport = reportTypes.stream()
                    .filter(item -> !ReportTypeEnum.getMapList().contains(item.getReportType()) && !ReportTypeEnum.FIN_TAX.getCode().equals(item.getReportType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(nonFinTaxReport)) return false;
            if (ReportTypeEnum.HK_BAS.getCode().equals(reportType) || ReportTypeEnum.HK_BAS_AR.getCode().equals(reportType)) {
                return nonFinTaxReport.stream().anyMatch(item -> StringUtils.equalsAny(item.getReportType(), ReportTypeEnum.HK_BAS.getCode(), ReportTypeEnum.HK_BAS_AR.getCode()));
            } else if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(reportType)) {
                return nonFinTaxReport.stream().anyMatch(item -> (StringUtils.equals(docNumber, item.getDocNumber()) && (StringUtils.equalsAny(item.getReportType(), ReportTypeEnum.HK_BAS_AR.getCode(), ReportTypeEnum.HK_DOCUMENT.getCode()))));
            } else {
                return nonFinTaxReport.stream().anyMatch(item -> !ReportTypeEnum.hkIcrisCorpList().contains(item.getReportType()));
            }
        }
        return false;
    }

    public List<ReportOrderInfoOfBillTO> listDuplicateOrderWithDateRange(TblCompReportOrderDuplicateForm form) throws MessageException {
        String companyId = UserUtils.getUserCompanyId();
        Company company = commSysCompanyService.get(companyId);
        if (company == null) {
            log.warn("invalid param companyId id:" + companyId);
            throw new MessageException("err.param.invalid");
        }
      
        // 获取用户可见范围
        UserAccessScopeTO userAccessScope = ruleBusinessService.getUserAccessScope(UserUtils.getUserId());
        form.setAccessUserIdList(userAccessScope.getUserIdList());

        if (CollectionUtils.isEmpty(form.getKeyNoList())) {
            MsgExceptionUtils.checkIsNull(form.getReportGroup(), "err.param.invalid");
            String keyNoType = ReportGroupMappingEnum.getKeyNoTypeByReportGroup(form.getReportGroup());
            if (Constants.Report.KEY_NO_TYPE_CORP.equals(keyNoType)) {
                if (StringUtils.isBlank(form.getCorpKeyNo())) {
                    log.warn("CorpKeyNo is required");
                    throw new MessageException("err.access");
                }
                if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(form.getReportType())) {
                    if (StringUtils.isBlank(form.getDocNumber())) {
                        log.warn("HK DocNumber is required");
                        throw new MessageException("err.access");
                    }
                }
            } else {
                if (StringUtils.isBlank(form.getPersKeyNo())) {
                    log.warn("PersKeyNo is required");
                    throw new MessageException("err.access");
                }
            }
        }
        Calendar cal = Calendar.getInstance();
        Date date = new Date();
        cal.setTime(date);
        cal.add(Calendar.DATE, -30); //获取当前时间前30天
        form.setExcludeInvalid(1);
        form.setMinOrderCreatedDate(cal.getTime());
        form.setMaxOrderCreatedDate(DateUtils.getCurrentDate());
        boolean isFinTax = StringUtils.equals(ReportTypeEnum.FIN_TAX.getCode(), form.getReportType());
        form.setOnlyFinTax(isFinTax ? 1 : 0);
        form.setExcludeFinTax(!isFinTax ? 1 : 0);
        // updated for v2.2.2 KNZT-7314 查询重复订单 排除指定类型 当前只排除图谱的，后续可能会有别的
        List<TblCompReportOrder> list = orderService.listDuplicateOrderWithDateRangeV2(companyId, form, ReportTypeEnum.getMapList());
        List<ReportOrderInfoOfBillTO> orderTos = ReportOrderInfoOfBillTO.build(list);
        fillOrderTos(orderTos);
        return orderTos;
    }

    public void fillOrderTos(List<ReportOrderInfoOfBillTO> tos) {
        if (CollectionUtils.isEmpty(tos)) {
            return;
        }
        List<String> hkDocIdList = tos.stream().filter(item -> ReportTypeEnum.HK_DOCUMENT.getCode().equals(item.getReportType())).map(ReportOrderInfoOfBillTO::getId).collect(Collectors.toList());
        Map<String, TblCompReportOrderHkDoc> hkDocMap = hkDocService.batchGetHkDocMap(hkDocIdList, OrderHkDocRelTypeEnum.ORDER.getCode());

        orderService.fillDocumentInfo4ApiOrder(tos);
        for (ReportOrderInfoOfBillTO orderTo : tos) {
            List<ReportOrderInfoOfBillTO.Action> actions = new ArrayList<>();
            CollectionUtils.addIgnoreNull(actions, CommTblCompReportOrderService.buildActionDownloadIfNecessary(orderTo));
            orderTo.setActions(actions);
            orderTo.setRptStatusDesc(OrderStatusEnum.getDescEnByCode(orderTo.getRptStatus()));
            if (ReportTypeEnum.persTypeList().contains(orderTo.getReportType())) {
                orderTo.setCorpJurisdiction(null);
                orderTo.setUnitGroup(null);
            }
            orderTo.setIsSelf(UserUtils.getUserLoginName().equals(orderTo.getLoginName()) ? 1 : 0);
            orderTo.setCorpStatus(null);
            orderTo.setCorpStatusCheck(null);
            // 香港文档订单，设置香港文档全称
            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(orderTo.getReportType())) {
                TblCompReportOrderHkDoc hkDoc = hkDocMap.get(orderTo.getId());
                if (Objects.nonNull(hkDoc)) {
                    orderTo.setDocNumber(hkDoc.getDocNumber());
                    orderTo.setDocTitle(hkDoc.getDocTitle());
                    orderTo.setDocTitleEn(hkDoc.getDocTitleEn());
                    orderTo.setDocYear(hkDoc.getDocYear());
                    orderTo.setDocDate(hkDoc.getDocDate());
                }
            }
        }
    }

    /**
     * 订单页面生成倒计时
     * 
     * @return
     */
    public OrderGenerateTimeTO generateOrderCountdown() throws MessageException {
        // 获取用户ID
        String userId = UserUtils.getUserId();
        // 生成key，前20位为用户ID，后部分为随机数
        String key = StringUtils.left(userId, 20) + StringUtils.generateNo();
        // 获取当前时间戳
        String timespan = String.valueOf(new Date().getTime());
        // 记录日志
        log.info("userId:{}, order page key:{}, timespan:{}", userId, key, timespan);
        // 将key和timespan存入redis，有效期为10分钟
        redisService.saveString(Constants.RedisKey.ORDER_PAGE_TIME, key, timespan, 10 * 60);
        // 返回OrderGenerateTimeTO对象，包含key和timespan
        return new OrderGenerateTimeTO(key, timespan);
    }

    public Long queryOrderCountdown(String key) throws MessageException {
        String timespan = redisService.getStringById(Constants.RedisKey.ORDER_PAGE_TIME, key);
        log.info("order page key:{}, timespan:{}", key, timespan);
        if(StringUtils.isNotBlank(timespan)){
            return Long.parseLong(timespan);
        }
        return null;
    }


    public void clearReport(String orderId) throws MessageException {
        TblCompReportOrder order =  orderService.get(orderId);
        if (order == null) {
            log.warn("invalid param orderId id:" + orderId);
            return;
        }
        if (!ReportTypeEnum.getVerifyList().contains(order.getReportType())) {
            log.info("only verify order can be cleared, orderId:{}", orderId);
            return;
        }
        if (StringUtils.isBlank(order.getUrl())) {
            log.info("order url is blank, orderId:{}", orderId);
            return;
        }
        redisService.deleteById(Constants.RedisKey.VERIFY_REQ, order.getOrderNo());
        String url = order.getUrl();
        HuaweiObsServUtils.getInstance().deleteObjectByUrl(url);
        order.setUrl(null);
        orderService.save(order);
    }


    private void saveEncryptedVerifyForm(String orderNo, EncryptedForm form) throws MessageException {
        if (StringUtils.isNotBlank(form.getD())) {
            form.setD(form.getD().substring(4));
        }
        if (StringUtils.isNotBlank(form.getE())) {
            form.setE(form.getE().substring(4));
        }
        if (StringUtils.isNotBlank(form.getO())) {
            form.setO(form.getO().substring(4));
        }
        if (StringUtils.isNotBlank(form.getCo())) {
            form.setCo(form.getCo().substring(4));
        }
        redisService.saveObject(Constants.RedisKey.VERIFY_REQ, orderNo, form, 48 * 60 * 60);
    }

    public void refreshHKAnnouncement(String keyNo) throws MessageException {
        String documentUpdateDate = ovsQccOvsBasicService.getDocumentUpdateDate(keyNo);
        String nowDate = DateUtils.getDate();
        if (StringUtils.equals(nowDate, documentUpdateDate)) {
            log.warn("公告列表当天已刷新, keyNo: " + keyNo);
            return;
        }
        String timestamp = redisService.getStringById(Constants.RedisKey.HK_ANNOUNCEMENT_LIST_REFRESH, keyNo);
        if (StringUtils.isNotBlank(timestamp)) {
            log.warn("公告列表正在刷新中, keyNo: " + keyNo);
            return;
        }
        // 5分钟内不能重复刷新，设定5分钟底层数据处理完成
        redisService.saveString(Constants.RedisKey.HK_ANNOUNCEMENT_LIST_REFRESH, keyNo, String.valueOf(System.currentTimeMillis()), 5 * 60);
        overseaKafkaService.sendMessage4RefreshHkReportList(keyNo);
    }

    private JsonResult<OrderSubmitResult> executeAfterMapOrderSubmit(MapReportOrderSubmitForm form, OrderSubmitResult submitResult) throws MessageException {
        String reportType = form.getOrderInfoList().get(0).getReportType();
        String userId = UserUtils.getUserId();

        // 保存图谱节点记录
        List<TblCompReportOrderMap> mapList;
        if (ReportTypeEnum.isRelationMap(reportType)) {
            String orderNo = submitResult.getSubmitSuccessList().get(0).getOrderNo();
            mapList = form.getRelationNodeKeyNoList().stream()
                    .map(nodeKeyNo -> TblCompReportOrderMap.buildRelation(orderNo, nodeKeyNo, userId))
                    .collect(Collectors.toList());
        } else {
            String mapNo = form.getOrderInfoList().get(0).getMapNo();
            mapList = submitResult.getSubmitSuccessList().stream()
                    .map(order -> TblCompReportOrderMap.buildOrder(mapNo, order.getMapNodeNo(),
                            order.getOrderNo(), order.getKeyNo(), userId))
                    .collect(Collectors.toList());
        }
        commTblCompReportOrderMapService.batchInsert(mapList);

        // 更新图谱
        JsonResult<OrderSubmitResult> resp = JsonResult.buildSuccess(submitResult);
        String mapNo = form.getOrderInfoList().get(0).getMapNo();
        if (StringUtils.isNotBlank(form.getUpdateMapId()) && StringUtils.isNotBlank(mapNo)) {
            List<String> syncKeyNoList = form.getOrderInfoList().stream().map(OrderSubmitTO::getKeyNo).collect(Collectors.toList());
            String syncMapResp = DataMapInterface.syncMap(form.getUpdateMapId(), mapNo, syncKeyNoList);
            resp.setJsonStr(syncMapResp);
        }
        return resp;
    }
}
