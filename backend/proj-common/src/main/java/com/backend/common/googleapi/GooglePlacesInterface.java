package com.backend.common.googleapi;

import com.alibaba.fastjson.JSONObject;
import com.backend.common.googleapi.model.PlacesAutoCompleteResp;
import com.backend.common.googleapi.model.PlacesAutoCompleteTO;
import com.backend.common.googleapi.model.PlacesDetailResp;
import com.backend.common.googleapi.model.PlacesDetailTO;
import com.google.common.collect.Lists;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.HttpUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.model.LimitConfig;
import com.qcc.frame.jee.modules.sys.service.RateLimitUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GooglePlacesInterface {

    private static final Logger logger = LoggerFactory.getLogger(GooglePlacesInterface.class);
    private static final String BASE_URL = "https://places.googleapis.com";
    private static final String API_KEY = Global.getConfig("google.api.key");

    private static final RateLimitUtils rateLimitUtils = SpringContextHolder.getBean(RateLimitUtils.class);

    public static boolean checkLimit(String userId) {
        LimitConfig config = LimitConfig.forApi("google_places", userId)
                .setDailyLimit(100)
                .setAutoBlockOnWindowLimit(true)
                .setBlockSeconds(5 * 60);
        return rateLimitUtils.checkCombinedLimits(config);
    }

    public static List<PlacesAutoCompleteTO> placesAutoComplete(String input) throws MessageException {
        PlacesAutoCompleteResp resp = placesAutoCompleteApi(input);
        return PlacesAutoCompleteTO.buildFrom(resp);
    }

    public static PlacesDetailTO placesDetail(String placeId) throws MessageException {
        PlacesDetailResp resp = placesDetailApi(placeId);
        return buildFrom(resp);
    }

    /**
     * 将places detail Api数据结构化成国际版表单所需结构
     * 映射规则参考Google官方示例: <a href="https://console.cloud.google.com/google/maps-hosted/discover/address-autocomplete"/>
     */
    public static PlacesDetailTO buildFrom(PlacesDetailResp resp) {
        if (resp == null) {
            return null;
        }
        PlacesDetailTO to = new PlacesDetailTO();
        List<PlacesDetailResp.AddressComponent> addressComponents = MappingUtils.getValue(resp, PlacesDetailResp::getAddressComponents);

        String streetNumberVal = MappingUtils.getValue(extracted(addressComponents, "street_number"), PlacesDetailResp.AddressComponent::getLongText);
        String routeVal = MappingUtils.getValue(extracted(addressComponents, "route"), PlacesDetailResp.AddressComponent::getShortText);
        String addressLine1 = StringUtils.joinIgnoreNull(Lists.newArrayList(streetNumberVal, routeVal), " ");
        to.setAddressLine1(addressLine1);

        String postalCodeVal = MappingUtils.getValue(extracted(addressComponents, "postal_code"), PlacesDetailResp.AddressComponent::getLongText);
        String postalCodeSuffixVal = MappingUtils.getValue(extracted(addressComponents, "postal_code_suffix"), PlacesDetailResp.AddressComponent::getLongText);
        String postalCode = StringUtils.joinIgnoreNull(Lists.newArrayList(postalCodeVal, postalCodeSuffixVal), "-");
        to.setPostalCode(postalCode);

        String localityVal = MappingUtils.getValue(extracted(addressComponents, "locality"), PlacesDetailResp.AddressComponent::getLongText);
        to.setCity(localityVal);

        String administrativeAreaLevel1Val = MappingUtils.getValue(extracted(addressComponents, "administrative_area_level_1"), PlacesDetailResp.AddressComponent::getLongText);
        to.setProvince(administrativeAreaLevel1Val);

        String countryVal = MappingUtils.getValue(extracted(addressComponents, "country"), PlacesDetailResp.AddressComponent::getShortText);
        to.setCountry(countryVal);

        return to;
    }

    private static PlacesDetailResp.AddressComponent extracted(List<PlacesDetailResp.AddressComponent> addressComponents, String type) {
        if (CollectionUtils.isEmpty(addressComponents)) {
            return null;
        }
        return addressComponents.stream()
                .filter(addressComponent -> addressComponent.getTypes() != null && addressComponent.getTypes().contains(type))
                .findFirst().orElse(null);
    }

    /**
     * 官方文档: <a href="https://developers.google.cn/maps/documentation/places/web-service/place-autocomplete"/>
     */
    private static PlacesAutoCompleteResp placesAutoCompleteApi(String input) throws MessageException {
        MsgExceptionUtils.failBuildAndLogError(StringUtils.isBlank(input), logger, "input is required");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("input", input);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("X-Goog-Api-Key", API_KEY);
        headerMap.put("X-Goog-FieldMask", "suggestions.placePrediction.placeId,suggestions.placePrediction.text.text");
        String url = BASE_URL + "/v1/places:autocomplete";
        String response = HttpUtils.postJson(url, paramMap, headerMap, HttpUtils.HTTP_CLIENT_TIMEOUT_10000);
        JSONObject responseJo = JsonUtils.parseObject(response);
        if (responseJo == null) {
            return null;
        }
        return responseJo.toJavaObject(PlacesAutoCompleteResp.class);
    }

    /**
     * 官方文档: <a href="https://developers.google.cn/maps/documentation/places/web-service/place-details"/>
     */
    private static PlacesDetailResp placesDetailApi(String placeId) throws MessageException {
        MsgExceptionUtils.failBuildAndLogError(StringUtils.isBlank(placeId), logger, "placeId is required");
        Map<String, String> paramMap = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("X-Goog-Api-Key", API_KEY);
        headerMap.put("X-Goog-FieldMask", "id,formattedAddress,addressComponents");
        String url = BASE_URL + "/v1/places/" + placeId;
        String response = HttpUtils.get(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_10000, headerMap);
        JSONObject responseJo = JsonUtils.parseObject(response);
        if (responseJo == null) {
            return null;
        }
        return responseJo.toJavaObject(PlacesDetailResp.class);
    }
}
