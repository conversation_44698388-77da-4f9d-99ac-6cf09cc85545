package com.backend.common.modules.report_data.corp.oversea;

import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.report.model.HkOriginalOrderInfo;
import com.backend.common.modules.report.model.OrderData4LoopResult;
import com.backend.common.modules.report_data.corp.oversea.base.AbstractReportDataService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.backend.common.yunjuapi.GlobalCompanyDetailsInterface;
import com.backend.common.yunjuapi.IntranetInterface;
import com.backend.common.yunjuapi.model.hk.basic.annual.HkCorpInfoBasicAnnualResult;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.translation.GlobalAreaTranslationUtil;
import com.qcc.frame.jee.modules.sys.translation.HkIndustryTranslationUtil;
import com.qcc.frame.jee.modules.sys.translation.HkTranslationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;

@Service
public class HkBasicArReportDataService extends AbstractReportDataService<HkCorpInfoBasicAnnualResult> {

    private static final Logger logger = LoggerFactory.getLogger(HkBasicArReportDataService.class);
    @Autowired
    private SysConfigCacheService sysConfigCacheService;

    @Override
    public List<ReportTypeEnum> getReportTypes() {
        return Lists.newArrayList(ReportTypeEnum.HK_BAS_AR);
    }

    @Override
    public boolean isDataSuccess(ReportDataGetResultForm form) {
        if (super.doCheckOrderDataSuccess(form)) {
            return true;
        }
        HkCorpInfoBasicAnnualResult data = getData(form);
        Integer verified = Optional.ofNullable(data)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData)
                .map(HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getVerified)
                .orElse(null);
        return verified != null && verified == 1;
    }

    @Override
    protected BiConsumer<OrderData4LoopResult, HkCorpInfoBasicAnnualResult> getPropSetter() {
        return OrderData4LoopResult::setHkBasicAnnualData;
    }

    @Override
    protected HkCorpInfoBasicAnnualResult getData(ReportDataGetResultForm form) {
        HkCorpInfoBasicAnnualResult hkCorpInfoBasicAnnualResult = GlobalCompanyDetailsInterface.getHKDataWithArByOrderNo(form.getApiOrderNo());
        fill4TO(hkCorpInfoBasicAnnualResult, form.getApiOrderNo(), form.getKeyNo(), form.getBankUser());
        return hkCorpInfoBasicAnnualResult;
    }

    @Override
    protected void doAfterInitOrderResult(OrderData4LoopResult result) {
        if (sysConfigCacheService.isNotInWorkingHours(LocalDateTime.now())) {
            result.setWorkdayFlag(Constants.NO);
        }
    }

    private void fill4TO(HkCorpInfoBasicAnnualResult realResult, String apiOrderNo, String keyNo, Boolean bankUser) {
        if (realResult != null && realResult.getData() != null) {
            decryptForHkBasicAr(realResult);
            // added for v1.9.0 KNZT-4164
            mappingShareClass(realResult);
            mappingDirectorCountry(realResult); // added for v1.9.1 KNZT-4162 报告里董事国家翻译处理
            mappingBusinessNature(realResult); // added for v1.9.1 KNZT-4258
            TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.HK);
            HkCorpInfoBasicAnnualResult.ResData data = MappingUtils.getValue(realResult, HkCorpInfoBasicAnnualResult::getData);
            HkCorpInfoBasicAnnualResult.ResData.Basic basic = MappingUtils.getValue(data, HkCorpInfoBasicAnnualResult.ResData::getBasic);
            transWrapper.add4Entity(basic, HkCorpInfoBasicAnnualResult.ResData.Basic::setCompanyNameChn, HkCorpInfoBasicAnnualResult.ResData.Basic::setCompanyNameEng, HkCorpInfoBasicAnnualResult.ResData.Basic::getCompanyNameChn, HkCorpInfoBasicAnnualResult.ResData.Basic::getCompanyNameEng, t -> keyNo, TransWrapper.ENTRY_TYPE_CORP)
                    .add4NonEntityWithOverwrite(basic, HkCorpInfoBasicAnnualResult.ResData.Basic::setAddress, HkCorpInfoBasicAnnualResult.ResData.Basic::getAddress);

            List<HkCorpInfoBasicAnnualResult.ResData.Basic.NameRecord> nameRecords = MappingUtils.getValue(basic, HkCorpInfoBasicAnnualResult.ResData.Basic::getNameRecordList);
            transWrapper.batchAdd4Entity(nameRecords, HkCorpInfoBasicAnnualResult.ResData.Basic.NameRecord::setName, HkCorpInfoBasicAnnualResult.ResData.Basic.NameRecord::setEnName, HkCorpInfoBasicAnnualResult.ResData.Basic.NameRecord::getName, HkCorpInfoBasicAnnualResult.ResData.Basic.NameRecord::getEnName, null, t -> TransWrapper.ENTRY_TYPE_CORP);

            HkCorpInfoBasicAnnualResult.ResData.RealTimeData realTimeData = MappingUtils.getValue(data, HkCorpInfoBasicAnnualResult.ResData::getRealTimeData);

            List<HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder> shareholders = MappingUtils.getValue(realTimeData, HkCorpInfoBasicAnnualResult.ResData.RealTimeData::getShareholders);
            transWrapper.batchAdd4Entity(shareholders, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder::setFullNameChn, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder::setFullNameEng, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder::getFullNameChn, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder::getFullNameEng, null, o -> detectEntityType4Trans(o.getType()))
                    .batchAdd4NonEntityWithOverwrite(shareholders, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder::setAddress, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Shareholder::getAddress);

            List<HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Director> directors = MappingUtils.getValue(realTimeData, HkCorpInfoBasicAnnualResult.ResData.RealTimeData::getDirectors);
            transWrapper.batchAdd4Entity(directors, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Director::setFullNameChn, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Director::setFullNameEng, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Director::getFullNameChn, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Director::getFullNameEng, null, o -> detectEntityType4Trans(o.getType()));

            List<HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary> companySecretaryList = MappingUtils.getValue(realTimeData, HkCorpInfoBasicAnnualResult.ResData.RealTimeData::getCompanySecretary);
            transWrapper.batchAdd4Entity(companySecretaryList, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary::setFullNameChn, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary::setFullNameEng, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary::getFullNameChn, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary::getFullNameEng, null, o -> detectEntityType4Trans(o.getType()))
                    .batchAdd4NonEntityWithOverwrite(companySecretaryList, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary::setAddress, HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary::getAddress);

            HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData annualReturnData = MappingUtils.getValue(data, HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData);
            List<HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder> arShareholders = MappingUtils.getValue(annualReturnData, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getShareholders);
            transWrapper.batchAdd4Entity(arShareholders, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder::setFullNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder::setFullNameEng, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder::getFullNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder::getFullNameEng, null, null)
                    .batchAdd4NonEntityWithOverwrite(arShareholders, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder::setAddress, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Shareholder::getAddress);

            List<HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director> arDirectors = MappingUtils.getValue(annualReturnData, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getDirectors);
            transWrapper.batchAdd4Entity(arDirectors, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director::setFullNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director::setFullNameEng, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director::getFullNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director::getFullNameEng, null, t -> detectEntityType4Trans(t.getType()))
                    .batchAdd4NonEntityWithOverwrite(arDirectors, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director::setAddress, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director::getAddress);

            List<HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary> arCompanySecretaryList = MappingUtils.getValue(annualReturnData, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getCompanySecretary);
            transWrapper.batchAdd4Entity(arCompanySecretaryList, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary::setFullNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary::setFullNameEng, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary::getFullNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary::getFullNameEng, null, t -> detectEntityType4Trans(t.getType()))
                    .batchAdd4NonEntityWithOverwrite(arCompanySecretaryList, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary::setAddress, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary::getAddress);

            HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference arPresentorReference = MappingUtils.getValue(annualReturnData, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getPresentorReference);
            transWrapper.add4Entity(arPresentorReference, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference::setNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference::setNameEng, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference::getNameChn, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference::getNameEng, null, TransWrapper.ENTRY_TYPE_CORP)
                    .add4NonEntityWithOverwrite(arPresentorReference, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference::setAddress, HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.PresentorReference::getAddress);

            commonDataService.enPostProcessor(transWrapper);
            fill4AnnualReturn(realResult, apiOrderNo);
        }
    }

    private static Integer detectEntityType4Trans(String entityType) {
        if (StringUtils.equals("自然人", entityType)) {
            return TransWrapper.ENTRY_TYPE_PERSON;
        } else if (StringUtils.equals("法人团体", entityType)) {
            return TransWrapper.ENTRY_TYPE_CORP;
        }
        return null;
    }

    // added for v1.7.8 KNZT-3320
    // updated for v1.9.1 KNZT-3885
    private void decryptForHkBasicAr(HkCorpInfoBasicAnnualResult result) {
//        String userKeyByCompanyId = OpenApiUserInfoInterface.getUserKeyByCompanyId();
        String userKeyByCompanyId = Global.getConfig("openApi.global.qcc.com.key");
        if (StringUtils.isBlank(userKeyByCompanyId)) {
            logger.error("userKeyByCompanyId获取失败");
            return;
        }
        if (result == null || result.getData() == null) {
            return;
        }
        // updated for v2.0.9 fensw KNZT-5608 解密香港basic+AR 实时数据维度 报告中董事和公司秘书的人员证件号数据
        decryptRealTimeData4BasicAr(result, userKeyByCompanyId);
        // updated for v1.8.7 KNZT-3965【bug】香港企业，法团表格场景下，董事中部分字段未返回，原始文件是有的
        if (result.getData().getAnnualReturnData() != null && result.getData().getAnnualReturnData().getVerified() != null && result.getData().getAnnualReturnData().getVerified() == 1) {
            result.getData().setArVerify("Y");
        } else {
            result.getData().setArVerify("N");
            return;
        }
        // updated for v2.0.9 fensw KNZT-5608 解密香港basic+AR 实时数据维度 报告中董事和公司秘书的人员证件号数据
        decryptAnnualReturnData4BasicAr(result, userKeyByCompanyId);
    }

    /**
     * 解密香港basic+AR 实时数据维度中董事和公司秘书的人员证件号数据
     *
     * @param result
     * @param userKeyByCompanyId
     */
    public void decryptRealTimeData4BasicAr(HkCorpInfoBasicAnnualResult result, String userKeyByCompanyId) {
        List<HkCorpInfoBasicAnnualResult.ResData.RealTimeData.Director> directors = Optional.ofNullable(result)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getRealTimeData)
                .map(HkCorpInfoBasicAnnualResult.ResData.RealTimeData::getDirectors)
                .orElse(null);
        HkBasicReportDataService.processIdentificationData(directors, userKeyByCompanyId);

        List<HkCorpInfoBasicAnnualResult.ResData.RealTimeData.CompanySecretary> companySecretaryList = Optional.ofNullable(result)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getRealTimeData)
                .map(HkCorpInfoBasicAnnualResult.ResData.RealTimeData::getCompanySecretary)
                .orElse(null);
        HkBasicReportDataService.processIdentificationData(companySecretaryList, userKeyByCompanyId);
    }

    /**
     * 解密香港basic+AR 报告中董事和公司秘书的人员证件号数据
     *
     * @param result
     * @param userKeyByCompanyId
     */
    public void decryptAnnualReturnData4BasicAr(HkCorpInfoBasicAnnualResult result, String userKeyByCompanyId) {
        List<HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.Director> directors = Optional.ofNullable(result)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData)
                .map(HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getDirectors)
                .orElse(null);
        HkBasicReportDataService.processIdentificationData(directors, userKeyByCompanyId);

        List<HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData.CompanySecretary> companySecretaryList = Optional.ofNullable(result)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData)
                .map(HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getCompanySecretary)
                .orElse(null);
        HkBasicReportDataService.processIdentificationData(companySecretaryList, userKeyByCompanyId);
    }

    // added for v1.9.0 KNZT-4164
    private static void mappingShareClass(HkCorpInfoBasicAnnualResult realResult) {
        Optional.ofNullable(realResult)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getRealTimeData)
                .map(HkCorpInfoBasicAnnualResult.ResData.RealTimeData::getShareholders)
                .ifPresent(shareholderList ->
                        shareholderList.forEach(shareholder -> shareholder.setClassofShares(HkTranslationUtil.mappingShareClass(shareholder.getClassofShares())))
                );
        Optional.ofNullable(realResult)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData)
                .ifPresent(annualReturnData -> {
                    Optional.ofNullable(annualReturnData.getShareCapital())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(shareCapitals -> shareCapitals.forEach(shareCapital ->
                                    shareCapital.setClassofShares(HkTranslationUtil.mappingShareClass(shareCapital.getClassofShares()))
                            ));
                    Optional.ofNullable(annualReturnData.getShareholders())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(shareholders -> shareholders.forEach(shareholder ->
                                    shareholder.setClassofShares(HkTranslationUtil.mappingShareClass(shareholder.getClassofShares()))
                            ));
                });
    }

    // added for v1.9.1 KNZT-4162
    private static void mappingDirectorCountry(HkCorpInfoBasicAnnualResult realResult) {
        Optional.ofNullable(realResult)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData)
                .map(HkCorpInfoBasicAnnualResult.ResData.AnnualReturnData::getDirectors)
                .ifPresent(directors ->
                        directors.forEach(director -> {
                            director.setPassportCountryOri(director.getPassportCountry());
                            director.setPassportCountry(GlobalAreaTranslationUtil.getShortNameEn4Hk(director.getPassportCountry()));
                        })
                );
    }

    // added for v1.9.1 KNZT-4258
    private static void mappingBusinessNature(HkCorpInfoBasicAnnualResult realResult) {
        Optional.ofNullable(realResult)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getBasic)
                .ifPresent(basic -> {
                    basic.setBusinessNatureDes(HkIndustryTranslationUtil.getIndustryEn(basic.getBusinessNatureCode(), basic.getBusinessNatureDes()));
                });
    }

    // added for v1.9.9 chenbl KNZT-5125
    private static void fill4AnnualReturn(HkCorpInfoBasicAnnualResult realResult, String apiOrderNo) {
        Optional.ofNullable(realResult)
                .map(HkCorpInfoBasicAnnualResult::getData)
                .map(HkCorpInfoBasicAnnualResult.ResData::getAnnualReturnData)
                .ifPresent(annualReturnData -> {
                    // 填充公告日期、年份
                    HkOriginalOrderInfo hkOriginalOrderInfo = IntranetInterface.getAnnouncementsInfoByOrderNo(apiOrderNo);
                    if (hkOriginalOrderInfo != null) {
                        annualReturnData.setDocumentDate(hkOriginalOrderInfo.getDocumentDate());
                        annualReturnData.setDocumentYear(hkOriginalOrderInfo.getDocumentYear());
                        annualReturnData.setDocumentType(hkOriginalOrderInfo.getDocumentType()); // added for v2.1.2 chenbl KNZT-6194
                    }
                });
    }

}
