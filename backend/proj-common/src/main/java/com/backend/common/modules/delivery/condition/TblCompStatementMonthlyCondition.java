package com.backend.common.modules.delivery.condition;

import java.util.List;

import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.qcc.frame.jee.base.AbstractCondition;

public class TblCompStatementMonthlyCondition extends AbstractCondition<TblCompStatementMonthly> {
    private String companyId;
    private List<String> payTypeListInOrCondition;
    private List<String> companyIdListInOrCondition;
    private String statementMonth;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public List<String> getPayTypeListInOrCondition() {
        return payTypeListInOrCondition;
    }

    public void setPayTypeListInOrCondition(List<String> payTypeListInOrCondition) {
        this.payTypeListInOrCondition = payTypeListInOrCondition;
    }

    public List<String> getCompanyIdListInOrCondition() {
        return companyIdListInOrCondition;
    }

    public void setCompanyIdListInOrCondition(List<String> companyIdListInOrCondition) {
        this.companyIdListInOrCondition = companyIdListInOrCondition;
    }

    public String getStatementMonth() {
        return statementMonth;
    }

    public void setStatementMonth(String statementMonth) {
        this.statementMonth = statementMonth;
    }
}
