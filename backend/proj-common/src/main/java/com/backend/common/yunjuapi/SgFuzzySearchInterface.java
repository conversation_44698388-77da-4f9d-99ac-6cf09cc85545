package com.backend.common.yunjuapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.search.CorpSearchService;
import com.backend.common.modules.search.SearchBaseService;
import com.backend.common.overseamongo.model.QccOvsBasicTO;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.backend.common.yunjuapi.model.ApiAutocompleteGlobalTO;
import com.backend.common.yunjuapi.model.ApiAutocompleteTOResult;
import com.backend.common.yunjuapi.model.Page;
import com.backend.common.yunjuapi.model.search.HitReason;
import com.backend.common.yunjuapi.model.search.OverseaCorpSearchApiTO;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.ApiErrorCodeEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.exception.MessageExceptionEnum;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class SgFuzzySearchInterface {

    protected static Logger logger = LoggerFactory.getLogger(SgFuzzySearchInterface.class);
    private static OvsQccOvsBasicService ovsQccOvsBasicService = SpringContextHolder.getBean(OvsQccOvsBasicService.class);
    private static CommonDataService commonDataService = SpringContextHolder.getBean(CommonDataService.class);

    /**
     * 查询结果标识 - 公开接口
     */
    public static final int SEARCH_RESULT_IND_OPEN = 1;
    /**
     * 查询结果标识 - 授权接口
     */
    public static final int SEARCH_RESULT_IND_AUTH = 2;

    public static ApiAutocompleteTOResult searchAdvance4AutocompleteWithImgReason4Global(String nationCode, String regPlace, String searchKey, String pageIndex, String pageSize) throws MessageException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("searchKey", searchKey);
        paramMap.put("nationCode", nationCode);
        paramMap.put("regPlace", regPlace);
        paramMap.put("pageIndex", pageIndex);
        paramMap.put("pageSize", pageSize);
        paramMap.put("isShowEm", true); // updated for v2.1.5 chenbl KNZT-6434
        ApiAutocompleteTOResult result = null;
        try {
            String resp = null;
            resp = SgYunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/ECIOs/OverseaSearch", "2", JSON.toJSONString(paramMap));
            logger.info("/ECIOs/OverseaSearch resp:" + resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject jsonObj = JSON.parseObject(resp);
                if (jsonObj != null) {
                    if (ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                            ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                        result = new ApiAutocompleteTOResult();
                        JSONArray resultJa = jsonObj.getJSONArray("Result");
                        if (CollectionUtils.isNotEmpty(resultJa)) {
                            List<OverseaCorpSearchApiTO> apiTOs = resultJa.toJavaList(OverseaCorpSearchApiTO.class);
                            result.setResult(transfer4CorpSearch(apiTOs, nationCode));
                        }
                        JSONObject pagingJo = jsonObj.getJSONObject("Paging");
                        if (pagingJo != null) {
                            result.setPaging(pagingJo.toJavaObject(Page.class));
                        }
                    } else if (ApiErrorCodeEnum.CODE_ERROR_215.getCode().equals(jsonObj.getString("Status"))) {
                        logger.warn("搜索包含宽泛词, searchKey: " + searchKey);
                        throw new MessageException(MessageExceptionEnum.TOO_MANY_RESULTS);
                    } else {
                        logger.error("/ECIOs/OverseaSearch:" + resp);
                    }
                }
            }
        } catch (MessageException e) {
            throw e;
        } catch (Exception e) {
            logger.error("/ECIOs/OverseaSearch:", e);
        }
        return result;
    }

    private static List<ApiAutocompleteGlobalTO> transfer4CorpSearch(List<OverseaCorpSearchApiTO> apiTOs, String nationCode) {
        if (CollectionUtils.isEmpty(apiTOs)) {
            return new ArrayList<>();
        }
        List<String> keyNos = apiTOs.stream().map(OverseaCorpSearchApiTO::getKeyNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, QccOvsBasicTO> keyNo2InfoMap = ovsQccOvsBasicService.getKeyNo2InfoMap(keyNos);
        List<ApiAutocompleteGlobalTO> resultList = new ArrayList<>();
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.getByNameCode(nationCode));
        for (OverseaCorpSearchApiTO apiTO : apiTOs) {
            ApiAutocompleteGlobalTO globalTO = new ApiAutocompleteGlobalTO();
            globalTO.setKeyNo(apiTO.getKeyNo());
            globalTO.setName(apiTO.getName());
            globalTO.setEnName(apiTO.getEnglishName());
//            globalTO.setEnName(CompanyDetailsInterface.getEnNameByCnNameEnd(globalTO.getKeyNo(), globalTO.getName(), globalTO.getEnName()));
            if (GlobalAreaEnum.HK.getNameCode().equals(nationCode)) {
                globalTO.setRegNo(getValueFromMapList(apiTO.getMapList(), "regNo"));
            } else {
                globalTO.setRegNo(getValueFromMapList(apiTO.getMapList(), "compNo")); // updated for v1.9.4 KNZT-4492
            }
            String statusDesc = apiTO.getStatusDesc();
            // updated for v1.8.6 KNZT-3927
//            String shortStatus = StringUtils.upperFirst(statusDesc);
//            globalTO.setShortStatus(ForeignCorpStatusEnum.getForeignStatus(shortStatus, null));
            // added for v1.9.4 KNZT-4492
            globalTO.setAddress(getValueFromMapList(apiTO.getMapList(), "contactAddr"));
            globalTO.setStartDate(getValueFromMapList(apiTO.getMapList(), "startDate"));

            // 填充行业
            QccOvsBasicTO qccOvsBasic = keyNo2InfoMap.get(apiTO.getKeyNo());
            List<QccOvsBasicTO.IndustryTO> industryTOS = Optional.ofNullable(qccOvsBasic).map(QccOvsBasicTO::getBasicInfo).map(QccOvsBasicTO.QccOvsBasicInfoTO::getIndustry).orElse(null);
            if (CollectionUtils.isNotEmpty(industryTOS)) {
                globalTO.setIndustryList(industryTOS.stream()
                        .map(industryItem -> new ApiAutocompleteGlobalTO.Industry(industryItem.getCode(), industryItem.getDesc(), industryItem.getDescEn()))
                        .collect(Collectors.toList()));
            }
            // 填充qccCode
            globalTO.setQccCode(getValueFromMapList(apiTO.getMapList(), "qccCode"));
            fillHitReason(globalTO, apiTO, nationCode);
            transWrapper.add4Entity(globalTO, ApiAutocompleteGlobalTO::setName, ApiAutocompleteGlobalTO::setEnName, ApiAutocompleteGlobalTO::getName, ApiAutocompleteGlobalTO::getEnName, ApiAutocompleteGlobalTO::getKeyNo, TransWrapper.ENTRY_TYPE_CORP);
            resultList.add(globalTO);
        }
        commonDataService.enPostProcessor(transWrapper);
        return resultList;
    }

    // added for v1.9.4 KNZT-4492
    private static String getValueFromMapList(List<OverseaCorpSearchApiTO.MapItem> mapList, String name) {
        if (CollectionUtils.isEmpty(mapList)) {
            return null;
        }
        return mapList.stream()
                .filter(mapItem -> StringUtils.equals(mapItem.getName(), name))
                .findFirst()
                .map(OverseaCorpSearchApiTO.MapItem::getValue).orElse(null);
    }

    // added for v2.1.5 chenbl KNZT-6434
    private static void fillHitReason(ApiAutocompleteGlobalTO globalTO, OverseaCorpSearchApiTO apiTO, String nationCode) {
        if (globalTO == null || apiTO == null) {
            return;
        }
        try {
            int maxEmContentLengthOfListField = 0;
            if (StringUtils.hasPairedEmTags(globalTO.getEnName())) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getEnName()));
            } else if (StringUtils.hasPairedEmTags(globalTO.getName())) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getName()));
            } else if (StringUtils.hasPairedEmTags(globalTO.getRegNo())) {
                maxEmContentLengthOfListField = Integer.max(maxEmContentLengthOfListField, SearchBaseService.getEmContentLength(globalTO.getRegNo()));
            }
            List<HitReason> hitReasonsTemp = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(apiTO.getOriginalNameArray())) {
                for (String originalNameItem : apiTO.getOriginalNameArray()) {
                    CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4BaseField(originalNameItem, "Previous Name"));
                }
            }
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "compSname", "Alias"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "compSnameNative", "Alias"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "tradename", "Alias"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "compNameHis", "Previous Name"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "compNameHisNative", "Previous Name"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "qccCode", "QCC Code"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "leiCode", "LEI"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "stockCode", "Stock Symbol"));
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "compBizNo", "Other BRN"));
            if (GlobalAreaEnum.HK.getNameCode().equals(nationCode)) {
                CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "compNo", "Other BRN"));
            } else {
                CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4MapList(apiTO.getMapList(), "regNo", "Other BRN"));
            }
            CollectionUtils.addIgnoreNull(hitReasonsTemp, generateHitReason4ApiHitReason(apiTO.getHitReason(), nationCode));
            HitReason hitReasonWithMaxEmContentLength = hitReasonsTemp.stream().max(Comparator.comparing(HitReason::getEmContentLength)).orElse(null);
            if (hitReasonWithMaxEmContentLength != null && hitReasonWithMaxEmContentLength.getEmContentLength() > maxEmContentLengthOfListField) {
                globalTO.setHitReason(hitReasonWithMaxEmContentLength);
            }
        } catch (Exception e) {
            logger.error("填充HIT REASON失败", e);
        }
    }

    private static HitReason generateHitReason4BaseField(String fieldValue, String hitReasonDesc) {
        if (StringUtils.hasPairedEmTags(fieldValue)) {
            return new HitReason(hitReasonDesc, hitReasonDesc, Lists.newArrayList(fieldValue), CorpSearchService.getEmContentLength(fieldValue));
        }
        return null;
    }

    private static HitReason generateHitReason4MapList(List<OverseaCorpSearchApiTO.MapItem> mapList, String mapListFieldName, String hitReasonDesc) {
        String mapListFieldValue = getValueFromMapList(mapList, mapListFieldName);
        return generateHitReason4BaseField(mapListFieldValue, hitReasonDesc);
    }

    private static HitReason generateHitReason4ApiHitReason(OverseaCorpSearchApiTO.HitReason apiHitReason, String nationCode) {
        if (apiHitReason == null) {
            return null;
        }
        String field = apiHitReason.getField();
        if (StringUtils.isBlank(field)) {
            return null;
        }
        String hitReasonDesc = null;
        switch (field) {
            case "在港营业名称":
                hitReasonDesc = "Alias";
                break;
            case "简体名称":
                hitReasonDesc = "Alias";
                break;
            case "企业简称":
                hitReasonDesc = "Alias";
                break;
            case "别名":
                hitReasonDesc = "Previous Name";
                break;
            case "曾用名":
                hitReasonDesc = "Previous Name";
                break;
            case "全球法人识别编码（LEI）":
                hitReasonDesc = "LEI";
                break;
            case "商业编号": // compBizNo
                hitReasonDesc = "Other BRN";
                break;
            case "注册号码": // regNo
                if (GlobalAreaEnum.HK.getNameCode().equals(nationCode)) {
                    break;
                }
                hitReasonDesc = "Other BRN";
                break;
            default:
                break;
        }
        if (StringUtils.isNotBlank(hitReasonDesc)) {
            return new HitReason(hitReasonDesc, hitReasonDesc, Lists.newArrayList(apiHitReason.getValue()), CorpSearchService.getEmContentLength(apiHitReason.getValue()));
        }
        return null;
    }
}
