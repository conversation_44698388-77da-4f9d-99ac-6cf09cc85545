package com.backend.common.modules.delivery.model;

import com.backend.common.modules.api.entity.TblCompApiChargeUnit;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProd;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.openapi.model.KzzContractDetailTO;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;

import java.util.List;

/**
 * added for v1.8.8 KNZT-3324
 * 合同详情页面对象
 *
 * <AUTHOR>
 * @datetime 2024/6/27 15:08
 */
public class ContractDetailVO {

    private TblContractDelivery baseInfo;
    private List<TblContractDeliveryProd> productList;
//    private Role2ServiceTO role2ServiceTO;  removed for v1.9.8 KNZT-4538
    private List<KzzContractDetailTO.Attachment> attachments;
    private String submitName;
    private String payTypeDesc;

    private List<TblReportChargeUnit> chargeUnits;
    private List<TblCompApiChargeUnit> apiChargeUnits;


    public ContractDetailVO(TblContractDelivery baseInfo, List<TblContractDeliveryProd> productList,
                            List<KzzContractDetailTO.Attachment> attachments, String submitName,
                            List<TblReportChargeUnit> chargeUnits, List<TblCompApiChargeUnit> apiChargeUnits) {
        this.baseInfo = baseInfo;
        this.productList = productList;
        this.attachments = attachments;
        this.submitName = submitName;

        this.chargeUnits = chargeUnits;
        this.apiChargeUnits = apiChargeUnits;

        this.payTypeDesc = CompPayTypeEnum.getDescCnByCode(baseInfo.getPayType());
    }


    public TblContractDelivery getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(TblContractDelivery baseInfo) {
        this.baseInfo = baseInfo;
    }

    public List<TblContractDeliveryProd> getProductList() {
        return productList;
    }

    public void setProductList(List<TblContractDeliveryProd> productList) {
        this.productList = productList;
    }

    public List<KzzContractDetailTO.Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<KzzContractDetailTO.Attachment> attachments) {
        this.attachments = attachments;
    }

    public String getSubmitName() {
        return submitName;
    }

    public void setSubmitName(String submitName) {
        this.submitName = submitName;
    }

    public List<TblReportChargeUnit> getChargeUnits() {
        return chargeUnits;
    }

    public void setChargeUnits(List<TblReportChargeUnit> chargeUnits) {
        this.chargeUnits = chargeUnits;
    }

    public List<TblCompApiChargeUnit> getApiChargeUnits() {
        return apiChargeUnits;
    }

    public void setApiChargeUnits(List<TblCompApiChargeUnit> apiChargeUnits) {
        this.apiChargeUnits = apiChargeUnits;
    }

    public String getPayTypeDesc() {
        return payTypeDesc;
    }

    public void setPayTypeDesc(String payTypeDesc) {
        this.payTypeDesc = payTypeDesc;
    }
}
