package com.backend.common.modules.report.service;

import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommTblCompOpIpLogService;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.report.condition.TblCompReportCartCondition;
import com.backend.common.modules.report.entity.TblCompReportCart;
import com.backend.common.modules.report.entity.TblCompReportOrderHkDoc;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.mapper.TblCompReportCartDao;
import com.backend.common.modules.report.model.CartGroupInfoTO;
import com.backend.common.modules.report.model.CartInfoSubmitTO;
import com.backend.common.modules.report.model.CartInfoTO;
import com.backend.common.modules.report.model.OrderSubmitHkDocParamTO;
import com.backend.common.modules.report.model.OrderSubmitTO;
import com.backend.common.modules.report.model.order.OrderSubmitCheckErrorTO;
import com.backend.common.modules.report.service.report.order.custom.SgReportOrderCustomService;
import com.backend.common.service.CommSysCompanyService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.OpIpLogTypeEnum;
import com.qcc.frame.commons.ienum.OrderHkDocRelTypeEnum;
import com.qcc.frame.commons.ienum.ReportGroupMappingEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.commons.ienum.UnitGroupEnum;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.PinyinUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CommTblCompReportCartService extends CrudService<TblCompReportCartDao, TblCompReportCart> {
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private CommTblReportChargeUnitService commTblReportChargeUnitService;
    @Autowired
    private SgReportOrderCustomService sgReportOrderCustomService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private CommTblCompOpIpLogService commTblCompOpIpLogService;
    @Autowired
    private OrderBusinessService orderBusinessService;
    @Autowired
    private CommTblCompReportOrderHkDocService hkDocService;
    @Autowired
    private CommonDataService commonDataService;

    /**
     * 删除或者保存购物车
     * 前置校验&填充数据
     *
     * @param cartInfoSubmitTOList
     */
    public List<CartInfoTO> saveOrUpdateCartInfo(List<CartInfoSubmitTO> cartInfoSubmitTOList) throws MessageException {
        List<CartInfoTO> resultCartList = new ArrayList<>();
        String userId = UserUtils.getUserId();
        String companyId = UserUtils.getUserCompanyId();
        List<TblReportChargeUnit> chargeUnitList = commTblReportChargeUnitService.listEnableChargeUnitByCompany(UserUtils.getUserCompanyId());
        Map<String, TblReportChargeUnit> chargeUnitMap = chargeUnitList.stream().collect(Collectors.toMap(TblReportChargeUnit::getReportType, Function.identity(), (a, b) -> a));

        List<OrderSubmitTO> orderSubmitTOList = OrderSubmitTO.build4CartSubmitTO(cartInfoSubmitTOList);
        logger.info("saveOrUpdateCartInfo check order can submit begin");
        List<OrderSubmitCheckErrorTO> errorList = orderBusinessService.checkOrderCanSubmitWithoutException(orderSubmitTOList, chargeUnitMap);
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new MessageException(errorList.get(0).getErrorMsg());
        }
        logger.info("saveOrUpdateCartInfo check order can submit end");  

        logger.info("saveOrUpdateCartInfo batch save cart info begin");
        Map<String, OrderSubmitTO> corpParamFromRealDataMap = orderSubmitTOList.stream().collect(Collectors.toMap(OrderSubmitTO::getKeyNo, Function.identity(), (a, b) -> a));
        List<OrderSubmitHkDocParamTO> hkDocToSaveList = new ArrayList<>();
        for (CartInfoSubmitTO cartInfoSubmitTO : cartInfoSubmitTOList) {
            logger.info("save cart info begin");
            TblReportChargeUnit rptUnit = chargeUnitMap.get(cartInfoSubmitTO.getReportType());
            validateCartInfoByUserId(userId, cartInfoSubmitTO, rptUnit);
            OrderSubmitTO corpParam = corpParamFromRealDataMap.get(cartInfoSubmitTO.getCorpKeyNo());
            TblCompReportCart compReportCart = processCartInfo(cartInfoSubmitTO, rptUnit, userId, companyId, corpParam);
            this.save(compReportCart);
            CartInfoTO cartInfoTO = CartInfoTO.build(compReportCart);
            cartInfoTO.setTotalUnit(rptUnit.getUnit());
            cartInfoTO.setCorpStatus(null);
            cartInfoTO.setCorpStatusCheck(null);
            resultCartList.add(cartInfoTO);
            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(rptUnit.getReportType())) {
                OrderSubmitHkDocParamTO hkDocParamTO = OrderSubmitHkDocParamTO.build(compReportCart.getId(), OrderHkDocRelTypeEnum.CART.getCode(), compReportCart.getKeyNo(), cartInfoSubmitTO.getHkDocNumber());
                hkDocToSaveList.add(hkDocParamTO);
            }
            commTblCompOpIpLogService.saveIpLog(OpIpLogTypeEnum.CART.getCode(), compReportCart.getId());
            logger.info("saveOrUpdateCartInfo save cart info end");
        }
        // 批量保存香港文档数据
        hkDocService.batchSave(hkDocToSaveList);
        logger.info("saveOrUpdateCartInfo batch save cart info end");
        return resultCartList;
    }

    /**
     * 处理购物车信息
     * 
     * @param cartInfoSubmitTO
     * @param rptUnit
     * @param userId
     * @param companyId
     * @return
     * @throws MessageException
     */
    private TblCompReportCart processCartInfo(CartInfoSubmitTO cartInfoSubmitTO, TblReportChargeUnit rptUnit, String userId, String companyId, OrderSubmitTO corpParam) throws MessageException {
        TblCompReportCart compReportCart;
        if (StringUtils.isBlank(cartInfoSubmitTO.getCartId())) {
            compReportCart = buildCartInfoFromParam(cartInfoSubmitTO, rptUnit, corpParam);
            compReportCart.setIsNewRecord(true);
            compReportCart.setId(IdGenUtil.uuid());
        } else { // 编辑更新
            compReportCart = get(cartInfoSubmitTO.getCartId());
            MsgExceptionUtils.checkIsNull(compReportCart, "err.param.invalid");
            compReportCart.setUnitGroup(rptUnit.getUnitGroup());
            compReportCart.setReportGroup(rptUnit.getReportGroup());
            compReportCart.setReportType(rptUnit.getReportType());
            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(rptUnit.getReportType())) {
                compReportCart.setExtraInfo1(cartInfoSubmitTO.getHkDocNumber());
            } else if (ReportTypeEnum.MY_BASIC.getCode().equals(rptUnit.getReportType())) {
                compReportCart.setExtraInfo1(cartInfoSubmitTO.getHkDocNumber());
            }
        }
        compReportCart.setCorpJurisdiction(UnitGroupEnum.getNameByUnitGroup(rptUnit.getUnitGroup()));
        compReportCart.setUserId(userId);
        compReportCart.setCompanyId(companyId);
        return compReportCart;
    }

    /**
     * 校验产品服务价目配置信息
     * 
     * @param companyId
     * @param cartInfoSubmitTO
     * @return
     * @throws MessageException
     */
    private TblReportChargeUnit validateReportChargeUnit(String companyId, CartInfoSubmitTO cartInfoSubmitTO) throws MessageException {
        TblReportChargeUnit rptUnit = commTblReportChargeUnitService.getChargeUnitByIdOrReportConfig(companyId, cartInfoSubmitTO.getReportChargeUnitId(), cartInfoSubmitTO.getReportGroup(), cartInfoSubmitTO.getReportType(), cartInfoSubmitTO.getCorpKeyNo());
        if (rptUnit == null || StringUtils.equals(Constants.NO, rptUnit.getEnabled())) {
            logger.error("invalid param charge unit id:" + cartInfoSubmitTO.getReportChargeUnitId());
            throw new MessageException("err.account.no.service");
        }
        return rptUnit;
    }

    /**
     * 校验购物车数量不得超过20，以及企业购物车产品情况是否存在重复
     * 同一家企业只允许同时存在一个类型的购物车产品
     * 允许同一家企业存在一个企业产品(C)和一个高管(P)购物车产品情况出现
     * 校验keyNo、企业名称、高管人名不得为空
     *
     * @param userId
     * @param cartInfoSubmitTO
     * @param rptUnit
     * @throws MessageException
     */
    private void validateCartInfoByUserId(String userId, CartInfoSubmitTO cartInfoSubmitTO, TblReportChargeUnit rptUnit) throws MessageException {
        if (StringUtils.isBlank(userId) || Objects.isNull(cartInfoSubmitTO) || Objects.isNull(rptUnit)) return;
        List<TblCompReportCart> cartList = getCartByUserId(userId);
        if (CollectionUtils.isEmpty(cartList)) return;
        String keyNo;
        String keyNoType = ReportGroupMappingEnum.getKeyNoTypeByReportGroup(rptUnit.getReportGroup());
        if (Constants.Report.KEY_NO_TYPE_CORP.equals(keyNoType)) {
            MsgExceptionUtils.checkIsNull(cartInfoSubmitTO.getCorpKeyNo());
            MsgExceptionUtils.checkIsNull(StringUtils.getNotBlankStr(cartInfoSubmitTO.getCorpName(), cartInfoSubmitTO.getCorpNameEn()));
            keyNo = cartInfoSubmitTO.getCorpKeyNo();
        } else {
            MsgExceptionUtils.checkIsNull(cartInfoSubmitTO.getPersKeyNo());
            MsgExceptionUtils.checkIsNull(cartInfoSubmitTO.getPersName());
            keyNo = cartInfoSubmitTO.getPersKeyNo();
        }
        if (StringUtils.isBlank(cartInfoSubmitTO.getCartId())) {
            if (cartList.size() >= 20) {
                throw new MessageException("err.cart.items.over.limit");
            }
        }
        boolean res = cartList.stream()
                .filter(k -> Objects.isNull(cartInfoSubmitTO.getCartId()) || !StringUtils.equals(k.getId(), cartInfoSubmitTO.getCartId()))
                .anyMatch(item -> isDuplicateCartItem(item, cartInfoSubmitTO, keyNo));
        if (res) {
            throw new MessageException("err.duplicate.items.cart");
        }
        if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(cartInfoSubmitTO.getReportType())) {
            MsgExceptionUtils.checkIsNull(cartInfoSubmitTO.getHkDocNumber(), "err.param.invalid");
        }
    }

    /**
     * 检查购物车项是否重复
     * 对于 HK_DOC 类型，除了 keyNo 和 reportType，还需要比较文档编号 (extraInfo1)
     *
     * @param item             购物车中的现有项
     * @param cartInfoSubmitTO 待添加/修改的购物车项信息
     * @param keyNo            当前操作的目标 keyNo
     * @return 如果重复则返回 true，否则返回 false
     */
    private boolean isDuplicateCartItem(TblCompReportCart item, CartInfoSubmitTO cartInfoSubmitTO, String keyNo) {
        // 1. 基础校验：keyNo 和 reportType 必须匹配
        boolean keyNoMatch = StringUtils.equals(keyNo, item.getKeyNo());
        boolean reportTypeMatch = StringUtils.equals(cartInfoSubmitTO.getReportType(), item.getReportType());

        if (!keyNoMatch || !reportTypeMatch) {
            return false; // keyNo 或 reportType 不匹配，肯定不是重复项
        }

        // 2. 特殊校验：如果 reportType 是 HK_DOC，则额外比较文档编号
        if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(cartInfoSubmitTO.getReportType())) {
            // 只有当文档编号也相同时，才视为 HK_DOC 的重复项
            return StringUtils.equals(cartInfoSubmitTO.getHkDocNumber(), item.getExtraInfo1());
        } else {
            // 3. 对于非 HK_DOC 类型，keyNo 和 reportType 匹配即视为重复项
            return true;
        }
    }


    /**
     * 构建购物车信息
     *
     * @param cartInfoSubmitTO
     * @param rptUnit
     * @return
     */
    private TblCompReportCart buildCartInfoFromParam(CartInfoSubmitTO cartInfoSubmitTO, TblReportChargeUnit rptUnit, OrderSubmitTO corpParam) {
        TblCompReportCart compReportCart = new TblCompReportCart();
        compReportCart.setKeyNoType(ReportGroupMappingEnum.getKeyNoTypeByReportGroup(rptUnit.getReportGroup()));
        if (Constants.Report.KEY_NO_TYPE_CORP.equals(compReportCart.getKeyNoType())) {
            compReportCart.setKeyNo(cartInfoSubmitTO.getCorpKeyNo());
        } else {
            compReportCart.setKeyNo(cartInfoSubmitTO.getPersKeyNo());
            if (PinyinUtils.notConvert2Pinyin(cartInfoSubmitTO.getPersName())) {
                compReportCart.setPersNameEn(cartInfoSubmitTO.getPersName());
            } else {
                compReportCart.setPersName(cartInfoSubmitTO.getPersName());
                compReportCart.setPersNameEn(PinyinUtils.chineseNameToPinyin(cartInfoSubmitTO.getPersName()));
            }
        }
        compReportCart.setCorpKeyNo(cartInfoSubmitTO.getCorpKeyNo());
        compReportCart.setCorpName(cartInfoSubmitTO.getCorpName());
        compReportCart.setCorpNameEn(cartInfoSubmitTO.getCorpNameEn());
        compReportCart.setCorpNumber(cartInfoSubmitTO.getCorpNumber());
        compReportCart.setCorpStatus(cartInfoSubmitTO.getCorpStatusEn());
        compReportCart.setCorpStatusCheck(cartInfoSubmitTO.getCorpShortStatusCn());
        compReportCart.setUnitGroup(rptUnit.getUnitGroup());
        compReportCart.setReportGroup(rptUnit.getReportGroup());
        compReportCart.setReportType(rptUnit.getReportType());
        // 香港订单 加入购物车时，需要保存一下extraInfo1字段，避免编辑购物车时，切换香港套餐时还去调用接口
        if (Constants.Report.REPORT_GROUP_KYB_HK.equals(rptUnit.getReportGroup())) {
            compReportCart.setExtraInfo1(cartInfoSubmitTO.getHkDocNumber());
        }

        if (Objects.nonNull(corpParam)) {
            compReportCart.setCorpDistrict(corpParam.getCorpDistrict());
            compReportCart.setCorpDateOfReg(corpParam.getCorpDateOfReg());
            compReportCart.setCorpStatus(corpParam.getCorpStatusEn());
            compReportCart.setCorpStatusCheck(corpParam.getCorpShortStatusCn());
            compReportCart.setCorpNumber(corpParam.getCorpNumber());
            if (ReportTypeEnum.MY_BASIC.getCode().equals(rptUnit.getReportType())) {
                compReportCart.setExtraInfo1(corpParam.getRegNo());
            }
        }
        return compReportCart;
    }


    public List<TblCompReportCart> getCartByUserId(String userId) {
        return dao.getCartByUserId(userId);
    }


    public Page<CartInfoTO> findListByCondition(TblCompReportCartCondition condition) {
        String userId = UserUtils.getUserId();
        condition.removePage();
        List<CartInfoTO> list = dao.listCartInfoByCondition(userId, condition);
        Page<CartInfoTO> rtnPage = new Page<>();
        mappingReportName(list);
        BeanUtils.copyProperties(condition.getPage(), rtnPage);
        rtnPage.setList(list);
        enPostProcessor4Cart(list);
        return rtnPage;
    }

    private void enPostProcessor4Cart(List<CartInfoTO> list) {
        TransWrapper transWrapper = TransWrapper.buildWrapper();
        for (CartInfoTO cartInfoTO : list) {
            transWrapper.add4Entity(cartInfoTO, CartInfoTO::setCorpName, CartInfoTO::setCorpNameEn, CartInfoTO::getCorpName, CartInfoTO::getCorpNameEn, CartInfoTO::getCorpKeyNo, TransWrapper.ENTRY_TYPE_CORP, UnitGroupEnum.getGlobalAreaEnumByUnitGroup(cartInfoTO.getUnitGroup()));
            transWrapper.add4Entity(cartInfoTO, CartInfoTO::setPersName, CartInfoTO::setPersNameEn, CartInfoTO::getPersName, CartInfoTO::getPersNameEn, CartInfoTO::getPersKeyNo, TransWrapper.ENTRY_TYPE_PERSON, UnitGroupEnum.getGlobalAreaEnumByUnitGroup(cartInfoTO.getUnitGroup()));
        }
        commonDataService.enPostProcessor(transWrapper);
    }

    /**
     * 购物车编辑时，香港产品切换BASIC+AR时，需要用到extraInfo1字段判断展示Incorporation Form|Annual Return
     * 手动处理，避免前端自己调接口操作
     * 
     * @param list
     */
    private void mappingReportName(List<CartInfoTO> list) {
        List<String> hkDocIdList = list.stream().filter(item -> ReportTypeEnum.HK_DOCUMENT.getCode().equals(item.getReportType())).map(CartInfoTO::getId).collect(Collectors.toList());
        Map<String, TblCompReportOrderHkDoc> hkDocMap = hkDocService.batchGetHkDocMap(hkDocIdList, OrderHkDocRelTypeEnum.CART.getCode());

        if (CollectionUtils.isEmpty(list)) return;
        list.forEach(item -> {
            String serviceName = ReportTypeEnum.getDesc(item.getReportType());
            // updated for v2.1.2 fengsw KNZT-6194【后端】1:标题根据用户下单后获取的最新的数据进行IM或AR的动态变更 2:订单确认页中，服务名称改为 KYC Basic + Annual Return/Incorporation Form
            /*if (ReportTypeEnum.HK_BAS_AR.getCode().equals(item.getReportType())) {
                serviceName = Constants.HkAnnouncement.APPLICATION.equals(item.getExtraInfo1()) ? "KYC Basic + Incorporation Form" : "KYC Basic + Annual Return";
            } else {
                serviceName = ReportTypeEnum.getDesc(item.getReportType());
            }*/
            item.setService(serviceName);
            if (Constants.Report.KEY_NO_TYPE_PERS.equals(item.getKeyNoType())) {
                item.setPersKeyNo(item.getKeyNo());
            }
            item.setCorpStatus(null);
            item.setCorpStatusCheck(null);

            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(item.getReportType())) {
                TblCompReportOrderHkDoc hkDoc = hkDocMap.get(item.getId());
                if (Objects.nonNull(hkDoc)) {
                    item.setDocNumber(hkDoc.getDocNumber());
                    item.setDocTitle(hkDoc.getDocTitle());
                    item.setDocTitleEn(hkDoc.getDocTitleEn());
                    item.setDocYear(hkDoc.getDocYear());
                    item.setDocDate(hkDoc.getDocDate());
                }
            }
        });
    }

    /**
     * 删除购物车信息
     *
     * @param cartInfoList
     */
    public void deleteCartInfo(List<CartInfoSubmitTO> cartInfoList) {
        if (CollectionUtils.isEmpty(cartInfoList)) return;
        String companyId = UserUtils.getUserCompanyId();
        String userId = UserUtils.getUserId();
        List<String> cartInfoIdList = cartInfoList.stream().map(CartInfoSubmitTO::getCartId).collect(Collectors.toList());
        logger.info("delete cart info companyId:{}, cartId:{}, userId{}", companyId, userId, cartInfoIdList);
        dao.deleteCartByIdList(userId, cartInfoIdList);
    }

    /**
     * 根据userId清空购物车
     * added for lvcy v1.9.9 KNZT-4980
     *
     * @param userId
     * @return
     */
    public void clearByUserId(String userId) {
        TblCompReportCart entity = new TblCompReportCart();
        entity.setUserId(userId);
        entity.preUpdate();
        dao.deleteByUserId(entity);
    }

    /**
     * 按keyNo分组查询购物车列表
     * @param condition 查询条件
     * @return 分组后的购物车信息列表
     */
    public List<CartGroupInfoTO> findGroupListByCondition(TblCompReportCartCondition condition) {
        String userId = UserUtils.getUserId();
        condition.removePage();
        List<CartInfoTO> cartList = dao.listCartInfoByCondition(userId, condition);
        mappingReportName(cartList);
        enPostProcessor4Cart(cartList);
        // 按keyNo分组
        Map<String, List<CartInfoTO>> groupedMap = cartList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(CartInfoTO::getKeyNo));
        
        // 构建分组结果
        List<CartGroupInfoTO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<CartInfoTO>> entry : groupedMap.entrySet()) {
            String keyNo = entry.getKey();
            List<CartInfoTO> groupCartList = entry.getValue();
            
            if (CollectionUtils.isEmpty(groupCartList)) {
                continue;
            }
            
            // 取第一个元素的信息作为组信息
            CartInfoTO firstCart = groupCartList.get(0);
            CartGroupInfoTO groupInfo = new CartGroupInfoTO();
            groupInfo.setKeyNo(keyNo);
            groupInfo.setCorpJurisdiction(firstCart.getCorpJurisdiction());
            groupInfo.setCorpName(firstCart.getCorpName());
            groupInfo.setCorpNameEn(firstCart.getCorpNameEn());
            groupInfo.setPersName(firstCart.getPersName());
            groupInfo.setCorpNumber(firstCart.getCorpNumber());
            groupInfo.setPersNameEn(firstCart.getPersNameEn());
            groupInfo.setCartList(groupCartList);
            groupInfo.setExtraInfo1(firstCart.getExtraInfo1());
            
            // 找出最新的创建时间用于排序
            Date latestDate = groupCartList.stream().map(CartInfoTO::getCreateDate).max(Comparator.naturalOrder()).orElse(null);
            groupInfo.setLatestCreateDate(latestDate);
            
            resultList.add(groupInfo);
        }
        
        // 根据最新创建时间倒序排序
        resultList.sort(Comparator.comparing(CartGroupInfoTO::getLatestCreateDate, Comparator.nullsLast(Comparator.reverseOrder())));
        
        return resultList;
    }
}
