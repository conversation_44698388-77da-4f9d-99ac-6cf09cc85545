package com.backend.common.modules.shell.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.dd.model.*;
import com.backend.common.modules.shell.model.ScCompanyList;
import com.backend.common.modules.shell.model.ScCompanyListForm;
import com.backend.common.yunjuapi.YunJuRequestForwardingGetDetailInterface;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.ShellLabelEnum;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * added for v1.0.9 KNZT-506
 */
@Service
public class ShellLabelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShellLabelService.class);

    public static final int SHELL_DUPLICATION_DESCRIPTION_52 = 52;
    public static final int SHELL_DUPLICATION_DESCRIPTION_51 = 51;
    private static final Pattern SPAN_PATTERN = Pattern.compile("<span[^>]*>\\s*(.*?)\\s*</span>");
    private static final String HIGHLIGHT_TAG_START = "<span style='color:#F04040;' class='font-weight-bold'>";
    private static final String HIGHLIGHT_TAG_END = "</span>";
    private static TranslaterService translaterService = SpringContextHolder.getBean(TranslaterService.class);

    // added for v2.0.5 chenbl KNZT-5562
    public static EnLabelTOResult getShellCompanyLabel(String corpKeyNo) {
        return getShellCompanyLabel(corpKeyNo, true);
    }

    // updated for v2.0.2 chenbl KNZT-5353
    public static EnLabelTOResult getShellCompanyLabel(String corpKeyNo, boolean needCompanyKey) {
        ScCompanyListForm form = new ScCompanyListForm();
        ScCompanyList entity = new ScCompanyList();
        entity.setKeyno(corpKeyNo);
        form.setDataEntity(entity);

        String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/innerslb/shellco/shellCompanyLabel", "2", JSON.toJSONString(form), needCompanyKey);
        JSONObject jsonObject = JsonUtils.parseObject(resp);
        if (jsonObject != null && StringUtils.equals(jsonObject.getString("status"), Constants.Result.SUCCESS_STR) && jsonObject.getJSONObject("result") != null) {
            ShellCompanyTO shellCompanyTO = jsonObject.getJSONObject("result").toJavaObject(ShellCompanyTO.class);
            return convertShellLabel2EnLabel(shellCompanyTO);
        }
        return null;
    }

    public static EnLabelTOResult convertShellLabel2EnLabel(ShellCompanyTO shellCompanyTO) {
        EnLabelTOResult result = new EnLabelTOResult();
        if (shellCompanyTO == null) {
            return null;
        }
        result.setStatus(shellCompanyTO.getStatus());
        // updated for v2.0.5 chenbl KNZT-5562
        if (!StringUtils.equals("700", result.getStatus())) {
            result.setMessage("Shell company scanning is not applicable for this type of organizations.");
        }
        if (shellCompanyTO.getCompanyLabelTO() == null) {
            return result;
        }
        List<LabelResultTO> allHitLabelList = new ArrayList<>();
        Optional.of(shellCompanyTO)
                .map(ShellCompanyTO::getCompanyLabelTO)
                .map(ShellCompanyLabelTO::getLabelHasDetail)
                .ifPresent(allHitLabelList::addAll);
        Optional.of(shellCompanyTO)
                .map(ShellCompanyTO::getCompanyLabelTO)
                .map(ShellCompanyLabelTO::getLabelNoDetail)
                .ifPresent(allHitLabelList::addAll);
        Map<Integer, LabelResultTO> code2LableMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allHitLabelList)) {
            code2LableMap.putAll(allHitLabelList.stream().collect(Collectors.toMap(LabelResultTO::getCode, Function.identity(), (k1, k2) -> k1)));
        }
        List<EnLabelTO> resultLabelList = new ArrayList<>();
        for (ShellLabelEnum shellLabelEnum : ShellLabelEnum.values()) {
            if (ShellLabelEnum.LABEL_51 == shellLabelEnum || ShellLabelEnum.LABEL_52 == shellLabelEnum) {
                continue;
            }
            LabelResultTO labelResultTO = code2LableMap.get(shellLabelEnum.getCode());
            EnLabelTO enLabelTO = getEnLabelTO(shellLabelEnum, labelResultTO);
            resultLabelList.add(enLabelTO);
        }
        LabelResultTO labelResultTO51_52 = code2LableMap.getOrDefault(ShellLabelEnum.LABEL_51.getCode(), code2LableMap.get(ShellLabelEnum.LABEL_52.getCode()));
        EnLabelTO enLabelTO = getEnLabelTO(ShellLabelEnum.LABEL_51, labelResultTO51_52);
        resultLabelList.add(enLabelTO);
        resultLabelList.sort(Comparator.comparingInt(EnLabelTO::getSort));
        result.setLabelList(resultLabelList);
        result.setAllNotHitFlag(resultLabelList.stream().noneMatch(EnLabelTO::getHitFlag));
        return result;
    }

    private static EnLabelTO getEnLabelTO(ShellLabelEnum shellLabelEnum, LabelResultTO labelResultTO) {
        EnLabelTO enLabelTO = new EnLabelTO();
        enLabelTO.setCode(shellLabelEnum.getCode());
        enLabelTO.setLevel(shellLabelEnum.getLevel());
        enLabelTO.setTitleEn(shellLabelEnum.getTitle());
        enLabelTO.setNameEn(shellLabelEnum.getName());
        enLabelTO.setSort(shellLabelEnum.getSort());
        if (labelResultTO != null) {
            enLabelTO.setDescriptionEn(getDescriptionEn(shellLabelEnum, labelResultTO.getDescription()));
            enLabelTO.setReason(labelResultTO.getReason());
            enLabelTO.setReasonEn(getReasonEn(shellLabelEnum, labelResultTO.getReason()));
            enLabelTO.setUpdateDate(labelResultTO.getUpdateDate());
            enLabelTO.setHitFlag(true);
        }
        return enLabelTO;
    }

    private static String getDescriptionEn(ShellLabelEnum shellLabelEnum, String descOriginal) {
        try {
            List<String> paramByRegex = getParamByRegex(descOriginal);
            if (ShellLabelEnum.LABEL_11 == shellLabelEnum || ShellLabelEnum.LABEL_12 == shellLabelEnum || ShellLabelEnum.LABEL_21 == shellLabelEnum) {
                if (paramByRegex.size() == 1) {
                    if ("1".equals(paramByRegex.get(0))) {
                        return formatText(shellLabelEnum.getDescriptionTemplateSingle(), paramByRegex);
                    } else {
                        return formatText(shellLabelEnum.getDescriptionTemplate(), paramByRegex);
                    }
                } else {
                    LOGGER.error("参数个数非法");
                }
            } else if (ShellLabelEnum.LABEL_61 == shellLabelEnum) {
                if (paramByRegex.size() == 2) {
                    String param0 = paramByRegex.get(0);
                    param0 = DateUtils.formatDateForSg(DateUtils.parseDate(param0, DateUtils.DATE_FORMAT), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
                    paramByRegex.set(0, param0);
                    String param1 = paramByRegex.get(1);
                    param1 = translaterService.getEnglishText(param1, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
                    param1 = StringUtils.removeEndPunctuation(param1); // 机器翻译后面会自动加上符号，要去掉
                    paramByRegex.set(1, param1);
                    return formatText(shellLabelEnum.getDescriptionTemplate(), paramByRegex);
                } else {
                    LOGGER.error("参数个数非法");
                }
            } else if (ShellLabelEnum.LABEL_13 == shellLabelEnum) {
                if (paramByRegex.size() == 2) {
                    String param0 = paramByRegex.get(0);
                    if (!PinyinUtils.notConvert2Pinyin(param0)) {
                        param0 = PinyinUtils.chineseNameToPinyin(param0);
                        paramByRegex.set(0, param0);
                    }
                    if ("1".equals(paramByRegex.get(1))) {
                        return formatText(shellLabelEnum.getDescriptionTemplateSingle(), paramByRegex);
                    } else {
                        return formatText(shellLabelEnum.getDescriptionTemplate(), paramByRegex);
                    }
                } else {
                    LOGGER.error("参数个数非法");
                }
            } else if (ShellLabelEnum.LABEL_72 == shellLabelEnum) {
                if (paramByRegex.size() == 1) {
                    String param0 = paramByRegex.get(0);
                    param0 = DateUtils.formatDateForSg(DateUtils.parseDate(param0, DateUtils.DATE_FORMAT), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
                    paramByRegex.set(0, param0);
                    return formatText(shellLabelEnum.getDescriptionTemplate(), paramByRegex);
                } else {
                    LOGGER.error("参数个数非法");
                }
            } else {
                return shellLabelEnum.getDescriptionTemplate();
            }
        } catch (Exception e) {
            LOGGER.error("空壳描述获取失败", e);
        }
        return null;
    }

    private static List<String> getParamByRegex(String text) {
        if (StringUtils.isBlank(text)) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        Matcher matcher = SPAN_PATTERN.matcher(text);
        while (matcher.find()) {
            resultList.add(matcher.group(1));
        }
        return resultList;
    }

    private static String formatText(String template, List<String> params) {
        if (params == null) {
            params = new ArrayList<>();
        }
        params = params.stream().map(s -> HIGHLIGHT_TAG_START + s + HIGHLIGHT_TAG_END).collect(Collectors.toList());
        return String.format(template, params.toArray());
    }

    private static String replaceSpan(String text) {
        String highlightTagBeginPro = "<span style='color:#F2784B;'>";
        return StringUtils.replace(text, highlightTagBeginPro, HIGHLIGHT_TAG_START);
    }

    private static String getReasonEn(ShellLabelEnum shellLabelEnum, String reasonOriginal) {
        try {
            reasonOriginal = replaceSpan(reasonOriginal);
            if (ShellLabelEnum.LABEL_41 == shellLabelEnum) {
                reasonOriginal = StringUtils.removeStart(reasonOriginal, "注册地址：");
                reasonOriginal = translaterService.getEnglishText(reasonOriginal, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
                reasonOriginal = "Registered Address: " + reasonOriginal;
                return reasonOriginal;
            } else if (ShellLabelEnum.LABEL_82 == shellLabelEnum) {
                reasonOriginal = StringUtils.removeStart(reasonOriginal, "企业名称：");
                reasonOriginal = "Company Name: " + reasonOriginal;
                return reasonOriginal;
            } else if (ShellLabelEnum.LABEL_83 == shellLabelEnum) {
                List<String> reasonOrginalList = StringUtils.split2List(reasonOriginal, "<br>");
                List<String> reasonEnList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(reasonOrginalList)) {
                    for (String reasonOriginalItem : reasonOrginalList) {
                        if (StringUtils.startsWith(reasonOriginalItem, "企业名称：")) {
                            reasonOriginalItem = StringUtils.removeStart(reasonOriginalItem, "企业名称：");
                            reasonOriginalItem = translaterService.getEnglishText(reasonOriginalItem, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
                            reasonEnList.add("Company Name: " + reasonOriginalItem);
                        } else if (StringUtils.startsWith(reasonOriginalItem, "经营范围：")) {
                            reasonOriginalItem = StringUtils.removeStart(reasonOriginalItem, "经营范围：");
                            reasonOriginalItem = translaterService.getEnglishText(reasonOriginalItem, "", "", SysConstants.TRANSLATE_HIST_FUNCTION_CORP_REASON);
                            reasonEnList.add("Business Scope: " + reasonOriginalItem);
                        }
                    }
                    return StringUtils.join(reasonEnList, "<br>");
                } else {
                    return null;
                }
            }
        } catch (
                Exception e) {
            LOGGER.error("空壳Reason获取失败", e);
        }
        return null;
    }
}
