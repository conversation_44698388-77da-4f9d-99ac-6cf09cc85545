package com.backend.common.modules.report_data.corp.oversea;

import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.model.OrderData4LoopResult;
import com.backend.common.modules.report_data.corp.oversea.base.AbstractReportDataService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.overseamongo.entity.QccProdHkTaxReport;
import com.backend.common.overseamongo.model.HkTaxReportTO;
import com.backend.common.overseamongo.service.QccProdHkTaxReportService;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.qcc.frame.jee.commons.utils.MappingUtils.getValue;

@Slf4j
@Service
public class HkIRDReportDataService extends AbstractReportDataService<HkTaxReportTO> {

    @Autowired
    private QccProdHkTaxReportService qccProdHkTaxReportService;
    @Autowired
    private SysConfigCacheService sysConfigCacheService;
    @Autowired
    private CommonDataService commonDataService;

    @Override
    public List<ReportTypeEnum> getReportTypes() {
        return Lists.newArrayList(ReportTypeEnum.HK_BR_EXTRACT, ReportTypeEnum.HK_DUP_CERT);
    }

    @Override
    public boolean isDataSuccess(ReportDataGetResultForm form) {
        if (super.doCheckOrderDataSuccess(form)) {
            return true;
        }
        boolean dataSuccess = false;
        QccProdHkTaxReport entity = qccProdHkTaxReportService.findById(form.getApiOrderNo());
        if (entity != null) {
            if (entity.getApiCallback() != null && entity.getApiCallback().equals(1)) {
                dataSuccess = true;
            } else {
                log.info("QccProdHkTaxReport ApiCallback is null or not 1, apiOrderNo: " + form.getApiOrderNo());
            }
        } else {
            log.info("QccProdHkTaxReport is null, apiOrderNo: " + form.getApiOrderNo());
        }
        return dataSuccess;
    }

    @Override
    protected BiConsumer<OrderData4LoopResult, HkTaxReportTO> getPropSetter() {
        return OrderData4LoopResult::setHkBrExtractRealData;
    }

    @Override
    public HkTaxReportTO getData(ReportDataGetResultForm form) {
        TblCompReportOrder tblCompReportOrder = commTblCompReportOrderService.get(form.getOrderId());
        if (tblCompReportOrder == null) {
            logger.warn("TblCompReportOrder is null, orderId: " + form.getOrderId());
            return null;
        }
        QccProdHkTaxReport entity = qccProdHkTaxReportService.findById(form.getApiOrderNo());
        if (entity == null) {
            log.info("QccProdHkTaxReport is null, apiOrderNo: " + form.getApiOrderNo());
            return null;
        }
        return convert2TO(entity, tblCompReportOrder, form.getBankUser());
    }

    private HkTaxReportTO convert2TO(QccProdHkTaxReport entity, TblCompReportOrder tblCompReportOrder, boolean bankUser) {
        if (entity == null) {
            return null;
        }
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.HK);
        HkTaxReportTO to = new HkTaxReportTO();
        HkTaxReportTO.CompanyInfo companyInfoTO = new HkTaxReportTO.CompanyInfo();
        HkTaxReportTO.OwnerInfo ownerInfoTO = new HkTaxReportTO.OwnerInfo();
        Date orderCreateDate = getValue(tblCompReportOrder, TblCompReportOrder::getCreateDate);
        to.setLastUpdatedDate(DateUtils.formatDate(orderCreateDate));
        to.setCompanyInfo(companyInfoTO);
        to.setOwnerInfo(ownerInfoTO);
        to.setReportUrl(entity.getReportUrl());

        // map companyInfo
        QccProdHkTaxReport.BasicInfo basicInfo = getValue(entity, QccProdHkTaxReport::getBasicInfo);
        companyInfoTO.setBrn(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getBRN));
        companyInfoTO.setLegalCompName(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getCompNameCn));
        companyInfoTO.setLegalCompNameEn(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getCompNameEn));
        transWrapper.buildAndAddEntry(companyInfoTO, HkTaxReportTO.CompanyInfo::setLegalCompName, HkTaxReportTO.CompanyInfo::setLegalCompNameEn, HkTaxReportTO.CompanyInfo::getLegalCompName, HkTaxReportTO.CompanyInfo::getLegalCompNameEn).withEntity(null, TransWrapper.ENTRY_TYPE_CORP).withMachineTranslateFlag(HkTaxReportTO.CompanyInfo::setLegalCompNameEnMt);
        companyInfoTO.setBranchName(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getBusinessBranchNameCn));
        companyInfoTO.setBranchNameEn(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getBusinessBranchNameEn));
        transWrapper.buildAndAddEntry(companyInfoTO, HkTaxReportTO.CompanyInfo::setBranchName, HkTaxReportTO.CompanyInfo::setBranchNameEn, HkTaxReportTO.CompanyInfo::getBranchName, HkTaxReportTO.CompanyInfo::getBranchNameEn).withEntity(null, TransWrapper.ENTRY_TYPE_CORP).withMachineTranslateFlag(HkTaxReportTO.CompanyInfo::setBranchNameEnMt);
        companyInfoTO.setBusinessEntityType(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getBusinessStatusEn));
        companyInfoTO.setDateOfCommencement(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getCommencementDate));
        companyInfoTO.setDateOfCessation(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getCessationDate));
        companyInfoTO.setDateOfExpiry(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getExpiryDate));
        companyInfoTO.setFee(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getFee));
        companyInfoTO.setLevy(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getLevy));
        companyInfoTO.setBusinessAddress(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getAddress));
        transWrapper.buildAndAddEntry(companyInfoTO, HkTaxReportTO.CompanyInfo::setBusinessAddress, HkTaxReportTO.CompanyInfo::setBusinessAddressEn, HkTaxReportTO.CompanyInfo::getBusinessAddress, HkTaxReportTO.CompanyInfo::getBusinessAddressEn).withMachineTranslateFlag(HkTaxReportTO.CompanyInfo::setBusinessAddressEnMt);
        companyInfoTO.setBusinessNature(getValue(basicInfo, QccProdHkTaxReport.BasicInfo::getBusinessNature));
        transWrapper.buildAndAddEntry(companyInfoTO, HkTaxReportTO.CompanyInfo::setBusinessNature, HkTaxReportTO.CompanyInfo::setBusinessNatureEn, HkTaxReportTO.CompanyInfo::getBusinessNature, HkTaxReportTO.CompanyInfo::getBusinessNatureEn).withMachineTranslateFlag(HkTaxReportTO.CompanyInfo::setBusinessNatureEnMt);

        // map ownerInfo
        List<QccProdHkTaxReport.OwnerInfo> ownerInfos = getValue(entity, QccProdHkTaxReport::getOwnerInfo);
        if (CollectionUtils.isNotEmpty(ownerInfos)) {
            List<HkTaxReportTO.Owner> owners = ownerInfos.stream().map(ownerInfo -> {
                        HkTaxReportTO.Owner ownerItemTO = new HkTaxReportTO.Owner();
                        ownerItemTO.setEntityType(ownerInfo.getType());
                        ownerItemTO.setKeyNo(ownerInfo.getKeyno());
                        ownerItemTO.setName(ownerInfo.getNameCn());
                        ownerItemTO.setNameEn(ownerInfo.getNameEn());
                        Integer entityType4Trans = detectEntityType4Trans(ownerInfo.getType());
                        transWrapper.buildAndAddEntry(ownerItemTO, HkTaxReportTO.Owner::setName, HkTaxReportTO.Owner::setNameEn, HkTaxReportTO.Owner::getName, HkTaxReportTO.Owner::getNameEn).withEntity(HkTaxReportTO.Owner::getKeyNo, entityType4Trans).withMachineTranslateFlag(HkTaxReportTO.Owner::setNameEnMt);
                        ownerItemTO.setNationCode(ownerInfo.getNationCode());
                        ownerItemTO.setNationCodeDesc(GlobalAreaEnum.getShortNameEnByNameCode(ownerItemTO.getNationCode()));
                        ownerItemTO.setIdNo(bankUser ? ownerInfo.getIDBRN() : ownerInfo.getIDBRNMask());
                        ownerItemTO.setIdType(ownerInfo.getIDType());
                        ownerItemTO.setAddress(ownerInfo.getAddress());
                        transWrapper.buildAndAddEntry(ownerItemTO, HkTaxReportTO.Owner::setAddress, HkTaxReportTO.Owner::setAddressEn, HkTaxReportTO.Owner::getAddress, HkTaxReportTO.Owner::getAddressEn).withMachineTranslateFlag(HkTaxReportTO.Owner::setAddressEnMt);
                        ownerItemTO.setIncomingDate(ownerInfo.getIncomingDate());
                        ownerItemTO.setOutgoingDate(ownerInfo.getOutgoingDate());
                        return ownerItemTO;
                    }).sorted(HkIRDReportDataService::ownerComparator)
                    .collect(Collectors.toList());
            ownerInfoTO.setItems(owners);
        }
        commonDataService.enPostProcessor(transWrapper);
        return to;
    }

    private static Integer detectEntityType4Trans(String entityType) {
        Integer entityType4Trans = null;
        if ("Individual".equals(entityType)) {
            entityType4Trans = TransWrapper.ENTRY_TYPE_PERSON;
        } else if ("Company".equals(entityType)) {
            entityType4Trans = TransWrapper.ENTRY_TYPE_CORP;
        }
        return entityType4Trans;
    }

    private static int ownerComparator(HkTaxReportTO.Owner o1, HkTaxReportTO.Owner o2) {
        String outgoingDate1 = o1.getOutgoingDate();
        String outgoingDate2 = o2.getOutgoingDate();
        if (!StringUtils.equals(outgoingDate1, outgoingDate2)) {
            if (StringUtils.isBlank(outgoingDate1)) {
                return -1;
            } else if (StringUtils.isBlank(outgoingDate2)) {
                return 1;
            } else {
                return outgoingDate2.compareTo(outgoingDate1);
            }
        }

        String incomingDate1 = o1.getIncomingDate();
        String incomingDate2 = o2.getIncomingDate();
        if (!StringUtils.equals(incomingDate1, incomingDate2)) {
            if (StringUtils.isBlank(incomingDate1)) {
                return 1;
            } else if (StringUtils.isBlank(incomingDate2)) {
                return -1;
            } else {
                return incomingDate1.compareTo(incomingDate2);
            }
        }
        return 0;
    }

    @Override
    protected void doAfterInitOrderResult(OrderData4LoopResult result) {
        if (sysConfigCacheService.isNotInWorkingHours(LocalDateTime.now())) {
            result.setWorkdayFlag(Constants.NO);
        }
    }
}
