package com.backend.common.service;

import com.backend.common.entity.SysCompRole;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryTransactionService;
import com.backend.common.modules.util.OpLogUtils;
import com.backend.common.yunjuapi.QccAppToolInterface;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CommDelayedTaskTypeEnum;
import com.qcc.frame.commons.ienum.OpIpLogTypeEnum;
import com.qcc.frame.commons.ienum.PwdSecurityPolicyEnum;
import com.qcc.frame.commons.ienum.TransactionCategoryEnum;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.commons.ienum.exception.MessageExceptionEnum;
import com.qcc.frame.commons.ienum.search.CompRoleEnum;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.dao.UserDao;
import com.qcc.frame.jee.modules.sys.entity.CompGroup;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.entity.mapping.UserWithGroupRoleTO;
import com.qcc.frame.jee.modules.sys.form.CompanyAddressUpdateForm;
import com.qcc.frame.jee.modules.sys.form.UserProfileUpdateForm;
import com.qcc.frame.jee.modules.sys.form.UserActivateForm;
import com.qcc.frame.jee.modules.sys.model.CompanyAddressTO;
import com.qcc.frame.jee.modules.sys.model.CompanyChargeTO;
import com.qcc.frame.jee.modules.sys.model.UserProfileTO;
import com.qcc.frame.jee.modules.sys.service.*;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class CommCompUserService extends CrudService<UserDao, User> {
	@Autowired
	private CommSysCompanyService commSysCompanyService;
	@Autowired
	private UserService userService;
	@Autowired
	private SysUserCompRoleService sysUserCompRoleService;
	@Autowired
	private SysCompRoleService sysCompRoleService;
	@Autowired
	private CommSysCompGroupService commSysCompGroupService;
	@Autowired
	private RedisService redisService;
	@Autowired
	private CommSysCompInfoExtService commSysCompInfoExtService;
	@Autowired
	private ApiUserLoginService apiUserLoginService;
	@Autowired
	private CommTblContractDeliveryTransactionService contractDeliveryTransactionService;


	public void saveUser(User user) throws MessageException {
		if(user != null) {
			User dbEntity = null;
			if(StringUtils.isNotBlank(user.getId())) {//update
				dbEntity = this.get(user.getId());
				if(dbEntity != null) {
					//to do :
					this.save(dbEntity);
				}
				
			} else {//insert
				this.save(user);
			}
		}
	}
	
	public List<User> findAllUsersForCompany(){
		User user = new User();
		Company company = new Company();
		company.setId(UserUtils.getUserCompanyId());
		user.setCompany(company);
//		Page<User> users= new Page<User>();
//		users.setList(dao.findAllUsersForCompany(user));
		return dao.findAllUsersForCompany(user);
	}
	
	public List<User> findAllUsersForCompany(String companyId){
		User user = new User();
		Company company = new Company();
		company.setId(companyId);
		user.setCompany(company);
//		Page<User> users= new Page<User>();
//		users.setList(dao.findAllUsersForCompany(user));
		return dao.findAllUsersForCompany(user);
	}
    
    /**
     *只逻辑删除用户表, 其它关联表通过定时任务清理
     * @param companyId
     * @param userIdList
     * @throws MessageException
     */
    public void batchDeleteUser(String companyId, Set<String> userIdList) throws MessageException {
        if(userIdList != null) {
			// updated for v1.6.5 KNZT-2836
//        	if(!ableToDeleteUser(userIdList.size(), companyId)) {
//        		throw new MessageException("sysCompany.err.exceed.delete_user_num");
//        	}
            for(String userId : userIdList) {
                if(StringUtils.isNotBlank(userId)) {
                    if(commSysCompanyService.isMainUser(companyId, userId)) {
                        throw new MessageException("sysCompany.err.mainUser.cannot.delete");
                    }
                    User user = userService.getUserByCompanyIdAndUserId(companyId, userId);
                    if(user == null) {
                        return;
                    }
					// added for v1.6.9 KNZT-3009
					// updated for v2.0.7 chenbl KNZT-5787
//					List<String> compRoleIdsByUserId = sysUserCompRoleService.listCompRoleIdByUserId(userId);
//					boolean editingUserIsPersonnel = compRoleIdsByUserId.stream().allMatch(Constants.Sys.COMP_ROLE_ID_PERSONNEL::equals);
//					if (!editingUserIsPersonnel) {
//						checkHasAdminPermission();
//					}
					boolean checkBeforeEdit = checkBeforeEdit(UserUtils.getUserId(), userId);
					MsgExceptionUtils.failBuild(!checkBeforeEdit);
					user.setId(userId);
                    user.setLoginFlag(Constants.UserRelationType.NOT_DELETE);
                    this.delete(user);
                }
            }
			// 记录操作日志 added for lvcy v2.1.2 KNZT-6149
			OpLogUtils.saveOpLog(OpIpLogTypeEnum.DELETE_USER);
        }
    }

	// added for v1.6.9 KNZT-3009 removed for v2.0.7 chenbl KNZT-5787
//	public void checkHasAdminPermission() throws MessageException {
//		if (!UserUtils.isCompanyAdmin()) {
//			// updated for v1.7.6 KNZT-3349
//			throw new MessageException("msg:You do not have permission. Please contact admin.");
//		}
//	}
	
	public void validateUserCount(int importCount) throws MessageException{
		validateUserCount(UserUtils.getUserCompanyId(), importCount);
	}

	public void validateUserCount(String companyId, int importCount) throws MessageException{
		Company company = commSysCompanyService.get(companyId);
		if(company != null){
			commSysCompanyService.save(company);
			User user = new User();
			user.setCompany(company);
			int count = dao.countUserByCompanyId(user);
			if(count + importCount > company.getPurchaseAccountNum()){
				throw new MessageException(MessageExceptionEnum.WARN_IMPORT_USER_OVER_COMPANY_LIMIT);
			}
		}else{
			throw new MessageException(MessageExceptionEnum.WARN_IMPORT_USER_OVER_COMPANY_LIMIT);
		}
	}

	/**
	 * 生成加密用户id
	 * 逗号前面的是加密前的数字, 逗号后面的是加密后的值
	 * added for lvcy v2.0.0 KNZT-5142
	 *
	 * @return
	 */
	public static String generateEncryptUserId() throws MessageException {
		String userIdLong = QccAppToolInterface.generateRandomLong();
		String encryptUserId = QccAppToolInterface.getCustom33digitsString(userIdLong);
		return userIdLong + "," + encryptUserId;
	}

	// 利用SysCompRole判断是否有权限编辑，true有 added for v2.0.7 chenbl KNZT-5787
	public boolean checkBeforeEdit(String editingUserId, String editedUserId) {
		if (StringUtils.isBlank(editingUserId) || StringUtils.isBlank(editedUserId)) {
			return false;
		}
		SysCompRole editingUserMaxCompRole = sysCompRoleService.getMaxCompRole(editingUserId);
		SysCompRole editedUserMaxCompRole = sysCompRoleService.getMaxCompRole(editedUserId);
		return SysCompRoleService.compare(editingUserMaxCompRole, editedUserMaxCompRole) < 0;
	}

	// 检查是否有权限分配角色 added for v2.0.7 chenbl KNZT-5787
	public boolean checkBeforeUpdateCompRole(String editingUserId, Collection<String> compRoleIds) {
		List<SysCompRole> assignableCompRoles = sysCompRoleService.listAssignableCompRoles(editingUserId);
		if (CollectionUtils.isEmpty(assignableCompRoles)) {
			return false;
		}
		Set<String> assignableCompRoleIds = assignableCompRoles.stream().map(SysCompRole::getId).collect(Collectors.toSet());
		return assignableCompRoleIds.containsAll(compRoleIds);
	}

	// 检查是否能分配主管 added for v2.0.7 chenbl KNZT-5787
	public void check4ManagerGroup(String companyIdOfUser, Collection<String> compRoleIds, Collection<String> compGroupIds) throws MessageException {
		if (!compRoleIds.contains(Constants.Sys.COMP_ROLE_ID_MANAGER)) {
			return;
		}
		if (commSysCompGroupService.hasRootGroup(companyIdOfUser, compGroupIds)) {
			// updated for v1.7.6 KNZT-3349
			throw new MessageException("msg:You cannot create a Group Manager in this group.");
		}
	}

	/**
	 * 用户激活
	 * updated for v2.0.7 chenbl KNZT-6595
	 * updated for v2.0.4 chenbl KNZT-5442
	 * added for v1.9.3 KNZT-4193
	 *
	 * @param form
	 * @return boolean
	 */
	public boolean activateUser(UserActivateForm form) throws MessageException {
		String userId = form.getUserId();
		User user = this.get(userId);
		if (Objects.isNull(user)) {
			logger.warn("user is null, userId:{}", userId);
			throw new MessageException("err.activation.failed");
		}
		if (Constants.Sys.USER_STATUS_ACTIVE.equals(user.getStatus())) {
			logger.warn("user status has already been active, userId:{}", userId);
			throw new MessageException("err.activation.again");
		}
		if (!Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
			logger.warn("user status is not inactive, userId:{}", userId);
			throw new MessageException("err.activation.failed");
		}
		apiUserLoginService.loginUser(userId, () -> {
			Company company = user.getCompany();
			PwdSecurityPolicyEnum securityPolicyEnum = userService.getCompPwdSecurityPolicy(user.getCompanyId());
			String newPassword = form.getNewPassword();
			userService.checkPwd4Security(securityPolicyEnum, user.getId(), user.getLoginName(), user.getPassword(), newPassword);
			user.setPassword(UserService.entryptPassword(newPassword));
			user.setStatus(Constants.Sys.USER_STATUS_ACTIVE);
			this.save(user);
			UserProfileUpdateForm userProfileUpdateForm = UserProfileUpdateForm.buildFrom(form);
			this.updateProfile(userProfileUpdateForm);
			userService.savePwdHist4Security(securityPolicyEnum, user.getCompanyId(), user.getId(), newPassword);
			redisService.removeDelayedCommTask(RedisService.generateCommTaskId(CommDelayedTaskTypeEnum.CLIENT_ACTIVATE_CHECK.getCode(), company.getId())); // added for v1.9.9 chenbl KNZT-4974
			logger.info("company activate success, userId:{}, companyId:{}", userId, user.getCompanyId());
			return null;
		});
		return true;
	}

	// added for v2.0.7 chenbl KNZT-6595
	public CompanyAddressTO getCompanyAddress(String companyId) {
		CompanyAddressTO companyAddressTO = new CompanyAddressTO();
		companyAddressTO.setCountry(commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.COUNTRY));
		companyAddressTO.setProvince(commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.PROVINCE));
		companyAddressTO.setCity(commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.CITY));
		companyAddressTO.setAddressLine1(commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.ADDRESS_LINE1));
		companyAddressTO.setAddressLine2(commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.ADDRESS_LINE2));
		companyAddressTO.setZipCode(commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.ZIP_CODE));
		return companyAddressTO;
	}

	// added for v2.0.7 chenbl KNZT-6595
	public void updateCompanyAddress(CompanyAddressUpdateForm form) throws MessageException {
		if (form == null || StringUtils.isBlank(form.getCompanyId())) {
			return;
		}
		String companyId = form.getCompanyId();
		commSysCompInfoExtService.saveExtValue(companyId, Constants.CompInfoColExt.COUNTRY, Lists.newArrayList(form.getCountry()));
		commSysCompInfoExtService.saveExtValue(companyId, Constants.CompInfoColExt.PROVINCE, Lists.newArrayList(form.getProvince()));
		commSysCompInfoExtService.saveExtValue(companyId, Constants.CompInfoColExt.CITY, Lists.newArrayList(form.getCity()));
		commSysCompInfoExtService.saveExtValue(companyId, Constants.CompInfoColExt.ADDRESS_LINE1, Lists.newArrayList(form.getAddressLine1()));
		commSysCompInfoExtService.saveExtValue(companyId, Constants.CompInfoColExt.ADDRESS_LINE2, Lists.newArrayList(form.getAddressLine2()));
		commSysCompInfoExtService.saveExtValue(companyId, Constants.CompInfoColExt.ZIP_CODE, Lists.newArrayList(form.getZipCode()));
	}

	// added for v2.0.7 chenbl KNZT-6595
	// 获取个人信息
	public UserProfileTO getUserProfile(String userId) {
		if (StringUtils.isBlank(userId)) {
			return null;
		}
		User user = this.get(userId);
		if (user == null) {
			return null;
		}
		String companyId = user.getCompanyId();
		Company companyDb = commSysCompanyService.get(companyId);
		if (companyDb == null) {
			return null;
		}
		UserProfileTO userProfileTO = new UserProfileTO();
		userProfileTO.setUserId(user.getId());
		userProfileTO.setLoginName(user.getLoginName());
		userProfileTO.setName(user.getName());
		userProfileTO.setLastName(user.getLastName());
		userProfileTO.setCountry(user.getCountry());
		userProfileTO.setPhone(user.getPhone());
		userProfileTO.setCompanyName(companyDb.getShortName());
		userProfileTO.setCompanyBeginDate(DateUtils.formatDate(companyDb.getContractBeginDate() != null ? companyDb.getContractBeginDate() : companyDb.getBeginDate()));
		userProfileTO.setCompanyEndDate(DateUtils.formatDate(companyDb.getEndDate()));
		CompanyAddressTO companyAddress = getCompanyAddress(companyId);
		if (companyAddress != null) {
			userProfileTO.setCompanyCountry(companyAddress.getCountry());
			userProfileTO.setCompanyProvince(companyAddress.getProvince());
			userProfileTO.setCompanyCity(companyAddress.getCity());
			userProfileTO.setCompanyAddressLine1(companyAddress.getAddressLine1());
			userProfileTO.setCompanyAddressLine2(companyAddress.getAddressLine2());
			userProfileTO.setCompanyZipCode(companyAddress.getZipCode());
		}
		return userProfileTO;
	}

	// added for v2.0.7 chenbl KNZT-6595
	// 更新个人信息，注意该方法是覆盖模式，例如入参form.name为空，数据库中也会被更新成空
	public void updateProfile(UserProfileUpdateForm form) throws MessageException {
		MsgExceptionUtils.checkIsNull(form.getUserId());
		MsgExceptionUtils.failBuild(StringUtils.isBlank(form.getName()));
		MsgExceptionUtils.failBuild(StringUtils.isBlank(form.getLastName()));
		User user = this.get(form.getUserId());
		MsgExceptionUtils.checkIsNull(user);
		user.setName(form.getName());
		user.setLastName(form.getLastName());
		user.setCountry(form.getCountry());
		user.setPhone(form.getPhone());
		this.save(user);
		String companyId = user.getCompanyId();
		if (UserUtils.isCompanyAdmin(user.getDataScope())) {
			// 主账号支持填写公司级别的信息
			Company companyDb = commSysCompanyService.get(companyId);
			MsgExceptionUtils.checkIsNull(companyDb);
			MsgExceptionUtils.failBuild(StringUtils.isBlank(form.getCompanyName()));
			companyDb.setShortName(form.getCompanyName());
			commSysCompanyService.save(companyDb);
			CompanyAddressUpdateForm companyAddressUpdateForm = CompanyAddressUpdateForm.buildFrom(companyId, form);
			this.updateCompanyAddress(companyAddressUpdateForm);
		}
	}

	// added for v2.1.8 chenbl KNZT-6899
	public void updateUserDataScope(String userId, List<SysCompRole> latestCompRoles) {
		User user = userService.get(userId);
		if (user == null) {
			logger.error("用户不存在, userId: " + userId);
			return;
		}
		Integer dataScope = null;
		if (CollectionUtils.isNotEmpty(latestCompRoles)) {
			dataScope = latestCompRoles.stream()
					.map(SysCompRole::getDataScope)
					.filter(Objects::nonNull)
					.map(Integer::parseInt)
					.min(Integer::compareTo)
					.orElse(null);
		}
		user.setDataScope(dataScope == null ? null : dataScope.toString());
		userService.saveUserInfo(user);
	}

	/**
	 * added for v.2.2.7 fengsw KNZT-7785 获取用户在线充值金额
	 * 
	 * @param companyId
	 * @return
	 */
	public CompanyChargeTO getTopUpInfo(String companyId) {
		BigDecimal chargeAmount = contractDeliveryTransactionService.sumChangeUnitByCategoryAndType(companyId, TransactionCategoryEnum.TOP.getCode(), Lists.newArrayList(TransactionTypeEnum.TOP_UP.getCode()));
		String defaultLimit = commSysCompInfoExtService.retrieveInfoValueByCompanyIdAndTypeWithDefault(companyId, Constants.CompInfoColExt.CommExt.TOP_UP_AMOUNT_LIMIT, "3000");
		BigDecimal limit = new BigDecimal(defaultLimit);
		return new CompanyChargeTO(chargeAmount.abs(), limit);
	}

	/**
	 * 获取用户信息，包括分组、角色
	 * 补齐根分组以及admin角色
	 * 
	 * @param companyId
	 * @param userIdList
	 * @return
	 */
	public List<UserWithGroupRoleTO> getUserWithGroupRole(String companyId, List<String> userIdList) {
		List<UserWithGroupRoleTO> userList = dao.getUserWithGroupRole(companyId, userIdList);
		if (CollectionUtils.isEmpty(userList)) {
			return Lists.newArrayList();
		}
		CompGroup rootGroup = commSysCompGroupService.getRootGroup(companyId);
		SysCompRole adminRole = sysCompRoleService.get(Constants.Sys.COMP_ROLE_ID_SYS_ADMIN);
		for (UserWithGroupRoleTO user : userList) {
			if (StringUtils.isBlank(user.getGroupId())) {
				user.setGroupId(Constants.COMP_GROUP_ROOT_GROUP_ID);
				user.setGroupName(rootGroup.getOfficeName());
			}
			if (StringUtils.isBlank(user.getCompRoleId()) && StringUtils.equals(user.getUserId(), user.getMainUserId())) {
				user.setCompRoleId(adminRole.getId());
				user.setCompRoleDesc(adminRole.getDescription());
			}
		}
		userList.sort(Comparator.comparing(k -> CompRoleEnum.getSeqByCompRoleId(k.getCompRoleId())));
		return userList;
	}

	/**
	 * 批量获取用户信息
	 *
	 * @param companyId
	 * @param userIds
	 * @return
	 */
	public List<User> batchGetUserInfo(String companyId, List<String> userIds) {
		if (CollectionUtils.isEmpty(userIds)) {
			return Lists.newArrayList();
		}
		return dao.batchGetUserInfo(companyId, userIds);
	}
}
