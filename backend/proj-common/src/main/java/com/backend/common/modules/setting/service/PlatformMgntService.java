package com.backend.common.modules.setting.service;

import com.alibaba.fastjson.JSON;
import com.backend.common.entity.SysTemplate;
import com.backend.common.entity.SysUserRole;
import com.backend.common.entity.TblGlobalTrialApply;
import com.backend.common.entity.mapping.*;
import com.backend.common.model.OpenApiInterfaceInfoTO;
import com.backend.common.modules.api.entity.TblCompApiChargeUnit;
import com.backend.common.modules.api.service.CommTblCompApiChargeUnitService;
import com.backend.common.modules.benefit.entity.TblBenefitPool;
import com.backend.common.modules.benefit.model.BenefitPoolTO;
import com.backend.common.modules.benefit.service.CommTblBenefitDeliveryService;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolService;
import com.backend.common.modules.common.condition.TblGlobalOutboundListSyncCondition;
import com.backend.common.modules.common.condition.TblGlobalTrialApplyCondition;
import com.backend.common.modules.common.condition.TblUserFeedbackCondition;
import com.backend.common.modules.common.entity.TblGlobalOutboundListSync;
import com.backend.common.modules.common.model.DictItem;
import com.backend.common.modules.common.model.RelFileTO;
import com.backend.common.modules.delivery.service.CommCrmCompTrackingRecordService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.open.service.TrialApplyService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.condition.CompanyListCondition;
import com.backend.common.modules.setting.form.HkSearcherInfoSaveForm;
import com.backend.common.modules.setting.form.CompanyAdminForm;
import com.backend.common.modules.setting.form.TrialApplyOperateForm;
import com.backend.common.modules.setting.model.CompApiKeyTO;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.modules.setting.model.PlatformBriefCountTO;
import com.backend.common.modules.setting.model.ProxyUserOrderParamTO;
import com.backend.common.modules.setting.model.TrialApplyDetailVO;
import com.backend.common.modules.setting.model.TrialApplyListVO;
import com.backend.common.openapi.OpenApiUserInfoInterface;
import com.backend.common.openapi.model.UserSaveResponse;
import com.backend.common.openapi.model.UserSaveResponseResult;
import com.backend.common.proapi.ProUserInfoInterface;
import com.backend.common.service.*;
import com.backend.common.thread.TrialApplyRunnable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.*;
import com.qcc.frame.commons.ienum.company.CompCategoryEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.condition.CompUserListCondition;
import com.qcc.frame.jee.modules.sys.entity.*;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompUserListTO;
import com.qcc.frame.jee.modules.sys.form.CompanyAddressUpdateForm;
import com.qcc.frame.jee.modules.sys.service.*;
import com.backend.common.service.login.LoginAttemptRedisService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PlatformMgntService {
	@Autowired
	private UserService userService;
	@Autowired
	private CommSysCompanyService commSysCompanyService;
	@Autowired
	private CommSysCompGroupService commSysCompGroupService;
	@Autowired
	private SystemService systemService;
	@Autowired
	private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
	@Autowired
	private SysCompRoleService sysCompRoleService;
	@Autowired
	private CommTblCompReportOrderService commTblCompReportOrderService;
	@Autowired
	private CommTblReportChargeUnitService commTblReportChargeUnitService;
	@Autowired
	private SaasLoginService saasLoginService;
	@Autowired
	private DictService dictService;
	@Autowired
	private CommTblGlobalOutboundListSyncService outboundListSyncService;
	@Autowired
	private TransactionBusinessService transactionBusinessService;
	@Autowired
	private CommTblContractDeliveryProdAccService prodAccService;
	@Autowired
	private CommCrmCompTrackingRecordService commCrmCompTrackingRecordService;
	@Autowired
	private TrialApplyService trialApplyService;
	@Autowired
	private SysRelFileService relFileService;
	@Autowired
	private SysRelFileService sysRelFileService;
	@Autowired
	private ThreadPoolService threadPoolService;
	@Autowired
	private CommTblCompApiChargeUnitService apiChargeUnitService;
	@Autowired
	private RedisService redisService;
	@Autowired
	private CommTblUserFeedbackService userFeedbackService;
	@Autowired
	private SysCompInfoColExtService sysCompInfoColExtService;
	@Autowired
	private LoginAttemptRedisService loginAttemptRedisService;
	@Autowired
	private CommSysTemplateService commSysTemplateService;
	@Autowired
	private QccMailSenderService qccMailSenderService;
	@Autowired
	private CommCompUserService commCompUserService;
	@Autowired
	private CommTblBenefitDeliveryService benefitDeliveryService;
	@Autowired
	private CommTblBenefitPoolService benefitPoolService;
	@Autowired
	private CommSysCompInfoExtService commSysCompInfoExtService;


	protected static Logger logger = LoggerFactory.getLogger(PlatformMgntService.class);
	public void deleteCompany(List<String> idList) throws MessageException {
		if(idList != null) {
			Company company = null;
			for(String id : idList) {
				company = commSysCompanyService.get(id);
				if(company != null) {
					if (CompTypeEnum.SIGN.getCode().equals(company.getType())) { // added for v1.9.2 KNZT-4346
						logger.info("签约客户不能删除 跳过, companyId:{}", id);
						continue;
					}
					//added for v2.0.2 ZS-409 如果存在嵌入账号，先删除专业版子账号
					if(StringUtils.isNotBlank(company.getProMainLoginName())){
						this.deleteProUser(company.getProMainLoginName());
					}
					systemService.deleteUserByCompanyId(id);
					commSysCompGroupService.deleteCompGroupByCompanyId(id);
					commSysCompanyService.delete(company);
					// added for v2.0.9 fengsw KNZT-5944 删除平台账号时，开放平台查册人信息、主账号也要删除
					if (StringUtils.isNotBlank(company.getOpenApiUserId())) {
						OpenApiUserInfoInterface.deleteHkSearchInfo(company.getId());
						// added for KNZT-1419 删除账号，调用开放平台接口去删除用户信息
						OpenApiUserInfoInterface.deleteOpenApiUser(company.getOpenApiUserId());
					}
					// added for v1.5.8 KNZT-2587
					commTblReportChargeUnitService.updateStatusByCompanyId(company.getId(), UserUtils.getUserId(), "1");
				}
			}
		}
	}


	/**
	 * added for v2.0.5 fengsw KNZT-5574【后端】支持管理员可对个人自助账号禁用
	 * 禁用-账号结束日期置为当前时间-1天
	 * 启用-账号结束日期置为2199-01-01
	 *
	 * @param idList
	 * @throws MessageException
	 */
	public void disableOrRetrieveCompany(List<String> idList, String disableAccount) throws MessageException {
		if (idList != null) {
			Company company;
			for (String id : idList) {
				company = commSysCompanyService.get(id);
				if (company != null) {
					if (CompTypeEnum.SIGN.getCode().equals(company.getType())) {
						logger.info("签约客户不能删除 跳过, companyId:{}", id);
						continue;
					}
					if (Constants.YES.equals(disableAccount)) {
						company.setEndDate(DateUtils.getSpecialDateWithCurDate(-1));
					} else {
						company.setEndDate(DateUtils.parseDate("2199-01-01", DateUtils.DATE_FORMAT));
					}
					commSysCompanyService.save(company);
					// 禁用&撤销之后需要踢出用户
					saasLoginService.logoutUserByCompanyId(company.getId(), company.getName());
				}
			}
		}
	}

	/**
	 * added for v1.8.7 KNZT-3878
	 * 创建账号包含基本信息（试用\正式）
	 * @param form
	 * @return
	 * @throws MessageException
	 */
	public Company saveOrUpdateCompanyBasicInfo(CompanyAdminForm form) throws MessageException {
		checkSavaCompanyParam(form);
		// 增加账号有效期校验 公司的结束日期必须大于开始日期
		boolean inValidityPeriod = !DateUtils.isInValidityPeriod(DateUtils.parseDate(form.getBeginDate(), DateUtils.DATE_FORMAT), DateUtils.parseDate(form.getEndDate(), DateUtils.DATE_FORMAT));
		MsgExceptionUtils.failBuild(inValidityPeriod,"msg:公司的开始日期和结束日期不正确");

		Company company;
		if (StringUtils.isBlank(form.getId())) {
			MsgExceptionUtils.checkIsNull(form.getLoginPwd(),"msg:登录密码不能为空");
//			removed for lvcy v2.1.0 KNZT-6054
//			company = commSysCompanyService.getByName(form.getCompanyName());
//			MsgExceptionUtils.failBuild(company != null,"msg:公司名称已存在");
			User user = userService.getByLoginName(form.getLoginName());
			MsgExceptionUtils.failBuild(user != null,"msg:主账号登录名已存在");

			String companyId = IdGenUtil.uuid();
			//调用专业版接口，保存嵌入主账号
			String createUserResp = this.createProUser(companyId);
			boolean createProAcc =  !StringUtils.equals(createUserResp, "success");
			MsgExceptionUtils.failBuild(createProAcc,"msg:" + createUserResp);

			String userId = IdGenUtil.uuid();
			company = new Company(companyId);
			company.setIsNewRecord(true);
			company.setType(form.getType() == null ? CompTypeEnum.TRIAL.getCode() : form.getType());
			populateCompanyInfo(form, company);
			company.setMainUserId(userId);
			company.setDelFlag("0");
			company.setProMainLoginName(companyId);
			company.setSecretKey(IdGenUtil.generateSecretKey()); // added for v1.9.7 KNZT-4708
			company.setPayType(CompPayTypeEnum.PREPAID.getCode()); // added for lvcy v2.0.6 KNZT-5499
			company.setCategory(form.getCategory());
			commSysCompanyService.save(company);

			//【优化】【出境额度】国际版管理后台，增加内部账号标识，统计用量时排除 保存是否为内部账号的字典项
			if (form.getInnerAccount()) {
				insertInnerAccountDict(company.getId());
			}
			User insertUser = new User();
			insertUser.setIsNewRecord(true);
			insertUser.setId(userId);
			insertUser.setLoginName(form.getLoginName());
//			insertUser.setName(form.getLoginName());
			insertUser.setName("");
			insertUser.setPassword(UserService.entryptPassword(form.getLoginPwd()));
			insertUser.setCompany(new Company(company.getId()));
			insertUser.setLang("zh_CN");
			// 创建公司基本信息时，主账号状态置为 失效，等充值时，再修改状态为有效
			insertUser.setStatus(Constants.Sys.USER_STATUS_INACTIVE);
			String encryptUserId = CommCompUserService.generateEncryptUserId();
			insertUser.setEncryptUserId(encryptUserId); //  added for lvcy v2.0.0 KNZT-5142 生成加密用户id
			userService.save(insertUser);

			List<String> roleIdList = Optional.ofNullable(form.getRoleInfoTable()).orElse(new ArrayList<>()).stream().map(RoleInfoTO::getRoleId).collect(Collectors.toList());
			// 【角色管理】开通新账号时，自动保存通用模块角色、以及主账号角色
			roleIdList.add("role_main_admin");
//			roleIdList.add("role_sub_common"); removed for v1.9.8 KNZT-4538
			userService.insertUserRole(userId, roleIdList);

			CompGroup compGroup = new CompGroup();
			compGroup.setCompanyId(company.getId());
			compGroup.setGroupId("000001");
			compGroup.setParentGroupId("0");
			compGroup.setParentGroupIds("0");
			compGroup.setOfficeName(form.getCompanyName());
			compGroup.setSequence(1);
			commSysCompGroupService.save(compGroup);
			// 创建|修改开放平台api账号
			updateOpenApiAccountInfo(form, company);
			company.setIsNewRecord(false);
			commSysCompanyService.save(company);
			commSysCompInfoFuncCountService.insertNewSysCompInfoFuncCountRecord(companyId, BigDecimal.ZERO); // added for v1.9.8 KNZT-4538
			sysCompInfoColExtService.saveColExtValue(company.getId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_DUPLICATE_ORDER_REMIND, form.getDuplicateOrderRemind());
			// added for v2.3.2 fengsw KNZT-7930 新建公司账号，默认配置未激活用户邮件通知
			sysCompInfoColExtService.saveColExtValue(company.getId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_NOT_ACTIVATE_USER_NOTIFY, Constants.YES);
		} else {
			company = commSysCompanyService.getCompany(form.getId());
			MsgExceptionUtils.checkIsNull(company, "msg:账号不存在, 请重新查询后更新");
//			if (!StringUtils.equals(company.getName(), form.getCompanyName())) {
//				removed for lvcy v2.1.0 KNZT-6054
//				Company existed = commSysCompanyService.getByName(form.getCompanyName());
//				MsgExceptionUtils.failBuild(Objects.nonNull(existed), "msg:公司名称已存在");
//			}
			User user = userService.get(company.getMainUserId());
			MsgExceptionUtils.checkIsNull(user, "msg:主账号不存在");
			if (!StringUtils.equals(form.getLoginName(), user.getLoginName())) {
				User existed = userService.getByLoginName(form.getLoginName());
				MsgExceptionUtils.failBuild(Objects.nonNull(existed), "msg:主账号登录名已存在");
			}
			String orderId = commTblCompReportOrderService.existReportByCompanyId(company.getId());
			MsgExceptionUtils.failBuild(Objects.nonNull(form.getDataSave()) && !company.getDataSave().equals(form.getDataSave()) && StringUtils.isNotBlank(orderId), String.format("msg:用户已产生【%s】模式订单，无法更改数据存储模式", CompDataSaveEnum.getFullDesc(company.getDataSave())));
			//【优化】【出境额度】国际版管理后台，增加内部账号标识，统计用量时排除 内部账号配置不可修改
			Dict dict = dictService.getByTypeAndValue(Constants.DictType.INNER_ACCOUNT, company.getId());
			MsgExceptionUtils.failBuild(Objects.nonNull(dict) && !form.getInnerAccount(), "msg:内部账号配置不可修改，如有需求，请联系运维人员");
			//【优化】【额度共享】国际版账号变更账号结束日期|公司名称|用户账号，如果账号关联了开放平台账号，则需要把结束日期同步更新到开放平台
			String originDataSave = company.getDataSave();
			String originShowCredit = company.getShowCredit();
			String originCanUpgrade = company.getCanUpgrade();
			boolean originNeedMfa = userService.checkNeedMfaByCompanyId(company.getId());
			String originUiDefaultVersion = commSysCompanyService.getConfigUiDefaultVersion(company.getId());
			String originCategory = company.getCategory();
			if (StringUtils.isNotBlank(form.getLoginPwd())) {
				user.setPassword(UserService.entryptPassword(form.getLoginPwd()));
				if (!Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
					userService.savePwdHist4Security(PwdSecurityPolicyEnum.getByCode(form.getSecurityPolicy()), company.getId(), user.getId(), form.getLoginPwd());
				}
			}
			user.setLoginName(form.getLoginName());
//			user.setName(form.getLoginName());
			userService.save(user);
			populateCompanyInfo(form, company);
			// 创建|修改开放平台api账号
			updateOpenApiAccountInfo(form, company);
			commSysCompanyService.save(company);
			String colExtValueByTypeAndKey = sysCompInfoColExtService.getColExtValueByTypeAndKey(company.getId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_DUPLICATE_ORDER_REMIND);
			boolean duplicateOrderRemind = Constants.YES.equals(form.getDuplicateOrderRemind()) ^ Constants.YES.equals(StringUtils.defaultIfBlank(colExtValueByTypeAndKey, Constants.NO));
			if (duplicateOrderRemind) {
				sysCompInfoColExtService.saveColExtValue(company.getId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_DUPLICATE_ORDER_REMIND, form.getDuplicateOrderRemind());
			}
			List<SysUserRole> roleList = sysCompRoleService.getRoleListByUserId(company.getMainUserId());
			boolean roleChanged = sysCompRoleService.updateSysUserRoleInfo(form.getId(), form.getRoleInfoTable(), roleList);
			// 角色；计费模式；存储模式；修改时，需要踢出用户
			if (roleChanged || !StringUtils.equals(originDataSave, company.getDataSave()) || !StringUtils.equals(originShowCredit, company.getShowCredit()) || !StringUtils.equals(originCanUpgrade, company.getCanUpgrade())
					|| originNeedMfa != form.getEnableMfa()  // updated for v2.0.4 chenbl KNZT-5371
					|| !StringUtils.equals(originUiDefaultVersion, form.getUiDefaultVersion()) // updated for v2.1.7 chenbl KNZT-6477
			    	|| duplicateOrderRemind
					|| !StringUtils.equals(originCategory, form.getCategory())) {
				saasLoginService.logoutUserByCompanyId(company.getId(), company.getName());
			}
		}
		CompanyAddressUpdateForm companyAddress = form.getCompanyAddress();
		if (companyAddress != null) {
			companyAddress.setCompanyId(company.getId());
			commCompUserService.updateCompanyAddress(companyAddress);
		}
		// added for v2.0.4 chenbl KNZT-5371
		saveCompInfoColExt(form, company);
		// 保存跟进记录 added for v1.9.0 KNZT-4093
		commCrmCompTrackingRecordService.save(company.getId(), form.getTrackingContent(), CompTrackingRecordSourceEnum.COMPANY_MANAGEMENT.getCode());
		return company;
	}

	/**
	 * 更新开放平台账号信息
	 * @param form
	 * @param company
	 * @throws MessageException
	 */
	private void updateOpenApiAccountInfo(CompanyAdminForm form, Company company) throws MessageException {
		if (Objects.isNull(form) || Objects.isNull(company)) return;
		String openApiUserId = StringUtils.getNotBlankStr(form.getOpenApiUserId(), company.getOpenApiUserId());
		if (Boolean.TRUE.equals(form.getEnableOpenApi())) {
			String apiUserId = createApiUser(openApiUserId, null, company, form.getLoginName(), false);
			company.setOpenApiUserId(apiUserId);
		} else {
			if (StringUtils.isNotBlank(openApiUserId)) {
				// 调用开放平台接口去删除用户信息
				OpenApiUserInfoInterface.deleteOpenApiUser(openApiUserId);
				company.setOpenApiUserId(null);
			}
		}
	}

	// added for v2.0.4 chenbl KNZT-5371
	private void saveCompInfoColExt(CompanyAdminForm form, Company company) {
		sysCompInfoColExtService.saveColExtValue(company.getId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ENABLE_MFA, form.getEnableMfa() ? Constants.YES : Constants.NO);
		sysCompInfoColExtService.saveColExtValue(company.getId(), Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_SECURITY_POLICY, StringUtils.isNotBlank(form.getSecurityPolicy()) ? form.getSecurityPolicy() : PwdSecurityPolicyEnum.BAS.getCode()); // added for v2.0.4 chenbl KNZT-5442
		commSysCompanyService.configUiDefaultVersion4platformMgnt(company.getId(), form.getUiDefaultVersion()); // added for v2.1.7 chenbl KNZT-6477
	}

	/**
	 * added for v1.8.7 KNZT-3878 试用账号充值
	 *
	 * @param form
	 * @return
	 * @throws MessageException
	 */
	public Company rechargeTrialCompany(CompanyAdminForm form) throws MessageException {
		// 入参校验
		checkSavaCompanyParam(form);
		MsgExceptionUtils.failBuild(!Objects.equals(CompTypeEnum.TRIAL.getCode(), form.getType()), "msg:只允许试用账户号充值");
		MsgExceptionUtils.checkIsNull(form.getId(), "msg:账号ID不能为空");
		// 增加账号有效期校验 公司的结束日期必须大于开始日期
		boolean inValidityPeriod = !DateUtils.isInValidityPeriod(DateUtils.parseDate(form.getBeginDate(), DateUtils.DATE_FORMAT), DateUtils.parseDate(form.getEndDate(), DateUtils.DATE_FORMAT));
		MsgExceptionUtils.failBuild(inValidityPeriod, "msg:公司的开始日期和结束日期不正确");
		Company company = commSysCompanyService.getCompany(form.getId());
		MsgExceptionUtils.checkIsNull(company, "msg:账号不存在, 请重新查询后更新");
		User user = userService.get(company.getMainUserId());
		MsgExceptionUtils.checkIsNull(user, "msg:主账号不存在");
		SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.lockByCompanyId(company.getId());
		MsgExceptionUtils.checkIsNull(funcCount, "msg:账号额度不存在, 请重新查询后更新");

		//（国际版服务、境外服务）额度配置更新
		commSysCompInfoFuncCountService.updateFuncTotalCount(funcCount, form.getFunctionTotalCount()); // updated for v1.9.8 KNZT-4538
		// 更新服务列表产品信息
		boolean serviceHasChanged = commTblReportChargeUnitService.saveOrUpdateSysCompChargeUnitList(company.getId(), form.getCompChargeUnitList());
		// 更新api服务列表产品信息
		Boolean oriCompanyEnableApi = company.getEnableApi();
		company.setEnableApi(CollectionUtils.isNotEmpty(form.getApiChargeUnitSaveTOList()));
		apiChargeUnitService.batchSave(form.getId(), null, form.getApiChargeUnitSaveTOList());
		boolean enableApiChanged = !Objects.equals(oriCompanyEnableApi, company.getEnableApi());
		// 保存api产品服务价格 added for v1.9.7 KNZT-4708
		if (Boolean.TRUE.equals(company.getEnableApi())) {
			if (StringUtils.isBlank(company.getSecretKey())) {
				company.setSecretKey(IdGenUtil.generateSecretKey());
			}
			// 生成 api_mock key、secretKey
			commSysCompanyService.generateApiMockKeyAndSecretKey(company.getId());
		}
		commSysCompanyService.save(company);
		// 角色；计费模式；存储模式；修改时，需要踢出用户
		if (serviceHasChanged || enableApiChanged) {
			saasLoginService.logoutUserByCompanyId(company.getId(), company.getName());
		}
		// 保存跟进记录 added for v1.9.0 KNZT-4093
		commCrmCompTrackingRecordService.save(company.getId(), form.getTrackingContent(), CompTrackingRecordSourceEnum.TRIAL_RECHARGE.getCode());
		// 试用充值时需要保证 主账号处于有效状态
		if (Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
			/* updated for v2.0.4 chenbl KNZT-5442
			user.setStatus(Constants.Sys.USER_STATUS_ACTIVE);
			// 首次试用充值时 账号邮件发送 告知客户账号、密码
			String password = UserService.encryptWithMd5(user.getLoginName());
			user.setPassword(UserService.entryptPassword(password));
			userService.save(user);
			commSysCompanyService.sendNotification(company, password);*/
			commSysCompanyService.sendActivateUserEmail4MainUser(company, true);//updated for v2.0.5 fengsw KNZT-5602 正式账号激活邮件需要增加密送人
		}
		// 试用账号配置境外下单次数限制 added for v1.9.8 KNZT-4538
		if (CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
			dictService.saveOrUpdateValue(Constants.DictType.OVERSEAS_ORDER_LIMIT, company.getId(), form.getOverseasOrderLimit() == null ? "0" : form.getOverseasOrderLimit().toString());
		}
		return company;
	}

	// updated for v2.0.7 chenbl KNZT-6595
	public Company createSelfAccountCompany(Integer companyType, String companyName, String firstName, String lastName, String email, String phone, String country, Integer accountNum, String category) throws MessageException {
		User existUser = userService.getByLoginName(email);
		MsgExceptionUtils.failBuild(Objects.nonNull(existUser),"msg:已存在该用户");
		phone = StringUtils.removeAreaCode4Phone(phone);
		String proQccComKey = Global.getConfig("pro.qcc.com.key");
		String proQccComSecretKey = Global.getConfig("pro.qcc.com.secretKey");
		String openApiQccComKey = Global.getConfig("openApi.qcc.com.key");
		String openApiQccComSecretKey = Global.getConfig("openApi.qcc.com.secretKey");

		// 保存公司基本信息
		String companyId = IdGenUtil.uuid();
		String userId = IdGenUtil.uuid();
		Company company = new Company(companyId);
		company.setIsNewRecord(true);
		company.setType(companyType);
		company.setName(companyName);
		company.setShortName(companyName);
		company.setEmail(email);
		company.setPhone(phone);
		company.setBeginDate(DateUtils.getCurDate());
		company.setEndDate(DateUtils.parseDate("2199-01-01", DateUtils.DATE_FORMAT));
		company.setMainUserId(userId);
		company.setPurchaseAccountNum(CompTypeEnum.CLIENT.getCode().equals(companyType) ? 0 : accountNum);
		company.setEnableOpenApi(false);
		company.setOpenApiUserId(null);
		company.setProMainLoginName(companyId);
		company.setCountStd(SysConstants.UserAction.COUNT_STD_DT);
		company.setBusinessScenario(null);
		company.setDataSave(CompDataSaveEnum.SAVE.getCode());
		company.setShowCredit(CompShowCreditEnum.Y.getCode());
		company.setCanUpgrade(CompCanUpgradeEnum.N.getCode());
		company.setProKey(proQccComKey);
		company.setProSecretKey(proQccComSecretKey);
		company.setYunjuKey(openApiQccComKey);
		company.setYunjuSecretKey(openApiQccComSecretKey);
		company.setPayType(CompPayTypeEnum.REAL_TIME.getCode()); // added for lvcy v2.0.6 KNZT-5499
		company.setCategory(StringUtils.defaultIfBlank(category, CompCategoryEnum.NON_BANK.getCode()));
		company.setDelFlag("0");
		commSysCompanyService.save(company);
		CompGroup compGroup = new CompGroup();
		compGroup.setCompanyId(company.getId());
		compGroup.setGroupId("000001");
		compGroup.setParentGroupId("0");
		compGroup.setParentGroupIds("0");
		compGroup.setOfficeName(companyName);
		compGroup.setSequence(1);
		commSysCompGroupService.save(compGroup);
		// 保存用户基本信息
		String pwd = IdGenUtil.randomBase62(6);
		User insertUser = new User();
		insertUser.setIsNewRecord(true);
		insertUser.setId(userId);
		insertUser.setLoginName(email);
		insertUser.setName(firstName);
		insertUser.setLastName(lastName);
		insertUser.setPhone(phone);
		insertUser.setCountry(country);
		insertUser.setPassword(userService.entryptPassword(pwd));
		insertUser.setCompany(new Company(company.getId()));
		insertUser.setLang("zh_CN");
		insertUser.setStatus(Constants.Sys.USER_STATUS_INACTIVE);
		String encryptUserId = CommCompUserService.generateEncryptUserId();
		insertUser.setEncryptUserId(encryptUserId);
		userService.save(insertUser);
		if (CompTypeEnum.COMPANY_SELF.getCode().equals(companyType)) {
			List<String> roleIdList = Lists.newArrayList("role_main_admin", "role_sub_common");
			userService.insertUserRole(userId, roleIdList);
		}
		// 保存用户角色
		// removed for v1.9.8 KNZT-4538
//		List<String> roleIdList = new ArrayList<>();
//		roleIdList.add(Constants.Role.ID_ROLE_SUB_HK_GLOBAL_SERVICE);
//		roleIdList.add(Constants.Role.ID_ROLE_SUB_GLOBAL_SERVICE);
//		userService.insertUserRole(userId, roleIdList);
		// 保存服务列表产品信息
		List<CompChargeUnitTO> compChargeUnits = commTblReportChargeUnitService.getCompChargeUnits(Constants.DEFAULT_COMPANY_ID);
		// 根据配置来建产品 updated for lvcy v2.0.9 KNZT-5362
		List<String> selfAccReportTypeList = ConfigUtils.listValueByTypeAndKey("self_acc", "report_type");
		for (CompChargeUnitTO chargeUnit : compChargeUnits) {
			if (selfAccReportTypeList.contains(chargeUnit.getReportType())) {
				chargeUnit.setEnabled(Constants.YES);
			} else {
				chargeUnit.setEnabled(Constants.NO);
			}
			chargeUnit.setContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID);
		}
		commTblReportChargeUnitService.saveOrUpdateSysCompChargeUnitList(company.getId(), compChargeUnits);
		// 保存用户额度，设为最大值
		// updated for v1.9.8 KNZT-4538
//		List<FunctionCountInfoTO> functionTableList = new ArrayList<>();
//		functionTableList.add(FunctionCountInfoTO.build(Constants.FunctionTable.ID_HK_SERVICE_ID, Constants.MAX_UNIT));
//		functionTableList.add(FunctionCountInfoTO.build(Constants.FunctionTable.ID_REPORT_ID, Constants.MAX_UNIT));
		// updated for v1.9.8 KNZT-4992
//		SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.lockByCompanyId(companyId);
		commSysCompInfoFuncCountService.insertNewSysCompInfoFuncCountRecord(companyId, BigDecimal.ZERO);

		//调用专业版接口，保存嵌入主账号
		String createUserResp = this.createProUser(companyId);
		boolean createProAcc = !StringUtils.equals(createUserResp, "success");
		MsgExceptionUtils.failBuild(createProAcc,"msg:" + createUserResp);
		return company;
	}


	public static String createApiUser(String openApiUserId, List<CompChargeUnitTO> chargeUnitTOList,
							  Company company, String loginName, boolean serviceHasChanged) throws MessageException {
//		List<OpenApiInterfaceInfoTO> apiPriceList = getOpenApiInterfaceInfo(chargeUnitTOList);
//		if (CollectionUtils.isEmpty(apiPriceList)) {
//			throw new MessageException("msg:境外服务接口产品服务单价缺失");
//		}
		// 编辑账号信息时，如果选择了境外服务，也要开通openapi账号
		if (StringUtils.isBlank(openApiUserId)) {
			openApiUserId = createOpenApiUser(company.getId(), company.getName(), loginName, null);
			//创建API账号失败，报错并提示到前端
			if (openApiUserId == null) {
				throw new MessageException("msg:账号创建失败，API账号已存在");
			}
		}
		// 当价格 角色 变化时，调用接口更新
		boolean relatedToOpenApiUser = StringUtils.isNotBlank(openApiUserId);
		if (relatedToOpenApiUser || serviceHasChanged) {
			updateOpenApiUser(company.getId(), openApiUserId, company.getName(), loginName, DateUtils.formatDate(company.getBeginDate(), DateUtils.DATE_FORMAT), DateUtils.formatDate(company.getEndDate(), DateUtils.DATE_FORMAT), null);
		}
		return openApiUserId;
	}

	/**
	 * added for v1.8.7 KNZT-3878
	 * 企业信息组装
	 * @param form
	 * @param company
	 */
	private void populateCompanyInfo(CompanyAdminForm form, Company company) {
		String proQccComKey = Global.getConfig("pro.qcc.com.key");
		String proQccComSecretKey = Global.getConfig("pro.qcc.com.secretKey");
		String openApiQccComKey = Global.getConfig("openApi.qcc.com.key");
		String openApiQccComSecretKey = Global.getConfig("openApi.qcc.com.secretKey");
		company.setName(form.getCompanyName());
		company.setShortName(form.getShortName());//英文名称
		company.setEmail(form.getLoginName());//国际版的登陆用户都是邮箱形式，因此前端移除了邮箱，这里统一使用主账号的loginName
		company.setBeginDate(DateUtils.parseDate(form.getBeginDate(), DateUtils.DATE_FORMAT));
		company.setEndDate(DateUtils.parseDate(form.getEndDate(), DateUtils.DATE_FORMAT));
		company.setPhone(form.getPhone());
		company.setPurchaseAccountNum(form.getSubUserNum() + 1);
		company.setProKey(proQccComKey);
		company.setProSecretKey(proQccComSecretKey);
		company.setYunjuKey(openApiQccComKey);
		company.setYunjuSecretKey(openApiQccComSecretKey);
		company.setCountStd(SysConstants.UserAction.COUNT_STD_DT);
		company.setEnableOpenApi(form.getEnableOpenApi());
		company.setBusinessScenario(form.getBusinessScenario());
		company.setDataSave(StringUtils.isNotBlank(form.getDataSave()) ? form.getDataSave() : CompDataSaveEnum.SAVE.getCode());//阅后即焚
		company.setShowCredit(StringUtils.isNotBlank(form.getShowCredit()) ? form.getShowCredit() : CompShowCreditEnum.Y.getCode()); //展示产品单价
		company.setCanUpgrade(StringUtils.isNotBlank(form.getCanUpgrade()) ? form.getCanUpgrade() : CompCanUpgradeEnum.Y.getCode()); //是否允许订单升级
		company.setCategory(form.getCategory());
	}


// removed for v1.9.8 KNZT-4538
//	/**
//	 * added for v1.8.7 KNZT-3878
//	 * 校验账号已分配的角色（大陆企业服务、境外服务）是否能够移除
//	 * 以是否产生对应的订单数据来判断：如果已经存在相应的订单信息，则不允许移除该角色
//	 * @param companyId
//	 * @param mainUserId
//	 * @param roleInfoTable
//	 * @param roleList
//	 * @param roleEnumList
//	 * @throws MessageException
//	 */
//	private void checkRoleCanRemove(String companyId, String mainUserId, List<RoleInfoTO> roleInfoTable,List<SysUserRole> roleList, List<RoleEnum> roleEnumList) throws MessageException {
//		if (StringUtils.isAnyBlank(companyId, mainUserId) || CollectionUtils.isEmpty(roleList) || CollectionUtils.isEmpty(roleEnumList) || CollectionUtils.isEmpty(roleInfoTable)) return;
//		List<String> currentRoleIdList = roleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
//		List<String> newRoleIdList = roleInfoTable.stream().map(RoleInfoTO::getRoleId).collect(Collectors.toList());
//		for (RoleEnum roleEnum : roleEnumList) {
//			checkRoleCanRemove(companyId, roleEnum, currentRoleIdList, newRoleIdList);
//		}
//	}

	/**
	 * added for v1.6.4 KNZT-2806【优化】【出境额度】国际版管理后台，增加内部账号标识，统计用量时排除 添加内部账号字典项
	 *
	 * @param value
	 */
	private void insertInnerAccountDict(String value) {
		String id = IdGenUtil.uuid();
		Dict dict = new Dict();
		dict.setId(id);
		dict.setValue(value);
		dict.setType(Constants.DictType.INNER_ACCOUNT);
		dict.setLabel(Constants.DictLabel.SYS_COMP_INFO_ID);
		dict.setDescription("内部账号，value代表sys_comp_info.id");
		dict.setSort(0);
		dict.setIsNewRecord(true);
		dictService.save(dict);
	}

	/**
	 * added for v1.5.8 KNZT-2587 取出境外接口数据
	 *
	 * @param compChargeUnitList
	 * @return
	 */
	private static List<OpenApiInterfaceInfoTO> getOpenApiInterfaceInfo(List<CompChargeUnitTO> compChargeUnitList) {
		if (CollectionUtils.isNotEmpty(compChargeUnitList)) {
			// updated for v1.7.8 KNZT-3407【国际版】【管理平台】编辑新的香港产品KYC Basic + AR、AR服务价目配置时支持同步开放平台接口单价修改
			List<SysConfig> configList = ConfigUtils.listConfigByType("global_product_type_open_api_code_mapping");
			if (CollectionUtils.isNotEmpty(configList)) {
				Map<String, String> apiCodeMap = configList.stream().collect(Collectors.toMap(SysConfig::getConfigKey, SysConfig::getConfigValue, (v1, v2) -> v1));
				return compChargeUnitList.stream().filter(item -> StringUtils.equalsAny(item.getUnitGroup(), Constants.OpenApiInterfaceType.HK, Constants.OpenApiInterfaceType.SG)).map(
						item -> {
							OpenApiInterfaceInfoTO openApiInterfaceInfoTO = new OpenApiInterfaceInfoTO();
							openApiInterfaceInfoTO.setPrice(item.getUnit());
							openApiInterfaceInfoTO.setReportGroup(item.getReportGroup());
							openApiInterfaceInfoTO.setReportType(item.getReportType());
							openApiInterfaceInfoTO.setOpenApiCode(apiCodeMap.get(item.getReportType()));
							openApiInterfaceInfoTO.setType(item.getReportGroup());
							return openApiInterfaceInfoTO;
						}
				).collect(Collectors.toList());
			}
		}
		return null;
	}

	// removed for v1.9.8 KNZT-4538
//	/**
//	 * added for v1.6.0 KNZT-2560 校验角色是否可以被删除
//	 * updated for v1.8.2 KNZT-3441
//	 * @param companyId
//	 * @param roleEnum          角色枚举
//	 * @param currentRoleIdList 当前用户的角色
//	 * @param newRoleIdList     修改后的用户角色
//	 * @return
//	 */
//	private void checkRoleCanRemove(String companyId, RoleEnum roleEnum, List<String> currentRoleIdList, List<String> newRoleIdList) throws MessageException {
//		String roleErrorFormatter = "msg:用户已产生【%s】角色订单，无法删除角色";
//
//		if (currentRoleIdList.contains(roleEnum.getRoleId()) && !newRoleIdList.contains(roleEnum.getRoleId())) {
//			String existOrder = commTblCompReportOrderService.existReportByCompanyAndGroup(companyId, roleEnum.getReportGroupList());
//			if (StringUtils.isNotBlank(existOrder)) {
//				Role role = systemService.getRole(roleEnum.getRoleId());
//				if (Objects.nonNull(role)) {
//					throw new MessageException(String.format(roleErrorFormatter, role.getName()));
//				} else {
//					throw new MessageException(String.format(roleErrorFormatter, roleEnum.getRoleEnName()));
//				}
//			}
//		}
//	}

	// added for v1.5.0 KNZT-2253
	private boolean checkHasHkRole(CompanyAdminForm form) {
		if (CollectionUtils.isEmpty(form.getRoleInfoTable())) {
			return false;
		}
		List<String> roleIds = form.getRoleInfoTable().stream().map(RoleInfoTO::getRoleId).collect(Collectors.toList());
		List<Role> roles = systemService.findRoleByRoleIdList(roleIds);
		if (CollectionUtils.isEmpty(roles)) {
			return false;
		}
		return roles.stream().anyMatch(t -> StringUtils.equals(t.getEnname(), Constants.Role.EN_NAME_ROLE_HK_GLOBAL_SERVICE));
	}

	/**
	 * added for v1.3.9 KNZT-2131
	 * 校验保存公司表单参数
	 * @param form 入参
	 */
	private void checkSavaCompanyParam(CompanyAdminForm form) throws MessageException {
		if (StringUtils.isNotBlank(form.getBusinessScenario()) && form.getBusinessScenario().length() > 2000) {
			throw new MessageException("msg:应用场景参数长度超出2000，请重新输入");
		}
		if (StringUtils.isNotBlank(form.getCompanyName()) && form.getCompanyName().length() > 500) {
			throw new MessageException("msg:公司名称长度超出500，请重新输入");
		}
		if (StringUtils.isNotBlank(form.getShortName()) && form.getShortName().length() > 200) {
			throw new MessageException("msg:英文名称长度超出200，请重新输入");
		}
		if (StringUtils.isNotBlank(form.getLoginName()) && form.getLoginName().length() > 50) {
			throw new MessageException("msg:主账号登录名长度超出50，请重新输入");
		}
		if (StringUtils.isNotBlank(form.getPhone()) && form.getPhone().length() > 20) {
			throw new MessageException("msg:联系电话长度超出20，请重新输入");
		}

		// added for v1.5.8 KNZT-2587
		if (CollectionUtils.isNotEmpty(form.getCompChargeUnitList())) {
			// 去掉角色和服务限制关系 removed for v1.9.8 KNZT-4538
			Map<String, TblReportChargeUnit> reportTypeMap = commTblReportChargeUnitService.listChargeUnitByCompanyId(Constants.ChargeUnit.DEFAULT_COMPANY_ID).stream().collect(Collectors.toMap(TblReportChargeUnit::getReportType, Function.identity(), (a, b) -> a));
			for (CompChargeUnitTO item : form.getCompChargeUnitList()) {
				if (item.getUnit() == null || BigDecimal.ZERO.compareTo(item.getUnit()) >= 0) {
					throw new MessageException("msg:" + item.getRemark() + "产品服务单价须大于0，请重新输入");
				}
				String reportType = item.getReportType();
				TblReportChargeUnit dbEntity = reportTypeMap.get(reportType);
				MsgExceptionUtils.checkIsNull(dbEntity, "msg:" + item.getRemark() + "产品服务类型不存在，请重新输入");
				item.setReportGroup(dbEntity.getReportGroup());
				item.setReportName(dbEntity.getReportName());
				item.setUnitGroup(dbEntity.getUnitGroup());
				item.setGenDays(dbEntity.getGenDays());
				item.setSeq(dbEntity.getSeq());
			}
		}
	}

	/**
	 * added for KNZT-1419 创建开放平台用户账号
	 * update for v1.5.8 KNZT-2587
	 *
	 * @param companyId
	 * @param companyName
	 * @param loginName
	 * @param apiPriceList
	 * @return
	 * @throws MessageException
	 */
	private static String createOpenApiUser(String companyId, String companyName, String loginName, List<OpenApiInterfaceInfoTO> apiPriceList) throws MessageException {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("companyId", companyId);
		paramMap.put("kycCompanyName", companyName);
		paramMap.put("email", loginName);
		paramMap.put("apiPrices", apiPriceList);
		UserSaveResponse userSaveResponse = OpenApiUserInfoInterface.saveOpenApiUser(paramMap);
		if(Objects.nonNull(userSaveResponse)){
			return userSaveResponse.getUserId();
		}
		return null;
	}

	/**
	 * added for KNZT-1419 更新开放平台账号信息
	 * update for v1.5.8 KNZT-2587
	 *
	 * @param companyId
	 * @param openApiUserId
	 * @param companyName
	 * @param loginName
	 * @param beginDate
	 * @param endDate
	 * @param apiPriceList
	 * @throws MessageException
	 */
	public static void updateOpenApiUser(String companyId, String openApiUserId, String companyName, String loginName, String beginDate, String endDate, List<OpenApiInterfaceInfoTO> apiPriceList) throws MessageException {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("companyId", companyId);
		paramMap.put("userId", openApiUserId);
		paramMap.put("kycCompanyName", companyName);
		paramMap.put("email", loginName);
		// added for KNZT-2199【优化】【额度共享】国际版账号变更账号结束日期，如果账号关联了开放平台账号，则需要把结束日期同步更新到开放平台
		paramMap.put("serviceStartDate", beginDate);
		paramMap.put("serviceEndDate", endDate);
		paramMap.put("apiPrices", apiPriceList);
		OpenApiUserInfoInterface.updateOpenApiUser(paramMap);
	}

	public Page<CompanyListInfo> pageListCompanyInfo(CompanyListCondition condition) throws MessageException {
		Page<CompanyListInfo> resultPage = commSysCompanyService.pageListCompanyInfo(condition);
		return resultPage;
	}

	public CompanyListInfo getCompanyDetailInfo(String companyId) throws MessageException {
		CompanyListInfo companyDetail = commSysCompanyService.getCompanyDetailInfo(companyId);
		return companyDetail;
	}

	/**
	 *v2.0.2 ZS-409 调用专业版接口，创建用户
	 * @param proMainLoginName
	 * @return string
	 */
	private String createProUser(String proMainLoginName) throws MessageException{
		return ProUserInfoInterface.saveProUser(proMainLoginName);
	}

	/**
	 *v2.0.2 ZS-409 调用专业版接口，删除用户
	 * @param proMainLoginName
	 * @return string
	 */
	private String deleteProUser(String proMainLoginName) throws MessageException{
		return ProUserInfoInterface.deleteProUser(proMainLoginName);
	}

	/**
	 * v2.0.5 ZS-416 获取所有角色信息
	 * added for v2.0.6 ZS-453 【角色管理】提供角色列表查询接口，创建公司时加载角色列表下拉框的值
	 *
	 * @return FunctionCountInfoVO
	 */
	public List<RoleInfoTO> getRoleList() {
		return sysCompRoleService.getRoleList();
	}


	/**
	 * 	added for v2.0.6 ZS-454【角色管理】公司详情接口优化，查询已分配给公司的角色列表
	 *  标记是否已存在被选中的角色
	 *
	 * @param companyId
	 * @return
	 */
	public List<RoleInfoTO> getUserRoleWithSelectedList(String companyId) throws MessageException {
		List<RoleInfoTO> roleList = sysCompRoleService.getRoleList();
		if(roleList == null || roleList.size() <= 0){
			throw new MessageException("msg:系统权限信息不存在，请检查系统权限配置");
		}
		List<RoleInfoTO> userSelectedRoles = sysCompRoleService.getMainUserRoleList(companyId);
		if (userSelectedRoles != null && userSelectedRoles.size() > 0) {
			Map<String, List<RoleInfoTO>> listMap = userSelectedRoles.stream().collect(Collectors.groupingBy(RoleInfoTO::getRoleId));
			for (RoleInfoTO roleInfoTO : roleList) {
				if (listMap.containsKey(roleInfoTO.getRoleId())) {
					// 标记为该公司已配置的权限
					roleInfoTO.setSelectedFlag(1);
				}
			}
		}
		return roleList;
	}


// removed for v1.9.8 KNZT-4538
//	/**
//	 * updated for v2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口
//	 *
//	 * 根据选中的角色id及公司id查询服务列表数据
//	 * 再根据functionId去获取当前系统的额度信息
//	 * @param companyId
//	 * @param roleIds
//	 * @return FunctionCountInfoVO
//	 */
//	public List<FunctionTableInfoTO> getFunctionTableInfoByRoleIds(String companyId, List<String> roleIds) throws MessageException {
//		List<FunctionTableInfoTO> funcTableInfoList;
//		if (StringUtils.isNotBlank(companyId)) {
//			funcTableInfoList = commSysCompInfoFuncCountService.getFunctionTableInfoByRoleIdsAndCompanyId(companyId, roleIds);
//		} else {
//			funcTableInfoList = commSysCompInfoFuncCountService.getFunctionTableInfoByRoleIds(roleIds);
//		}
//		if (funcTableInfoList != null && funcTableInfoList.size() > 0) {
//			// 获取公司已消耗额度
//			for (FunctionTableInfoTO functionTableInfoTO : funcTableInfoList) {
//				List<String> unitGroupList;
//				if(Constants.FunctionTable.ID_HK_SERVICE_ID.equals(functionTableInfoTO.getFunctionTableId())) {
//					unitGroupList = UnitGroupEnum.globalFunctionTableUnitGroup();
//				} else {
//					unitGroupList = Collections.singletonList(UnitGroup2FunctionTableMapEnum.getUnitGroupByFunctionTableId(functionTableInfoTO.getFunctionTableId()));
//				}
//				BigDecimal consumedCount = commTblCompReportOrderService.sumOrderTotalUnitByCompanyId(companyId, unitGroupList);
//				functionTableInfoTO.setConsumedCount(consumedCount);
//			}
//		}
//		return funcTableInfoList;
//	}

	/**
	 * added for KNZT-1426
	 * 批量patch未绑定开放平台userId的存量用户，推送开放平台创建账号，然后绑定openApiUserId
	 */
	public void patchKycUser(List<String> idList) {
		List<CompanyPatchInfoListTO> patchList = commSysCompanyService.listCompanyToOpenApiForPatch(idList);
		if (CollectionUtils.isNotEmpty(patchList)) {
			String jsonStr = JSON.toJSONString(patchList);
			UserSaveResponseResult userSaveResponseResult = OpenApiUserInfoInterface.patchKycUser(jsonStr);
			if (userSaveResponseResult != null && CollectionUtils.isNotEmpty(userSaveResponseResult.getResult())) {
				for (UserSaveResponse userSaveResponse : userSaveResponseResult.getResult()) {
					if (StringUtils.isNoneBlank(userSaveResponse.getUserId(), userSaveResponse.getCompanyId())) {
						commSysCompanyService.updateOpenApiUserId(userSaveResponse.getCompanyId(), userSaveResponse.getUserId());
					} else {
						logger.info("companyId:{} bound openApi account failed, msg:{}", userSaveResponse.getCompanyId(), userSaveResponse.getMessage());
					}
				}
			}
		}
	}

	/**
	 * added for v1.5.6 KNZT-2561
	 */
	public Page<CompUserListTO> listCompUser(CompUserListCondition condition) {
		List<CompUserListTO> compUserListTos = userService.listCompUser(condition);
		condition.getPage().setList(compUserListTos);
		// added for v2.0.8 chenbl KNZT-5815
		User mainUserByCompanyId = userService.getMainUserByCompanyId(condition.getCompanyId());
		for (CompUserListTO compUserListTo : compUserListTos) {
			compUserListTo.setMainUserFlag(StringUtils.equals(compUserListTo.getId(), mainUserByCompanyId.getId()));
		}
		return condition.getPage();
	}

	// added for v2.0.8 chenbl KNZT-5815
	public void deleteCompUser(Set<String> userIdList) throws MessageException {
		if (CollectionUtils.isNotEmpty(userIdList)) {
			for (String userId : userIdList) {
				if (StringUtils.isBlank(userId)) {
					continue;
				}
				User user = userService.get(userId);
				if (user == null) {
					continue;
				}
				if (commSysCompanyService.isMainUser(user.getCompanyId(), userId)) {
					throw new MessageException("msg:不能删除主账号");
				}
				user.setId(userId);
				user.setLoginFlag(Constants.UserRelationType.NOT_DELETE);
				userService.delete(user);
			}
		}
	}

	/**
	 * added for v1.5.8 KNZT-2587
	 *
	 * @param companyId
	 * @return
	 */
	public List<TblReportChargeUnit> listChargeUnitByCompanyId(String companyId) {
		if(StringUtils.isBlank(companyId)) {
			return commTblReportChargeUnitService.listChargeUnitByCompanyIdWithDelFlag(Constants.ChargeUnit.DEFAULT_COMPANY_ID, "0");
		}
		return commTblReportChargeUnitService.listChargeUnitByCompanyIdWithDelFlag(companyId, "0");
	}

	// updated for v1.9.8 KNZT-4538
	public CompChargeUnitInfoTO getCompChargeUnitInfo(String id) {
		List<TblReportChargeUnit> defaultReportChargeUnits = commTblReportChargeUnitService.listChargeUnitByCompanyId(Constants.ChargeUnit.DEFAULT_COMPANY_ID);
		Map<String, String> defaultChoseMap = ConfigUtils.getConfigMap("service_default_chose");
		defaultReportChargeUnits.forEach(k -> {
			String configVal = defaultChoseMap.getOrDefault(k.getReportType(), "Y");
			if (Constants.NO.equals(configVal)) {
				k.setEnabled(Constants.NO);
			}
		});
		List<TblCompApiChargeUnit> defaultApiChargeUnits = apiChargeUnitService.getByCompanyId(Constants.ChargeUnit.DEFAULT_COMPANY_ID);

		List<TblReportChargeUnit> reportChargeUnits = new ArrayList<>();
		List<TblCompApiChargeUnit> apiChargeUnits = new ArrayList<>();
		if (StringUtils.isNotBlank(id)) {
			reportChargeUnits = commTblReportChargeUnitService.listChargeUnitByCompanyId(id);
			apiChargeUnits = apiChargeUnitService.getByCompanyId(id);
		}
		if (CollectionUtils.isNotEmpty(reportChargeUnits)) {
			Set<String> reportTypesDb = reportChargeUnits.stream().map(TblReportChargeUnit::getReportType).collect(Collectors.toSet());
			reportChargeUnits.addAll(defaultReportChargeUnits.stream().filter(k -> !reportTypesDb.contains(k.getReportType()))
							.peek(k -> k.setEnabled(Constants.NO))
					.collect(Collectors.toList()));
		} else {
			reportChargeUnits = defaultReportChargeUnits;
		}

		if (CollectionUtils.isNotEmpty(apiChargeUnits)) {
			Set<String> apiTypesDb = apiChargeUnits.stream().map(TblCompApiChargeUnit::getApiType).collect(Collectors.toSet());
			apiChargeUnits.addAll(defaultApiChargeUnits.stream().filter(k -> !apiTypesDb.contains(k.getApiType()))
					.peek(k -> k.setEnabled(Constants.NO))
					.collect(Collectors.toList()));
			reportChargeUnits.sort(Comparator.comparing(TblReportChargeUnit::getSeq));
			apiChargeUnits.sort(Comparator.comparing(TblCompApiChargeUnit::getSeq));
		} else {
			apiChargeUnits = defaultApiChargeUnits;
		}

		CompChargeUnitInfoTO compChargeUnitInfoTO = new CompChargeUnitInfoTO();
		compChargeUnitInfoTO.setApiChargeUnitList(apiChargeUnits);
		List<TblReportChargeUnit> mapChargeUnits = reportChargeUnits.stream().filter(k -> ReportTypeEnum.getMapList().contains(k.getReportType())).collect(Collectors.toList());
		compChargeUnitInfoTO.setMapChargeUnitList(mapChargeUnits);
		List<TblReportChargeUnit> kycChargeUnits = reportChargeUnits.stream().filter(k -> ReportTypeEnum.getKYCList().contains(k.getReportType())).collect(Collectors.toList());
		compChargeUnitInfoTO.setKycChargeUnitList(kycChargeUnits);
		List<TblReportChargeUnit> amlChargeUnits = reportChargeUnits.stream().filter(k -> ReportTypeEnum.SCAN.getCode().equals(k.getReportType())).collect(Collectors.toList());
		compChargeUnitInfoTO.setAmlChargeUnitList(amlChargeUnits);
		return compChargeUnitInfoTO;
	}


	/**
	 * 获取账户所有启用的服务
	 * added for v1.9.7 KNZT-4708
	 *
	 * @param id
	 * @return CompanyServiceTO
	 */
	public CompanyServiceTO getCompanyEnabledService(String id) {
		CompanyServiceTO companyServiceTO = new CompanyServiceTO();
		List<TblReportChargeUnit> chargeUnitList = commTblReportChargeUnitService.listChargeUnitByCompanyId(id)
				.stream().filter(k -> Constants.YES.equals(k.getEnabled())).collect(Collectors.toList());
		
		List<TblCompApiChargeUnit> apiChargeUnitList = apiChargeUnitService.getByCompanyId(id)
				.stream().filter(k -> Constants.YES.equals(k.getEnabled())).collect(Collectors.toList());
		companyServiceTO.setApiChargeUnitList(apiChargeUnitList);
		List<TblReportChargeUnit> kycChargeUnitList = chargeUnitList.stream()
			.filter(k -> ReportTypeEnum.getKYCList().contains(k.getReportType())).collect(Collectors.toList());
		companyServiceTO.setKycChargeUnitList(kycChargeUnitList);
		List<TblReportChargeUnit> mapChargeUnitList = chargeUnitList.stream()
			.filter(k -> ReportTypeEnum.getMapList().contains(k.getReportType())).collect(Collectors.toList());
		companyServiceTO.setMapChargeUnitList(mapChargeUnitList);
		List<TblReportChargeUnit> amlChargeUnitList = chargeUnitList.stream()
			.filter(k -> ReportTypeEnum.SCAN.getCode().equals(k.getReportType())).collect(Collectors.toList());
		companyServiceTO.setAmlChargeUnitList(amlChargeUnitList);
		List<TblBenefitPool> benefitPoolList = benefitPoolService.getEffectByCompanyId(id);
		companyServiceTO.setBenefitPoolList(BenefitPoolTO.build(benefitPoolList));
		return companyServiceTO;
	}


	/**
	 * 操作试用申请
	 * added for v1.9.3 KNZT-4193
	 * update for v2.0.4 KNZT-5484
	 *
	 * @param form
	 * @return
	 */
	public void operateTrialApply(TrialApplyOperateForm form) throws MessageException {
		MsgExceptionUtils.checkIsNull(form.getTrialApplyId(), "err.param.invalid");
		MsgExceptionUtils.checkIsNull(form.getOperateType(), "err.param.invalid");
		MsgExceptionUtils.checkIsNull(form.getEffFlag(), "err.param.invalid");
		MsgExceptionUtils.checkIsNull(form.getImpLevel(), "err.param.invalid");
		TblGlobalTrialApply apply = trialApplyService.get(form.getTrialApplyId());
		MsgExceptionUtils.checkIsNull(apply, "err.param.invalid");
		MsgExceptionUtils.failBuild(TrialApplyStatusEnum.DONE.getCode().equals(apply.getStatus()), "msg:该申请已经通过");

		switch (form.getOperateType()) {
			case SAVE_FOLLOW:
				apply.setStatus(TrialApplyStatusEnum.FOLLOW.getCode());
				break;
			case REJECT:
				apply.setStatus(TrialApplyStatusEnum.REJECT.getCode());
				// added for v2.0.9 fengsw KNZT-5959
				if (StringUtils.isBlank(form.getRemark())) {
					form.setRemark("依照审批规则文档，不予以通过");
				}
				break;
			case OPEN:
				apply.setStatus(TrialApplyStatusEnum.DONE.getCode());
				// 创建账户
				String companyName = apply.getCompanyName();
				Company company = createSelfAccountCompany(CompTypeEnum.CLIENT.getCode(), companyName, apply.getName(), apply.getLastName(), apply.getEmail(), apply.getPhone(), apply.getCountry(), 0, form.getCategory()); // updated for v2.0.7 chenbl KNZT-6595
				apply.setTransferCompanyId(company.getId());
				// 发送激活邮件 updated for v2.0.4 chenbl KNZT-5442
				commSysCompanyService.sendActivateUserEmail4MainUser(company);
				addDelayedTask4SendActivateUserEmail(company); // added for v1.9.9 chenbl KNZT-4974
				break;
			default:
				break;
		}
		apply.setEffFlag(form.getEffFlag());
		apply.setImpLevel(form.getImpLevel());
		apply.setCategory(form.getCategory());
		trialApplyService.save(apply);
		// added for v2.0.9 fengsw KNZT-5959 审批记录落表
		commCrmCompTrackingRecordService.save(apply.getId(), form.getRemark(), CompTrackingRecordSourceEnum.SELF_ACCOUNT_APPLY_APPROVAL.getCode());
		// 保存文件
		List<RelFileTO> fileList = form.getFileList();
		relFileService.saveFileList(apply.getId(),
				SysRelFile.RelTypeEnum.TRIAL.getCode(),
				RelFileTO.transferToSysRelFile(fileList));
		// 推送客找找线索 updated for lvcy v2.0.2 KNZT-5379
		// 拒绝、开通都需要推送客找找
		if ((TrialApplyOperateForm.OperateTypeEnum.OPEN == form.getOperateType() || TrialApplyOperateForm.OperateTypeEnum.REJECT == form.getOperateType()) && TrialApplyTypeEnum.C.getCode().equals(apply.getType())) {
			TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
				@Override
				public void afterCommit() {
					threadPoolService.execute("pool.creat.lead.task", new TrialApplyRunnable(apply.getId()));
				}
			});
		}
	}

	// added for v1.9.9 chenbl KNZT-4974
	// 添加延时任务, 3天后未激活再次发激活邮件
	private void addDelayedTask4SendActivateUserEmail(Company company){
		redisService.addDelayedCommTask(RedisService.generateCommTaskId(CommDelayedTaskTypeEnum.CLIENT_ACTIVATE_CHECK.getCode(), company.getId()), Constants.DelayedTask.CLIENT_ACTIVATE_CHECK_DELAYED_SEC);
	}

	/**
	 * 分页查询自主账号试用申请
	 * added for v1.9.3 KNZT-4193
	 *
	 * @param condition
	 * @return
	 */
	public Page<TrialApplyListVO> pageTrialApply(TblGlobalTrialApplyCondition condition) {
		List<String> stausList = CollectionUtils.isEmpty(condition.getStatusList()) ?
				TrialApplyStatusEnum.getValidStatusList() : condition.getStatusList();
		condition.setStatusList(stausList);
		Page<TblGlobalTrialApply> trialApplyPage = trialApplyService.findListByCondtion(condition);
		List<TrialApplyListVO> applyList = TrialApplyListVO.build(trialApplyPage.getList());

		Page<TrialApplyListVO> applyListVOPage = new Page<>();
		applyListVOPage.setList(applyList);
		applyListVOPage.setCount(trialApplyPage.getCount());
		applyListVOPage.setPageNo(trialApplyPage.getPageNo());
		applyListVOPage.setPageSize(trialApplyPage.getPageSize());
		return applyListVOPage;
	}

	/**
	 * 查询自主账号试用申请详情
	 * added for v1.9.3 KNZT-4193
	 *
	 * @param id 试用申请订单id
	 * @return TrialApplyDetailVO
	 */
	public TrialApplyDetailVO getTrialApplyDetail(String id) {
		TblGlobalTrialApply apply = trialApplyService.get(id);
		TrialApplyDetailVO detailVO = TrialApplyDetailVO.build(apply);
		List<SysRelFile> fileList = sysRelFileService.getByRelIdAndType(apply.getId(), SysRelFile.RelTypeEnum.TRIAL.getCode());
		if (CollectionUtils.isNotEmpty(fileList)) {
			detailVO.setFileList(RelFileTO.build(fileList));
		}
		List<SysRelFile> signFileList = sysRelFileService.getByRelIdAndType(apply.getId(), SysRelFile.RelTypeEnum.TRIAL_SIGN.getCode());
		if (CollectionUtils.isNotEmpty(signFileList)) {
			detailVO.setSignFile(RelFileTO.build(signFileList.get(0)));
		}
		List<TblGlobalTrialApply> otherTrialApplyList = trialApplyService.listByEmail(apply.getEmail())
				.stream().filter(k -> !k.getId().equals(apply.getId())).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(otherTrialApplyList)) {
			detailVO.setDuplicateApplyList(TrialApplyDetailVO.build(otherTrialApplyList));
		}

		return detailVO;
	}

	/**
	 * 获取数量
	 * added for v1.9.3 KNZT-4193
	 *
	 * @return PlatformBriefCountTO
	 */
	public PlatformBriefCountTO getBriefCount() {
		TblGlobalTrialApplyCondition condition = new TblGlobalTrialApplyCondition();
		condition.setStatusList(Lists.newArrayList(TrialApplyStatusEnum.PENDING.getCode()));
		condition.setType(TrialApplyTypeEnum.C.getCode());
		int trialApplyCount = trialApplyService.countByCondition(condition);
		TblUserFeedbackCondition userFeedbackCondition  = new TblUserFeedbackCondition();
		userFeedbackCondition.setType(UserFeedbackTypeEnum.REPO.getCode());
		userFeedbackCondition.setStatus(UserFeedbackStatusEnum.PENDING.getCode());
		// added for v2.0.7 chenbl KNZT-5785
		userFeedbackCondition.setExcludeTestAccount(Constants.YES);
		userFeedbackCondition.setFilterEmails(Sets.newHashSet("@qcc.com"));
		int pendingUserFeedbackCount = userFeedbackService.countByCondition(userFeedbackCondition);
		return new PlatformBriefCountTO(trialApplyCount, pendingUserFeedbackCount);
	}



	/**
	 * added for v1.7.1 KNZT-3149【DATA PATCH】匹配原服务 Snapshot 20美金 至 KYC Basic， 匹配360 Company Profile 至 KYC Advanced （OCBC应该没有数据），做好相应的衔接沟通
	 * 传入一组公司companyId,如果不传或者传空，那就处理全量数据
	 * 仅本次上线使用
	 */
	public void patchKycCompOrder() {
		// 处理账号存量订单数据
		commTblCompReportOrderService.handleOrderInfoByCompanyId();
	}

	/**
	 * added for 1.8.5 KNZT-3808
	 * 根据条件查询出境额度记录
	 *
	 * @param condition
	 * @return Page<TblGlobalOutboundListSync>
	 */
	public Page<TblGlobalOutboundListSync> listOutboundByCondition(TblGlobalOutboundListSyncCondition condition) {
		return outboundListSyncService.findListByCondtion(condition);
	}

	/**
	 * 重置公司secretKey
	 * added for v1.9.7 KNZT-4708
	 *
	 * @param companyId
	 * updated for v2.2.8 fengsw KNZT-7656
	 * @param type 密钥类型 SecretKey/MockKey/MockSecretKey
	 * @return
	 */
	public void resetSecretKey(String companyId, String type) throws MessageException {
		Company company = commSysCompanyService.get(companyId);
		MsgExceptionUtils.checkIsNull(company, "msg:公司不存在");
		MsgExceptionUtils.failBuild(Boolean.FALSE.equals(company.getEnableApi()), "msg:接口服务未开启，不可重置secretKey");
		if (StringUtils.isBlank(type) || "SecretKey".equals(type)) {
			String generatedSecretKey = IdGenUtil.generateSecretKey();
			logger.info("resetSecretKey companyId:{}. secretKey: [{}] -> [{}]", companyId, company.getSecretKey(), generatedSecretKey);
			company.setSecretKey(generatedSecretKey);
			commSysCompanyService.save(company);
		} else if ("Mock".equals(type)) {
			SysCompInfoColExt sysCompInfoColExt = commSysCompanyService.getMockConfigByCompanyId(companyId);
			if (Objects.isNull(sysCompInfoColExt)) {
				commSysCompanyService.generateApiMockKeyAndSecretKey(companyId);
			} else {
				sysCompInfoColExtService.deleteColExtValue(companyId, Constants.CompInfoColExt.API_MOCK_CONFIG, sysCompInfoColExt.getExtKey());
				String mockKey = IdGenUtil.uuid();
				String mockSecretKey = IdGenUtil.generateSecretKey();
				sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.API_MOCK_CONFIG, mockKey, mockSecretKey);
			}
		}
	}

	/**
	 * 获取secretKey
	 * added for v1.9.7 KNZT-4708
	 * updated for v2.2.8 fengsw KNZT-7656
	 *
	 * @param companyId
	 * @return String
	 */
	public CompApiKeyTO getSecretKey(String companyId) {
		if (StringUtils.isNotBlank(companyId)) {
			Company company = commSysCompanyService.get(companyId);
			if (Objects.isNull(company)) {
				return null;
			}
			if (Boolean.TRUE.equals(company.getEnableApi())) {
				CompApiKeyTO compApiKeyTO = new CompApiKeyTO();
				SysCompInfoColExt sysCompInfoColExt = commSysCompanyService.getMockConfigByCompanyId(companyId);
				compApiKeyTO.setApiKey(companyId);
				compApiKeyTO.setApiSecretKey(company.getSecretKey());
				if (Objects.nonNull(sysCompInfoColExt)) {
					compApiKeyTO.setMockKey(sysCompInfoColExt.getExtKey());
					compApiKeyTO.setMockSecretKey(sysCompInfoColExt.getExtValue());
				}
				return compApiKeyTO;
			} else {
				return null;
			}
		}
		return null;
	}

	// added for v2.0.0 chenbl KNZT-5094
	public void saveHkSearcherInfo(HkSearcherInfoSaveForm form) throws MessageException {
		commSysCompanyService.saveHkSearcherInfo(form);
	}


	/**
	 * 生成代理查看订单参数
	 * added for lvcy v2.0.3 KNZT-5404
	 *
	 * @param orderId
	 * @return ProxyCompOrderParamTO
	 */
	public ProxyUserOrderParamTO generateProxyUserOrderParam(String orderId) {
		TblCompReportOrder order = commTblCompReportOrderService.get(orderId);
		if (Objects.isNull(order)) {
			return null;
		}
		String dataToken = CommTblCompReportOrderService.generateDataToken(order.getId(), order.getKeyNo());
		String token = TokenUtils.getMd5Str4ValidateWithTime(order.getUserId());
		String proxyUserToken = order.getUserId() + Constants.TokenSplit.SPLIT + token;
		return new ProxyUserOrderParamTO(dataToken, proxyUserToken);
	}

	// added for v2.0.4 chenbl KNZT-5371 updated for v2.0.7 chenbl KNZT-5773
	public void resetCompMainUserMfaSecret(String companyId) throws MessageException {
		Company company = commSysCompanyService.get(companyId);
		MsgExceptionUtils.checkIsNull(company, "msg:公司不存在");
		String enableMfaStr = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ENABLE_MFA);
		MsgExceptionUtils.failBuild(!Constants.YES.equals(enableMfaStr), "msg:公司未开启MFA");
		User mainUser = userService.getMainUserByCompanyId(companyId);
		MsgExceptionUtils.checkIsNull(mainUser, "主账号不存在");
		if (StringUtils.isNotBlank(mainUser.getMfaSecret())) {
			logger.info("resetCompMainUserMfaSecret companyId:{}. mfaSecret: [{}]", mainUser.getCompanyId(), mainUser.getMfaSecret());
			userService.updateMfaSecret(mainUser.getId(), null, UserUtils.getUserId());
		}
		loginAttemptRedisService.resetFailCount4MFA(mainUser.getLoginName());
	}

	// added for v2.0.7 chenbl KNZT-5773
	public void unlockCompMainUser(String companyId) throws MessageException {
		User mainUser = userService.getMainUserByCompanyId(companyId);
		MsgExceptionUtils.checkIsNull(mainUser, "主账号不存在");
		loginAttemptRedisService.clearFailCountInRedis(mainUser.getLoginName());

		SysTemplate template = commSysTemplateService.getByTemplateName("u_email_account_unlock");
		if (template == null) {
			logger.error("邮件模板不存在");
			throw new MessageException("err.access");
		}
		qccMailSenderService.send(mainUser.getLoginName(), template.getTemplateSubject(), template.getTemplateContent());
	}

	// added for v2.0.5 chenbl KNZT-5491
	public List<DictItem> listUser(String loginName, int limitCount) {
		return userService.findUserByLoginName4Select(loginName, limitCount)
				.stream().map(user -> new DictItem(user.getId(), user.getLoginName()))
				.collect(Collectors.toList());
	}

	/**
	 * added for v.2.2.7 fengsw KNZT-7785
	 * 
	 * @param companyId
	 * @param topUpLimitAmount
	 */
	public void resetTopUpLimit(String companyId, BigDecimal topUpLimitAmount) {
		if(StringUtils.isBlank(companyId) || topUpLimitAmount == null){
			return;
		}
		commSysCompInfoExtService.saveColExtValue(companyId, Constants.CompInfoColExt.CommExt.TOP_UP_AMOUNT_LIMIT,  topUpLimitAmount.toPlainString());
	}
}
