package com.backend.common.mapper;

import com.backend.common.entity.mapping.CompanyListInfo;
import com.backend.common.entity.mapping.CompanyPatchInfoListTO;
import com.backend.common.entity.mapping.CompanyProKeyInfoPO;

import com.backend.common.entity.mapping.CompanyShortInfoTO;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.setting.condition.SysCompanyCondition;
import com.qcc.frame.jee.base.AbstractCondition;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompanyMainInfoTO;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompanyNameTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface SysCompanyDao extends CrudDao<Company> {
	List<CompanyNameTO> listCompanyNameByIdList(@Param("idList") List<String> idList);
	
	public Company getByName(@Param("name")String name);
	
	CompanyNameTO getByMainLoginName(@Param("loginName")String loginName);
	
	public List<Company> findAllList4Select();
	public List<Company> getCompsForJobRun();
	
	
	public List<Company> callProcList4Sample(@Param("name")String name, @Param("version")int version);
	
	public int callProcCount4Sample(@Param("name")String name, @Param("version")int version);

	public int countCompMainUserRole(@Param("companyId")String companyId, @Param("enname")String role);
	public Company getByCustomerId(@Param("customerId")Integer customerId);

	public List<String> getActiveCompanyIdByCondition(@Param("obj") SysCompanyCondition condition);
	
	/**
	 * 读取所有companyId, 按type排序, 用于Job批量处理
	 * @return
	 */
	List<String> listCompanyId4MonitorNewsJob();
	public Company getChannelCustomer(@Param("companyId")String companyId);


	List<String> listCompanyId4Job();
	
    
    void updateCompanyShortName(@Param("companyId") String companyId, @Param("shortName") String shortName);
    
    List<CompanyListInfo> pageListCompanyInfo(@Param("obj") AbstractCondition<CompanyListInfo> condition);

	CompanyMainInfoTO getByProMainLoginName(@Param("proMainLoginName")String proMainLoginName);

    // added for V2.0.7 ZS-ZS-477 【框架】调用专业版接口时（如添加监控），参数传值调整 新增根据登录名获取用户id和所属公司id
	CompanyMainInfoTO getCompanyByLoginName(@Param("loginName")String loginName);

	// added for V2.1.0 ZS-535 【产业招商云平台II】【WEB】-优化-删除尽职调查列表时，同步调用专业版接口
	CompanyMainInfoTO getCompByIdWithDelFlag(@Param("companyId")String companyId);

	List<CompanyProKeyInfoPO> listCompanyProKeyInfo();

	/**
	 * 根据companyId|主账号loginName获取公司账号信息
	 * @param searchKey
	 * @return
	 */
	List<CompanyShortInfoTO> listCompanyShortInfoBySearchKey(@Param("searchKey") String searchKey);

	/**
	 * 根据companyId获取公司账号信息
	 * @param id
	 * @return
	 */
	CompanyShortInfoTO getCompanyShortInfoById(@Param("id") String id);

	/**
	 * 更新账号的开放平台userId信息
	 * @param id
	 * @param openApiUserId
	 */
	void updateOpenApiUserId(@Param("id") String id, @Param("openApiUserId") String openApiUserId);

	/**
	 * 获取未绑定开放平台userId的账号（除去系统管理员账号）
	 * 
	 * @return
	 */
    List<CompanyPatchInfoListTO> listCompanyToOpenApiForPatch(@Param("idList") List<String> idList);

	/**
	 * 获取已经绑定了开放平台userId的账号
	 * 
	 * @return
	 */
	List<CompanyPatchInfoListTO> listCompanyAccountInfoForOpenApiToPatchWithExistsOpenUserId(@Param("idList") List<String> idList);

	//added for v1.3.7 KNZT-1420
	CompanyListInfo getCompanyDetailInfo(@Param("companyId") String companyId);

	// added for v1.8.8 KNZT-4013
	void updateCompanyType(Company company);

	List<String> getCompanyIdByCondition(@Param("obj") SysCompanyCondition condition);

	// added for v1.9.9 chenbl KNZT-4974
	int existUnactivatedCompanyByDateRange(@Param("beginTimeStr") String beginTimeStr,
										   @Param("endTimeStr") String endTimeStr);

	// added for v2.1.8 chenbl KNZT-6899
	void updateCompanyMainUser(@Param("companyId") String companyId,
							   @Param("mainUserId") String mainUserId,
							   @Param("email") String email,
							   @Param("updateBy") String updateBy);
}
