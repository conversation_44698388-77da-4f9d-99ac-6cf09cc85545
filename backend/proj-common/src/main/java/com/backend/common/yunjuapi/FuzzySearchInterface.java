package com.backend.common.yunjuapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.common.form.TransWrapper;
import com.backend.common.modules.common.service.CommonDataService;
import com.backend.common.yunjuapi.model.ApiAutocompleteGlobalPersonTO;
import com.backend.common.yunjuapi.model.search.PersonSearchApiTO;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.ApiErrorCodeEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.HttpUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.PinyinUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class FuzzySearchInterface {
    protected static Logger logger = LoggerFactory.getLogger(FuzzySearchInterface.class);
    private static final CommonDataService commonDataService = SpringContextHolder.getBean(CommonDataService.class);

    /**
     * // added for v6.7.2 KNZT-84
     * @param keyNo
     * @return
     */
    public static String getJurisdictionByKeyNo(String keyNo) {
        String jurisdiction = Constants.JURISDICTION.CHINA_MAINLAND;
        if (StringUtils.startsWith(keyNo, Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX)) {
            jurisdiction = Constants.JURISDICTION.HK;
        }
        return jurisdiction;
    }


    /**
     * // added for v1.0.5 KNZT-287
     * @param keyNo
     * @return
     * @throws MessageException
     */
    public static List<ApiAutocompleteGlobalPersonTO> listGlobalPersonByKeyNo(String keyNo) throws MessageException{
        String url = Global.getConfig("qcc.yunju.interface.domain") + "/PersonBriefOversea/GetList";//云聚编号9006
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("keyNo", keyNo);
        try {
        String resp = HttpUtils.getYunJuApiNoUser(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_10000);
        JSONObject jsonObj = JsonUtils.parseObject(resp);
        if(jsonObj != null) {
            if(ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode().equals(jsonObj.getString("Status")) ||
                    ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode().equals(jsonObj.getString("Status"))) {
                if(CollectionUtils.isNotEmpty(jsonObj.getJSONArray("Result"))) {
                    List<ApiAutocompleteGlobalPersonTO> resultList =  new ArrayList<>();
                    for(int i = 0; i < jsonObj.getJSONArray("Result").size(); i++) {
                        JSONObject jsonObject = jsonObj.getJSONArray("Result").getJSONObject(i);
                        ApiAutocompleteGlobalPersonTO to = new ApiAutocompleteGlobalPersonTO();
                        to.setPersonName(jsonObject.getString("StaffName"));
                        if(PinyinUtils.notConvert2Pinyin(jsonObject.getString("StaffName"))) {
                            to.setPersonEnglishName(jsonObject.getString("StaffName"));
                            to.setPersonName("");
                        } else {
                            to.setPersonEnglishName(PinyinUtils.chineseNameToPinyin(jsonObject.getString("StaffName")));
                        }
                        to.setPersonId(jsonObject.getString("StaffkeyNo"));
                        resultList.add(to);
                    }
                    return resultList;
                }
                return null;
            } else {
                logger.error("call /PersonBriefOversea/GetList error: " + resp);
            }
            }
        } catch (Exception e) {
            logger.error("call /PersonBriefOversea/GetList error", e);
            throw new MessageException("err.access");
        }
        return null;

    }

    // added for v2.1.4 chenbl KNZT-6254
    public static JsonResultList<PersonSearchApiTO> personSearch(String persSearchKey, Set<String> relatedCorpKeyNos, String pageIndex, String pageSize) throws MessageException {
        JsonResultList<PersonSearchApiTO> result = null;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name", persSearchKey);
        paramMap.put("relatedCompanies", relatedCorpKeyNos);
        paramMap.put("isKyc", true); // 会有kyc的特殊逻辑

        // isPerson, true: 直接拿搜索词匹配人名公司名啥的, 没结果直接返回; false: 在直接拿搜索词匹配人名公司名没结果的时候会把用户输入词进行拆词再搜索
        // 比如, 用户输入:小米雷军, 传true的话直接用着四个字搜索是没结果的,传false的话 会把用户输入拆成小米 雷军两个词再搜一遍 就会出结果了
        paramMap.put("isPerson", true);
        paramMap.put("pageIndex", pageIndex);
        paramMap.put("pageSize", pageSize);
        try {
            String resp = YunJuRequestForwardingGetDetailInterface.requestForwardingGetDetail("2", "/person/Search", "2", JSON.toJSONString(paramMap), false);
            JSONObject respJo = JsonUtils.parseObject(resp);
            if (respJo != null
                    && StringUtils.equalsAny(respJo.getString("Status"), ApiErrorCodeEnum.CODE_SEARCH_SUC_200.getCode(), ApiErrorCodeEnum.CODE_SEARCH_NORESULT_201.getCode())) {
                result = new JsonResultList<>();
                JSONArray resultJa = respJo.getJSONArray("Result");
                if (CollectionUtils.isNotEmpty(resultJa)) {
                    List<PersonSearchApiTO> personSearchApiTOS = resultJa.toJavaList(PersonSearchApiTO.class);
                    fill4PersonSearchTO(personSearchApiTOS);
                    result.setResultList(personSearchApiTOS);
                }
                JSONObject pagingJo = respJo.getJSONObject("Paging");
                if (pagingJo != null) {
                    result.setTotalCount(pagingJo.getLongValue("TotalRecords"));
                }
            } else {
                logger.error("call /api/person/Search error: " + resp);
            }
        } catch (Exception e) {
            logger.error("call /api/person/Search error", e);
        }
        return result;
    }

    private static void fill4PersonSearchTO(List<PersonSearchApiTO> personSearchApiTOS) {
        if (CollectionUtils.isEmpty(personSearchApiTOS)) {
            return;
        }
        Map<String, String> corpKeyNo2EnMap = new HashMap<>();
        Set<String> keyNos = personSearchApiTOS.stream().map(PersonSearchApiTO::getMainCompany)
                .map(PersonSearchApiTO.MainCompany::getKeyNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(keyNos)) {
            corpKeyNo2EnMap.putAll(GlobalCompanyDetailsInterface.batchGetCorpKeyNo2EnMap(keyNos, false));
        }
        for (PersonSearchApiTO personSearchApiTO : personSearchApiTOS) {
            personSearchApiTO.setNameEn(CompanyDetailsInterface.getEnNameByCnNameEnd(personSearchApiTO.getId(), personSearchApiTO.getName(), personSearchApiTO.getNameEn()));
            PersonSearchApiTO.MainCompany mainCompany = personSearchApiTO.getMainCompany();
            if (mainCompany != null) {
                mainCompany.setCompanyEn(corpKeyNo2EnMap.get(mainCompany.getKeyNo()));
                mainCompany.setCompanyEn(CompanyDetailsInterface.getEnNameByCnNameEnd(mainCompany.getKeyNo(), mainCompany.getCompany(), mainCompany.getCompanyEn()));
            }
        }
    }
}
