package com.backend.common.modules.delivery.condition;

import java.util.Date;
import java.util.List;

import com.backend.common.modules.delivery.model.TransactionListTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qcc.frame.jee.base.AbstractCondition;

/**
 * 流水查询条件
 * added for lvcy v2.1.2 KNZT-4565
 *
 * <AUTHOR>
 * @datetime 5/11/2024 5:15 pm
 */
public class TransactionPageCondition extends AbstractCondition<TransactionListTO> {
    /**
     * 交易流水id
     */    
    private String transactionId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 流水类型
     */
    private String transactionType;
    /**
     * 流水类型列表
     */
    private List<String> transactionTypeList;
    /**
     * 开始日期
     */
    private Integer beginDate;
    /**
     * 结束日期
     */
    private Integer endDate;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 服务类型
     */
    private String orderType;
    /**
     * 服务类型列表
     */
    private List<String> orderTypeList;
    /**
     * 是否排除测试账户
     */
    private String excludeTestAccount;
    /**
     * 关键字, 支持合同号、订单号精确搜索
     */
    private String keyword;
    private String companyId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
    private Date createDateBegin;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
    private Date createDateEnd;
    private String compType;
    private String orderSource;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }   

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Integer getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Integer beginDate) {
        this.beginDate = beginDate;
    }

    public Integer getEndDate() {
        return endDate;
    }

    public void setEndDate(Integer endDate) {
        this.endDate = endDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<String> getTransactionTypeList() {
        return transactionTypeList;
    }

    public void setTransactionTypeList(List<String> transactionTypeList) {
        this.transactionTypeList = transactionTypeList;
    }

    public List<String> getOrderTypeList() {
        return orderTypeList;
    }

    public void setOrderTypeList(List<String> orderTypeList) {
        this.orderTypeList = orderTypeList;
    }

    public String getExcludeTestAccount() {
        return excludeTestAccount;
    }

    public void setExcludeTestAccount(String excludeTestAccount) {
        this.excludeTestAccount = excludeTestAccount;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public Date getCreateDateBegin() {
        return createDateBegin;
    }

    public void setCreateDateBegin(Date createDateBegin) {
        this.createDateBegin = createDateBegin;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public String getCompType() {
        return compType;
    }

    public void setCompType(String compType) {
        this.compType = compType;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }
}