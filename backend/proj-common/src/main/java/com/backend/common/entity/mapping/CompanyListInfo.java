package com.backend.common.entity.mapping;

import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qcc.frame.commons.Constants;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class CompanyListInfo {
	private String id;
	private String companyName;
	private String loginName;
	private String phone;
	private String email;
	private int subUserNum;
	private Date beginDate;
	private Date endDate;
	private String proKey;
	private String proSecretKey;
	private String proMainLoginName;
	private String yunjuKey;
	private String yunjuSecretKey;
	private String userStatus;
	private String shortName;//added for v1.2.8 KNZT-1346 增加英文名称

	// updated fro ZS-424【平台管理】列表框增加三列，展示服务列表已消耗额度与总额度
	/**
	 * 已注册子账号数量
	 */
	private int consumedNum;
	/**
	 * 尽职调查额度
	 */
	private BigDecimal beneTotalCount;
	/**
	 * 尽职调查已消耗额度
	 */
	private BigDecimal beneConsumedCount;
	/**
	 * 风险监控额度
	 */
	private BigDecimal radarTotalCount;
	/**
	 * 风控监控已消耗额度
	 */
	private BigDecimal radarConsumedCount;
	/**
	 * 企业详情查看额度
	 */
	private BigDecimal corpDtlTotalCount;
	/**
	 * 企业详情查看已消耗额度
	 */
	private BigDecimal corpDtlConsumedCount;

	/**
	 * 征信报告查看额度
	 */
	private BigDecimal creditReportTotalCount;
	/**
	 * 征信报告查看已消耗额度
	 */
	private BigDecimal creditReportConsumedCount;

	/**
	 * 香港征信报告查看额度
	 */
	private BigDecimal hkCreditReportTotalCount;
	/**
	 * 香港征信报告查看已消耗额度
	 */
	private BigDecimal hkCreditReportConsumedCount;
	private String openApiUserId; // 开放平台userId
	private boolean enableOpenApi;//added for v1.3.7 KNZT-1420
	private boolean haveGlobalService;//added for v1.3.7 KNZT-1985
	private String businessScenario; // added ofr v1.3.9 KNZT-2131
//	private String chargeMode; // added  for v1.3.9 KNZT-2101 计费标准 // updated for v1.8.2 KNZT-3441
//	private String chargeModeDesc; // added  for v1.3.9 KNZT-2101 计费标准描述 // updated for v1.8.2 KNZT-3441
	private String dataSave; // added  for v1.5.8 KNZT-2611 数据保存
	private String dataSaveDesc;// added for v1.5.8 KNZT-2611
	private String showCredit; // added for v1.8.2 KNZT-3680
	private String showCreditDesc; // added for v1.8.2 KNZT-3680
	private String canUpgrade; // added for v1.8.2 KNZT-3700
	private String canUpgradeDesc; // added for v1.8.2 KNZT-3700
	private boolean innerAccount;// added for v1.6.4 KNZT-2806
	private Integer type; // 账号类型
	private Boolean enableApi; // 是否启用api // added for v1.9.7 KNZT-4708
	private String overseasOrderLimit; // 境外下单次数限制 added for v1.9.8 KNZT-4538
	private boolean enableMfa; // 是否开启MFA added for v2.0.4 chenbl KNZT-5371
	private String securityPolicy; // 密码安全策略 added for v2.0.4 chenbl KNZT-5442
	private String disableAccount;// 禁用账号 Y-是；N-撤销禁用 added for fengsw KNZT-5574
	private String payType; // 支付类型 added for lvcy v2.0.6 KNZT-5499
	private String payTypeDesc; // 支付类型描述 added for lvcy v2.0.6 KNZT-5499
	private String category; // 客户类型
	private String categoryDesc; // 客户类型描述
	private BigDecimal creditReportCurMonthConsumedCount;// 当月消耗总计 added for v2.1.3 fengsw KNZT-6263
	private String uiDefaultVersion; // 默认UI版本
	private String duplicateOrderRemind = "N";// 重复订单提醒 默认为否
	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}
	@JsonFormat(pattern = "yyyy-MM-dd", timezone=Constants.DEFAULT_TIMEZONE)
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public int getSubUserNum() {
		return subUserNum;
	}
	public void setSubUserNum(int subUserNum) {
		this.subUserNum = subUserNum;
	}
	
	public String getProKey() {
		return proKey;
	}
	public void setProKey(String proKey) {
		this.proKey = proKey;
	}
	public String getProSecretKey() {
		return proSecretKey;
	}
	public void setProSecretKey(String proSecretKey) {
		this.proSecretKey = proSecretKey;
	}
	public String getProMainLoginName() {
		return proMainLoginName;
	}
	public void setProMainLoginName(String proMainLoginName) {
		this.proMainLoginName = proMainLoginName;
	}
	public String getYunjuKey() {
		return yunjuKey;
	}
	public void setYunjuKey(String yunjuKey) {
		this.yunjuKey = yunjuKey;
	}
	public String getYunjuSecretKey() {
		return yunjuSecretKey;
	}
	public void setYunjuSecretKey(String yunjuSecretKey) {
		this.yunjuSecretKey = yunjuSecretKey;
	}
	public String getUserStatus() {
		return userStatus;
	}
	public void setUserStatus(String userStatus) {
		this.userStatus = userStatus;
	}

	public int getConsumedNum() {
		return consumedNum;
	}

	public void setConsumedNum(int consumedNum) {
		this.consumedNum = consumedNum;
	}

	public BigDecimal getBeneTotalCount() {
		return beneTotalCount;
	}

	public void setBeneTotalCount(BigDecimal beneTotalCount) {
		this.beneTotalCount = beneTotalCount;
	}

	public BigDecimal getBeneConsumedCount() {
		return beneConsumedCount;
	}

	public void setBeneConsumedCount(BigDecimal beneConsumedCount) {
		this.beneConsumedCount = beneConsumedCount;
	}

	public BigDecimal getRadarTotalCount() {
		return radarTotalCount;
	}

	public void setRadarTotalCount(BigDecimal radarTotalCount) {
		this.radarTotalCount = radarTotalCount;
	}

	public BigDecimal getRadarConsumedCount() {
		return radarConsumedCount;
	}

	public void setRadarConsumedCount(BigDecimal radarConsumedCount) {
		this.radarConsumedCount = radarConsumedCount;
	}

	public BigDecimal getCorpDtlTotalCount() {
		return corpDtlTotalCount;
	}

	public void setCorpDtlTotalCount(BigDecimal corpDtlTotalCount) {
		this.corpDtlTotalCount = corpDtlTotalCount;
	}

	public BigDecimal getCorpDtlConsumedCount() {
		return corpDtlConsumedCount;
	}

	public void setCorpDtlConsumedCount(BigDecimal corpDtlConsumedCount) {
		this.corpDtlConsumedCount = corpDtlConsumedCount;
	}

	public BigDecimal getCreditReportTotalCount() {
		return creditReportTotalCount;
	}

	public void setCreditReportTotalCount(BigDecimal creditReportTotalCount) {
		this.creditReportTotalCount = creditReportTotalCount;
	}

	public BigDecimal getCreditReportConsumedCount() {
		return creditReportConsumedCount;
	}

	public void setCreditReportConsumedCount(BigDecimal creditReportConsumedCount) {
		this.creditReportConsumedCount = creditReportConsumedCount;
	}

	public BigDecimal getHkCreditReportTotalCount() {
		return hkCreditReportTotalCount;
	}

	public void setHkCreditReportTotalCount(BigDecimal hkCreditReportTotalCount) {
		this.hkCreditReportTotalCount = hkCreditReportTotalCount;
	}

	public BigDecimal getHkCreditReportConsumedCount() {
		return hkCreditReportConsumedCount;
	}

	public void setHkCreditReportConsumedCount(BigDecimal hkCreditReportConsumedCount) {
		this.hkCreditReportConsumedCount = hkCreditReportConsumedCount;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getOpenApiUserId() {
		return openApiUserId;
	}

	public void setOpenApiUserId(String openApiUserId) {
		this.openApiUserId = openApiUserId;
	}

	public boolean isEnableOpenApi() {
		return enableOpenApi;
	}

	public void setEnableOpenApi(boolean enableOpenApi) {
		this.enableOpenApi = enableOpenApi;
	}

	public boolean getHaveGlobalService() {
		return haveGlobalService;
	}

	public void setHaveGlobalService(boolean haveGlobalService) {
		this.haveGlobalService = haveGlobalService;
	}

	public String getBusinessScenario() {
		return businessScenario;
	}

	public void setBusinessScenario(String businessScenario) {
		this.businessScenario = businessScenario;
	}

	// updated for v1.8.2 KNZT-3441
//	public String getChargeMode() {
//		return chargeMode;
//	}
//
//	public void setChargeMode(String chargeMode) {
//		this.chargeMode = chargeMode;
//	}
//
//	public String getChargeModeDesc() {
//		return chargeModeDesc;
//	}
//
//	public void setChargeModeDesc(String chargeModeDesc) {
//		this.chargeModeDesc = chargeModeDesc;
//	}

	public String getDataSave() {
		return dataSave;
	}

	public void setDataSave(String dataSave) {
		this.dataSave = dataSave;
	}

	public String getDataSaveDesc() {
		return dataSaveDesc;
	}

	public void setDataSaveDesc(String dataSaveDesc) {
		this.dataSaveDesc = dataSaveDesc;
	}

	public String getShowCredit() {
		return showCredit;
	}

	public void setShowCredit(String showCredit) {
		this.showCredit = showCredit;
	}

	public String getShowCreditDesc() {
		return showCreditDesc;
	}

	public void setShowCreditDesc(String showCreditDesc) {
		this.showCreditDesc = showCreditDesc;
	}

	public String getCanUpgrade() {
		return canUpgrade;
	}

	public void setCanUpgrade(String canUpgrade) {
		this.canUpgrade = canUpgrade;
	}

	public String getCanUpgradeDesc() {
		return canUpgradeDesc;
	}

	public void setCanUpgradeDesc(String canUpgradeDesc) {
		this.canUpgradeDesc = canUpgradeDesc;
	}

	public boolean getInnerAccount() {
		return innerAccount;
	}

	public void setInnerAccount(boolean innerAccount) {
		this.innerAccount = innerAccount;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public boolean isHaveGlobalService() {
		return haveGlobalService;
	}

	public boolean isInnerAccount() {
		return innerAccount;
	}

	public Boolean getEnableApi() {
		return enableApi;
	}

	public void setEnableApi(Boolean enableApi) {
		this.enableApi = enableApi;
	}

	public String getOverseasOrderLimit() {
		return overseasOrderLimit;
	}

	public void setOverseasOrderLimit(String overseasOrderLimit) {
		this.overseasOrderLimit = overseasOrderLimit;
	}

	public boolean getEnableMfa() {
		return enableMfa;
	}

	public void setEnableMfa(boolean enableMfa) {
		this.enableMfa = enableMfa;
	}

	public String getSecurityPolicy() {
		return securityPolicy;
	}

	public void setSecurityPolicy(String securityPolicy) {
		this.securityPolicy = securityPolicy;
	}

	public String getDisableAccount() {
		return disableAccount;
	}

	public void setDisableAccount(String disableAccount) {
		this.disableAccount = disableAccount;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getPayTypeDesc() {
		return payTypeDesc;
	}

	public void setPayTypeDesc(String payTypeDesc) {
		this.payTypeDesc = payTypeDesc;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getCategoryDesc() {
		return categoryDesc;
	}

	public void setCategoryDesc(String categoryDesc) {
		this.categoryDesc = categoryDesc;
	}

	public BigDecimal getCreditReportCurMonthConsumedCount() {
		return creditReportCurMonthConsumedCount;
	}

	public void setCreditReportCurMonthConsumedCount(BigDecimal creditReportCurMonthConsumedCount) {
		this.creditReportCurMonthConsumedCount = creditReportCurMonthConsumedCount;
	}

	public String getUiDefaultVersion() {
		return uiDefaultVersion;
	}

	public void setUiDefaultVersion(String uiDefaultVersion) {
		this.uiDefaultVersion = uiDefaultVersion;
	}

	public String getDuplicateOrderRemind() {
		return duplicateOrderRemind;
	}

	public void setDuplicateOrderRemind(String duplicateOrderRemind) {
		this.duplicateOrderRemind = duplicateOrderRemind;
	}
}
