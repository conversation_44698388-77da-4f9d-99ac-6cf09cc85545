package com.backend.common.service;

import com.backend.common.model.MenuAuthResult;
import com.backend.common.modules.benefit.entity.TblBenefitDelivery;
import com.backend.common.modules.benefit.service.CommTblBenefitDeliveryService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.modules.setting.model.CompChargeUnitV2TO;
import com.qcc.frame.commons.ienum.BenefitPoolStatusEnum;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限业务服务
 * <AUTHOR>
 * @datetime 29/7/2025 5:48 下午
 */
@Service
public class AuthBusinessService {
    @Autowired
    private CommTblReportChargeUnitService chargeUnitService;
    @Autowired
    private CommTblBenefitDeliveryService commTblBenefitDeliveryService;

    public MenuAuthResult getMenuData() {
        List<CompChargeUnitTO> allEnabledChargeUnits = chargeUnitService.getCompChargeUnits(UserUtils.getUserCompanyId());
        CommTblReportChargeUnitService.removeCreditIfNecessary4UnitTO(allEnabledChargeUnits);
        List<CompChargeUnitV2TO> chargeUnitList = allEnabledChargeUnits.stream().map(CompChargeUnitV2TO::buildNewCompChargeUnitTO).collect(Collectors.toList());

        List<TblBenefitDelivery> mapBenefitList = commTblBenefitDeliveryService.getByCompanyId(UserUtils.getUserCompanyId())
                .stream().filter(k -> BenefitTypeEnum.getMapBenefitTypeList().contains(k.getBenefitType())).collect(Collectors.toList());
        // 判断是否进入workspace
        boolean mapWorkspace = chargeUnitList.stream().anyMatch(k -> ReportTypeEnum.getMapList().contains(k.getReportType()))
                && CollectionUtils.isNotEmpty(mapBenefitList);

        // 判断图谱是否试用
        boolean mapTrial = mapBenefitList.stream()
                .anyMatch(k -> StringUtils.isBlank(k.getContractNo())
                        && BenefitPoolStatusEnum.EFFECT.getCode().equals(k.getStatus()));

        MenuAuthResult result = new MenuAuthResult();
        result.setChargeUnitList(chargeUnitList);
        result.setMapWorkspace(mapWorkspace);
        result.setMapTrial(mapTrial);
        return result;
    }
}
