package com.backend.common.openapi.model;

import com.qcc.frame.jee.commons.utils.StringUtils;

import java.util.Date;

/**
 * added for v1.8.8 KNZT-4034
 * 客找找工单详情
 *
 * <AUTHOR>
 * @datetime 2024/7/15 17:18
 */
public class KzzWorkFlowDetailTO {
    private String code;
    private String curStatus; // 0 已删除  1 关闭  2 进行中  3驳回 4 撤回   11 正常结束

    // 非接口直接返回，从内部嵌入对象得到
    private String contractNo;
    private String submitName;
    private Date beginDate;
    private Date endDate;

    public boolean isPass() {
        return StringUtils.equals("11", curStatus);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCurStatus() {
        return curStatus;
    }

    public void setCurStatus(String curStatus) {
        this.curStatus = curStatus;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getSubmitName() {
        return submitName;
    }

    public void setSubmitName(String submitName) {
        this.submitName = submitName;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
