package com.backend.common.modules.report.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DataMapUnitResult {
    /**
     * 剩余额度
     */
    private BigDecimal remainCredits;
    /**
     * 图谱关联订单额度
     */
    private BigDecimal mapConsumedCredits;

    /**
     * 是否静默通知
     */
    private Boolean silentNotify;
    /**
     * 是否完成搜索引导
     */
    private Boolean fuzzySearchGuide;
    /**
     * 是否完成ownership引导
     */
    private Boolean ownershipGuide;
    /**
     * 是否完成network引导
     */
    private Boolean networkGuide;

    /**
     * 图谱关联节点编号
     */
    private List<String> mapNodeNoList;

    /**
     * 是否可以使用权益 true-使用权益，false-使用订单
     */
    private Boolean canUseBenefit;


    /**
     * 是否进入workspace true-workspace，false-公开首页
     */
    private Boolean workspace;
}