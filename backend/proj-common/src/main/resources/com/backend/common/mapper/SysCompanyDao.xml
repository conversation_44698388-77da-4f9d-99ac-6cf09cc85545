<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.backend.common.mapper.SysCompanyDao">
	<sql id="sysCompanyColumns">
		a.id AS "id",
		a.name AS "name",
		a.short_name AS "shortName",
		a.key_no AS "keyNo",
		a.email AS "email",
		a.phone AS "phone",
		a.contact_name AS "contactName",
		a.purchase_account_num AS "purchaseAccountNum",
		a.begin_date AS "beginDate",
		a.contract_begin_date AS "contractBeginDate",
		a.end_date AS "endDate",
		a.type AS "type",
		a.pay_type AS "payType",
		a.current_seller AS "currentSeller",
		a.total_contract_amt AS "totalContractAmt",
		a.customer_id AS "customerId",
		a.main_user_id AS "mainUserId",
		a.job_period AS "jobPeriod",
		a.next_job_run_date AS "nextJobRunDate",
		a.prev_job_run_date AS "prevJobRunDate",
		a.secret_key AS "secretKey",
		a.enable_api AS "enableApi",
		a.comp_type AS "compType",
		a.category as "category",
		a.menu_home_group_id AS "menuHomeGroupId",
		a.enable_export AS "enableExport",
		a.pro_key AS "proKey",
		a.pro_secret_key AS "proSecretKey",
		a.pro_main_login_name AS "proMainLoginName",
		a.yunju_key AS "yunjuKey",
		a.yunju_secret_key AS "yunjuSecretKey",
		<!--a.charge_mode AS "chargeMode",-->
		a.data_save AS "dataSave",
		a.show_credit AS "showCredit",
		a.can_upgrade AS "canUpgrade",
		a.del_flag AS "delFlag",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.version AS "version",
		a.open_api_user_id AS "openApiUserId",
		a.enable_open_api AS "enableOpenApi"
	</sql>
	
	

	<select id="get" resultType="com.qcc.frame.jee.modules.sys.entity.Company">
		SELECT
			<include refid="sysCompanyColumns"/>
		FROM sys_comp_info a
		WHERE a.id = #{id} AND a.del_flag = '0'
	</select>

	<insert id="insert">
		INSERT INTO sys_comp_info(
			id,
			name,
			short_name,
			key_no,
			email,
			phone,
			contact_name,
			purchase_account_num,
			begin_date,
			end_date,
			type,
			pay_type,
			current_seller,
			total_contract_amt,
			customer_id,
			main_user_id,
			open_api_user_id,
			enable_open_api,
			job_period,
			next_job_run_date,
			prev_job_run_date,
			secret_key,
			enable_api,
			enable_export,
			comp_type,
			category,
			menu_home_group_id,
			pro_key,
			pro_secret_key,
			pro_main_login_name,
			yunju_key,
			yunju_secret_key,
			business_scenario,
		    <!--charge_mode,-->
			data_save,
			show_credit,
			can_upgrade,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date,
			version
		) VALUES (
			#{id},
			#{name},
			#{shortName},
			#{keyNo},
			#{email},
			#{phone},
			#{contactName},
			#{purchaseAccountNum},
			#{beginDate},
			#{endDate},
			#{type},
			#{payType},
			#{currentSeller},
			#{totalContractAmt},
			#{customerId},
			#{mainUserId},
			#{openApiUserId},
			#{enableOpenApi},
			#{jobPeriod},
			#{nextJobRunDate},
			#{prevJobRunDate},
			#{secretKey},
			#{enableApi},
			#{enableExport},
			#{compType},
			#{category},
			#{menuHomeGroupId},
			#{proKey},
			#{proSecretKey},
			#{proMainLoginName},
			#{yunjuKey},
			#{yunjuSecretKey},
			#{businessScenario},
			<!--#{chargeMode},-->
			#{dataSave},
			#{showCredit},
			#{canUpgrade},
			#{delFlag},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{version}
		)
	</insert>

	<update id="update">
		UPDATE sys_comp_info SET
			name = #{name},
			short_name = #{shortName},
			key_no = #{keyNo},
			email = #{email},
			phone = #{phone},
			contact_name = #{contactName},
			purchase_account_num = #{purchaseAccountNum},
			begin_date = #{beginDate},
			contract_begin_date = #{contractBeginDate},
			end_date = #{endDate},
			type = #{type},
			pay_type = #{payType},
			current_seller = #{currentSeller},
			total_contract_amt = #{totalContractAmt},
			customer_id = #{customerId},
			main_user_id = #{mainUserId},
			open_api_user_id = #{openApiUserId},
			enable_open_api = #{enableOpenApi},
			job_period = #{jobPeriod},
			next_job_run_date = #{nextJobRunDate},
			prev_job_run_date = #{prevJobRunDate},
			secret_key = #{secretKey},
			enable_api = #{enableApi},
			enable_export = #{enableExport},
			comp_type = #{compType},
			category = #{category},
			menu_home_group_id = #{menuHomeGroupId},
			pro_key = #{proKey},
			pro_secret_key = #{proSecretKey},
			pro_main_login_name = #{proMainLoginName},
			yunju_key = #{yunjuKey},
			yunju_secret_key = #{yunjuSecretKey},
			business_scenario = #{businessScenario},
			<!--charge_mode = #{chargeMode},-->
			data_save = #{dataSave},
			show_credit = #{showCredit},
			can_upgrade = #{canUpgrade},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			version = #{version}
		WHERE id = #{id}
	</update>

	<update id="delete">
		UPDATE sys_comp_info SET
			del_flag = #{DEL_FLAG_DELETE},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>

	<update id="updateOpenApiUserId">
		UPDATE sys_comp_info SET open_api_user_id = #{openApiUserId} WHERE id = #{id}
	</update>
	
	<select id="getByName" resultType="com.qcc.frame.jee.modules.sys.entity.Company">
		SELECT
			<include refid="sysCompanyColumns"/>
		FROM sys_comp_info a
		WHERE a.name = #{name} and a.del_flag='0' limit 1
	</select>
	<select id="findAllList4Select" resultType="com.qcc.frame.jee.modules.sys.entity.Company">
		SELECT
			a.id AS "id",
			a.name AS "name"
		FROM sys_comp_info a
		WHERE a.del_flag = '0'
	</select>

	<select id="countCompMainUserRole" resultType="java.lang.Integer">
		select count(1) from sys_comp_info i, sys_user_role r, sys_role sysr
		where i.id = #{companyId} and i.del_flag = '0' and r.user_id = i.main_user_id
		and r.role_id = sysr.id and sysr.enname = #{enname} and sysr.del_flag = '0';
	</select>

	<select id="getCompsForJobRun" resultType="com.qcc.frame.jee.modules.sys.entity.Company">
		select
		<include refid="sysCompanyColumns"/>
 		from sys_comp_info a where a.next_job_run_date &lt;= curdate() and a.job_period != 'N' and a.del_flag = 0 and a.end_date >= curdate() and a.menu_home_group_id != 'qcc';
	</select>

	<select id="callProcList4Sample" resultType="com.qcc.frame.jee.modules.sys.entity.Company" statementType="CALLABLE">
		{call sp_return_list(#{name}, #{version})}
	</select>

	<select id="callProcCount4Sample" resultType="java.lang.Integer" statementType="CALLABLE">
		{call sp_return_count(#{name}, #{version})}
	</select>
	<select id="getByCustomerId" resultType="com.qcc.frame.jee.modules.sys.entity.Company">
		SELECT
			<include refid="sysCompanyColumns"/>
		FROM sys_comp_info a
		WHERE a.customer_id = #{customerId} AND a.del_flag = '0' limit 1;
	</select>


	<select id="getActiveCompanyIdByCondition" resultType="java.lang.String">
		SELECT
			a.id
		FROM sys_comp_info a
		<include refid="condition_where"/>
		and a.begin_date &lt;= curdate() and a.end_date &gt;= curdate()
	</select>


	<select id="getCompanyIdByCondition" resultType="java.lang.String">
		SELECT
		a.id
		FROM sys_comp_info a
		<include refid="condition_where"/>
	</select>

	<sql id="condition_where">
		<where>
			a.del_flag = '0'
			<if test="obj.typeList != null and obj.typeList.size() > 0">
				and a.type in
				<foreach collection="obj.typeList" index="index" item="i" open="(" separator="," close=")">
					#{i}
				</foreach>
			</if>
			<if test="obj.payTypeList != null and obj.payTypeList.size() > 0">
				and a.pay_type in
				<foreach collection="obj.payTypeList" index="index" item="i" open="(" separator="," close=")">
					#{i}
				</foreach>
			</if>
		</where>
	</sql>
	

	<delete id="physicallyDelete">
		DELETE FROM sys_comp_info WHERE id = #{id}
	</delete>


	<select id="listCompanyId4MonitorNewsJob" resultType="java.lang.String">
		SELECT
			a.id
		FROM sys_comp_info a
		WHERE a.del_flag = '0' and a.job_period != 'N' and exists(select 1 from tbl_comp_monitor_setting_news b where b.company_id=a.id and b.cond_content is not null and b.cond_content!='')
		and exists(select 1 from sys_user b where b.company_id=a.id AND b.msg_received_type is NOT null AND b.msg_received_type !='N' AND (b.next_notice_date=curdate() or b.prev_notice_date=curdate()))
		order by a.type desc,a.total_contract_amt desc
	</select>

	<select id="getChannelCustomer" resultType="com.qcc.frame.jee.modules.sys.entity.Company">
		SELECT
			<include refid="sysCompanyColumns"/>, b.value AS "saleId"
		FROM sys_comp_info a
		INNER JOIN sys_dict b on a.customer_id = b.label
		WHERE b.`type` = 'sales_channel'
		AND a.del_flag = '0'
		AND a.id = #{companyId};
	</select>


    <select id="listCompanyId4Job" resultType="java.lang.String">
        SELECT
            a.id
        FROM sys_comp_info a
        WHERE a.del_flag = '0' AND (a.begin_date is null or a.begin_date &lt;= CURDATE()) AND (a.end_date is null or a.end_date >= CURDATE())
        order by a.type desc,a.total_contract_amt desc
    </select>

    <update id="updateCompanyShortName">
        UPDATE sys_comp_info SET short_name=#{shortName} WHERE id=#{companyId}
    </update>
	<update id="updateCompanyType">
		UPDATE sys_comp_info
		SET type=#{type},
			update_date=now(),
			update_by=#{updateBy.id}
		WHERE id = #{id} AND del_flag = '0'
	</update>

	<select id="listCompanyNameByIdList" resultType="com.qcc.frame.jee.modules.sys.entity.mapping.CompanyNameTO">
        SELECT a.id, a.name, b.login_name mainLoginName FROM sys_comp_info a 
        inner join sys_user b on b.id = a.main_user_id
        WHERE a.id in
        <foreach collection="idList" index="index" item="obj" open="(" separator="," close=")">
            #{obj}
        </foreach>
        and a.del_flag='0'
    </select>
    
    <select id="getByMainLoginName" resultType="com.qcc.frame.jee.modules.sys.entity.mapping.CompanyNameTO">
        SELECT a.id, a.name, b.login_name mainLoginName FROM sys_comp_info a 
        inner join sys_user b on b.id = a.main_user_id
        WHERE b.login_name = #{loginName} and a.del_flag='0'
    </select>

	<select id="pageListCompanyInfo" resultType="com.backend.common.entity.mapping.CompanyListInfo">
        SELECT a.id, a.name companyName, b.login_name loginName, a.phone, a.email, a.purchase_account_num-1 as subUserNum, a.begin_date beginDate,
        a.end_date endDate,a.pro_key AS "proKey",
		a.pro_secret_key AS "proSecretKey",
		a.pro_main_login_name AS "proMainLoginName",
		a.yunju_key AS "yunjuKey",
		a.yunju_secret_key AS "yunjuSecretKey",
		b.status userStatus,
		c.consumedNum-1 as consumedNum,
		s4.total_count as creditReportTotalCount,
		s4.consumed_count as creditReportConsumedCount,
<!--		s5.total_count as hkCreditReportTotalCount,-->
<!--		s5.consumed_count as hkCreditReportConsumedCount,-->
<!--		CASE WHEN s5.id IS NOT NULL THEN 1 ELSE 0 END AS "haveGlobalService",-->
		a.short_name as shortName,
		a.open_api_user_id as openApiUserId,
		a.enable_open_api as enableOpenApi,
		a.business_scenario  AS "businessScenario",
		<!-- a.charge_mode AS "chargeMode", -->
		a.data_save AS "dataSave",
		a.show_credit AS "showCredit",
		a.can_upgrade AS "canUpgrade",
		a.type as "type",
		a.enable_api as "enableApi",
		a.pay_type as "payType",
		a.category,
		CASE WHEN sd.value IS NOT NULL THEN 1 ELSE 0 END AS "innerAccount",
		sd_overseas.`value` as "overseasOrderLimit"
		FROM sys_comp_info a
		inner join sys_user b on b.id = a.main_user_id
		LEFT JOIN (SELECT company_id ,COUNT(*) as consumedNum from sys_user where del_flag = '0' group by company_id) c on c.company_id = a.id
		left join sys_comp_info_func_count s4 on s4.company_id = a.id and s4.function_table_id = 'global_service_id' and  s4.del_flag = '0'
<!--		left join sys_comp_info_func_count s5 on s5.company_id = a.id and s5.function_table_id = 'hk_service_id' and  s5.del_flag = '0'-->
		left join sys_dict sd on a.id = sd.value and sd.label = 'sys_comp_info_id' and sd.`type` = 'inner_account' and sd.del_flag = '0'
		left join sys_dict as sd_overseas on sd_overseas.`type` = 'overseas_order_limit' and a.id = sd_overseas.label and sd_overseas.del_flag = '0'
		WHERE a.del_flag='0'
		AND b.login_name !='global_admin'
        <if test="obj.name != null and obj.name != ''">
        and (
				a.name like concat('%',#{obj.name},'%')
				or a.short_name like concat('%',#{obj.name},'%')
				or a.email like concat('%',#{obj.name},'%')
				or a.phone like concat('%',#{obj.name},'%')
				or a.business_scenario like concat('%',#{obj.name},'%')
				or exists(select user_inner.id
					from sys_user as user_inner
					where user_inner.del_flag = '0'
					and user_inner.company_id = a.id
					and (user_inner.login_name like concat('%', #{obj.name}, '%') or user_inner.name like concat('%', #{obj.name}, '%'))
				)
				or exists(
				    select tcd.id from tbl_contract_delivery tcd where tcd.company_id = a.id and tcd.contract_no = #{obj.name} and tcd.del_flag = '0'
				)
			)
        </if>
		<if test="obj.expireDateStr != null and obj.expireDateStr != ''">
			and a.end_date = #{obj.expireDateStr} and (sd.id = '' or sd.id is null)
		</if>
		<!-- updated for v1.9.7 KNZT-4896 筛选账号类型-->
		<!-- updated for v1.9.8 KNZT-4870-->
		<if test="obj.accountType != null and obj.accountType.size > 0">
			and a.type in
			<foreach collection="obj.accountType" item="accountTypeItem" open="(" separator="," close=")">
				#{accountTypeItem}
			</foreach>
		</if>
 		<!-- added for v2.1.4 KNZT-6387-->
		<if test="obj.payType != null and obj.payType.size > 0">
			and a.pay_type in
			<foreach collection="obj.payType" item="payTypeItem" open="(" separator="," close=")">
				#{payTypeItem}
			</foreach>
		</if>
		order by a.update_date desc
	</select>

	<select id="getCompanyDetailInfo" resultType="com.backend.common.entity.mapping.CompanyListInfo">
		SELECT a.id, a.name companyName, b.login_name loginName, a.phone, a.email, a.purchase_account_num-1 as subUserNum, a.begin_date beginDate,
		a.end_date endDate,a.pro_key AS "proKey",
		a.pro_secret_key AS "proSecretKey",
		a.pro_main_login_name AS "proMainLoginName",
		a.yunju_key AS "yunjuKey",
		a.yunju_secret_key AS "yunjuSecretKey",
		b.status userStatus,
		c.consumedNum,
		s4.total_count as creditReportTotalCount,
		s4.consumed_count as creditReportConsumedCount,
		s5.total_count as hkCreditReportTotalCount,
		s5.consumed_count as hkCreditReportConsumedCount,
		a.short_name as shortName,
		a.open_api_user_id as openApiUserId,
		a.enable_open_api as enableOpenApi,
	    a.business_scenario  AS "businessScenario",
	   	<!--a.charge_mode AS "chargeMode",-->
	   	a.data_save AS "dataSave",
	   	a.show_credit AS "showCredit",
	   	a.can_upgrade AS "canUpgrade",
		a.type as "type",
		CASE WHEN sd.value IS NOT NULL THEN 1 ELSE 0 END AS "innerAccount"
		FROM sys_comp_info a
		inner join sys_user b on b.id = a.main_user_id
		LEFT JOIN (SELECT company_id ,COUNT(*) as consumedNum from sys_user where del_flag = '0' group by company_id) c on c.company_id = a.id
		left join sys_comp_info_func_count s4 on s4.company_id = a.id and s4.function_table_id = 'global_service_id' and  s4.del_flag = '0'
		left join sys_comp_info_func_count s5 on s5.company_id = a.id and s5.function_table_id = 'hk_service_id' and  s5.del_flag = '0'
		left join sys_dict sd on a.id = sd.value and sd.label = 'sys_comp_info_id' and sd.`type` = 'inner_account' and sd.del_flag = '0'
		WHERE a.del_flag='0'
		AND b.login_name !='global_admin'
		AND a.id = #{companyId}
	</select>

	<select id="getByProMainLoginName" resultType="com.qcc.frame.jee.modules.sys.entity.mapping.CompanyMainInfoTO">
        SELECT a.id,a.main_user_id
        FROM sys_comp_info a
        WHERE a.pro_main_login_name = #{proMainLoginName} and a.del_flag='0'
        limit 1
    </select>

	<select id="getCompanyByLoginName" resultType="com.qcc.frame.jee.modules.sys.entity.mapping.CompanyMainInfoTO">
        SELECT a.id as "mainUserId" , a.company_id  as "id" FROM sys_user a
        WHERE a.login_name = #{loginName} and a.del_flag='0'
    </select>

	<select id="getCompByIdWithDelFlag" resultType="com.qcc.frame.jee.modules.sys.entity.mapping.CompanyMainInfoTO">
		SELECT a.id,a.main_user_id
		FROM sys_comp_info a
		WHERE a.id = #{companyId}
	</select>
	
    <select id="listCompanyProKeyInfo" resultType="com.backend.common.entity.mapping.CompanyProKeyInfoPO">
		select distinct pro_key, pro_secret_key from sys_comp_info where del_flag = '0'
	</select>
	
    <select id="listCompanyShortInfoBySearchKey" resultType="com.backend.common.entity.mapping.CompanyShortInfoTO">
		SELECT a.id, a.name companyName, b.login_name loginName
		FROM sys_comp_info a
		LEFT JOIN sys_user b on a.main_user_id = b.id and b.del_flag = '0'
		WHERE a.id = #{searchKey} or b.login_name = #{searchKey} and a.del_flag = '0'
	</select>
	
	<select id="getCompanyShortInfoById" resultType="com.backend.common.entity.mapping.CompanyShortInfoTO">
		SELECT a.id, a.name companyName, DATE_FORMAT(a.begin_date, '%Y-%m-%d') beginDate, DATE_FORMAT(a.end_date, '%Y-%m-%d') endDate
		FROM sys_comp_info a WHERE a.id = #{id} and a.del_flag = '0'
	</select>
	
    <select id="listCompanyToOpenApiForPatch" resultType="com.backend.common.entity.mapping.CompanyPatchInfoListTO">
		SELECT a.id as companyId, a.name kycCompanyName, b.login_name email
		FROM sys_comp_info a
		inner join sys_user b on b.id = a.main_user_id
		WHERE a.del_flag = '0' AND b.login_name !='global_admin' AND ifnull(a.open_api_user_id,'') = ''
		AND EXISTS( SELECT 1 FROM sys_user_role c WHERE c.user_id = b.id AND c.role_id = 'role_sub_hk_global_service')
		<if test="idList != null and idList.size() > 0">
			AND a.id in
			<foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="listCompanyAccountInfoForOpenApiToPatchWithExistsOpenUserId" resultType="com.backend.common.entity.mapping.CompanyPatchInfoListTO">
		SELECT a.id as companyId, a.name kycCompanyName, b.login_name email, a.begin_date serviceStartDate,
			   a.end_date serviceEndDate
		FROM sys_comp_info a
				 inner join sys_user b on b.id = a.main_user_id
		WHERE a.del_flag = '0' AND b.login_name !='global_admin' AND ifnull(a.open_api_user_id,'') != ''
		AND EXISTS( SELECT 1 FROM sys_user_role c WHERE c.user_id = b.id AND c.role_id = 'role_sub_hk_global_service')
		<if test="idList != null and idList.size() > 0">
			AND a.id in
			<foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>
    <select id="existUnactivatedCompanyByDateRange" resultType="java.lang.Integer">
		SELECT count(1)
		FROM sys_comp_info a
		inner join sys_user b on b.id = a.main_user_id and b.del_flag = '0'
		WHERE a.del_flag = '0'
		AND b.login_name !='global_admin'
		AND a.type = 3
		AND b.status != 'A'
		AND a.create_date >= #{beginTimeStr}
		AND a.create_date &lt;= #{endTimeStr}
	</select>
	<update id="updateCompanyMainUser">
		UPDATE sys_comp_info
		SET main_user_id = #{mainUserId},
		email = #{email},
		update_by = #{updateBy},
		update_date = now()
		WHERE id = #{companyId}
	</update>
</mapper>
