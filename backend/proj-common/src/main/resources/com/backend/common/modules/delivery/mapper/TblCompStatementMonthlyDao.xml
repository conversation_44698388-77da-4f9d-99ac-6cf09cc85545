<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.backend.common.modules.delivery.mapper.TblCompStatementMonthlyDao">
	<sql id="tblCompStatementMonthlyColumns">
		a.id AS "id",
		a.company_id AS "companyId",
		a.statement_month AS "statementMonth",
		a.issue_date AS "issueDate",
		a.invoice_no AS "invoiceNo",
		a.start_credit AS "startCredit",
		a.start_amount AS "startAmount",
		a.end_credit AS "endCredit",
		a.end_amount AS "endAmount",
		a.end_gifted_credit AS "endGiftedCredit",
		a.credits_topped_up AS "creditsToppedUp",
		a.credits_gifted AS "creditsGifted",
		a.credits_used AS "creditsUsed",
		a.amount_used AS "amountUsed",
		a.credits_refunded AS "creditsRefunded",
		a.credits_expired AS "creditsExpired",
		a.url AS "url",
		a.return_status AS "returnStatus",
		a.total_credit_due AS "totalCreditDue",
		a.total_amount_due AS "totalAmountDue",
		a.kzz_contract_No AS "kzzContractNo",
		a.del_flag AS "delFlag",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate"
	</sql>
	
	<select id="get" resultType="com.backend.common.modules.delivery.entity.TblCompStatementMonthly">
		SELECT 
			<include refid="tblCompStatementMonthlyColumns"/>
		FROM tbl_comp_statement_monthly a
		WHERE a.id = #{id} AND a.del_flag = '0'
	</select>
    <select id="getByCompanyAndYearMonth"
            resultType="com.backend.common.modules.delivery.entity.TblCompStatementMonthly">
		SELECT
			<include refid="tblCompStatementMonthlyColumns"/>
		FROM tbl_comp_statement_monthly a
		WHERE a.company_id = #{companyId} AND a.statement_month = #{statementMonth} AND a.del_flag = '0'
		limit 1
	</select>

    <insert id="insert">
		INSERT INTO tbl_comp_statement_monthly(
			id,
			company_id,
			statement_month,
			issue_date,
			invoice_no,
			start_credit,
			start_amount,
			end_credit,
			end_amount,
			end_gifted_credit,
			credits_topped_up,
			credits_gifted,
			credits_used,
			amount_used,
			credits_refunded,
			credits_expired,
			url,
			return_status,
			total_credit_due,
			total_amount_due,
			kzz_contract_no,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#{id},
			#{companyId},
			#{statementMonth},
			#{issueDate},
			#{invoiceNo},
			#{startCredit},
			#{startAmount},
			#{endCredit},
			#{endAmount},
			#{endGiftedCredit},
			#{creditsToppedUp},
			#{creditsGifted},
			#{creditsUsed},
			#{amountUsed},
			#{creditsRefunded},
			#{creditsExpired},
			#{url},
			#{returnStatus},
			#{totalCreditDue},
			#{totalAmountDue},
		    #{kzzContractNo},
			#{delFlag},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate}
		)
	</insert>
	
	<insert id="batchInsert">
		INSERT INTO tbl_comp_statement_monthly(
			id,
			company_id,
			statement_month,
			issue_date,
			invoice_no,
			start_credit,
			start_amount,
			end_credit,
			end_amount,
			end_gifted_credit,
			credits_topped_up,
			credits_gifted,
			credits_used,
			amount_used,
			credits_refunded,
			credits_expired,
			url,
			return_status,	
			total_credit_due,
			total_amount_due,
			kzz_contract_no,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES 
		<foreach collection="list" item="obj" index="index" separator=",">
		(
			#{obj.id},
			#{obj.companyId},
			#{obj.statementMonth},
			#{obj.issueDate},
			#{obj.invoiceNo},
			#{obj.startCredit},
			#{obj.startAmount},
			#{obj.endCredit},
			#{obj.endAmount},
			#{obj.endGiftedCredit},
			#{obj.creditsToppedUp},
			#{obj.creditsGifted},
			#{obj.creditsUsed},
			#{obj.amountUsed},
			#{obj.creditsRefunded},
			#{obj.creditsExpired},
			#{obj.url},
			#{obj.returnStatus},
			#{obj.totalCreditDue},
			#{obj.totalAmountDue},
		 	#{obj.kzzContractNo},
			#{obj.delFlag},
			#{obj.createBy.id},
			#{obj.createDate},
			#{obj.updateBy.id},
			#{obj.updateDate}
		)
		</foreach>
	</insert>
	
	<update id="update">
		UPDATE tbl_comp_statement_monthly SET 
			company_id = #{companyId},
			statement_month = #{statementMonth},
			issue_date = #{issueDate},
			invoice_no = #{invoiceNo},
			start_credit = #{startCredit},
			start_amount = #{startAmount},
			end_credit = #{endCredit},
			end_amount = #{endAmount},
			end_gifted_credit = #{endGiftedCredit},
			credits_topped_up = #{creditsToppedUp},
			credits_gifted = #{creditsGifted},
			credits_used = #{creditsUsed},
			amount_used = #{amountUsed},
			credits_refunded = #{creditsRefunded},
			credits_expired = #{creditsExpired},
			url = #{url},
			return_status = #{returnStatus},
			total_credit_due = #{totalCreditDue},
			total_amount_due = #{totalAmountDue},
			kzz_contract_no = #{kzzContractNo},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		UPDATE tbl_comp_statement_monthly SET 
			del_flag = #{DEL_FLAG_DELETE},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>

	<select id="findListByCondtion" resultType="com.backend.common.modules.delivery.entity.TblCompStatementMonthly">
		SELECT
		<include refid="tblCompStatementMonthlyColumns"/>
		FROM tbl_comp_statement_monthly a
		LEFT JOIN sys_comp_info b ON a.company_id = b.id
		<include refid="condition_where"/>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<sql id="condition_where">
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="companyId != null and companyId != ''">
				and a.company_id = #{companyId}
			</if>
			<if test="statementMonth != null and statementMonth != ''">
				and a.statement_month = #{statementMonth}
			</if>
			<if test="payTypeListInOrCondition != null and payTypeListInOrCondition.size() > 0 or companyIdListInOrCondition != null and companyIdListInOrCondition.size() > 0">
				and (
					<if test="payTypeListInOrCondition != null and payTypeListInOrCondition.size() > 0">
						b.pay_type in
						<foreach collection="payTypeListInOrCondition" item="item" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
					<if test="payTypeListInOrCondition != null and payTypeListInOrCondition.size() > 0 and companyIdListInOrCondition != null and companyIdListInOrCondition.size() > 0">
						or
					</if>
					<if test="companyIdListInOrCondition != null and companyIdListInOrCondition.size() > 0">
						a.company_id in
						<foreach collection="companyIdListInOrCondition" item="item" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
				)
			</if>
		</where>
	</sql>

	<select id="getByInvoiceNo" resultType="com.backend.common.modules.delivery.entity.TblCompStatementMonthly">
		SELECT
			<include refid="tblCompStatementMonthlyColumns"/>
		FROM tbl_comp_statement_monthly a
		WHERE a.invoice_no = #{invoiceNo} AND a.del_flag = '0'
		limit 1
	</select>

	<select id="page4Management" resultType="com.backend.common.modules.delivery.model.StatementMonthly4ManagementTO">
		SELECT
			a.id as "id",
			a.company_id as "companyId",
			a.invoice_no as "invoiceNo",
			b.short_name as "organization", 
			b.type as "companyType",
			b.pay_type as "payType",
			a.update_date as "updateDate",
			a.statement_month as "statementMonth",
			a.total_amount_due as "totalAmountDue",
			a.return_status as "returnStatus",
			a.url as "url",
			CASE
				WHEN a.return_status = 'S' THEN a.total_amount_due
				WHEN a.return_status = 'P' THEN 0
				ELSE NULL
			END as "returnAmount"
		FROM tbl_comp_statement_monthly a
		LEFT JOIN sys_comp_info b ON a.company_id = b.id AND b.del_flag = '0'
		LEFT JOIN sys_user u ON b.main_user_id = u.id AND u.del_flag = '0'
		<where>
			a.del_flag = '0'
			<if test="obj.statementMonth != null and obj.statementMonth != ''">
				AND a.statement_month = #{obj.statementMonth}
			</if>
			<if test="obj.startStatementMonth != null">
				AND a.statement_month &gt;= #{obj.startStatementMonth}
			</if>
			<if test="obj.endStatementMonth != null">
				AND a.statement_month &lt;= #{obj.endStatementMonth}
			</if>
			<if test="obj.companyTypeList != null and obj.companyTypeList.size() > 0">
				AND b.type IN
				<foreach collection="obj.companyTypeList" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="obj.payTypeList != null and obj.payTypeList.size() > 0">
				AND b.pay_type IN
				<foreach collection="obj.payTypeList" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="obj.keyword != null and obj.keyword != ''">
				AND (
					b.name LIKE CONCAT('%', #{obj.keyword}, '%')
					OR b.short_name LIKE CONCAT('%', #{obj.keyword}, '%')
					OR u.login_name LIKE CONCAT('%', #{obj.keyword}, '%')
					OR a.invoice_no = #{obj.keyword}
				)
			</if>
			<if test="obj.excludeTestAccount != null and obj.excludeTestAccount == 'Y'.toString()">
				and not exists (
					select 1 from sys_comp_info_ext e
					where a.company_id = e.company_id AND e.`type` = 'EXCLUDE_FROM_MONTHLY_BILL' AND e.value = 'Y'
				)
			</if>
			<if test="obj.returnStatusList != null and obj.returnStatusList.size() > 0">
				AND a.return_status IN
				<foreach collection="obj.returnStatusList" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		<choose>
			<when test="obj.page != null and obj.page.orderBy != null and obj.page.orderBy != ''">
				ORDER BY ${obj.page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="listByIds" resultType="com.backend.common.modules.delivery.entity.TblCompStatementMonthly">
		SELECT
		<include refid="tblCompStatementMonthlyColumns"/>
		FROM tbl_comp_statement_monthly a
		WHERE a.del_flag = '0'
			AND a.id IN 
			<foreach collection="ids" item="item" open="(" separator="," close=")">
		        #{item}
			</foreach>
		ORDER BY a.create_date DESC;
	</select>
	<select id="getUnfinishedPayments"
			resultType="com.backend.common.modules.delivery.entity.TblCompStatementMonthly">
		SELECT
		<include refid="tblCompStatementMonthlyColumns"/>
		FROM tbl_comp_statement_monthly a
		WHERE a.del_flag = '0' AND a.return_status = 'P' ORDER BY a.create_date DESC
	</select>
</mapper>