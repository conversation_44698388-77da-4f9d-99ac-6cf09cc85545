<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.backend.common.mapper.SysCompInfoExtDao">
	<sql id="sysCompInfoExtColumns">
		a.id AS "id",
		a.company_id AS "companyId",
		a.type AS "type",
		a.value AS "value",
		a.desc AS "desc",
		a.del_flag AS "delFlag",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.version AS "version"
	</sql>
	
	<select id="get" resultType="com.backend.common.entity.CompInfoExt">
		SELECT 
			<include refid="sysCompInfoExtColumns"/>
		FROM sys_comp_info_ext a
		WHERE a.id = #{id} AND a.del_flag = '0'
	</select>
	
	<insert id="insert">
		INSERT INTO sys_comp_info_ext(
			id,
			company_id,
			type,
			value,
			`desc`,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date,
			version
		) VALUES (
			#{id},
			#{companyId},
			#{type},
			#{value},
			#{desc},
			#{delFlag},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{version}
		)
	</insert>
	
	<insert id="batchInsert">
		INSERT INTO sys_comp_info_ext(
			id,
			company_id,
			type,
			value,
			`desc`,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date,
			version
		) VALUES 
		<foreach collection="list" item="obj" index="index" separator=",">
		(
			#{obj.id},
			#{obj.companyId},
			#{obj.type},
			#{obj.value},
			#{obj.desc},
			#{obj.delFlag},
			#{obj.createBy.id},
			#{obj.createDate},
			#{obj.updateBy.id},
			#{obj.updateDate},
			#{obj.version}
		)
		</foreach>
	</insert>
	
	
	<update id="update">
		UPDATE sys_comp_info_ext SET 
			company_id = #{companyId},
			type = #{type},
			value = #{value},
			`desc` = #{desc},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			version = #{version}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		UPDATE sys_comp_info_ext SET 
			del_flag = #{DEL_FLAG_DELETE},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>
	<select id="findListByCondtion" resultType="com.backend.common.entity.CompInfoExt">
		SELECT 
			<include refid="sysCompInfoExtColumns"/> 
		FROM sys_comp_info_ext a 
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL} 
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="retrieveInfoByCompanyIdAndType" resultType="java.lang.String">
		SELECT 
			a.value 
		FROM sys_comp_info_ext a 
		<where>
			a.del_flag = 0 and a.company_id = #{companyId} and a.type = #{type}
		</where>
	</select>
	
	<select id="retrieveCompanyIdByType" resultType="java.lang.String">
		SELECT 
			a.company_id 
		FROM sys_comp_info_ext a 
		<where>
			a.del_flag = '0' and a.type = #{type} and a.value = 'Y'
		</where>
	</select>
	
	<select id="retrieveCompanyIdByTypeAndValue" resultType="java.lang.String">
		SELECT 
			a.company_id 
		FROM sys_comp_info_ext a 
		<where>
			a.del_flag = '0' and a.type = #{type} and a.value =#{value}
		</where>
	</select>
	
	<select id="retrieveCompInfoExtInfo" resultType="com.backend.common.entity.CompInfoExt">
		SELECT 
			<include refid="sysCompInfoExtColumns"/> 
		FROM sys_comp_info_ext a 
		<where>
			a.del_flag = 0 and a.company_id = #{companyId} and a.type = #{type}
		</where>
	</select>
	
	<delete id="physicallyDeleteByCompanyId">
		DELETE FROM sys_comp_info_ext
		WHERE company_id = #{company_id}
	</delete>
	
	<!-- 执行逻辑删除 -->
	<update id="logicDeleteByCompanyIdAndType"> 
		UPDATE sys_comp_info_ext 
		SET    del_flag = '1'
		WHERE company_id = #{company_id} AND type = #{type}	
	</update>
	<update id="deleteValue">
		UPDATE sys_comp_info_ext
		SET del_flag = '1',
		update_by = #{updateBy},
		update_date = NOW()
		WHERE company_id = #{companyId}
		and del_flag = '0'
		AND type = #{type}
		and value = #{value}
	</update>
</mapper>