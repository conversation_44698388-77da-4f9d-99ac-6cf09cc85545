<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.backend.common.modules.delivery.mapper.TblContractDeliveryTransactionDao">
	<sql id="tblContractDeliveryTransactionColumns">
		a.id AS "id",
		a.date_int AS "dateInt",
		a.company_id AS "companyId",
		a.contract_delivery_id AS "contractDeliveryId",
		a.contract_delivery_prod_acc_id AS "contractDeliveryProdAccId",
		a.charge_unit_contract_delivery_id AS "chargeUnitContractDeliveryId",
		a.rel_id AS "relId",
		a.changed_unit AS "changedUnit",
		a.transaction_type AS "transactionType",
		a.transaction_category AS "transactionCategory",
		a.before_remain_unit AS "beforeRemainUnit",
		a.company_remain_unit AS "companyRemainUnit",
		a.del_flag AS "delFlag",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate"
	</sql>
	
	<select id="get" resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction">
		SELECT 
			<include refid="tblContractDeliveryTransactionColumns"/>
		FROM tbl_contract_delivery_transaction a
		WHERE a.id = #{id} AND a.del_flag = '0'
	</select>
    <select id="getByRelId"
            resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction">
		SELECT
			<include refid="tblContractDeliveryTransactionColumns"/>
		FROM tbl_contract_delivery_transaction a
		WHERE a.rel_id = #{relId} AND a.transaction_type = #{transactionType} AND a.del_flag = '0'
	</select>

	<select id="sumChangedUnitByProAccAndDate" resultType="java.math.BigDecimal">
		SELECT SUM(changed_unit) FROM tbl_contract_delivery_transaction a
		WHERE a.contract_delivery_prod_acc_id = #{contractDeliveryProdAccId} AND a.date_int = #{dateInt} AND a.transaction_category = 'CHG' AND a.del_flag = '0'
	</select>
    <select id="sumChangedUnitByContractDeliveryAndDate" resultType="java.math.BigDecimal">
		SELECT SUM(changed_unit) FROM tbl_contract_delivery_transaction a
		WHERE a.contract_delivery_id = #{contractDeliveryId}
		  AND a.date_int &gt;= #{beginDate} AND a.date_int &lt;= #{endDate} AND a.transaction_category = 'CHG' AND a.del_flag = '0'
	</select>
    <select id="getChangedByCompanyIdAdnDateRange"
            resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction">
		SELECT
			<include refid="tblContractDeliveryTransactionColumns"/>
		FROM tbl_contract_delivery_transaction a
		WHERE a.company_id = #{companyId} AND a.date_int &gt;= #{beginDate} AND a.date_int &lt;= #{endDate} AND a.transaction_category = 'CHG' AND a.del_flag = '0'
	</select>
	<select id="sumChangedUnitByProAccAndDateRange" resultType="java.math.BigDecimal">
		SELECT SUM(changed_unit) FROM tbl_contract_delivery_transaction a
		WHERE a.contract_delivery_prod_acc_id = #{contractDeliveryProdAccId} AND a.transaction_category = 'CHG' AND a.del_flag = '0'
		  <if test="beginDate != null">
		      AND a.date_int &gt;= #{beginDate}
		  </if>
		  <if test="endDate != null">
		      AND a.date_int &lt;= #{endDate} 
		  </if>
		  
	</select>
	<select id="sumChangedUnitByContractDeliveryWithType" resultType="java.math.BigDecimal">
		SELECT SUM(changed_unit) FROM tbl_contract_delivery_transaction a
		WHERE a.contract_delivery_id = #{contractDeliveryId}
		AND a.date_int &gt;= #{beginDate} AND a.date_int &lt;= #{endDate} AND a.del_flag = '0'
		AND a.transaction_type IN
		<foreach collection="transactionTypeList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<select id="sumCorrectOrderUnitByContractDeliveryAndDate" resultType="java.math.BigDecimal">
		select SUM(t.changed_unit)
		from tbl_contract_delivery_transaction t
		where t.del_flag = '0'
		  and t.transaction_type = 'CRORD'
		  and t.contract_delivery_id = #{contractDeliveryId}
		  and t.rel_id in (select t2.rel_id
						   from tbl_contract_delivery_transaction t2
						   where t2.contract_delivery_id = #{contractDeliveryId}
							 and t2.transaction_type = 'ORD'
							 AND t2.date_int &gt;= #{beginDate}
							 AND t2.date_int &lt;= #{endDate}
							 AND t2.del_flag = '0')

	</select>

	<insert id="insert">
		INSERT INTO tbl_contract_delivery_transaction(
			id,
			date_int,
			company_id,
			contract_delivery_id,
			contract_delivery_prod_acc_id,
			charge_unit_contract_delivery_id,
			rel_id,
			changed_unit,
			transaction_type,
			transaction_category,
			before_remain_unit,
			company_remain_unit,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#{id},
			#{dateInt},
			#{companyId},
			#{contractDeliveryId},
			#{contractDeliveryProdAccId},
			#{chargeUnitContractDeliveryId},
			#{relId},
			#{changedUnit},
			#{transactionType},
			#{transactionCategory},
			#{beforeRemainUnit},
			#{companyRemainUnit},
			#{delFlag},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate}
		)
	</insert>
	
	<insert id="batchInsert">
		INSERT INTO tbl_contract_delivery_transaction(
			id,
			date_int,
			company_id,
			contract_delivery_id,
			contract_delivery_prod_acc_id,
			charge_unit_contract_delivery_id,
			rel_id,
			changed_unit,
			transaction_type,
			transaction_category,
			before_remain_unit,
			company_remain_unit,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES 
		<foreach collection="list" item="obj" index="index" separator=",">
		(
			#{obj.id},
			#{obj.dateInt},
			#{obj.companyId},
			#{obj.contractDeliveryId},
			#{obj.contractDeliveryProdAccId},
			#{obj.chargeUnitContractDeliveryId},
			#{obj.relId},
			#{obj.changedUnit},
			#{obj.transactionType},
			#{obj.transactionCategory},
			#{obj.beforeRemainUnit},
			#{obj.companyRemainUnit},
			#{obj.delFlag},
			#{obj.createBy.id},
			#{obj.createDate},
			#{obj.updateBy.id},
			#{obj.updateDate}
		)
		</foreach>
	</insert>
	
	<update id="update">
		UPDATE tbl_contract_delivery_transaction SET 
			date_int = #{dateInt},
			company_id = #{companyId},
			contract_delivery_id = #{contractDeliveryId},
			contract_delivery_prod_acc_id = #{contractDeliveryProdAccId},
			charge_unit_contract_delivery_id = #{chargeUnitContractDeliveryId},
			rel_id = #{relId},
			changed_unit = #{changedUnit},
			transaction_type = #{transactionType},
			transaction_category = #{transactionCategory},
			before_remain_unit = #{beforeRemainUnit},
			company_remain_unit = #{companyRemainUnit},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		UPDATE tbl_contract_delivery_transaction SET 
			del_flag = #{DEL_FLAG_DELETE},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>
    <update id="updateProdAccIdByProdAccId">
		UPDATE tbl_contract_delivery_transaction
		SET contract_delivery_prod_acc_id = #{afterProdAccId}, update_by = 'datapatch2040927', update_date = now()
		WHERE contract_delivery_prod_acc_id = #{beforeProdAccId}
	</update>

	<select id="pageTransactionContract" resultType="com.backend.common.modules.delivery.model.TransactionContractTO">
		SELECT
			<include refid="tblContractDeliveryTransactionColumns"/>,
			d.contract_no AS 'contractNo',
			d.customer_name AS 'customerName',
			d.amount_std AS 'amountStd',
			d.party_divide_rate AS 'partyDivideRate',
			p.discount_amount AS 'unitAmount'
		FROM tbl_contract_delivery_transaction t
		left join tbl_contract_delivery d on d.id = t.contract_delivery_id and d.del_flag = '0'
		left join tbl_contract_delivery_prod p on p.contract_delivery_id = d.id and p.function_table_id = 'global_service_id' and d.del_flag = '0'
		WHERE
			t.del_flag = '0'
		  <if test="contractDeliveryId != null">
			  and t.contract_delivery_id = #{contractDeliveryId}
		  </if>
		  <if test="contractNo != null">
			  and d.contract_no = #{contractNo}
		  </if>
		  <if test="minDate != null">
			  and t.date_int &gt;= #{minDate}
		  </if>
		  <if test="maxDate != null">
			  and t.date_int &lt;= #{maxDate}
		  </if>
	</select>
	<select id="getChangedSumOrderUnitGroupByUser"
			resultType="com.backend.common.modules.delivery.model.TransactionOrderUserConsumeTO">
		select ro.user_id, ro.login_name, sum(t.changed_unit) as credits
		FROM tbl_contract_delivery_transaction t
				 left join tbl_comp_report_order ro on t.rel_id = ro.id
		where t.del_flag = '0'
		  and ro.id is not null
		  and t.contract_delivery_id = #{contractDeliveryId}
		  and t.date_int &gt;= #{beginDate}
		  and t.date_int &lt;= #{endDate}
		  and t.transaction_category = 'CHG'
		  and t.transaction_type in
		  <foreach collection="transactionList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		  </foreach>
		group by ro.user_id
		order by credits desc
	</select>


    <select id="sumChangedUnitByProAccAndDateRangeByContractNo" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(t.changed_unit), 0)
	        FROM tbl_contract_delivery_transaction t
        LEFT JOIN tbl_contract_delivery d ON t.contract_delivery_id = d.id
        WHERE d.contract_no = #{contractNo}
        AND t.date_int &gt;= #{beginDateInt}
        AND t.date_int &lt;= #{endDateInt}
        AND t.transaction_category = 'CHG'
        AND t.del_flag = '0'
    </select>

	<select id="sumCnChangedUnitByContractNoAndDateRange" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(t.changed_unit), 0)
		FROM tbl_contract_delivery_transaction t
		LEFT JOIN tbl_contract_delivery d ON t.contract_delivery_id = d.id
		LEFT JOIN tbl_comp_report_order ro ON t.rel_id = ro.id and t.transaction_type in ('ORD', 'REORD','CRORD')
		LEFT JOIN tbl_comp_api_order ao ON t.rel_id = ao.id and t.transaction_type in ('API', 'REAPI')
		WHERE d.contract_no = #{contractNo}
		AND t.date_int &gt;= #{beginDateInt}
		AND t.date_int &lt;= #{endDateInt}
		AND t.del_flag = '0'
		AND t.transaction_category = 'CHG'
		AND t.transaction_type in
		<foreach collection="transactionTypeList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
		AND ((ro.id IS NOT NULL AND ro.unit_group = 'CN') OR (ao.id IS NOT NULL AND ao.unit_group = 'CN'))
	</select>

	<select id="pageTransaction" resultType="com.backend.common.modules.delivery.model.TransactionListTO">
		SELECT 
			t.id AS "transactionId",
			t.date_int AS "dateInt",
			t.transaction_type AS "transactionType",
			d.contract_no AS "contractNo",
			ci.name AS "customerName",
			su.login_name AS "loginName",
			COALESCE(ro.order_no, ao.order_no) AS "orderNo",
			COALESCE(ro.unit_group, ao.unit_group) AS "unitGroup",
			COALESCE(ro.report_type, ao.api_type) AS "orderType",
			d.amount_std AS "amountStd",
			t.changed_unit AS "changedUnit",
			(t.changed_unit * d.actual_discount_amount) AS "confirmAmount",
			d.pay_type AS "payType",
			t.create_date AS "createDate",
			CASE WHEN cie.value = 'Y' THEN '是' ELSE '否' END AS "isTestAccount",
			CASE WHEN d.pay_type = 'R' THEN '线上' ELSE '线下' END AS "orderSourceDesc"
		FROM tbl_contract_delivery_transaction t
		LEFT JOIN tbl_contract_delivery d ON t.contract_delivery_id = d.id AND d.del_flag = '0'
		LEFT JOIN sys_comp_info ci ON ci.id = t.company_id AND ci.del_flag = '0'
		LEFT JOIN sys_user su on ci.main_user_id = su.id AND su.del_flag = '0'
		LEFT JOIN tbl_comp_report_order ro ON t.rel_id = ro.id AND ro.del_flag = '0'
		LEFT JOIN tbl_comp_api_order ao ON t.rel_id = ao.id AND ao.del_flag = '0'
		LEFT JOIN sys_comp_info_ext cie ON t.company_id = cie.company_id AND cie.type = 'EXCLUDE_FROM_MONTHLY_BILL'
		WHERE t.del_flag = '0'
			AND t.transaction_category = 'CHG'
		<if test="contractNo != null and contractNo != ''">
			AND d.contract_no = #{contractNo}
		</if>
		<if test="transactionType != null and transactionType != ''">
			AND t.transaction_type = #{transactionType}
		</if>
		<if test="beginDate != null">
			AND t.date_int &gt;= #{beginDate}
		</if>
		<if test="endDate != null">
			AND t.date_int &lt;= #{endDate}
		</if>
		<if test="orderNo != null and orderNo != ''">
			AND (ro.order_no = #{orderNo} OR ao.order_no = #{orderNo})
		</if>
		<if test="orderType != null and orderType != ''">
			AND (ro.report_type = #{orderType} OR ao.api_type = #{orderType})
		</if>
		<if test="transactionId != null and transactionId != ''">
			AND t.id = #{transactionId}
		</if>
		<if test="transactionTypeList != null and transactionTypeList.size() > 0">
			AND t.transaction_type IN
			<foreach collection="transactionTypeList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="orderTypeList != null and orderTypeList.size() > 0">
			AND (ro.report_type IN
			<foreach collection="orderTypeList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
			OR ao.api_type IN
			<foreach collection="orderTypeList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>)
		</if>
		<if test="excludeTestAccount != null and excludeTestAccount == 'Y'.toString()">
			AND (cie.value IS NULL OR cie.value != 'Y')
		</if>
		<if test="orderSource != null and orderSource == 'OL'.toString()">
			AND d.pay_type = 'R'
		</if>
		<if test="orderSource != null and orderSource == 'OFL'.toString()">
			AND (d.pay_type IS NULL OR d.pay_type != 'R')
		</if>
		<if test="keyword != null and keyword != ''">
			AND (d.contract_no = #{keyword} OR ro.order_no = #{keyword} OR ao.order_no = #{keyword} OR su.login_name
			like concat('%',#{keyword},'%') OR ci.name like concat('%',#{keyword},'%'))
		</if>
		ORDER BY t.date_int DESC, t.create_date DESC
	</select>
	<select id="pageTransaction4UserComp" resultType="com.backend.common.modules.delivery.model.TransactionBO">
		SELECT
			cdt.id,
			cdt.transaction_type,
			cdt.changed_unit,
			cdt.company_remain_unit,
			cdt.create_date,

			cd.id   AS 'contractDelivery.id',
			cd.contract_no   AS 'contractDelivery.contractNo',
			cd.begin_date   AS 'contractDelivery.beginDate',
			cd.end_date   AS 'contractDelivery.endDate',

			cdpa.id AS 'contractDeliveryProdAcc.id',
			cdpa.begin_date AS 'contractDeliveryProdAcc.beginDate',
			cdpa.end_date AS 'contractDeliveryProdAcc.endDate',
			cdpa.type AS 'contractDeliveryProdAcc.type',

			ci.id   AS 'compInfo.id',

			cro.id  AS 'compReportOrder.id',
			cro.order_no  AS 'compReportOrder.orderNo',
			cro.key_no_type  AS 'compReportOrder.keyNoType',
			cro.key_no  AS 'compReportOrder.keyNo',
			cro.corp_name  AS 'compReportOrder.corpName',
			cro.corp_name_en  AS 'compReportOrder.corpNameEn',
			cro.pers_name  AS 'compReportOrder.persName',
			cro.pers_name_en  AS 'compReportOrder.persNameEn',
			cro.unit_group  AS 'compReportOrder.unitGroup',
			cro.report_type  AS 'compReportOrder.reportType',
			cro.login_name  AS 'compReportOrder.loginName',

			cao.id  AS 'compApiOrder.id',
			cao.order_no  AS 'compApiOrder.orderNo',
			cao.unit_group  AS 'compApiOrder.unitGroup',
			cao.api_type  AS 'compApiOrder.apiType',

			csm.id  AS 'compStatementMonthly.id',
			csm.statement_month  AS 'compStatementMonthly.statementMonth',

			bd.id   AS 'benefitDelivery.id',
			bd.begin_date   AS 'benefitDelivery.beginDate',
			bd.end_date   AS 'benefitDelivery.endDate',
			bd.benefit_group   AS 'benefitDelivery.benefitGroup',
			bd.benefit_type   AS 'benefitDelivery.benefitType',
			bd.pool_count   AS 'benefitDelivery.poolCount'
		FROM
			tbl_contract_delivery_transaction cdt
			LEFT JOIN tbl_contract_delivery cd ON cdt.contract_delivery_id = cd.id AND cd.del_flag = '0'
			LEFT JOIN tbl_contract_delivery_prod_acc cdpa ON cdpa.id = cdt.contract_delivery_prod_acc_id AND cdpa.del_flag = '0'
			LEFT JOIN sys_comp_info ci ON ci.id = cd.company_id AND ci.del_flag = '0'
			LEFT JOIN tbl_comp_report_order cro ON cdt.rel_id = cro.id AND cro.del_flag = '0'
			LEFT JOIN tbl_comp_api_order cao ON cdt.rel_id = cao.id AND cao.del_flag = '0'
			LEFT JOIN tbl_comp_statement_monthly csm ON cdt.rel_id = csm.id AND csm.del_flag = '0'
			LEFT JOIN tbl_benefit_delivery bd ON cdt.transaction_type = 'BEN' AND cdt.rel_id = bd.id AND bd.del_flag = '0'
		WHERE
			cdt.del_flag = '0'
			AND cdt.company_id = #{companyId}
			<if test="createDateBegin != null">
				AND cdt.create_date &gt;= #{createDateBegin}
			</if>
			<if test="createDateEnd != null">
				AND cdt.create_date &lt;= #{createDateEnd}
			</if>
			<if test="transactionTypeList != null and transactionTypeList.size() > 0">
				AND cdt.transaction_type IN
				<foreach collection="transactionTypeList" item="transactionTypeItem" open="(" close=")" separator=",">
					#{transactionTypeItem}
				</foreach>
			</if>
			<if test="keyword != null and keyword != ''">
				AND (cd.contract_no = #{keyword} OR cro.order_no = #{keyword} OR cao.order_no = #{keyword})
			</if>
		ORDER BY
			cdt.date_int DESC, cdt.create_date DESC, cdt.company_remain_unit
	</select>

	<select id="getCompanyConsumeItemGroupByOrderTypeUnit" resultType="com.backend.common.modules.delivery.model.TransactionItemConsumeTO">
		SELECT 
			COALESCE(ro.report_type, ao.api_type) AS "orderType",
			t.changed_unit AS "singleUnit",
			t.changed_unit * d.actual_discount_amount AS "singleUnitAmount",
			SUM(t.changed_unit) AS "totalUnit",
			COUNT(t.id) AS "orderCount",
			SUM(t.changed_unit * d.actual_discount_amount) AS "totalAmount"
		FROM tbl_contract_delivery_transaction t
		LEFT JOIN tbl_contract_delivery d ON t.contract_delivery_id = d.id AND d.del_flag = '0'
		LEFT JOIN tbl_comp_report_order ro ON t.rel_id = ro.id AND ro.del_flag = '0'
		LEFT JOIN tbl_comp_api_order ao ON t.rel_id = ao.id AND ao.del_flag = '0'
		WHERE t.del_flag = '0'
		AND t.date_int &gt;= #{beginDateInt}
		AND t.date_int &lt;= #{endDateInt}
		AND t.company_id = #{companyId}
		AND t.transaction_category = 'CHG'
		AND t.transaction_type IN
		<foreach collection="transactionTypeList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
		GROUP BY orderType, singleUnit
	</select>
	
    <select id="listCompanyConsumedInfoWithDateRange" resultType="com.backend.common.entity.mapping.CompanyConsumedInfoTO">
		SELECT company_id, SUM(changed_unit) AS creditReportConsumedCount
		FROM tbl_contract_delivery_transaction
		WHERE create_date &gt;= #{startDate}
		AND create_date &lt;= #{endDate}
		AND transaction_category = 'CHG'
		AND del_flag = '0'
		AND company_id IN
		<foreach collection="companyIdList" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		group by company_id;
	</select>

	<update id="correctSelfOrderTransaction">
		UPDATE tbl_contract_delivery_transaction
		SET contract_delivery_prod_acc_id = #{prodAccId},
		contract_delivery_id = #{contractDeliveryId},
		charge_unit_contract_delivery_id = null,
		before_remain_unit = #{beforeRemainUnit},
		update_by = 'datapatch20250303',
		update_date = now()
		WHERE rel_id = #{relId}
	</update>

	<select id="sumChangeUnitByCompanyIdAndDateRange" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(t.changed_unit), 0)
		FROM tbl_contract_delivery_transaction t
		WHERE t.company_id = #{companyId}
		AND t.date_int &gt;= #{beginDateInt}
		AND t.date_int &lt;= #{endDateInt}
		AND t.del_flag = '0'
		AND t.transaction_type IN
		<foreach collection="transactionTypeList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<select id="findByCompanyId"
            resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction">
		SELECT
			<include refid="tblContractDeliveryTransactionColumns"/>
		FROM tbl_contract_delivery_transaction a
		WHERE a.company_id = #{companyId} AND a.del_flag = '0'
		ORDER BY a.date_int
	</select>

	<select id="getCorrectedUnitRelateOrderTransactionByUser" resultType="com.backend.common.modules.delivery.model.TransactionOrderUserConsumeTO">
		select ro.user_id, ro.login_name, sum(t.changed_unit) as credits
		from tbl_contract_delivery_transaction t
		left join tbl_comp_report_order ro on t.rel_id = ro.id 
		where t.del_flag = '0'
		  and ro.id is not null
		  and t.transaction_type = 'CRORD'
		  and t.contract_delivery_id = #{contractDeliveryId}
		  and t.rel_id in (select t2.rel_id
						   from tbl_contract_delivery_transaction t2
						   where t2.contract_delivery_id = #{contractDeliveryId}
							 and t2.transaction_type = 'ORD'
							 AND t2.date_int &gt;= #{beginDateInt}
							 AND t2.date_int &lt;= #{endDateInt}
							 AND t2.del_flag = '0')
		group by ro.user_id, ro.login_name
		order by credits desc
	</select>
	<select id="sumChangeUnitByCategoryAndType" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(t.changed_unit), 0)
		FROM tbl_contract_delivery_transaction t
		WHERE t.company_id = #{companyId}
		AND t.del_flag = '0'
		AND t.transaction_category = #{category}
		AND t.transaction_type IN
		<foreach collection="typeList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>
	<select id="calcTransactionConsumeGroupByUser"
			resultType="com.backend.common.modules.delivery.model.TransactionOrderUserConsumeTO">
		select ro.user_id, ro.login_name, sum(t.changed_unit) as credits
		FROM tbl_contract_delivery_transaction t
		left join tbl_comp_report_order ro on t.rel_id = ro.id
		where t.del_flag = '0'
		and ro.id is not null
		and t.company_id = #{companyId}
		and t.date_int &gt;= #{beginDateInt}
		and t.date_int &lt;= #{endDateInt}
		and t.transaction_category = 'CHG'
		and t.transaction_type in
		<foreach collection="transactionList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
		<if test="userIdList != null and userIdList.size() > 0">
			AND ro.user_id in
			<foreach collection="userIdList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		group by ro.user_id
		order by credits desc
	</select>

	<select id="calcCompanyTotalConsumeByDateRange" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(t.changed_unit), 0) as credits
		FROM tbl_contract_delivery_transaction t
		left join tbl_comp_report_order ro on t.rel_id = ro.id
		WHERE t.del_flag = '0'
		AND ro.id is not null
		AND t.company_id = #{companyId}
		AND t.date_int &gt;= #{beginDateInt}
		AND t.date_int &lt;= #{endDateInt}
		AND t.transaction_category = 'CHG'
		AND t.transaction_type in
		<foreach collection="transactionTypeList" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>
</mapper>