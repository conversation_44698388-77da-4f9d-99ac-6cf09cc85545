package com.backend.admin.task;

import com.backend.admin.exception.BreakException;
import com.backend.admin.scheduler.BaseCompanySchedulerJob;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.setting.condition.SysCompanyCondition;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.YearMonth;
import java.util.List;
import java.util.Objects;

/**
 * added for v1.9.5 KNZT-4117
 *
 * <AUTHOR>
 * @datetime 2024/8/30 16:12
 */
public class CompanyStatementMonthlyJob extends BaseCompanySchedulerJob {
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;

    @Override
    public boolean isSupportTriggerWithCompanyParam() {
        return true;
    }

    public List<String> prepareProcessCompanyList() {
        List<String> companyIdList = listTriggerCompanyIdList();
        if (CollectionUtils.isNotEmpty(companyIdList)) {
            return companyIdList;
        } else {
            SysCompanyCondition condition = new SysCompanyCondition();
            condition.setTypeList(Lists.newArrayList(CompTypeEnum.SIGN.getCode(), CompTypeEnum.CLIENT.getCode()));
            condition.removePage();
            return commSysCompanyService.getActiveCompanyIdByCondition(condition);
        }
    }

    @Override
    public void processCompany(List<String> companyIdList) throws BreakException {
        if (CollectionUtils.isEmpty(companyIdList)) {
            logger.info("CompanyStatementMonthlyJob companyIdList is empty");
            return;
        }
        YearMonth targetProcessYearMonth = YearMonth.now().minusMonths(1L);


        // added for v1.9.8 KNZT-4921 临时判断，20241001后可去除
        String flag = companyIdList.get(0);
        if (companyIdList.size() == 3 && Objects.equals(flag, "target")) {
            targetProcessYearMonth = YearMonth.parse(companyIdList.get(1));
            companyIdList = Lists.newArrayList(companyIdList.get(2));
        }


        for (String companyId : companyIdList) {
            logger.info("CompanyStatementMonthlyJob start companyId:{}", companyId);
            try {
                transactionBusinessService.calStatementMonthly(companyId, targetProcessYearMonth, false);
                logger.info("CompanyStatementMonthlyJob end companyId:{}", companyId);
            } catch (Exception e) {
                logger.error("CompanyStatementMonthlyJob error companyId:{}", companyId, e);
                sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.GENERATE_STATEMENT_ERROR, e, companyId);
            }
        }

    }

    @Override
    public String getTaskName() {
        return "CompanyStatementMonthlyJob";
    }
}
