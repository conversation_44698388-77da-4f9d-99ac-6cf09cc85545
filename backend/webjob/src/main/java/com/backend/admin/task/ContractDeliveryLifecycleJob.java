package com.backend.admin.task;

import com.backend.admin.scheduler.BaseSchedulerJob;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.DeliveryBusinessService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;

/**
 * added for v1.8.8 KNZT-3324
 * 交付合同生命周期管理定时任务
 *
 * <AUTHOR>
 * @datetime 2024/7/1 10:45
 */
public class ContractDeliveryLifecycleJob extends BaseSchedulerJob {

    @Autowired
    private DeliveryBusinessService deliveryBusinessService;
    @Autowired
    private CommTblContractDeliveryService commTblContractDeliveryService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private CommTblContractDeliveryProdAccService prodAccService;
    @Autowired
    private CommSysCompInfoFuncCountService funcCountService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;

    @Override
    public void processTask() {
        logger.info("ContractDeliveryLifecycleJob begin");

        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        int batchSize = 1000;

        try {
            // 1. 处理状态为 EFFECT 但已过期的合同
            List<String> expiredContractIds = commTblContractDeliveryService.getExpiredEffectiveContractIds(yesterday);
            if (CollectionUtils.isNotEmpty(expiredContractIds)) {
                Lists.partition(expiredContractIds, batchSize).forEach(batch -> {
                    List<TblContractDelivery> expiredContracts = commTblContractDeliveryService.batchGetByIds(batch);
                    logger.info("ContractDeliveryLifecycleJob found {} expired contracts", batch.size());
                    for (TblContractDelivery contract : expiredContracts) {
                        try {
                            deliveryBusinessService.contractExpiry(contract);
                            logger.info("ContractDeliveryLifecycleJob processed expired contract: {}", contract.getId());
                        } catch (Exception e) {
                            logger.error("ContractDeliveryLifecycleJob process expired contract error, contractDeliveryId: {}", contract.getId(), e);
                            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.CONTRACT_DELIVERY_JOB_ERROR, e, contract.getId());
                        }
                    }
                });
            }

            // 2. 处理状态为 PENDING 但生效时间已到的合同
            List<String> pendingContractIds = commTblContractDeliveryService.getPendingContractsReadyToEffect(today);
            if (CollectionUtils.isNotEmpty(pendingContractIds)) {
                Lists.partition(pendingContractIds, batchSize).forEach(batch -> {
                    List<TblContractDelivery> pendingContracts = commTblContractDeliveryService.batchGetByIds(batch);
                    logger.info("ContractDeliveryLifecycleJob found {} pending contracts ready to effect", batch.size());
                    for (TblContractDelivery contract : pendingContracts) {
                        try {
                            deliveryBusinessService.contractEffect(contract);
                            logger.info("ContractDeliveryLifecycleJob processed pending contract: {}", contract.getId());
                        } catch (Exception e) {
                            logger.error("ContractDeliveryLifecycleJob process pending contract error, contractDeliveryId: {}", contract.getId(), e);
                            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.CONTRACT_DELIVERY_JOB_ERROR, e, contract.getId());
                        }
                    }
                });
            }

            // 3. 处理即将过期的合同，发送钉钉消息
            List<String> nearExpiryContractIds = commTblContractDeliveryService.getIdByNearExpiryDates(today);
            if (CollectionUtils.isNotEmpty(nearExpiryContractIds)) {
                Lists.partition(nearExpiryContractIds, batchSize).forEach(batch -> {
                    List<TblContractDelivery> nearExpiryContracts = commTblContractDeliveryService.batchGetByIds(batch);
                    logger.info("ContractDeliveryLifecycleJob found {} contracts near expiry", batch.size());
                    for (TblContractDelivery contract : nearExpiryContracts) {
                        try {
                            // 发送钉钉消息
                            sysDingMsgNewTranService.sendBusinessReminder(AlarmTypeEnum.CONTRACT_NEAR_EXPIRY,
                                    contract.getContractNo(),
                                    DateUtils.formatDate(contract.getEndDate()),
                                    contract.getCustomerName());
                            logger.info("ContractDeliveryLifecycleJob sent reminder for contract: {}", contract.getId());
                        } catch (Exception e) {
                            logger.error("ContractDeliveryLifecycleJob send reminder error, contractDeliveryId: {}", contract.getId(), e);
                        }
                    }
                });
            }

            // 4. 处理过期额度账户
            logger.info("ContractDeliveryLifecycleJob begin to process expired accounts, query date: {}", yesterday);

            // 查询昨天过期的未消耗完的额度账户ID列表
            List<String> expiredAccountIds = prodAccService.findUnconsumedExpiredAccountIds(yesterday);
            if (CollectionUtils.isNotEmpty(expiredAccountIds)) {
                logger.info("ContractDeliveryLifecycleJob found {} expired accounts", expiredAccountIds.size());
                // 按批次处理
                Lists.partition(expiredAccountIds, batchSize).forEach(batch -> {
                    // 批量获取账户详情
                    List<TblContractDeliveryProdAcc> expiredAccounts = prodAccService.batchGetByIds(batch);
                    logger.info("ContractDeliveryLifecycleJob found {} expired accounts", expiredAccounts.size());

                    for (TblContractDeliveryProdAcc account : expiredAccounts) {
                        try {
							logger.info("ContractDeliveryLifecycleJob processing expired account: {}", account.getId());
                            transactionBusinessService.processExpiredAccounts(account);
                        } catch (Exception e) {
                            logger.error("ContractDeliveryLifecycleJob process expired account error, accountId: {}", account.getId(), e);
                            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.CONTRACT_DELIVERY_JOB_ERROR, e, account.getContractDeliveryId());
                        }
                    }
                });
                logger.info("ContractDeliveryLifecycleJob expired accounts processed");
            }

        } catch (Exception e) {
            logger.error("ContractDeliveryLifecycleJob error", e);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.CONTRACT_DELIVERY_JOB_ERROR, e, "all");
        }

        logger.info("ContractDeliveryLifecycleJob end");
    }

    @Override
    public String getTaskName() {
        return "ContractDeliveryLifecycleJob";
    }

}
