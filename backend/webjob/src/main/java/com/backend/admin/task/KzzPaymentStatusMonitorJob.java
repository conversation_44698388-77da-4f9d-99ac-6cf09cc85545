package com.backend.admin.task;

import com.backend.admin.scheduler.BaseSchedulerJob;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.service.SysDingMsgNewTranService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 客找找回款状态监控
 * 基于当前系统中未回款的数据
 * 判断收入确认合同关联的回款计划下 已核销的回款单金额，如果已核销回款单金额 = 回款计划总金额，
 * 则代表该该回款计划已经完成
 */
public class KzzPaymentStatusMonitorJob extends BaseSchedulerJob {
    @Autowired
    private TransactionBusinessService businessService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;

    @Override
    public void processTask() {
        businessService.checkInvoicePaymentStatus();
    }

    @Override
    public String getTaskName() {
        return "KzzPaymentStatusMonitorJob";
    }
}
