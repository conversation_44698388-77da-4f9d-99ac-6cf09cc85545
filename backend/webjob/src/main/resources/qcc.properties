#=============================#
#===== Database settings =====#
#=============================#

#mysql database setting
jdbc.type=mysql
#============================#
#===== System settings ======#
#============================#
productName=
copyrightYear=2017-
version=V1.0

qcc.company.id=2bf0dc2458dc4610b3d0f75926eb296d
#qcc.office=1
qcc.menu.root=000root
qfk.contact.phone=

# \u6F14\u793A\u6A21\u5F0F: \u4E0D\u80FD\u64CD\u4F5C\u548C\u4FDD\u5B58\u7684\u6A21\u5757\uFF1A sys: area/office/user/role/menu/dict, cms: site/category
demoMode=false

qcc.mobile.verify.code.content=\u3010\u4F01\u67E5\u67E5\u3011\u60A8\u7684\u9A8C\u8BC1\u7801\u662F%s\u3002\u5982\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5FFD\u7565\u672C\u77ED\u4FE1

#\u7BA1\u7406\u57FA\u7840\u8DEF\u5F84, \u9700\u540C\u6B65\u4FEE\u6539\uFF1Aweb.xml
adminPath=/a

#\u5206\u9875\u914D\u7F6E
page.pageSize=10

#============================#
#==== Framework settings ====#
#============================#

#\u4F1A\u8BDD\u8D85\u65F6\uFF0C \u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF0C 20m=1200000ms, 30m=1800000ms, 60m=3600000ms
session.sessionTimeout=1800000
#\u4F1A\u8BDD\u6E05\u7406\u95F4\u9694\u65F6\u95F4\uFF0C \u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF0C2m=120000ms\u3002
session.sessionTimeoutClean=120000

#\u7D22\u5F15\u9875\u8DEF\u5F84
web.view.index=/a

#\u89C6\u56FE\u6587\u4EF6\u5B58\u653E\u8DEF\u5F84
web.view.prefix=/WEB-INF/views/
web.view.suffix=.jsp

#\u6700\u5927\u6587\u4EF6\u4E0A\u4F20\u9650\u5236\uFF0C\u5355\u4F4D\u5B57\u8282. 10M=10*1024*1024(B)=10485760 bytes\uFF0C\u9700\u540C\u6B65\u4FEE\u6539\uFF1Ackfinder.xml
web.maxUploadSize=*********
web.maxUploadSize2=10485760

#\u4E0A\u4F20\u6587\u4EF6\u7EDD\u5BF9\u8DEF\u5F84, \u8DEF\u5F84\u4E2D\u4E0D\u5141\u8BB8\u5305\u542B\u201Cuserfiles\u201D
logfiles.basedir=${app.qcc.log.dir}



#unit:second
mobile.security.code.expiry.time.interval=60


application.type=job
application.type.job.createby=webjob
app.contextPath=qcc-webjob

project.env=${project.env}


qfk.domain=${qfk.domain}
qcc.pro.interface.domain=${qcc.pro.interface.domain}
qcc.yunju.interface.domain=${qcc.yunju.interface.domain}
qcc.intranet.interface.domain=${qcc.intranet.interface.domain}
qcc.yunju.report.interface.domain=${qcc.yunju.report.interface.domain}
qcc.kyc.gateway.interface.domain=${qcc.kyc.gateway.interface.domain}
qcc.yunju.report.internal.interface.domain=${qcc.yunju.report.internal.interface.domain}
qcc.yunju.global.interface.domain=${qcc.yunju.global.interface.domain}
openApi.global.admin.qcc.com.domain=${openApi.global.admin.qcc.com.domain}
database.jdbc.driver=${database.jdbc.driver}
global.web.convert.interface.domain=${global.web.convert.interface.domain}


#second session timeout
saas.login.session.timout=604800
#\u91CD\u590D\u767B\u5F55\u7684Access Token\u4F1A\u4FDD\u7559\u7684\u65F6\u95F4(\u91CD\u590D\u767B\u5F55\u65F6, \u9700\u8981\u5C06\u5931\u6548\u7684Access Token\u52A0\u5165\u5230\u5F3A\u5236\u767B\u51FA\u961F\u5217)
saas.logout.access.token.expiry.sec=3600
#\u767B\u5F55\u6210\u529F\u540E\u7528\u6237Access Token\u7684\u5931\u6548\u65F6\u95F4(\u524D\u63D0\u662F\u6CA1\u6709\u88ABRefresh)
saas.login.access.token.expiry.sec=604800
#\u524D\u7AEF\u8C03\u7528\u5237\u65B0Access Token\u7684\u95F4\u9694\u65F6\u95F4, \u6BCF\u9694\u4E00\u6BB5\u65F6\u95F4\u8981\u5237\u65B0\u4E00\u6B21Access Token
saas.login.access.token.refresh.interval.sec=0
#\u88AB\u5237\u65B0\u7684Access Token\u7684\u4FDD\u7559\u65F6\u95F4, (\u5F53Access Token\u88AB\u5237\u65B0\u65F6, \u4E4B\u524D\u7684Access Token\u4F1A\u4FDD\u7559\u4E00\u6BB5\u65F6\u95F4, \u4EE5\u9632\u5E76\u53D1\u64CD\u4F5C)
saas.login.access.token.refresh.expiry.interval.sec=180
#Refresh Token\u7684\u5931\u6548\u65F6\u95F4, \u8BBE\u7F6E\u8DDF\u7528\u6237Access Token\u7684\u5931\u6548\u65F6\u95F4\u4E00\u81F4
saas.login.refresh.token.expiry.sec=36000
saas.login.name.aes.key=5d5FO3VBgiRZS1nrR4jEiA==
saas.login.name.aes.iv=qtxhaj622y8oajd0
saas.login.name.sign.key=f80282e34093f52
#MFA Token Expire Seconds
saas.login.mfa.token.expiry.sec=600
saas.hk.searcher.info.aes.key=68k8NEZ2ok1YL7ouOX6cFQ==
saas.hk.searcher.info.aes.iv=rs2bzrjzkkrjk273
common.aes.key=GB2v012SF+5uWFp4cEC0yA==
common.aes.iv=aijoypuknk6va5t8


mail.host=${mail.smtp.host}
mail.port=${mail.smtp.port}
mail.auth=${mail.smtp.auth}
mail.timeout=25000
mail.username=${mail.smtp.username}
mail.password=${mail.smtp.password}

oss.internal.domail.url=${oss.internal.domail.url}
oss.external.domail.url=${oss.external.domail.url}
oss.accesskey=${oss.accesskey}
oss.accesskeysecret=${oss.accesskeysecret}
oss.bucket=${oss.bucket}

obs.domail.url=${obs.domail.url}
obs.accesskey=${obs.accesskey}
obs.secretaccesskey=${obs.secretaccesskey}
obs.bucket=${obs.bucket}

#unit:second
email.security.code.expiry.time.interval=300

userfiles.basedir=${app.qcc.upload.dir}

#\u4F01\u67E5\u67E5 key \u548C secretKey
pro.qcc.com.key=${pro.qcc.com.key}
pro.qcc.com.secretKey=${pro.qcc.com.secretKey}
#\u5F00\u653E\u5E73\u53F0 key \u548C secretKey
openApi.qcc.com.key=${openApi.qcc.com.key}
openApi.qcc.com.secretKey=${openApi.qcc.com.secretKey}

openApi.global.qcc.com.key=${openApi.global.qcc.com.key}
openApi.global.qcc.com.secretKey=${openApi.global.qcc.com.secretKey}
openApi.global.admin.qcc.com.key=${openApi.global.admin.qcc.com.key}
openApi.global.admin.qcc.com.secretKey=${openApi.global.admin.qcc.com.secretKey}
###15995460586,13776098447
exception.notify.dingtalk.mobile=15995460586,13776098447
exception.notify.dingtalk.accesstoken=${exception.notify.dingtalk.accesstoken}
global.webjob.ftp.ip=${global.webjob.ftp.ip}
global.webjob.ftp.port=${global.webjob.ftp.port}
global.webjob.ftp.username=${global.webjob.ftp.username}
global.webjob.ftp.password=${global.webjob.ftp.password}

kzz.api.domain=${kzz.api.domain}
kzz.api.secret.key=${kzz.api.secret.key}
kzz.api.access.key=${kzz.api.access.key}
stripe.api.key=${stripe.api.key}
global.api.internal.key=${global.api.internal.key}
global.api.internal.secret.key=${global.api.internal.secret.key}
webjob.domain=${webjob.domain}
data.map.node.url=${data.map.node.url}

kafka.oversea.spider.hk.report.refresh.topic=${kafka.oversea.spider.hk.report.refresh.topic}
kafka.oversea.spider.hk.report.buy.topic=${kafka.oversea.spider.hk.report.buy.topic}
kafka.oversea.spider.hk.ird.report.buy.topic=${kafka.oversea.spider.hk.ird.report.buy.topic}
kafka.oversea.spider.hk.ird.search.topic=${kafka.oversea.spider.hk.ird.search.topic}
kafka.oversea.spider.hk.ird.goods.topic=${kafka.oversea.spider.hk.ird.goods.topic}
kafka.oversea.spider.my.report.buy.topic=${kafka.oversea.spider.my.report.buy.topic}
kafka.oversea.spider.nz.report.buy.topic=${kafka.oversea.spider.nz.report.buy.topic}
kafka.oversea.spider.tw.report.buy.topic=${kafka.oversea.spider.tw.report.buy.topic}