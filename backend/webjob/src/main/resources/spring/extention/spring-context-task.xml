<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd"
	default-lazy-init="true">
	<!-- <context:component-scan
		base-package="com.backend.admin.task" />
	<task:executor id="executor" pool-size="10" />-->
	<task:scheduler id="scheduler" pool-size="30" />

	<!-- <task:annotation-driven scheduler="scheduler"
		executor="executor" proxy-target-class="true" /> -->
	<bean id="sampleJob" class="com.backend.admin.task.SampleJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

<!--	<bean id="synCompPluginAccessLogDataJob" class="com.backend.admin.task.SynCompPluginAccessLogDataJob" init-method="initBean" destroy-method="destroyBean">-->
<!--		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>-->
<!--	</bean>-->

<!--	<bean id="synCompCorporatesDataJob" class="com.backend.admin.task.SynCompCorporatesDataJob" init-method="initBean" destroy-method="destroyBean">-->
<!--		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>-->
<!--	</bean>-->
	<bean id="syncAndCheckReportJob" class="com.backend.admin.task.SyncAndCheckReportJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="pushDingMsgJob" class="com.backend.admin.task.PushDingMsgJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="outboundLimitRemindJob" class="com.backend.admin.task.OutboundLimitRemindJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="syncOutboundListJob" class="com.backend.admin.task.SyncOutboundListJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="accountLimitRemindJob" class="com.backend.admin.task.AccountLimitRemindJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="huaWeiYunLogMonitorJob" class="com.backend.admin.task.HuaWeiYunLogMonitorJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="trialApplyRemindJob" class="com.backend.admin.task.TrialApplyRemindJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="webConverterHealthCheckJob" class="com.backend.admin.task.WebConverterHealthCheckJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="accountExpireRemindJob" class="com.backend.admin.task.AccountExpireRemindJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="sendIncrCorpDataToCustomerJob" class="com.backend.admin.task.SendIncrCorpDataToCustomerJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="contractDeliveryLifecycleJob" class="com.backend.admin.task.ContractDeliveryLifecycleJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="contractDeliveryTransactionBillDailyJob" class="com.backend.admin.task.ContractDeliveryTransactionBillDailyJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="contractDeliveryTransactionBillMonthlyJob" class="com.backend.admin.task.ContractDeliveryTransactionBillMonthlyJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="contractDeliveryFinanceEmailJob" class="com.backend.admin.task.ContractDeliveryFinanceEmailJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>


	<bean id="onlinePayOrderLifecycleJob" class="com.backend.admin.task.OnlinePayOrderLifecycleJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="reconciliationJob" class="com.backend.admin.task.ReconciliationJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="companyStatementMonthlyJob" class="com.backend.admin.task.CompanyStatementMonthlyJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="commDelayedTaskLoopExecuteJob" class="com.backend.admin.task.CommDelayedTaskLoopExecuteJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="companyStatementMonthlyEmailJob" class="com.backend.admin.task.CompanyStatementMonthlyEmailJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="pushConfirmAmountContractJob" class="com.backend.admin.task.PushConfirmAmountContractJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="kzzPaymentStatusMonitorJob" class="com.backend.admin.task.KzzPaymentStatusMonitorJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="pushChangeEventsNotifyJob" class="com.backend.admin.task.PushChangeEventsNotifyJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="benefitLifecycleJob" class="com.backend.admin.task.BenefitLifecycleJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>
	<bean id="quotaMonthlySnapshotJob" class="com.backend.admin.task.QuotaMonthlySnapshotJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<bean id="pushNotActivateUserNotifyJob" class="com.backend.admin.task.PushNotActivateUserNotifyJob" init-method="initBean" destroy-method="destroyBean">
		<property name="sysScheduleTaskService" ref="sysScheduleTaskService"/>
	</bean>

	<task:scheduled-tasks scheduler="scheduler">
		<!--<task:scheduled ref="sampleJob" method="execute" cron="0/5 * * * * ?"/>
		<task:scheduled ref="sampleJob" method="execute" cron="0 0 0 * * ?"/>-->
		<!-- 监控、尽调、页面嵌入消费同步数据 -->
<!--		<task:scheduled ref="synCompPluginAccessLogDataJob" method="execute" cron="0 30 * * * ?"/>&lt;!&ndash; 每小时执行，半时触发 &ndash;&gt;-->
<!--		<task:scheduled ref="synCompCorporatesDataJob" method="execute" cron="0 0/30 0-23 * * ?"/>&lt;!&ndash; 每半小时触发 &ndash;&gt;-->

		<task:scheduled ref="syncAndCheckReportJob" method="execute" cron="0 0 8,9,10,11,12,13,14,15,16,17,18,19,20,21,22 * * ?"/><!-- 8,10,12,14,16,18,20,22点触发 -->
		<task:scheduled ref="pushDingMsgJob" method="execute" cron="0 * * * * ?"/><!--暂定每1分钟触发 -->
		<task:scheduled ref="outboundLimitRemindJob" method="execute" cron="0 0 9 * * *"/><!--暂定每天9点触发 -->
		<task:scheduled ref="syncOutboundListJob" method="execute" cron="0 0 20 * * ?"/><!--暂定每天晚上20点触发 -->
		<task:scheduled ref="accountLimitRemindJob" method="execute" cron="0 0 9 ? * MON,WED,FRI"/><!--暂定每周一、三、五9点触发 -->
		<task:scheduled ref="huaWeiYunLogMonitorJob" method="execute" cron="0 0 0,6,12,18 * * ? "/><!--6,12,18,24时触发 -->
		<task:scheduled ref="trialApplyRemindJob" method="execute" cron="0 0 10 * * ?"/><!--每天10点触发 -->
		<task:scheduled ref="webConverterHealthCheckJob" method="execute" cron="0 0/10 * * * ?"/><!--暂定每10分钟触发 -->
		<task:scheduled ref="accountExpireRemindJob" method="execute" cron="0 30 8 * * ?"/><!--暂定每天8点30触发 -->
		<task:scheduled ref="sendIncrCorpDataToCustomerJob" method="execute" cron="0 0 16 * * ?"/><!--暂定每天16点触发 -->
		<task:scheduled ref="contractDeliveryLifecycleJob" method="execute" cron="0 8 0 * * *"/><!--暂定每天00:08点触发 -->
		<task:scheduled ref="contractDeliveryTransactionBillDailyJob" method="execute" cron="0 40 0 * * *"/><!--暂定每天00:40点触发 -->
		<task:scheduled ref="contractDeliveryTransactionBillMonthlyJob" method="execute" cron="0 0 3 * * *"/><!--暂定每天03:00点触发 -->
		<task:scheduled ref="contractDeliveryFinanceEmailJob" method="execute" cron="0 0 9 2 * *"/><!--暂定每月2号09:00点触发 -->
		<task:scheduled ref="onlinePayOrderLifecycleJob" method="execute" cron="0 0/30 * * * ? "/><!--暂定每1分钟触发 -->
        <task:scheduled ref="reconciliationJob" method="execute" cron="0 0 8 * * ? "/><!--暂定每天08:00点触发 -->
		<task:scheduled ref="companyStatementMonthlyJob" method="execute" cron="0 0 6 1 * ?"/><!--暂定每月1号06:00点触发 -->
		<task:scheduled ref="commDelayedTaskLoopExecuteJob" method="execute" fixed-delay="60000"/><!--暂定每1分钟触发 -->
		<task:scheduled ref="companyStatementMonthlyEmailJob" method="execute" cron="0 0 10 1 * ?"/><!--暂定每月1号10:00点触发 -->
		<task:scheduled ref="pushConfirmAmountContractJob" method="execute" cron="0 30 12 1-10 * ?"/><!--暂定每月1-10号12:30触发 -->
		<task:scheduled ref="kzzPaymentStatusMonitorJob" method="execute" cron="0 30 8,12,18,22 * * ?"/><!--暂定每天每天上午8.30，中午12.30，晚上18.30，晚上22.30触发 -->
		<task:scheduled ref="pushChangeEventsNotifyJob" method="execute" cron="0 0 0 * * ?"/><!--暂定每天凌晨触发-->
		<task:scheduled ref="benefitLifecycleJob" method="execute" cron="0 16 0 * * *"/><!--暂定每天00:16点触发 -->
		<task:scheduled ref="quotaMonthlySnapshotJob" method="execute" cron=" 0 3 0 1 * ?"/><!--每月1号00:03触发 -->
		<task:scheduled ref="pushNotActivateUserNotifyJob" method="execute" cron="0 0 0 ? * MON"/><!--暂定每周一凌晨触发-->
	</task:scheduled-tasks>
</beans>
