package com.backend.iservice.controller.internal;

import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.model.json.JsonSimpleResult;
import com.backend.common.modules.dd.model.EnLabelTOResult;
import com.backend.common.modules.industry.entity.TblGlobalIndustry;
import com.backend.common.modules.industry.service.CommTblGlobalIndustryService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.industry.model.TblGlobalIndustryBaseTO;
import com.backend.common.modules.report.form.ReportOrderInitForm;
import com.backend.common.modules.report.model.CorpInfoTO;
import com.backend.common.modules.report.model.CorpMerchantShopTO;
import com.backend.common.modules.report.model.CorpSimpleInfoTO;
import com.backend.common.modules.report.model.FinancialData;
import com.backend.common.modules.report.model.OrderData4LoopResult;
import com.backend.common.modules.report.model.OrderInitInfoTO;
import com.backend.common.modules.report.model.sanction.ScanCommonDetailTO;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.MerchantShopBusinessService;
import com.backend.common.modules.report.service.ScanBusinessService;
import com.backend.common.modules.shell.service.ShellLabelService;
import com.backend.common.openapi.form.ProCorpSearchForm;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.ECILocalInterface;
import com.backend.common.yunjuapi.form.CourtCaseForm;
import com.backend.common.yunjuapi.form.ShellCompanySearchForm;
import com.backend.common.yunjuapi.model.*;
import com.backend.common.yunjuapi.model.merchant.*;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.web.BaseController;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * add for v1.7.2 KNZT-3104
 */
@Controller
@RequestMapping(value = "/internal/corp")
public class InternalCommCorpController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(InternalCommCorpController.class);

    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private ScanBusinessService scanBusinessService;
    @Autowired
    private MerchantShopBusinessService merchantShopBusinessService;
    @Autowired
    private CommTblGlobalIndustryService commTblGlobalIndustryService;

    //    @ApiOperation(value = "初始化报告订单", httpMethod = "POST", notes = "初始化报告订单")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "corpKeyNo", value = "企业KeyNo - reportGroup为CORP_G", paramType = "body")})
    @RequestMapping(value = "/initOrder", method = RequestMethod.POST)
    @ResponseBody
    public JsonSimpleResult<OrderInitInfoTO> initOrder(@RequestBody ReportOrderInitForm form) {
        JsonSimpleResult<OrderInitInfoTO> result = new JsonSimpleResult<OrderInitInfoTO>();
        OrderInitInfoTO infoTO = null;
        try {
            MsgExceptionUtils.failBuildAndLogError(StringUtils.isBlank(form.getOrderNo()), logger, "orderNo is required");
            TblCompReportOrder order = commTblCompReportOrderService.getByOrderNo(form.getOrderNo());
            if (order != null) {
                infoTO = commTblCompReportOrderService.getInitOrderInfoNotNeedOrder(form, order);
                if (infoTO != null) {
                    List<CorpPartnerTO> pubPartnerList = Optional.of(infoTO).map(OrderInitInfoTO::getCorpInfo).map(CorpInfoTO::getBuyBasicDetailTO).map(CorpBasicDetail4UnitTO::getPubPartnerList).orElse(null);
                    List<CorpPartnerTO> partnerList = Optional.of(infoTO).map(OrderInitInfoTO::getCorpInfo).map(CorpInfoTO::getBuyBasicDetailTO).map(CorpBasicDetail4UnitTO::getPartnerList).orElse(null);
                    boolean isBank = UserUtils.isBank(order.getUserId());
                    CompanyDetailsInterface.removeSensitiveInfo(pubPartnerList, isBank);
                    CompanyDetailsInterface.removeSensitiveInfo(partnerList, isBank);
                }
            }
            result.setStatus(Constants.Result.SUCCESS_STR);
            result.setResult(infoTO);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
        }
        return result;
    }

    //    @ApiOperation(value = "股东信息", httpMethod = "POST", notes = "工商股东，有分页")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "type", value = "数据类型，可选值 Partners, IpoPartners，默认为：Partners", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "getPartnerList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpPartnerTO> getPartnerList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpPartnerTO> result = new JsonResultList<>();
        if (StringUtils.isBlank(form.getOrderNo())) {
            logger.error("orderNo is required");
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("orderNo is required");
            return result;
        }
        TblCompReportOrder order = commTblCompReportOrderService.getByOrderNo(form.getOrderNo());
        if (order == null) {
            return result;
        }
        if (StringUtils.isBlank(form.getType())) {
            form.setType("Partners");
        }
        CompanyPartnerResult resultData = ECILocalInterface.getPartnerWithGroup(form.getKeyNo(), form.getType(), form.getPageIndex(), form.getPageSize(), false);
        if (resultData != null) {
            List<CorpPartnerTO> partnerTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resultData.getResult())) {
                // updated for v2.0.2 chenbl KNZT-5310
//                List<String> corpKeyNoList = new ArrayList<>();
                for (CompanyParnter companyParnter : resultData.getResult()) {
                    CorpPartnerTO partnerTO = ECILocalInterface.convertPartner2GlobalPartner(companyParnter, form.getType());
                    partnerTOList.add(partnerTO);
                }
                CompanyDetailsInterface.populateInfoByAdvanceSearch(partnerTOList, null, false);
                CompanyDetailsInterface.removeSensitiveInfo(partnerTOList, UserUtils.isBank(order.getUserId()));
                /*
                if (CollectionUtils.isNotEmpty(corpKeyNoList)) {
                    List<AdvanceSearchTO> advanceSearchTOS = GlobalCompanyDetailsInterface.batchGetCorpEnglishNameByKeyNoList(corpKeyNoList, false);
                    Map<String, String> keyNoEnNameMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(advanceSearchTOS)) {
                        for (AdvanceSearchTO advanceSearchTO : advanceSearchTOS) {
                            keyNoEnNameMap.put(advanceSearchTO.getKeyNo(), advanceSearchTO.getNameEn());
                        }
                        CompanyDetailsInterface.setEnglishName4PartnerByInterface(partnerTOList, keyNoEnNameMap);
                    }
                }*/
            }
            result.setResultList(partnerTOList);
            result.setTotalCount(resultData.getPaging() != null ? resultData.getPaging().getTotalRecords() : 0);
        }
        result.setStatus(Constants.Result.SUCCESS_STR);

        return result;
    }

    //    @ApiOperation(value = "获取企业主要人员", httpMethod = "POST", notes = "获取企业主要人员 工商主要人员和自主公示的主要人员")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "type", value = "类型 Employees-工商主要人员 * IpoEmployees-自主公示主要人员", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getCorpEmployeeList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpEmployeeTO> getCorpEmployeeList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpEmployeeTO> result = new JsonResultList<>();
        CorpEmployeeResult employeeResult = ECILocalInterface.getEmployeeList(form.getKeyNo(), form.getType(), form.getPageIndex(), form.getPageSize(), false);
        if (employeeResult != null && employeeResult.getResult() != null) {
            CompanyDetailsInterface.populateEmployee(employeeResult.getResult());
            result.setResultList(employeeResult.getResult());
            result.setTotalCount(employeeResult.getPaging() != null ? employeeResult.getPaging().getTotalRecords() : 0);
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    //    @ApiOperation(value = "获取分支机构列表", httpMethod = "POST", notes = "获取企业分支机构列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getBranchList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CompanyBranch> getBranchList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CompanyBranch> result = new JsonResultList<>();

        CompanyBranchResult branchResult = CompanyDetailsInterface.getBranchList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (branchResult != null && branchResult.getResultList() != null && branchResult.getResultList().size() > 0) {
            result.setResultList(branchResult.getResultList());
            result.setTotalCount(branchResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    //    @ApiOperation(value = "获取UBO和实际控制人", httpMethod = "POST", notes = "展示获取UBO和实际控制人，不分页")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getCorpSimpleInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<CorpSimpleInfoTO> getCorpSimpleInfo(@RequestBody ProCorpSearchForm form) {
        JsonResult<CorpSimpleInfoTO> result = new JsonResult<>();
        CorpSimpleInfoTO corpSimpleInfoTO = new CorpSimpleInfoTO();
        try {
            StockResult4UnitTO corpUBO4Unit = CompanyDetailsInterface.getCorpUBO4Unit(form.getKeyNo(), false);
            corpSimpleInfoTO.setUboDetailTO(corpUBO4Unit);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
            return result;
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        result.setResult(corpSimpleInfoTO);
        return result;
    }

//    @ApiOperation(value = "获取对外投资企业", httpMethod = "POST", notes = "获取对外投资企业")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/listCompanyInvestment", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<InvestmentCompTO> listCompanyInvestment(@RequestBody InvestmentCompForm form) {
        JsonResultList<InvestmentCompTO> resp = new JsonResultList<>();
        ApiCompanyInvestmentResult result = CompanyDetailsInterface.listCompanyInvestment(form.getKeyNo(), form.getFundedRatioMin(), form.getFundedRatioMax(), form.getPageIndex(), form.getPageSize(), false);
        resp.setTotalCount(Optional.ofNullable(result).map(ApiCompanyInvestmentResult::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(result).map(ApiCompanyInvestmentResult::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

//    @ApiOperation(value = "获取对外间接投资企业", httpMethod = "POST", notes = "获取对外间接投资企业")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/listCompanyIndirectInvestment", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<IndirectInvestmentCompTO> listCompanyIndirectInvestment(@RequestBody IndirectInvestmentCompForm form) {
        JsonResultList<IndirectInvestmentCompTO> resp = new JsonResultList<>();
        ApiCompanyIndirectInvestmentResult result = CompanyDetailsInterface.listCompanyIndirectInvestment(form.getKeyNo(), form.getStockPercentMin(), form.getStockPercentMax(), form.getPageIndex(), form.getPageSize(), false);
        resp.setTotalCount(Optional.ofNullable(result).map(ApiCompanyIndirectInvestmentResult::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(result).map(ApiCompanyIndirectInvestmentResult::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.0 KNZT-3596
//    @ApiOperation(value = "获取工商变更信息", httpMethod = "POST", notes = "获取工商变更信息, 历史注册资本, 历史地址")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getCoyHistoryInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<HistoryInfoTO> getCoyHistoryInfo(@RequestBody ProCorpSearchForm form) {
        JsonResult<HistoryInfoTO> result = new JsonResult<>();
        HistoryInfoTO historyInfoTO = CompanyDetailsInterface.getCoyHistoryInfo(form.getKeyNo(), false);
        result.setResult(historyInfoTO);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

//    @ApiOperation(value = "获取历史法定代表人", httpMethod = "POST", notes = "获取历史法定代表人")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getHistoryOperList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistOper> getHistoryOperList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HistOper> result = new JsonResultList<>();
        HistOperResult histOperResult = CompanyDetailsInterface.getHistoryOperList(form.getKeyNo(), false);
        if (histOperResult != null && histOperResult.getResultList() != null && histOperResult.getResultList().size() > 0) {
            result.setResultList(histOperResult.getResultList());
            result.setTotalCount(histOperResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

//    @ApiOperation(value = "获取历史主要人员", httpMethod = "POST", notes = "获取历史主要人员")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getHistoryEmployeeList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistEmployee> getHistoryEmployeeList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HistEmployee> result = new JsonResultList<>();
        HistEmployeeResult histEmployeeResult = CompanyDetailsInterface.getHistoryEmployeeList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (histEmployeeResult != null && histEmployeeResult.getResultList() != null && histEmployeeResult.getResultList().size() > 0) {
            result.setResultList(histEmployeeResult.getResultList());
            result.setTotalCount(histEmployeeResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v2.0.5 chenbl KNZT-5562
    //    @ApiOperation(value = "获取历史股东", httpMethod = "POST", notes = "获取历史股东")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getHistoryPartnerList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistShareHolder> getHistoryPartnerList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HistShareHolder> result = new JsonResultList<>();
        HistShareHolderResult histShareHolderResult = CompanyDetailsInterface.getHistoryPartnerList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (histShareHolderResult != null && histShareHolderResult.getResultList() != null && histShareHolderResult.getResultList().size() > 0) {
            result.setResultList(histShareHolderResult.getResultList());
            result.setTotalCount(histShareHolderResult.getTotalCount());
            result.setGroupItems(histShareHolderResult.getGroupItems());//added for V1.3.1 KNZT-1599 历史股东认缴出资额字段名，应根据内容做判断
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

//    @ApiOperation(value = "获取对外历史投资企业", httpMethod = "POST", notes = "获取对外历史投资企业")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "listCompanyHistoryInvestment", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistoryInvestmentCompTO> listCompanyHistoryInvestment(@RequestBody HistoryInvestmentCompForm form) {
        JsonResultList<HistoryInvestmentCompTO> resp = new JsonResultList<>();
        ApiCompanyHistoryInvestmentResult result = CompanyDetailsInterface.listCompanyHistoryInvestment(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        resp.setTotalCount(Optional.ofNullable(result).map(ApiCompanyHistoryInvestmentResult::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(result).map(ApiCompanyHistoryInvestmentResult::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.9.4 KNZT-4373
     */
//    @ApiOperation(value = "获取子公司（根据投资企业）", httpMethod = "POST", notes = "获取子公司（根据投资企业）")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "listSubsidiaries", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<AllInvestmentTO> listSubsidiaries(@RequestBody AllInvestmentForm form) {
        JsonResultList<AllInvestmentTO> resp = new JsonResultList<>();
        JsonResultList<AllInvestmentTO> resultList = CompanyDetailsInterface.listSubsidiaries(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        resp.setTotalCount(Optional.ofNullable(resultList).map(JsonResultList::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(resultList).map(JsonResultList::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.9.4 KNZT-4373
     */
//    @ApiOperation(value = "获取附属公司（根据投资企业）", httpMethod = "POST", notes = "获取附属公司（根据投资企业）")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "listAffiliates", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<AllInvestmentTO> listAffiliates(@RequestBody AllInvestmentForm form) {
        JsonResultList<AllInvestmentTO> resp = new JsonResultList<>();
        JsonResultList<AllInvestmentTO> resultList = CompanyDetailsInterface.listAffiliates(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        resp.setTotalCount(Optional.ofNullable(resultList).map(JsonResultList::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(resultList).map(JsonResultList::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

//    @ApiOperation(value = "获取股东镜像", httpMethod = "POST", notes = "获取股东镜像")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getEquityShareChangesByDate", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<EquityShareChangesByDateResultTO> getEquityShareChangesByDate(@RequestBody ProCorpSearchForm form) {
        // updated for v2.0.6 chenbl KNZT-5555
        return JsonResult.buildSuccess(CorpGatewayInterface.getEquityShareChangesByDate(form.getKeyNo()));
    }

//    @ApiOperation(value = "获取企业失信被执行人信息", httpMethod = "POST", notes = "获取企业失信被执行人信息")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "isValid", value = "是否历史 0-历史 1-当前", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getShiXinList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CoutShiXin> getShiXinList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CoutShiXin> result = new JsonResultList<>();
        CoutShiXinResult coutShiXinResult = CompanyDetailsInterface.getShiXinList(form.getKeyNo(), form.getIsValid(), form.getPageIndex(), form.getPageSize(), false);
        if (coutShiXinResult != null && coutShiXinResult.getResultList() != null && coutShiXinResult.getResultList().size() > 0) {
            result.setResultList(coutShiXinResult.getResultList());
            result.setTotalCount(coutShiXinResult.getTotalCount());
            // added for KNZT-1040
            result.setGroupItems(coutShiXinResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

//    @ApiOperation(value = "获取企业被执行人信息", httpMethod = "POST", notes = "获取企业被执行人信息")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "isValid", value = "是否历史 0-历史 1-当前", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getZhiXingList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CoutZhiXing> getZhiXingList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CoutZhiXing> result = new JsonResultList<>();
        CoutZhiXingResult coutZhiXingResult = CompanyDetailsInterface.getZhiXingList(form.getKeyNo(), form.getIsValid(), form.getPageIndex(), form.getPageSize(), false);
        if (coutZhiXingResult != null && coutZhiXingResult.getResultList() != null && coutZhiXingResult.getResultList().size() > 0) {
            result.setResultList(coutZhiXingResult.getResultList());
            result.setTotalCount(coutZhiXingResult.getTotalCount());
            // added for KNZT-1040
            result.setGroupItems(coutZhiXingResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

//    @ApiOperation(value = "获取企业限制高消费信息", httpMethod = "POST", notes = "获取企业限制高消费信息")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "isValid", value = "是否历史 0-历史 1-当前", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getSumptuaryList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<PersonSumptuary> getSumptuaryList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<PersonSumptuary> result = new JsonResultList<>();
        PersonSumptuaryResult personSumptuaryResult = CompanyDetailsInterface.getSumptuaryList(form.getKeyNo(), form.getIsValid(), form.getPageIndex(), form.getPageSize(), false);
        if (personSumptuaryResult != null && personSumptuaryResult.getResultList() != null && personSumptuaryResult.getResultList().size() > 0) {
            result.setResultList(personSumptuaryResult.getResultList());
            result.setTotalCount(personSumptuaryResult.getTotalCount());
            // added for KNZT-1040
            result.setGroupItems(personSumptuaryResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

//    @ApiOperation(value = "获取限制出境信息", httpMethod = "POST", notes = "获取企业限制出境信息")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getLimitExitList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<LimitExit> getLimitExitList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<LimitExit> result = new JsonResultList<>();
        LimitExitResult limitExitResult = CompanyDetailsInterface.getLimitExitList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (limitExitResult != null && limitExitResult.getResultList() != null && limitExitResult.getResultList().size() > 0) {
            result.setResultList(limitExitResult.getResultList());
            result.setTotalCount(limitExitResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v1.8.1 KNZT-3610
//    @ApiOperation(value = "获取行政处罚", httpMethod = "POST", notes = "获取行政处罚")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getRiskPenaltySumList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<AdministrativePunishmentTO> getRiskPenaltySumList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<AdministrativePunishmentTO> resp = new JsonResultList<>();
        JsonResultList<AdministrativePunishmentTO> resultPage = CompanyDetailsInterface.getRiskPenaltySumList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (resultPage != null) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
            resp.setGroupItems(resultPage.getGroupItems());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.1 KNZT-3610
//    @ApiOperation(value = "获取经营异常", httpMethod = "POST", notes = "获取经营异常")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getExceptionList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpExceptionInfoTO> getExceptionList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpExceptionInfoTO> resp = new JsonResultList<>();
        JsonResultList<CorpExceptionInfoTO> resultPage = CompanyDetailsInterface.getExceptionList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.1 KNZT-3610
//    @ApiOperation(value = "获取严重违法", httpMethod = "POST", notes = "获取严重违法")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getSeriousViolationList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpSeriousViolationTO> getSeriousViolationList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpSeriousViolationTO> resp = new JsonResultList<>();
        JsonResultList<CorpSeriousViolationTO> resultPage = CompanyDetailsInterface.getSeriousViolationList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.1 KNZT-3610
//    @ApiOperation(value = "获取股权出质", httpMethod = "POST", notes = "获取股权出质")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getPledgeList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpPledgeV2TO> getPledgeList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpPledgeV2TO> resp = new JsonResultList<>();
        JsonResultList<CorpPledgeV2TO> resultPage = CompanyDetailsInterface.getPledgeList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.9.0 KNZT-4145
//    @ApiOperation(value = "获取司法案件", httpMethod = "POST", notes = "获取司法案件")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "orderId", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body"),
//            @ApiImplicitParam(name = "reason", value = "案由", paramType = "body"),
//            @ApiImplicitParam(name = "caseTypeMain", value = "案件类型", paramType = "body"),
//            @ApiImplicitParam(name = "roleType", value = "身份", paramType = "body"),
//            @ApiImplicitParam(name = "year", value = "年份", paramType = "body")})
    @RequestMapping(value = "/getCourtCaseList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CourtCaseTO> getCourtCaseList(@RequestBody CourtCaseForm form) {
        JsonResultList<CourtCaseTO> resp = new JsonResultList<>();
        JsonResultList<CourtCaseTO> resultPage = CompanyDetailsInterface.getCourtCaseList(form, false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
            resp.setGroupItems(resultPage.getGroupItems());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v2.0.2 chenbl KNZT-5271
//    @ApiOperation(value = "获取财税数据", httpMethod = "POST", notes = "获取财税数据")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getFinancialTaxDetail", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<FinancialData> getFinancialTaxDetail(@RequestBody ProCorpSearchForm form) {
        JsonResult<FinancialData> resp = new JsonResult<>();
        FinancialData financialData = ECILocalInterface.getFinancialTaxDetail(form.getKeyNo(), false);
        resp.setResult(financialData);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v2.0.5 chenbl KNZT-5562
//    @ApiOperation(value = "获取空壳概览", httpMethod = "POST", notes = "获取空壳概览")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getShellCompanyLabel", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<EnLabelTOResult> getShellCompanyLabel(@RequestBody ProCorpSearchForm form) {
        JsonResult<EnLabelTOResult> resp = new JsonResult<>();
        EnLabelTOResult shellCompanyLabel = ShellLabelService.getShellCompanyLabel(form.getKeyNo(), false);
        resp.setResult(shellCompanyLabel);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v2.0.5 chenbl KNZT-5562
//    @ApiOperation(value = "获取相同董监高列表", httpMethod = "POST", notes = "获取相同董监高列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "similarExecutiveList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellRelateCompanyDetailTO> getSimilarExecutiveList(@RequestBody ShellCompanySearchForm form) {
        JsonResultList<ShellRelateCompanyDetailTO> result = new JsonResultList<>();
        ShellRelateCompanyDetailResult detailResult = CompanyDetailsInterface.getSimilarExecutiveList(form, false);
        if (Objects.nonNull(detailResult)) {
            result.setResultList(detailResult.getResultList());
            result.setTotalCount(detailResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v2.0.5 chenbl KNZT-5562
//    @ApiOperation(value = "获取相同董监高列表", httpMethod = "POST", notes = "获取相同董监高列表")
    @RequestMapping(value = "sameCorporateList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellRelateCompanyDetailTO> getSameCorporateList(@RequestBody ShellCompanySearchForm form) {
        JsonResultList<ShellRelateCompanyDetailTO> result = new JsonResultList<>();
        ShellRelateCompanyDetailResult detailResult = CompanyDetailsInterface.getSimilarExecutiveList(form, false);
        if (Objects.nonNull(detailResult)) {
            result.setResultList(detailResult.getResultList());
            result.setTotalCount(detailResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v2.0.5 chenbl KNZT-5562
//    @ApiOperation(value = "同电话/同地址企业列表", httpMethod = "POST", notes = "同电话/同地址企业列表")
    @RequestMapping(value = "sameTelAddressListV2", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellScCompanyRepeatAddressTO> getSameTelAddressListV2(@RequestBody ShellCompanySearchForm form) {
        JsonResultList<ShellScCompanyRepeatAddressTO> result = new JsonResultList<>();
        ShellScCompanyRepeatAddressResult sameTelAddressResult = CompanyDetailsInterface.getSameTelAddressList(form, false);
        if (Objects.nonNull(sameTelAddressResult)) {
            result.setResultList(sameTelAddressResult.getResultList());
            result.setTotalCount(sameTelAddressResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v2.0.5 chenbl KNZT-5562
//    @ApiOperation(value = "经营异常列表-无法联系", httpMethod = "POST", notes = "经营异常列表-无法联系")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "noContactException", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellCorpExceptionTO> getNoContactExceptionByReason(@RequestBody ProCorpSearchForm form) {
        JsonResultList<ShellCorpExceptionTO> result = new JsonResultList<>();
        List<ShellCorpExceptionTO> resultList = CompanyDetailsInterface.getNoContactExceptionWithReasonByKeyNo(form.getKeyNo(), false);
        result.setResultList(resultList);
        result.setTotalCount(CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v2.0.5 chenbl KNZT-5562
//    @ApiOperation(value = "经营异常列表-未公示年报", httpMethod = "POST", notes = "经营异常列表-未公示年报")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "annualReportNotPublish", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellScCompanyWnjTO> getAnnualReportNotPublishList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<ShellScCompanyWnjTO> result = new JsonResultList<>();
        List<ShellScCompanyWnjTO> resultList = CompanyDetailsInterface.getCompanyWnjListByKeyNo(form.getKeyNo(), false);
        result.setResultList(resultList);
        result.setTotalCount(CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v2.0.6 chenbl KNZT-5506
//    @ApiOperation(value = "获取API订单数据", httpMethod = "POST", notes = "获取API订单数据")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "orderId", value = "orderId", paramType = "body")})
    @RequestMapping(value = "getApiOrderDataByOrderNo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<OrderData4LoopResult> getApiOrderDataByOrderNo(@RequestBody ReportOrderInitForm form) {
        try {
            return JsonResult.buildSuccess(commTblCompReportOrderService.getApiOrderDataByOrderNo(form.getOrderNo()));
        } catch (MessageException e) {
            return JsonResult.buildFail(I18NUtil.getMessage("err.access"));
        }
    }

    // added for v2.0.9 chenbl KNZT-5362
    //    @ApiOperation(value = "获取扫描订单详情数据(通过轮询)", httpMethod = "POST", notes = "获取扫描订单详情数据(通过轮询)")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "orderNo", value = "订单号", paramType = "body")})
    @RequestMapping(value = "/getScanDetailByLoop", method = RequestMethod.POST)
    @ResponseBody
    public JsonSimpleResult<ScanCommonDetailTO> getScanDetailByLoop(@RequestBody ReportOrderInitForm form) {
        JsonSimpleResult<ScanCommonDetailTO> result = new JsonSimpleResult<>();
        ScanCommonDetailTO scanOrderDetail = scanBusinessService.getScanOrderDetailInternal(form.getOrderNo());
        result.setStatus(Constants.Result.SUCCESS_STR);
        result.setResult(scanOrderDetail);
        return result;
    }

    // added for v2.1.1 lvcy KNZT-5973
    @RequestMapping(value = "getMerchantShop", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<CorpMerchantShopTO> getMerchantShop(@RequestBody ProCorpSearchForm form) {
        JsonResult<CorpMerchantShopTO> resp = new JsonResult<>();
        TblCompReportOrder order = commTblCompReportOrderService.getByOrderNo(form.getOrderNo());
        CorpMerchantShopTO merchantShop = merchantShopBusinessService.getMerchantShop(order);
        resp.setResult(merchantShop);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v2.1.1 chenbl KNZT-6038
    // 企查分
//    @ApiOperation(value = "企查分", httpMethod = "POST", notes = "企查分")
    @RequestMapping(value = "getCreditRate", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @ResponseBody
    public JsonResult<GetCreditRateTO> getCreditRate(@RequestBody ProCorpSearchForm form) {
        JsonResult<GetCreditRateTO> result = null;
        GetCreditRateTO creditRate = CompanyDetailsInterface.getCreditRate(form.getKeyNo(), false);
        if (creditRate != null) {
            GetCreditRateTO.IndustryAnalysis industryAnalysis = creditRate.getIndustryAnalysis();
            if (industryAnalysis != null && StringUtils.isNotBlank(industryAnalysis.getIndustryDesc())) {
                // updated for v2.1.3 fengsw KNZT-6226 获取二级行业英文名 方法切换到 CommTblGlobalIndustryService
                TblGlobalIndustry tblGlobalIndustry = commTblGlobalIndustryService.getCnInd2NameEnByName(industryAnalysis.getIndustryDesc());
                if (tblGlobalIndustry != null) {
                    industryAnalysis.setIndustryDescEn(tblGlobalIndustry.getInd2NameEn());
                    industryAnalysis.setIndustryCode(tblGlobalIndustry.getInd1Code() + tblGlobalIndustry.getInd2Code());
                }
            }
        }
        result = JsonResult.buildSuccess(creditRate);
        return result;
    }

    // 历史企查分
//    @ApiOperation(value = "历史企查分", httpMethod = "POST", notes = "历史企查分")
    @RequestMapping(value = "getCreditRateTrend", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @ResponseBody
    public JsonResult<GetCreditRateTrendTO> getCreditRateTrend(@RequestBody ProCorpSearchForm form) {
        JsonResult<GetCreditRateTrendTO> result = null;
        GetCreditRateTrendTO creditRateTrend = CompanyDetailsInterface.getCreditRateTrend(form.getKeyNo(), false);
        result = JsonResult.buildSuccess(creditRateTrend);
        return result;
    }

    // 发票抬头
//    @ApiOperation(value = "发票抬头", httpMethod = "POST", notes = "发票抬头")
    @RequestMapping(value = "getInvoiceDetail", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @ResponseBody
    public JsonResult<GetInvoiceDetailTO> getInvoiceDetail(@RequestBody ProCorpSearchForm form) {
        JsonResult<GetInvoiceDetailTO> result = null;
        GetInvoiceDetailTO invoiceDetail = CompanyDetailsInterface.getInvoiceDetail(form.getKeyNo(), false);
        result = JsonResult.buildSuccess(invoiceDetail);
        return result;
    }

    // 税务基本信息
//    @ApiOperation(value = "税务基本信息", httpMethod = "POST", notes = "税务基本信息")
    @RequestMapping(value = "listGeneralTaxPayer", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GeneralTaxPayerTO> listGeneralTaxPayer(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GeneralTaxPayerTO> result = null;
        JsonResultList<GeneralTaxPayerTO> apiResult = CompanyDetailsInterface.listGeneralTaxPayer(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 税务非正常户核查
//    @ApiOperation(value = "税务非正常户核查", httpMethod = "POST", notes = "税务非正常户核查")
    @RequestMapping(value = "getTaxUnnormals", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GetTaxUnnormalsTO> getTaxUnnormals(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GetTaxUnnormalsTO> result = null;
        JsonResultList<GetTaxUnnormalsTO> apiResult = CompanyDetailsInterface.getTaxUnnormals(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 税收违法
//    @ApiOperation(value = "税收违法", httpMethod = "POST", notes = "税收违法")
    @RequestMapping(value = "getIllegalList", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GetIllegalListTO> getIllegalList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GetIllegalListTO> result = null;
        JsonResultList<GetIllegalListTO> apiResult = CompanyDetailsInterface.getIllegalList(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 欠税公告
//    @ApiOperation(value = "欠税公告", httpMethod = "POST", notes = "欠税公告")
    @RequestMapping(value = "getListOfOweNoticeNew", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GetListOfOweNoticeNewTO> getListOfOweNoticeNew(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GetListOfOweNoticeNewTO> result = null;
        JsonResultList<GetListOfOweNoticeNewTO> apiResult = CompanyDetailsInterface.getListOfOweNoticeNew(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
            result.setGroupItems(apiResult.getGroupItems());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 进出口信用
//    @ApiOperation(value = "进出口信用", httpMethod = "POST", notes = "进出口信用")
    @RequestMapping(value = "listIE", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<IETO> listIE(@RequestBody ProCorpSearchForm form) {
        JsonResultList<IETO> result = null;
        JsonResultList<IETO> apiResult = CompanyDetailsInterface.listIE(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 资质证书
//    @ApiOperation(value = "资质证书", httpMethod = "POST", notes = "资质证书")
    @RequestMapping(value = "getCertificationList", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<CertificationListTO> getCertificationList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CertificationListTO> result = null;
        JsonResultList<CertificationListTO> apiResult = CorpGatewayInterface.getCertificationList(form.getKeyNo(), form.getPageIndex(), form.getPageSize());
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 荣誉
//    @ApiOperation(value = "荣誉", httpMethod = "POST", notes = "荣誉")
    @RequestMapping(value = "listHonorCertificationV2", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<HonorCertificationV2TO> listHonorCertificationV2(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HonorCertificationV2TO> result = null;
        JsonResultList<HonorCertificationV2TO> apiResult = CompanyDetailsInterface.listHonorCertificationV2(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 商标信息
//    @ApiOperation(value = "商标信息", httpMethod = "POST", notes = "商标信息")
    @RequestMapping(value = "listTrademark", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<TrademarkTO> listTrademark(@RequestBody ProCorpSearchForm form) {
        JsonResultList<TrademarkTO> result = null;
        JsonResultList<TrademarkTO> apiResult = CompanyDetailsInterface.listTrademark(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    // 专利信息
//    @ApiOperation(value = "专利信息", httpMethod = "POST", notes = "专利信息")
    @RequestMapping(value = "listPatent", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<PatentTO> listPatent(@RequestBody ProCorpSearchForm form) {
        JsonResultList<PatentTO> result = null;
        JsonResultList<PatentTO> apiResult = CompanyDetailsInterface.listPatent(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    /**
     * added for v2.1.1 fengsw KNZT-6127【新增】补充国标-联合国，国标-新加坡，国标-北美，国标-欧盟，国标-新加坡行业映射数据(2025)
     */
    @RequestMapping(value = "getIndustryMappingInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<TblGlobalIndustryBaseTO> getIndustryMappingInfo(@RequestBody ProCorpSearchForm form) {
        List<TblGlobalIndustryBaseTO> industryList = commTblCompReportOrderService.getIndustryMappingInfo(form.getKeyNo(), false);
        if (industryList != null) {
            return JsonResultList.buildSuccess(industryList, industryList.size());
        } else {
            return JsonResultList.buildSuccess(new ArrayList<>());
        }
    }
}

