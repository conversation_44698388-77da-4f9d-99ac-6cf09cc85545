package com.qcc.pro.interceptor;

import com.alibaba.fastjson.JSON;
import com.backend.common.service.CommSysCompanyService;
import com.qcc.common.annnotation.UserProxyApi;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.annotation.LockCompany;
import com.qcc.frame.jee.commons.annotation.LockUser;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.RequestTO;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.service.BaseService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.commons.web.Servlets;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.SaasLoginService;
import com.qcc.frame.jee.modules.sys.service.SysLockAnnotationService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class AAInterceptor extends BaseService implements HandlerInterceptor {
	
	protected static Logger log = LoggerFactory.getLogger(AAInterceptor.class);
	
	public final static String HEADER_DEV_LOGIN_NAME = "dev-login-name";
	public final static String HEADER_CLIENT_ID = "pro-client-id";
	public final static String HEADER_ACCESS_TOKEN = "pro-access-token";
	public final static String HEADER_ACCESS_SECRET_KEY = "pro-access-secret-key";
	public final static String HEADER_FRONT_REQUEST_ID = "requestId";
	public final static String HEADER_PROXY_COMP_TOKEN = "simulate-user-token";

	private final static String adminPath = Global.getConfig("adminPath");
	
	private static List<String> logExeList = new ArrayList<>();

	private static SaasLoginService saasLoginService = SpringContextHolder.getBean(SaasLoginService.class);
	private static CommSysCompanyService commSysCompanyService = SpringContextHolder.getBean(CommSysCompanyService.class);
	private static SysLockAnnotationService sysLockAnnotationService = SpringContextHolder.getBean(SysLockAnnotationService.class);
	
	static {
		logExeList.add(adminPath + "/sys/message/loadUnreadMsg");
		logExeList.add("/webapi" + adminPath + "/sys/message/loadUnreadMsg");
		logExeList.add("/webapi" + adminPath + "/common/iframeDownload");
		logExeList.add(adminPath + "/common/iframeDownload");
		// added for v1.5.7 KNZT-2601【优化】【国际版】使用身份证+姓名校验有无犯罪记录的操作，返回结果不留存，访问日志中身份证信息全脱敏 (阅后即焚 - 记录支持不保存 固定方案 Hardcode )
		logExeList.add("/webapi" + adminPath + "/person_vrfy/init");
		logExeList.add("/webapi" + adminPath + "/person_vrfy/order");
		logExeList.add("/webapi" + adminPath + "/person_vrfy/validateSubmitOrder");
		logExeList.add("/webapi" + adminPath + "/person_vrfy/getOrder");
	}
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		LocaleContextHolder.setLocale(Locale.ENGLISH);
		boolean result = processPreHandle(request, response, handler);
		if(result) {
			try {
				User user = WebContextHolder.getCurrentUser();
				if(user != null && user.getCompany() != null) {
		        	HandlerMethod h = (HandlerMethod)handler;
					LockCompany lockComp = h.getMethodAnnotation(LockCompany.class);
				    if(lockComp != null) {
				    	sysLockAnnotationService.checkAndSetLogicLockCompFuncByInterceptor(user.getCompany().getId(), null, lockComp.functionCd(), lockComp.timeout(), lockComp.spreadToThread());
				    } else {
				    	LockUser lockUser = h.getMethodAnnotation(LockUser.class);
				    	if(lockUser != null) {
				    		sysLockAnnotationService.checkAndSetLogicLockCompFuncByInterceptor(user.getCompany().getId(), user.getId(), lockUser.functionCd(), lockUser.timeout(), lockUser.spreadToThread());
				    	}
				    }
				}
			} catch(MessageException e) {
				JsonResult<String> jsonResult = new JsonResult<String>();
				jsonResult.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
				jsonResult.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
				Servlets.returnJson(response, JSON.toJSONString(jsonResult));
	        	return false;
			} catch(Exception e) {
				logger.error("", e);
			}
		}
		return result;
	    
	}
	
	public boolean processPreHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		
		String uri = request.getRequestURI();
		String contextPath = request.getContextPath();
		String permUri = uri.substring(uri.indexOf(contextPath) + contextPath.length());
		
		if(!(Global.isProjectEnv("dev") || Global.isProjectEnv("sit"))) {//生产环境防止访问接口生成文档相关的URL
			if(permUri.startsWith("/doc.html") || permUri.startsWith("/webjars/")) {
				return false;
			}
		} else {
			if(permUri.startsWith("/doc.html") || permUri.startsWith("/webjars/")) {
				return true;
			}
		}
		WebContextHolder.setReadOnlyTran(null);
		WebContextHolder.setDownloadBrowserFlag(null);
    	WebContextHolder.resetAroundCount();
		WebContextHolder.setRequestType(Constants.RequestType.AJAX_SUBMIT);
		WebContextHolder.setProLoginType(null);
		WebContextHolder.removeCompSubTableId();
		
		RequestTO requestTO = new RequestTO();
		requestTO.setRequestUrl(uri);
		requestTO.setRequestUUID(IdGenUtil.uuid());
		requestTO.setBrowserUserAgent(request.getHeader("User-Agent"));
		requestTO.setBeginTimestamp(System.currentTimeMillis());
		requestTO.setClientIp(StringUtils.getRemoteAddr(request));
		WebContextHolder.setRequest(requestTO);
		LogUtil.setRequestId2MDC(requestTO.getRequestUUID());
		
		boolean checkPermission = true;
		
		if(checkPermission) {//如果是需要权限验证的页面
			WebContextHolder.setDownloadBrowserFlag(false);
			JsonResult<String> jsonResult = new JsonResult<String>();
			String clientId = request.getHeader(HEADER_CLIENT_ID);
	        String accessToken = request.getHeader(HEADER_ACCESS_TOKEN);
	        String accessSecretKey = request.getHeader(HEADER_ACCESS_SECRET_KEY);
			String devLoginName = request.getHeader(HEADER_DEV_LOGIN_NAME);
			String proxyUserToken = request.getHeader(HEADER_PROXY_COMP_TOKEN);

	        String frontRequestId = request.getHeader(HEADER_FRONT_REQUEST_ID);
	        boolean accessValidate = false;
	        try {
	        	if (!(handler instanceof HandlerMethod)) {
	        		jsonResult.setStatus(Constants.Result.FALSE_STR);
	        		jsonResult.setMsg("接口不存在");
					response.setStatus(404);
					logger.warn("saas notexistapi " + request.getRequestURI() + " IP:" + StringUtils.getRemoteAddr(request));
	        	} else {
		        	HandlerMethod h = (HandlerMethod)handler;
	        		RequiresPermissions rp = h.getMethodAnnotation(RequiresPermissions.class);//方法的权限标识
	    	        String [] permissionArr = null;
	    	        if(rp != null) {
	    	        	permissionArr = rp.value();
	    	        } else {
	    	        	if(h.getBeanType().isAnnotationPresent(RequiresPermissions.class)) {//方法所在类的权限标识
	    	        		rp = h.getBeanType().getAnnotation(RequiresPermissions.class);
	    	        		if(rp != null) {
	    	        			permissionArr = rp.value();
	    	        		}
	    	        	}
	    	        }

	    	        String permissionLogic = Constants.Sys.PERMISSION_LOGIC_AND;
	    	        if(rp != null) {
	    	        	if(Logical.REVERSE == rp.logical()) {
	    	        		permissionLogic = Constants.Sys.PERMISSION_LOGIC_REVERSE;
	    	        	} else if(Logical.OR == rp.logical()) {
	    	        		permissionLogic = Constants.Sys.PERMISSION_LOGIC_OR;
	    	        	}
	    	        }

					// updated for lvcy v2.0.3 KNZT-5404 针对可代理的接口
					UserProxyApi upa = h.getMethodAnnotation(UserProxyApi.class);
					User user;
					if (upa != null && StringUtils.isNotBlank(proxyUserToken)) {
						user = saasLoginService.checkProxyUser(clientId, accessToken, accessSecretKey, proxyUserToken);
					} else {
						user = saasLoginService.checkLoginUser(devLoginName, clientId, accessToken, accessSecretKey, permissionArr, permissionLogic);
					}

		        	if(user == null) {
		        		logger.info("[unlogin]uri:" + uri);
		        		logger.info("[unlogin]clientIp:" + requestTO.getClientIp() + ";clientId:" + clientId);
		        		logger.info("[unlogin]accessToken:" + accessToken);
		        		logger.info("[unlogin]accessSecretKey:" + accessSecretKey);
		        		jsonResult.setStatus(Constants.Result.UNLOGIN_1001);
		        		jsonResult.setMsg(I18NUtil.getMessage("warn.login.status.timeout"));
		        	} else {
		        		accessValidate = true;
		        		WebContextHolder.setCurrentUser(user);

		        	}
	        	}
	        } catch(MessageException e) {
	        	if(SaasLoginService.MSG_KEY_MULTIPLE_LOGIN.equals(e.getMessage())) {
	        		jsonResult.setStatus(Constants.Result.MULI_LOGIN_1000);
	        		if(e.getArgs() != null && e.getArgs().length > 0) {
	        			jsonResult.setMsg(I18NUtil.getMessage(e.getArgs()[0]));
	        		} else {
	        			jsonResult.setMsg(I18NUtil.getMessage("err.system.multi.login"));
	        		}
	        	} else if(SaasLoginService.MSG_KEY_SESSION_TIMEOUT.equals(e.getMessage())) {
	        		jsonResult.setStatus(Constants.Result.SESSION_TIMEOUT_1002);
	        		jsonResult.setMsg(I18NUtil.getMessage("warn.login.status.timeout"));
	        	} else if(SaasLoginService.MSG_KEY_NO_PERMISSION.equals(e.getMessage())) {
	        		jsonResult.setStatus(Constants.Result.FORBIDDEN_403);
	        		jsonResult.setMsg(I18NUtil.getMessage("err.access.permission"));
	        	} else if("err.system.account.company.not.in.date.range".equals(e.getMessage())) {
	        		jsonResult.setStatus(Constants.Result.SERVICE_EXPIRED);
	        		jsonResult.setMsg(I18NUtil.getMessage("err.system.account.company.not.in.date.range"));
	        	} else if("err.system.account.company.forbidden.client".equals(e.getMessage())) { // added for v2.0.5 fengsw KNZT-5574 自助账号禁用提示
					jsonResult.setStatus(Constants.Result.UNLOGIN_1001);
					jsonResult.setMsg(I18NUtil.getMessage("err.system.account.company.forbidden.client"));
				}else {
	        		jsonResult.setStatus(Constants.Result.FALSE_STR);
	        		jsonResult.setMsg(I18NUtil.getMessage(e.getMessage()));
//	        		if("获取登录标识失败".equals(jsonResult.getMsg())) {
//	        			logger.warn("accesssecretkey fail " + jsonResult.getMsg() + "  " + uri);
//	        		}
	        	}
	        } catch(Throwable e) {
	        	logger.error("", e);
	        	jsonResult.setStatus(Constants.Result.FALSE_STR);
				jsonResult.setMsg(I18NUtil.getMessage("err.access"));
			}
	        
	        if(!accessValidate) {
	        	Servlets.returnJson(response, JSON.toJSONString(jsonResult));
	        	return false;
	        }
	        
	        
	        logger.info("frontRequestId=" + frontRequestId + " : backendRequestId=" + requestTO.getRequestUUID());
	        User user = WebContextHolder.getCurrentUser();

	        
			String userId = user != null ? user.getId() : "nouserid";
			
	        logger.info("X-Real-IP:" + request.getHeader("X-Real-IP"));
	        logger.info("X-Forwarded-For:" + request.getHeader("X-Forwarded-For"));
	        logger.info(userId + ":" + requestTO.getBrowserUserAgent());

	        if(!logExeList.contains(WebContextHolder.getRequest().getRequestUrl())) {
				logger.info("[ip=" + StringUtils.getRemoteAddr(request) + "][userid=" + userId + "]");
		        logger.info(" accessurl[" + WebContextHolder.getRequest().getRequestUrl() + "] [user id="+userId+"] [request id=" + WebContextHolder.getRequest().getRequestUUID() + "] begin");
		        Servlets.logReqParam(WebContextHolder.getRequest().getRequestUUID(), request);
	        } else {
	        	requestTO.setDisabledLog(true);
	        }
		}
		return true;
	}
	
	private void clearThreadLocal() {
		WebContextHolder.setProLoginSource(null);
		WebContextHolder.setProLoginType(null);
		WebContextHolder.setDownloadBrowserFlag(null);
		WebContextHolder.setCurrentUser(null);
		WebContextHolder.setRequestType(-1);
		WebContextHolder.setRequest(null);
		WebContextHolder.setReadOnlyTran(null);
    	WebContextHolder.resetAroundCount();
    	WebContextHolder.setAnnotationLock(null);
		LogUtil.removeRequestIdFromMDC();
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		
		if(WebContextHolder.getRequest() != null) {
			User user = WebContextHolder.getCurrentUser();
			String userId = user != null ? user.getId() : "nouserid";
			long took = (System.currentTimeMillis() - WebContextHolder.getRequest().getBeginTimestamp());
			String customerType = user != null && user.getCompany() != null ? user.getCompany().getType() +"" : "";
			String appType = "webapp";
			if(!logExeList.contains(WebContextHolder.getRequest().getRequestUrl())) {
				logger.info(" accessurl[" + WebContextHolder.getRequest().getRequestUrl() + "] [user id="+userId+"] [request id=" + WebContextHolder.getRequest().getRequestUUID() + "] end took " + took + " ms.");
			} else {
				appType = "webapp_excl";
			}
			LogMonitorUtil.accessLogLimit(WebContextHolder.getRequest().getRequestUrl(), userId, customerType, WebContextHolder.getRequest().getRemark(), took, appType, WebContextHolder.getRequest().getClientIp(), WebContextHolder.getRequest().getBrowserUserAgent());
		}
		if(WebContextHolder.getAnnotationLock() != null && StringUtils.isNotBlank(WebContextHolder.getAnnotationLock().getAnnotationLockId())) {
			try {
				sysLockAnnotationService.logicUnLockCompFunc(WebContextHolder.getAnnotationLock().getAnnotationLockId());
			} catch(Exception e) {
				logger.error("", e);
			}
		}
		clearThreadLocal();
	}

}
