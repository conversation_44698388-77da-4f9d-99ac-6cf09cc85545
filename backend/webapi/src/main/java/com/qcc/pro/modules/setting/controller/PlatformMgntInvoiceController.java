package com.qcc.pro.modules.setting.controller;

import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.form.Statement4ManagementForm;
import com.backend.common.modules.delivery.model.StatementMonthly4ManagementTO;
import com.backend.common.modules.delivery.service.CommTblCompStatementMonthlyService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


/**
 * 平台管理-invoice管理
 * <AUTHOR>
 * @datetime 2025/3/10 18:14
 */
@Api(tags = "平台管理-invoice管理")
@Controller
@RequestMapping(value = "/saas/admin/invoice")
public class PlatformMgntInvoiceController { ;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(PlatformMgntInvoiceController.class);

    @ApiOperation(value = "查询invoice列表", httpMethod = "POST", notes = "查询invoice列表")
    @RequiresPermissions("sys:platform_mgnt_invoice:view")
    @RequestMapping(value = "/list", method=RequestMethod.POST)
    @ResponseBody
    public JsonResultList<StatementMonthly4ManagementTO> listInvoice(@RequestBody StatementMonthly4ManagementCondition condition) { 
        Page<StatementMonthly4ManagementTO> page = transactionBusinessService.pageStatement4Management(condition);
        return JsonResultList.buildSuccess(page.getList(), page.getCount());
    }

    @ApiOperation(value = "预览账单", httpMethod = "POST", notes = "预览账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "statementId", value = "账单ID", required = true, paramType = "body")
    })
    @RequiresPermissions(value = {"sys:platform_mgnt_invoice:view", "sys:operate"}, logical = Logical.AND)
    @RequestMapping(value = "/preview", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> previewInvoice(@RequestBody Statement4ManagementForm form) {
        try {
            String url = transactionBusinessService.previewStatementMonthly(form.getStatementId());
            return JsonResult.buildSuccess(url);
        } catch (MessageException e) {
            logger.info("预览账单失败", e);
            return JsonResult.buildFail(e.getMessage());
        } catch (Exception e) {
            logger.error("预览账单失败", e);
            return JsonResult.buildFail("预览账单失败");
        }
    }

    @ApiOperation(value = "重新生成账单", httpMethod = "POST", notes = "重新生成账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "statementId", value = "账单ID", required = true, paramType = "body")
    })
    @RequiresPermissions(value = {"sys:platform_mgnt_invoice:view", "sys:operate"}, logical = Logical.AND)
    @RequestMapping(value = "/regenerate", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> regenerateInvoice(@RequestBody Statement4ManagementForm form) {
        try {
            transactionBusinessService.reGenerateStatementMonthly(form.getStatementId());
            return JsonResult.buildSuccess("生成账单成功");
        } catch (MessageException e) {
            logger.info("重新生成账单失败", e);
            return JsonResult.buildFail(e.getMessage());
        } catch (Exception e) {
            logger.error("重新生成账单失败", e);
            return JsonResult.buildFail("重新生成账单失败");
        }
    }
}
