package com.qcc.pro.modules.setting.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.backend.common.entity.SysCompRole;
import com.backend.common.entity.TblGlobalTrialApply;
import com.backend.common.entity.mapping.FunctionCountInfoTO;
import com.backend.common.entity.mapping.UserTrialApplyTO;
import com.backend.common.form.user.FrontConfigForm;
import com.backend.common.form.user.FrontListConfigForm;
import com.backend.common.model.user.FrontConfigTO;
import com.qcc.frame.jee.modules.sys.form.UserSimpleCondition;
import com.backend.common.model.user.FrontListConfigTO;
import com.qcc.frame.jee.modules.sys.model.UserSimpleTO;
import com.backend.common.modules.common.form.KeyNoBaseForm;
import com.backend.common.modules.delivery.form.CompCreditChargeHistoryForm;
import com.backend.common.modules.delivery.form.CompCreditSummaryForm;
import com.backend.common.modules.delivery.form.TransactionPageForm;
import com.backend.common.modules.delivery.model.CreditsSummaryTO;
import com.backend.common.modules.delivery.model.TopUpHistoryTO;
import com.backend.common.modules.delivery.model.TransactionListV2TO;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.open.form.TrialApplyForm;
import com.backend.common.modules.open.service.TrialApplyService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.condition.CompUserTOCondition;
import com.backend.common.modules.setting.form.*;
import com.backend.common.modules.setting.form.user.BatchImportUserV2Form;
import com.backend.common.modules.setting.model.*;
import com.backend.common.modules.setting.model.user.BatchImportUserV2TO;
import com.backend.common.modules.util.OpLogUtils;
import com.backend.common.service.*;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;
import com.qcc.frame.commons.ienum.OpIpLogTypeEnum;
import com.qcc.frame.commons.ienum.TrialApplyTypeEnum;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.thirdparty_service.FileStoreService;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.commons.web.BaseController;
import com.qcc.frame.jee.commons.web.Servlets;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.Dict;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.excel.ExportExcel;
import com.qcc.frame.jee.modules.sys.excel.ImportExcel;
import com.qcc.frame.jee.modules.sys.form.UserProfileUpdateForm;
import com.qcc.frame.jee.modules.sys.model.User4ExportTO;
import com.qcc.frame.jee.modules.sys.model.UserGroupSelectTO;
import com.qcc.frame.jee.modules.sys.model.UserProfileTO;
import com.qcc.frame.jee.modules.sys.model.UserTO;
import com.qcc.frame.jee.modules.sys.service.DictService;
import com.qcc.frame.jee.modules.sys.service.SaasLoginService;
import com.qcc.frame.jee.modules.sys.service.ThreadPoolService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.qcc.pro.modules.setting.service.CompUserService;
import com.qcc.pro.modules.setting.service.SysCompanyService;
import com.qcc.pro.modules.setting.thread.ExportTestRunnable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户管理 - 客户模块
 * <AUTHOR>
 *
 */
@Api(tags = "用户管理")
@Controller
@RequestMapping(value = "${adminPath}/comp/user")
public class CompUserController extends BaseController{
	@Autowired
	private CompUserService compUserService;
	@Autowired
	private CommCompUserService commCompUserService;
	
	@Autowired
	private SysCompanyService sysCompanyService;
	@Autowired
	private SysCompRoleService sysCompRoleService;
	@Autowired
	private SysUserCompRoleService sysUserCompRoleService;
	@Autowired
	private CommSysCompanyService commSysCompanyService;
	@Autowired
	private CommSysCompGroupService commSysCompGroupService;
    @Autowired
    private ThreadPoolService threadPoolService;
	@Autowired
	private DictService dictService;

	@Autowired
	private CommTblReportChargeUnitService commTblReportChargeUnitService;
    @Autowired
    private UserService userService;
	@Autowired
	private TransactionBusinessService transactionBusinessService;
	@Autowired
	private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
	@Autowired
	private CommTblContractDeliveryProdAccService commTblContractDeliveryProdAccService;
	@Autowired
	private TrialApplyService trialApplyService;
	@Autowired
	private SaasLoginService saasLoginService;

    @RequiresPermissions("set:memb:user:view")
	@ApiOperation(value = "保存用户(新增/编辑)", httpMethod = "POST", notes = "新增用户/编辑用户时的保存用户接口")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "name", value = "新增-姓名", paramType = "body"),
		@ApiImplicitParam(name = "loginName", value = "新增-手机号码(登录)", paramType = "body"),
		@ApiImplicitParam(name = "newPassword", value = "新增-密码", paramType = "body"),
		@ApiImplicitParam(name = "email", value = "新增-邮箱", paramType = "body"),
		@ApiImplicitParam(name = "compRoleIds", value = "新增-角色ID", paramType = "body"),
		@ApiImplicitParam(name = "msgReceivedType", value = "新增-消息接受方式(M:短信; E:邮箱)", paramType = "body"),
		@ApiImplicitParam(name = "period", value = "新增-消息接受频率(D:每日; W:每周; M:每月)", paramType = "body"),
		@ApiImplicitParam(name = "periodExtend", value = "新增-消息接受频率 - 每周/每月的值", paramType = "body"),
		@ApiImplicitParam(name = "periodTime", value = "新增-消息接受频率-时间范围", paramType = "body"),
		@ApiImplicitParam(name = "compGroupIds", value = "新增-分组, 如果是根分组, 该值为空", paramType = "body"),
		@ApiImplicitParam(name = "compRoleIds", value = "新增-角色", paramType = "body"),
		@ApiImplicitParam(name = "sendMsgNotifyFlag", value = "新增-发送短信通知标识(true:通知, false:不通知)", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.id", value = "更新-用户ID", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.name", value = "更新-姓名", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.loginName", value = "更新-手机号码(登录)", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.email", value = "更新-邮箱", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.msgReceivedType", value = "更新-消息接受方式(M:短信; E:邮箱)", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.msgReceivedPeriod", value = "更新-消息接受频率(D:每日; W:每周; M:每月)", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.periodExtendValue", value = "更新-消息接受频率 - 每周/每月的值", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.periodTime", value = "更新-消息接受频率-时间范围", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.compGroupIds", value = "更新-分组, 如果是根分组, 该值为空", paramType = "body"),
		@ApiImplicitParam(name = "updateUser.compRoleIds", value = "更新-角色", paramType = "body")})
	@RequestMapping(value="save", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> saveUser(@RequestBody CompanyUserForm form){
		JsonResult<String> result = new JsonResult<String>();
		try{
			if(form != null){
				if(form.getUpdateUser() != null) {
                        /*if(StringUtils.isBlank(form.getUpdateUser().getMsgReceivedType())) {
                            form.getUpdateUser().setMsgReceivedType(Constants.MsgReceivedType.NOT_RECEIVE);
                        } else if(StringUtils.contains(form.getUpdateUser().getMsgReceivedType(), ",")){
                            String msgRecievedType = form.getUpdateUser().getMsgReceivedType().replaceAll(",", "");
                            form.getUpdateUser().setMsgReceivedType(msgRecievedType);
                        }
                        if(Constants.JobPeriod.EVERY_DAY.equals(form.getUpdateUser().getMsgReceivedPeriod())) {
                            form.getUpdateUser().setNextNoticeDate(DateUtils.getNextDate(new Date(), 1));
                        } else {
                            String extendValue = form.getUpdateUser().getPeriodExtendValue();
                            if(StringUtils.isBlank(extendValue)) {
                                if(Constants.JobPeriod.EVERY_MONTH.equals(form.getUpdateUser().getMsgReceivedPeriod())) {
                                    extendValue = "1";
                                } else {
                                    extendValue = "2";
                                }
                            }
                            form.getUpdateUser().setNextNoticeDate(DateUtils.getNextIndicatedDay(new Date(), form.getUpdateUser().getMsgReceivedPeriod(), Integer.valueOf(extendValue)));
                        }*/
	
					Company company = new Company();
					company.setId(UserUtils.getUserCompanyId());
					form.getUpdateUser().setCompany(company);
					compUserService.saveUserAndRole(form.getUpdateUser(), form.getCompGroupIds(), form.getCompRoleIds(), form.getUpdateUser().getMsgReceivedType(), form.getUpdateUser().getMsgReceivedPeriod(), form.getUpdateUser().getPeriodExtendValue(), form.getUpdateUser().getPeriodTime(), form.getSendMsgNotifyFlag());
				} else {
     				if(StringUtils.isBlank(form.getLoginName())) {
     					logger.error("err.login.name.required");
     					throw new MessageException("err.access");
					}
					String msgReceivedType = "";
                    //批量设置推送
                        if(StringUtils.isBlank(form.getMsgReceivedType())) {
                            msgReceivedType = Constants.MsgReceivedType.NOT_RECEIVE;
                        } else if(StringUtils.contains(form.getMsgReceivedType(), ",")) {
                            msgReceivedType = Constants.MsgReceivedType.MESSAGE + Constants.MsgReceivedType.EMAIL;
                        } else {
                            msgReceivedType = form.getMsgReceivedType();
                        }
                    
                    User user = new User();
					user.setId(form.getUserId());
//					user.setMobile(form.getMobile());
					user.setName(form.getName());
					user.setLastName(form.getLastName());
					user.setLoginName(form.getLoginName());
					user.setMobile(form.getLoginName());
					user.setEmail(form.getEmail());
					user.setNewPassword(form.getNewPassword());
					user.setMsgReceivedType(msgReceivedType);
					user.setStatus(Constants.Sys.USER_STATUS_INACTIVE); // updated for v2.0.4 chenbl KNZT-5442
                    user.setMsgReceivedPeriod(form.getPeriod());
                    user.setPeriodExtendValue(form.getPeriodExtend());
					user.setPeriodTime(form.getPeriodTime());
					/*if(Constants.JobPeriod.EVERY_DAY.equals(form.getPeriod())) {
                        user.setNextNoticeDate(DateUtils.getNextDate(new Date(), 1));
                    } else {
                        String extendValue = form.getPeriodExtend();
                        if(StringUtils.isBlank(extendValue)) {
                            if(Constants.JobPeriod.EVERY_MONTH.equals(form.getUpdateUser().getMsgReceivedPeriod())) {
                                extendValue = "1";
                            } else {
                                extendValue = "2";
                            }
                        }
                        user.setNextNoticeDate(DateUtils.getNextIndicatedDay(new Date(), form.getPeriod(), Integer.valueOf(extendValue)));
                    }*/
					Company company = new Company();
					company.setId(UserUtils.getUserCompanyId());
					user.setCompany(company);
					compUserService.saveUserAndRole(user, form.getCompGroupIds(), form.getCompRoleIds(), msgReceivedType, form.getPeriod(), form.getPeriodExtend(), form.getPeriodTime(), form.getSendMsgNotifyFlag());
				}
				result.setStatus(Constants.Result.SUCCESS_STR);
			}
		}catch(MessageException e){
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}catch(Exception e){
			logger.error("", e);
			result.setStatus(Constants.Result.FALSE_STR);
			// updated for v1.6.2 KNZT-2833
			result.setMsg(I18NUtil.getMessage("err.access"));
		}
		return result;
	}
	
	/**
	 * 列出所有客户可分配角色
	 * @return
	 */
	@RequiresPermissions("set:memb:user:view")
	@ApiOperation(value = "可分配角色", httpMethod = "POST", notes = "新增用户/编辑用户时选择的角色列表")
	@RequestMapping(value="listCompRole", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<SysCompRole> listCompRole() {
		JsonResultList<SysCompRole> result = new JsonResultList<SysCompRole>();
		result.setStatus(Constants.Result.SUCCESS_STR);
		result.setResultList(sysCompRoleService.listAssignableCompRoles(UserUtils.getUserId())); // updated for v2.0.7 chenbl KNZT-5787
		return result;
	}
	
	/**
	 * 得到用户信息和用户的分配角色
	 * @param form
	 * @return
	 */
	@ApiOperation(value = "得到用户信息", httpMethod = "POST", notes = "编辑用户时得到用户信息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userId", value = "编辑用户的ID", paramType = "body")})
	@RequestMapping(value="getUserAndRole", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<UserTO> getUserAndRole(@RequestBody CompanyUserForm form){
		JsonResult<UserTO> result = new JsonResult<UserTO>();
		if(form != null && StringUtils.isNotBlank(form.getUserId())){
			UserTO userTO = sysUserCompRoleService.getUserAndCompRoleId(UserUtils.getUserCompanyId(), form.getUserId());
			result.setResult(userTO);
			result.setStatus(Constants.Result.SUCCESS_STR);
		}
		return result;
	}

	/**
	 * 筛选用户列表
	 * @param form
	 * @return
	 */
	@RequiresPermissions("set:memb:user:view")
	@ApiOperation(value = "用户列表", httpMethod = "POST", notes = "用户列表查询")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "condition.name", value = "用户姓名", paramType = "body"),
		@ApiImplicitParam(name = "condition.compGroupId", value = "选中的分组, 如果是根分组, 该值为空", paramType = "body"),
		@ApiImplicitParam(name = "condition.page.pageNo", value = "分页页码", paramType = "body"),
		@ApiImplicitParam(name = "condition.page.pageSize", value = "每页数量", paramType = "body")})
	@RequestMapping(value = "listUser", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<UserTO> listUser(@RequestBody CompanyUserTOForm form){
		JsonResultList<UserTO> results = new JsonResultList<UserTO>();
		logger.debug("Input Param[form]  -> " + form);
		logger.debug("Input Param[form.getCondition()]  -> " + form.getCondition());
		Page<UserTO> users = compUserService.listUserByCompGroupId(form.getCondition());
		results.setResultList(users.getList());
		results.setTotalCount(users.getCount());
		results.setStatus(Constants.Result.SUCCESS_STR);
		return results;
	}

	// added for v2.1.9 chenbl KNZT-6952
	@ApiOperation(value = "用户列表", httpMethod = "POST", notes = "用户列表")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "unGrouped", value = "未被分组的", paramType = "body"),
			@ApiImplicitParam(name = "compGroupId", value = "分组ID", paramType = "body"),
			@ApiImplicitParam(name = "searchKey", value = "搜索词", paramType = "body"),
			@ApiImplicitParam(name = "page.pageNo", value = "分页页码", paramType = "body"),
			@ApiImplicitParam(name = "page.pageSize", value = "每页数量", paramType = "body")})
	@RequestMapping(value = "listSimpleUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<UserSimpleTO> listSimpleUser(@RequestBody UserSimpleCondition condition) {
		return JsonResultList.buildSuccess(compUserService.listSimpleUser(condition));
	}
	
	/**
	 * 列出所有用户, 用于下拉框选择
	 * @return
	 */
	@ApiOperation(value = "移交-交接人列表", httpMethod = "POST", notes = "移交-交接人列表")
	@RequestMapping(value = "listAllUser4Select", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<UserGroupSelectTO> listAllUser4Select(){
		JsonResultList<UserGroupSelectTO> results = new JsonResultList<UserGroupSelectTO>();
		List<UserGroupSelectTO> userList = compUserService.listAllUser4Select(UserUtils.getUserCompanyId());
		results.setStatus(Constants.Result.SUCCESS_STR);
		results.setResultList(userList);
		return results;
	}

	@ApiOperation(value = "用户列表初始化", httpMethod = "POST", notes = "用户列表初始化得到主账号ID")
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "load", method=RequestMethod.POST)
    @ResponseBody
	public JsonResult<CompUserInitTO> load() {
		JsonResult<CompUserInitTO> result = new JsonResult<CompUserInitTO>();
		CompUserInitTO initTO = new CompUserInitTO();
		initTO.setMainUserId(sysCompanyService.getMainUserId());
        result.setStatus(Constants.Result.SUCCESS_STR);
		result.setResult(initTO);
//		model.addAttribute("mainUserId", sysCompanyService.getMainUserId());
//		return "modules/company/companyUser";
		return result;
	}
//	@RequiresPermissions("set:memb:user:view")
//	@RequestMapping(value = "userimport")
//	public String userImport(CompanyUserForm form, Model model) {
//		String companyId = UserUtils.getUserCompanyId();
//		model.addAttribute("mainUserId", sysCompanyService.getMainUserId());
//		model.addAttribute("hideHeader", true);
//		if (StringUtils.isNotBlank(form.getCompGroupId())) {
//			CompGroup selectedCompGroup = commSysCompGroupService.getCompGroupById(companyId, form.getCompGroupId());
//			model.addAttribute("compGroupIds", selectedCompGroup.getId());
//			model.addAttribute("compGroupName", selectedCompGroup.getOfficeName());
//		}
//		return "modules/company/companyUserImport";
//	}
	
	/**
	 * 批量分组
	 */
	@ApiOperation(value = "批量分组", httpMethod = "POST", notes = "用户列表查询")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userIds", value = "用户ID(多个用逗号分隔)", paramType = "body"),
		@ApiImplicitParam(name = "compGroupId", value = "分组ID(不能是根节点)", paramType = "body")})
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "batchAssignCompGroup", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> batchAssignCompGroup(@RequestBody CompanyUserTOForm form) {
		JsonResult<String> result = new JsonResult<String>();
		
		Set<String> userIdSet = StringUtils.split2Set(form.getUserIds(), true);
		if(userIdSet == null || userIdSet.isEmpty()) {
			result.setStatus(Constants.Result.FALSE_STR);
			// updated for v1.6.2 KNZT-2463
			result.setMsg(I18NUtil.getMessage("err.access"));
			return result;
		}
		try {
			compUserService.batchUpdateUserCompGroup(UserUtils.getUserCompanyId(), userIdSet, form.getCompGroupId());
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		
		return result;
	}
	
	/**
	 * 批量角色分配
	 * @param form
	 * @return
	 */
	@ApiOperation(value = "批量角色分配-确定", httpMethod = "POST", notes = "批量角色分配保存")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userIds", value = "用户ID(多个用逗号分隔)", paramType = "body"),
		@ApiImplicitParam(name = "compRoleIds", value = "角色ID", paramType = "body")})
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "batchAssignCompRole", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> batchAssignCompRole(@RequestBody CompanyUserTOForm form) {
		JsonResult<String> result = new JsonResult<String>();
		
		Set<String> userIdSet = StringUtils.split2Set(form.getUserIds(), true);
		if(userIdSet == null || userIdSet.isEmpty()) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("请至少选择一个姓名");
			return result;
		}
		Set<String> compRoleIdSet = StringUtils.split2Set(form.getCompRoleIds(), true);

		try {
			compUserService.batchUpdateUserCompRole(userIdSet, compRoleIdSet);
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage()));
		}
		
		return result;
	}
	
	@ApiOperation(value = "导出导入用户结果", httpMethod = "POST", notes = "导出导入用户结果")
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "exportImportUserResult", method=RequestMethod.POST)
    public String exportImportUserResult(@RequestBody CompUserInitTO form, HttpServletResponse response) {
		JsonResult<String> result = new JsonResult<String>();
		result.setStatus(Constants.Result.FALSE_STR);
    	try {
    		if(StringUtils.isBlank(form.getJson())) {
    			result.setMsg("无数据");
    			Servlets.returnJson(response, JSON.toJSONString(result));
    			return null;
    		} else {
	    		JSONArray jsonObj = JsonUtils.parseArray(form.getJson());
	    		if(jsonObj != null) {
	    			List<BatchImportUserResultExportTO> exportTOList = jsonObj.toJavaList(BatchImportUserResultExportTO.class);
	    			String fileName = "用户导入结果"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
	    			new ExportExcel("Data", "用户导入结果", BatchImportUserResultExportTO.class).setDataList(exportTOList).write(response, fileName).dispose();
	    			return null;
	    		} else {
	    			result.setMsg(I18NUtil.getMessage("err.system.data.exception"));
	    			Servlets.returnJson(response, JSON.toJSONString(result));
	    			return null;
	    		}
    		}
    	} catch(Exception e) {
    		logger.error("", e);
    		result.setMsg(I18NUtil.getMessage("err.system.data.exception"));
			Servlets.returnJson(response, JSON.toJSONString(result));
			return null;
    	}
    }

    
    @ApiOperation(value = "导入用户", httpMethod = "POST", notes = "导入用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "compGroupIds", value = "内部 id", paramType = "body"),
            @ApiImplicitParam(name = "url", value = "上传文件的url eg: FrontUpd/xxxx.xlsx", paramType = "body")})
	@RequiresPermissions("set:memb:user:view")
    @RequestMapping(value = "import", method=RequestMethod.POST)
    @ResponseBody
    public JsonResultList<BatchImportUserTO> importUser(@RequestBody CompUserInitTO form) {
        JsonResultList<BatchImportUserTO> result = new JsonResultList<BatchImportUserTO>();
        try {
            if (StringUtils.isBlank(form.getUrl())) {
				// updated for v1.5.2 KNZT-2436
				logger.error("导入文件url不能为空");
                throw new MessageException("err.access");
            }
			// updated for v1.5.2 KNZT-2393 //updated v1.7.2 KNZT-2428
            File file = FileStoreService.getInstance().getObjectFile4Upload(form.getUrl(), "xlsx");

			// updated for v1.5.2 KNZT-2435
			// updated for v1.5.3 KNZT-2496
			if (file == null) {
				throw new MessageException("err.import.excel.blank");
			}
			ImportExcel ei = new ImportExcel(file, 2, 0);
			List<BatchImportUserTO> list = ei.getDataList(BatchImportUserTO.class);
			if(CollectionUtils.isEmpty(list)){
				throw new MessageException("err.import.excel.blank");
			}
			compUserService.saveImportUser(list,form.getCompGroupIds());
			result.setStatus(Constants.Result.SUCCESS_STR);
			// updated for v1.5.2 KNZT-2436
			result.setMsg("Success.");
			result.setResultList(list);
			result.setJsonStr(JSON.toJSONString(list));
			// updated for v1.5.2 KNZT-2436
        }catch(MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e.getMessage()));
        }
        catch(Exception e) {
            logger.error("", e);
            result.setStatus(Constants.Result.FALSE_STR);
			// updated for v1.5.2 KNZT-2436
            result.setMsg(I18NUtil.getMessage("err.access"));
        }
        return result;
    }

	// added for v2.1.8 chenbl KNZT-6662
	@ApiOperation(value = "导入用户V2前校验EXCEL", httpMethod = "POST", notes = "导入用户V2前校验EXCEL")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "url", value = "上传文件的url eg: FrontUpd/xxxx.csv", paramType = "body")})
	@RequestMapping(value = "validateExcel4ImportV2", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<BatchImportUserV2TO> validateExcel4ImportV2(@RequestBody CompUserInitTO form) {
		JsonResultList<BatchImportUserV2TO> result = null;
		try {
			MsgExceptionUtils.failBuild(StringUtils.isBlank(form.getUrl()), "err.import.excel.blank");
			File file = FileStoreService.getInstance().getObjectFile4Upload(form.getUrl(), "csv");
			result = JsonResultList.buildSuccess(compUserService.validateExcel4ImportV2(file));
		} catch (MessageException e) {
			result = JsonResultList.buildFail(e);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			result = JsonResultList.buildFail("err.access");
		}
		return result;
	}

	// added for v2.1.8 chenbl KNZT-6662
	@ApiOperation(value = "导入用户V2前校验", httpMethod = "POST", notes = "导入用户V2前校验")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "emails", value = "待上传的邮箱", paramType = "body")})
	@RequestMapping(value = "validate4ImportV2", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<BatchImportUserV2TO> validate4ImportV2(@RequestBody BatchImportUserV2Form form) {
		JsonResultList<BatchImportUserV2TO> result = null;
		try {
			result = JsonResultList.buildSuccess(compUserService.validate4ImportV2(form));
		} catch (MessageException e) {
			result = JsonResultList.buildFail(e);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			result = JsonResultList.buildFail("err.access");
		}
		return result;
	}

	// added for v2.1.8 chenbl KNZT-6662
	@ApiOperation(value = "导入用户V2", httpMethod = "POST", notes = "导入用户V2")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "emails", value = "待上传的邮箱", paramType = "body"),
			@ApiImplicitParam(name = "compGroupId", value = "分组ID", paramType = "body"),
			@ApiImplicitParam(name = "compGroupName", value = "分组名称", paramType = "body")})
	@RequestMapping(value = "importV2", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<BatchImportUserV2TO> importV2(@RequestBody BatchImportUserV2Form form) {
		JsonResultList<BatchImportUserV2TO> result = null;
		try {
			result = JsonResultList.buildSuccess(compUserService.saveImportUserV2(form));
		} catch (MessageException e) {
			result = JsonResultList.buildFail(e);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			result = JsonResultList.buildFail("err.access");
		}
		return result;
	}

	/**
	 * 批量删除用户
	 * @param form
	 * @return
	 */
	@ApiOperation(value = "(批量)删除用户", httpMethod = "POST", notes = "(批量)删除用户")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userIds", value = "用户ID(如果是多个用逗号分隔)", paramType = "body")})
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value="batchDeleteUser", method=RequestMethod.POST)
    @ResponseBody
	public JsonResult<String> batchDeleteUser(@RequestBody CompanyUserTOForm form){
	    JsonResult<String> result = new JsonResult<>();
		if(form != null && StringUtils.isNotBlank(form.getUserIds())){
			try {
				commCompUserService.batchDeleteUser(UserUtils.getUserCompanyId(), StringUtils.split2Set(form.getUserIds(), true));
//				addMessage(redirectAttributes, I18NUtil.getMessage("msg.delete.success"));
                result.setStatus(Constants.Result.SUCCESS_STR);
			} catch (MessageException e) {
                result.setStatus(Constants.Result.FALSE_STR);
                result.setMsg(I18NUtil.getMessage(e.getMessage()));
//				addErrMessage(redirectAttributes, I18NUtil.getMessage(e.getMessage()));
			}
		}else{
            result.setStatus(Constants.Result.FALSE_STR);
//			addErrMessage(redirectAttributes, I18NUtil.getMessage("err.sys.user.para.invalid"));
		}
        return result;
	}
    
    
    /**
     * 用户剩余期限
     * @param form
     * @return
     */
    @ApiOperation(value = "用户剩余期限", httpMethod = "POST", notes = "用户剩余期限")
    @RequestMapping(value="getUserRemainDays", method=RequestMethod.POST)
    @ResponseBody
    public JsonResult<CompUserInfoTO> getUserRemainDays(@RequestBody CompanyUserTOForm form){
        JsonResult<CompUserInfoTO> result = new JsonResult<CompUserInfoTO>();
        CompUserInfoTO compUserInfo = new CompUserInfoTO();
        Company company = sysCompanyService.get(UserUtils.getUserCompanyId());
        if(company != null && company.getEndDate() !=null) {
            double diff = DateUtils.getDistanceOfTwoDate(new Date(), company.getEndDate())+1;
            compUserInfo.setRemainDays(Double.valueOf(diff).shortValue());
        }
		compUserInfo.setCategory(company.getCategory());
        compUserInfo.setType(company.getType());
        compUserInfo.setEndDate(company.getEndDate());
        compUserInfo.setCurrentDate(new Date());
        result.setStatus(Constants.Result.SUCCESS_STR);
        result.setResult(compUserInfo);
        return result;
    }

	@RequestMapping(value = "setUserInfo")
	@ResponseBody
	public JsonResult<String> setUserInfo(@RequestBody CompanyUserForm form) {
		JsonResult<String> result = new JsonResult<String>();
		try {
			User user = UserUtils.getUser();
//			user.setMsgReceivedType(form.getMsgReceivedType());
//			systemService.updateMsgReceivedTypeById(user.getId(), msgRecivedType);
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			result.setStatus(Constants.Result.FALSE_STR);
			logger.error("setUserInfo", e);
		}
		return result;
	}
	
	@ApiOperation(value = "测试导出", httpMethod = "POST", notes = "测试导出")
	@RequestMapping(value = "testExport")
	@ResponseBody
	public JsonResult<String> test(@RequestBody CompanyUserForm form) {
		JsonResult<String> result = new JsonResult<String>();
		try {
			threadPoolService.executeExport("test", "test", "test", new ExportTestRunnable());
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			result.setStatus(Constants.Result.FALSE_STR);
			logger.error("setUserInfo", e);
		}
		return result;
	}

	// added for v1.5.8 KNZT-2608
	@ApiOperation(value = "获取公司计费单价", httpMethod = "POST", notes = "获取公司计费单价")
	@RequestMapping(value = "getCompChargeUnits", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<CompChargeUnitTO> getCompChargeUnits(@RequestBody KeyNoBaseForm form) {
		JsonResultList<CompChargeUnitTO> jsonResultList = new JsonResultList<>();
		try {
			List<CompChargeUnitTO> unitTOs = commTblReportChargeUnitService.getCompChargeUnitsWithSubList(form.getKeyNo());
			jsonResultList.setResultList(unitTOs);
			jsonResultList.setStatus(Constants.Result.SUCCESS_STR);
		} catch (MessageException e) {
			jsonResultList.setStatus(Constants.Result.FALSE_STR);
			logger.warn("getCompChargeUnits", e);
		}
		return jsonResultList;
	}

	// added for v1.6.2 KNZT-2463
	@ApiOperation(value = "用户列表导出", httpMethod = "POST", notes = "用户列表导出")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition.name", value = "用户姓名", paramType = "body"),
			@ApiImplicitParam(name = "condition.compGroupId", value = "选中的分组, 如果是根分组, 该值为空", paramType = "body")})
	@RequestMapping(value = "batchExportUserList")
	public JsonResult<Void> batchExportUserList(@RequestBody CompanyUserTOForm form, HttpServletResponse response) {
		JsonResult<Void> jsonResult = new JsonResult<>();
		try {
			CompUserTOCondition condition = form.getCondition();
			if (condition == null) {
				condition = new CompUserTOCondition();
			}
			condition.removePage();
			Page<UserTO> userTos = compUserService.listUserByCompGroupId(condition);
			doExportUser(response, userTos.getList());
			// 记录操作日志 added for lvcy v2.1.2 KNZT-6149
			OpLogUtils.saveOpLog(OpIpLogTypeEnum.EXPORT_USERS);
		} catch (IOException e) {
			logger.error("", e);
			jsonResult = JsonResult.buildFail("err.access");
		}
		return jsonResult;
	}

	private void doExportUser(HttpServletResponse response, List<UserTO> userTos) throws IOException {
		List<User4ExportTO> exportTos = userTos.stream().map(User4ExportTO::buildFrom).collect(Collectors.toList());
		String fileName = "Team_user_table_" + DateUtils.formatDate(new Date(), "yyyyMMdd") + ".xlsx";
		new ExportExcel(null, User4ExportTO.class, true).setDataList(exportTos).write(response, fileName).dispose();
	}

	// added for v1.9.0 KNZT-4110
	@ApiOperation(value = "获取主页需要的用户信息", httpMethod = "POST", notes = "获取主页需要的用户信息")
	@RequestMapping(value="getUserInfo4Home", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<UserInfoHomeTO> getUserInfo4Home(@RequestBody CompanyUserTOForm form){
		JsonResult<UserInfoHomeTO> result = new JsonResult<UserInfoHomeTO>();
		UserInfoHomeTO userInfoHomeTO = compUserService.getUserInfo4Home();
		result.setResult(userInfoHomeTO);
		result.setStatus(Constants.Result.SUCCESS_STR);
		return result;
	}

	// added for v2.0.0 chenbl KNZT-5094
	@ApiOperation(value = "保存香港查册人信息", httpMethod = "POST", notes = "保存香港查册人信息")
	@RequestMapping(value = "saveHkSearcherInfo", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> saveHkSearcherInfo(@RequestBody HkSearcherInfoSaveForm form) {
		try {
			form.setCompanyId(UserUtils.getUserCompanyId());
			commSysCompanyService.saveHkSearcherInfo(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error("saveHkSearcherInfo异常", e);
			return JsonResult.buildFail("err.access");
		}
	}

	// added for v2.0.0 chenbl KNZT-5094
	@ApiOperation(value = "查询香港查册人IssuingPlace字典列表", notes = "查询香港查册人IssuingPlace字典列表")
	@RequestMapping(value = "listHkIssuingPlace", method = {RequestMethod.GET, RequestMethod.POST})
	@ResponseBody
	public JsonResultList<DictTO> listHkIssuingPlace() {
		List<Dict> dictList = dictService.getDictList(Constants.DictType.HK_SEARCHER_INFO_ISSUING_PLACE);
		return JsonResultList.buildSuccess(dictList.stream().map(DictTO::build).collect(Collectors.toList()));
	}

	// added for v2.0.4 chenbl KNZT-5371
	// updated for v2.0.7 chenbl KNZT-5773
	@ApiOperation(value = "重置子账号MFA", httpMethod = "POST", notes = "重置子账号MFA")
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "resetMfaSecret", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> resetMfaSecret(@RequestBody CompanyUserForm form) {
		try {
			compUserService.resetMfaSecret(form.getUserId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}

	// added for v2.0.7 chenbl KNZT-5773
	@ApiOperation(value = "解锁子账号", httpMethod = "POST", notes = "解锁子账号")
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "unlockUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> unlockUser(@RequestBody CompanyUserForm form) {
		try {
			compUserService.unlockUser4MainUser(form.getUserId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}


	// added for v2.3.2 fengsw KNZT-7930
	@ApiOperation(value = "获取当前公司偏好", httpMethod = "POST", notes = "获取当前公司偏好")
	@RequestMapping(value = "listPreferences", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<CompanyPreferenceInfo> listPreferences() {
		List<CompanyPreferenceInfo> preferenceInfoList = commSysCompanyService.listPreferences();
		return JsonResultList.buildSuccess(preferenceInfoList);
	}

	@ApiOperation(value = "设置当前公司偏好", httpMethod = "POST", notes = "设置当前公司偏好")
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "configPreferences", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> configPreferences(@RequestBody List<CompanyPreferenceInfo> form) {
		try {
			commSysCompanyService.configPreferences(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage(), e.getArgs());
		}
	}

	// added for v2.1.7 chenbl KNZT-6477
	@ApiOperation(value = "配置UI版本", httpMethod = "POST", notes = "配置UI版本")
	@RequestMapping(value = "configUiVersion", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> configReportShare(@RequestBody ConfigUiVersionForm form) {
		try {
			commSysCompanyService.configUiVersion4CompanyAdmin(form.getVersion());
			saasLoginService.logoutUserByCompanyIdExceptSelf(UserUtils.getUserCompanyId(), UserUtils.getUserId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage(), e.getArgs());
		}
	}

	/**
	 * added for v2.1.5 fengdsw KNZT-6454
	 * 保存或者更新新手指引标记
	 *
	 * @return
	 */
	@ApiOperation(value = "保存新手指引标志", httpMethod = "POST", notes = "保存新手指引标志")
	@RequestMapping(value = "saveGuide", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<User> saveBeginningGuideConfig() {
		compUserService.saveBeginningGuideConfig();
		return JsonResult.buildSuccess();
	}

	@ApiOperation(value = "查询余额", httpMethod = "POST", notes = "非后付费和自助账号查询余额")
	@RequestMapping(value = "/queryBalance", method = RequestMethod.POST)
	@ResponseBody
	private JsonResult<FunctionCountInfoTO> queryBalance() {
		if (!CompPayTypeEnum.AFTER.getCode().equals(UserUtils.getUser().getCompany().getPayType())) {
			SysCompInfoFuncCount compInfoFuncCount = commSysCompInfoFuncCountService.readOnlyGetCompFuncCount(UserUtils.getUserCompanyId());
			if (compInfoFuncCount != null) {
				FunctionCountInfoTO functionCountInfoTO = new FunctionCountInfoTO();
				functionCountInfoTO.setTotalCount(compInfoFuncCount.getTotalCount());// 总额度
				functionCountInfoTO.setConsumedCount(compInfoFuncCount.getConsumedCount());// 已消耗额度
				functionCountInfoTO.setRemainCount(functionCountInfoTO.getTotalCount().subtract(functionCountInfoTO.getConsumedCount())); // 剩余额度
				BigDecimal credits = commTblContractDeliveryProdAccService.countExpireSoonAccountCredits(UserUtils.getUserCompanyId());
				if(credits!=null){
					functionCountInfoTO.setExpireSoonCount(credits);
				}
				return JsonResult.buildSuccess(functionCountInfoTO);
			}
		}
		return JsonResult.buildSuccess();
	}

	@ApiOperation(value = "查询余额详情", httpMethod = "POST", notes = "查询余额详情")
	@RequestMapping(value = "/pageCreditsBreakdown", method = RequestMethod.POST)
	@ResponseBody
	private JsonResultList<CreditsSummaryTO> pageCreditsBreakdown(@RequestBody CompCreditSummaryForm form) {
		Page<CreditsSummaryTO> page = commTblContractDeliveryProdAccService.pageCreditsBreakdownCondition(form);
		return JsonResultList.buildSuccess(page.getList(), page.getCount());
	}

	@ApiOperation(value = "查询充值记录", httpMethod = "POST", notes = "查询充值记录")
	@RequestMapping(value = "/pageChargeRecords", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<TopUpHistoryTO> pageChargeRecords(@RequestBody CompCreditChargeHistoryForm form) {
		Page<TopUpHistoryTO> page = commTblContractDeliveryProdAccService.pageChargeRecords(form);
		return JsonResultList.buildSuccess(page.getList(), page.getCount());
	}

	@ApiOperation(value = "用户流水列表", httpMethod = "POST", notes = "用户查询全量流水")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "contractNo", value = "合同编号", paramType = "body"),
			@ApiImplicitParam(name = "transactionType", value = "交易类型", paramType = "body"),
			@ApiImplicitParam(name = "beginDate", value = "开始日期", paramType = "body"),
			@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "body"),
			@ApiImplicitParam(name = "page.pageNo", value = "页码", paramType = "body"),
			@ApiImplicitParam(name = "page.pageSize", value = "每页数量", paramType = "body")
	})
	@RequestMapping(value = "transaction/page", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<TransactionListV2TO> pageTransaction(@RequestBody TransactionPageForm form) {
		try {
			form.getCondition().setCompanyId(UserUtils.getUserCompanyId());
			List<TransactionListV2TO> list = transactionBusinessService.pageTransaction4UserComp(form.getCondition());
			Page page = form.getCondition().getPage();
			return JsonResultList.buildSuccess(list, page.getCount());
		} catch (MessageException e) {
			return JsonResultList.buildFail(e.getMessage());
		}
	}

	@ApiOperation(value = "用户流水列表导出", httpMethod = "POST", notes = "用户流水列表导出")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "contractNo", value = "合同编号", paramType = "body"),
			@ApiImplicitParam(name = "transactionType", value = "交易类型", paramType = "body"),
			@ApiImplicitParam(name = "beginDate", value = "开始日期", paramType = "body"),
			@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "body")
	})
	@RequestMapping(value = "transaction/generate", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> exportTransaction(@RequestBody TransactionPageForm form, HttpServletResponse response) {
		try {
			form.getCondition().removePage();
			form.getCondition().setCompanyId(UserUtils.getUserCompanyId());
			List<TransactionListV2TO> list = transactionBusinessService.pageTransaction4UserComp(form.getCondition());
			if (CollectionUtils.isEmpty(list)) {
				return JsonResult.buildFail("No data.");
			}
			new ExportExcel(null, TransactionListV2TO.class, true)
					.setDataList(list)
					.write(response, String.format("Purchase History_%s.xlsx", DateUtils.getDate(DateUtils.DATE_FORMAT_YMD)))
					.dispose();
			return JsonResult.buildSuccess();
		} catch (Exception e) {
			logger.error("ExportExcel error ", e);
			return JsonResult.buildFail(e.getMessage());
		}
	}

	@ApiOperation(value = "查询历史留资信息", httpMethod = "POST", notes = "非后付费和自助账号查询余额")
	@RequestMapping(value = "/getHistoryContactInfo", method = RequestMethod.POST)
	@ResponseBody
	private JsonResult<UserTrialApplyTO> getHistoryContactInfo() {
		String userLoginName = UserUtils.getUserLoginName();
		List<TblGlobalTrialApply> trialApplyList = trialApplyService.listByEmail(userLoginName);
		if (CollectionUtils.isNotEmpty(trialApplyList)) {
			return JsonResult.buildSuccess(UserTrialApplyTO.build(trialApplyList.get(0)));
		}
		return JsonResult.buildSuccess();
	}

	@ApiOperation(value = "保存额度充值留资申请", httpMethod = "POST", notes = "保存额度充值留资申请")
	@RequestMapping(value = "/saveTrialApply4TopUp", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> saveTrialApply4TopUp(@RequestBody TrialApplyForm form, HttpServletRequest request) {
		try {
			form.setType(TrialApplyTypeEnum.B.getCode());
			TrialApplyService.validateApplyForm(form);
			trialApplyService.readOnlySaveTrialApplyAsyncCreateLeadAndEmailNotify(form,request);
		} catch (MessageException e) {
			return JsonResult.buildFail("err.access");
		}
		return JsonResult.buildSuccess();
	}

	// added for v2.0.7 chenbl KNZT-6595
	@ApiOperation(value = "获取个人信息", httpMethod = "POST", notes = "获取个人信息")
	@RequestMapping(value = "getUserProfile", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<UserProfileTO> getUserProfile() {
		return JsonResult.buildSuccess(commCompUserService.getUserProfile(UserUtils.getUserId()));
	}

	// added for v2.0.7 chenbl KNZT-6595
	@ApiOperation(value = "编辑个人信息", httpMethod = "POST", notes = "编辑个人信息")
	@RequestMapping(value = "updateProfile", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> updateProfile(@RequestBody UserProfileUpdateForm form) {
		try {
			form.setUserId(UserUtils.getUserId());
			commCompUserService.updateProfile(form);
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage(), e.getArgs());
		}
		return JsonResult.buildSuccess();
	}

	// added for v2.1.8 chenbl KNZT-6899
	@ApiOperation(value = "切换主账号", httpMethod = "POST", notes = "切换主账号")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "userId", value = "需要切换的子账号", paramType = "body")})
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "toggleMainUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> toggleMainUser(@RequestBody CompanyUserForm form) {
		try {
			compUserService.toggleMainUser(form.getUserId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}

	// added for v2.1.9 chenbl KNZT-6952
	@ApiOperation(value = "获取列表配置", httpMethod = "POST", notes = "获取列表配置")
	@RequestMapping(value = "getFrontListConfig", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<FrontListConfigTO> getFrontListConfig(@RequestBody FrontListConfigForm form) {
		try {
			return JsonResult.buildSuccess(compUserService.getFrontListConfig(form));
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}

	// added for v2.1.9 chenbl KNZT-6952
	@ApiOperation(value = "保存列表配置", httpMethod = "POST", notes = "保存列表配置")
	@RequestMapping(value = "saveFrontListConfig", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> saveFrontListConfig(@RequestBody FrontListConfigForm form) {
		try {
			compUserService.saveFrontListConfig(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}

	@ApiOperation(value = "获取前端配置", httpMethod = "POST", notes = "获取前端配置")
	@RequestMapping(value = "getFrontConfig", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<FrontConfigTO> getFrontConfig(@RequestBody FrontConfigForm form) {
		try {
			return JsonResult.buildSuccess(compUserService.getFrontConfig(form));
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}

	@ApiOperation(value = "保存前端配置", httpMethod = "POST", notes = "保存前端配置")
	@RequestMapping(value = "saveFrontConfig", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> saveFrontConfig(@RequestBody FrontConfigForm form) {
		try {
			compUserService.saveFrontConfig(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}

	// added for v2.3.2 fengsw KNZT-7930
	@ApiOperation(value = "重新发送激活邮件", httpMethod = "POST", notes = "重新发送激活邮件")
	@RequiresPermissions("set:memb:user:view")
	@RequestMapping(value = "resendInvitation", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> resendInvitation(@RequestBody CompanyUserForm form) {
		try {
			compUserService.resendInvitation(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail("err.access");
		}
	}
}
