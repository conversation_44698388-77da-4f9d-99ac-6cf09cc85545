package com.qcc.pro.modules.cart.controller;

import com.backend.common.modules.report.form.CartSubmitForm;
import com.backend.common.modules.report.form.TblCompReportCartForm;
import com.backend.common.modules.report.model.CartGroupInfoTO;
import com.backend.common.modules.report.model.CartInfoTO;
import com.backend.common.modules.report.service.CommTblCompReportCartService;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.web.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 购物车处理
 */
@Api(tags = "购物车", value = "购物车增删改查控制层")
@Controller
@RequestMapping(value = "${adminPath}/kyc/cart")
public class TblCompReportCartController extends BaseController {
    @Autowired
    private CommTblCompReportCartService commTblCompReportCartService;

    @ApiOperation(value = "添加购物车", httpMethod = "POST", notes = "添加购物车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartInfoList.corpKeyNo", value = "企业KeyNo", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.corpName", value = "企业中文名", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.corpNameEn", value = "企业英文名", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.reportGroup", value = "报告分组(CORP_G:企业(国内和香港) / PERS_G:人员 / CORP_G_SG:新加坡企业)", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.reportType", value = "报告类型, 搜索页面不传, 默认值由后端确定, 大陆企业详情页如果未解锁360, 该值CORP_360", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.persKeyNo", value = "高管KeyNo", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.persName", value = "高管名称", paramType = "body")
    })
    @RequestMapping(value = "/addCart", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CartInfoTO> addCart(@RequestBody CartSubmitForm form) {
        JsonResultList<CartInfoTO> result = new JsonResultList<>();
        try {
            if (CollectionUtils.isEmpty(form.getCartInfoList())) {
                throw new MessageException("err.param.invalid");
            }
            List<CartInfoTO> cartList = commTblCompReportCartService.saveOrUpdateCartInfo(form.getCartInfoList());
            result.setStatus(Constants.Result.SUCCESS_STR);
            result.setResultList(cartList);
        } catch (MessageException e) {
            CommTblCompReportOrderService.resetStatusAndMsg4Order(e, result);
        }
        return result;
    }

    @ApiOperation(value = "编辑购物车", httpMethod = "POST", notes = "编辑购物车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartInfoList.corpKeyNo", value = "企业KeyNo", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.corpName", value = "企业中文名", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.corpNameEn", value = "企业英文名", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.reportGroup", value = "报告分组(CORP_G:企业(国内和香港) / PERS_G:人员 / CORP_G_SG:新加坡企业)", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.reportType", value = "报告类型, 搜索页面不传, 默认值由后端确定, 大陆企业详情页如果未解锁360, 该值CORP_360", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.persKeyNo", value = "高管KeyNo", paramType = "body"),
            @ApiImplicitParam(name = "cartInfoList.persName", value = "高管名称", paramType = "body")
    })
    @RequestMapping(value = "/editCart", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CartInfoTO> editCart(@RequestBody CartSubmitForm form) {
        JsonResultList<CartInfoTO> result = new JsonResultList<>();
        try {
            if (CollectionUtils.isEmpty(form.getCartInfoList())) {
                throw new MessageException("err.param.invalid");
            }
            List<CartInfoTO> cartList = commTblCompReportCartService.saveOrUpdateCartInfo(form.getCartInfoList());
            result.setStatus(Constants.Result.SUCCESS_STR);
            result.setResultList(cartList);
        } catch (MessageException e) {
            CommTblCompReportOrderService.resetStatusAndMsg4Order(e, result);
        }
        return result;
    }

    @ApiOperation(value = "删除购物车", httpMethod = "POST", notes = "删除购物车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartInfoList.cartId", value = "购物车记录Id", paramType = "body")
    })
    @RequestMapping(value = "/deleteCart", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> deleteCart(@RequestBody CartSubmitForm form) {
        JsonResult<String> result = new JsonResult<>();
        try {
            if (CollectionUtils.isEmpty(form.getCartInfoList())) {
                throw new MessageException("err.param.invalid");
            }
            commTblCompReportCartService.deleteCartInfo(form.getCartInfoList());
            result.setStatus(Constants.Result.SUCCESS_STR);
        } catch (MessageException e) {
            CommTblCompReportOrderService.resetStatusAndMsg4Order(e, result);
        }
        return result;
    }

    @ApiOperation(value = "查询购物车", httpMethod = "POST", notes = "查询购物车")
    @RequestMapping(value = "/queryCart", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CartInfoTO> queryCart(@RequestBody TblCompReportCartForm form) {
        JsonResultList<CartInfoTO> result = new JsonResultList<>();
        Page<CartInfoTO> resultPage = commTblCompReportCartService.findListByCondition(form.getCondition());
        result.setStatus(Constants.Result.SUCCESS_STR);
        if (resultPage != null) {
            result.setResultList(resultPage.getList());
            result.setTotalCount(resultPage.getCount());
        }
        return result;
    }

    @ApiOperation(value = "按主体分组查询购物车", httpMethod = "POST", notes = "按keyNo分组查询购物车，按分组内最新创建时间倒序排序")
    @RequestMapping(value = "/queryCartGroup", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<List<CartGroupInfoTO>> queryCartGroupByKey(@RequestBody TblCompReportCartForm form) {
        List<CartGroupInfoTO> groupedList = commTblCompReportCartService.findGroupListByCondition(form.getCondition());
        return JsonResult.buildSuccess(groupedList);
    }
}
