package com.qcc.pro.modules.setting.controller;

import com.backend.common.entity.mapping.CompChargeUnitInfoTO;
import com.backend.common.entity.mapping.CompanyListInfo;
import com.backend.common.entity.mapping.CompanyServiceTO;
import com.backend.common.entity.mapping.RoleInfoTO;
import com.backend.common.model.json.JsonSimpleResult;
import com.backend.common.modules.common.entity.TblGlobalOutboundListSync;
import com.backend.common.modules.common.form.OrderBaseForm;
import com.backend.common.modules.common.form.TblGlobalOutboundListSyncForm;
import com.backend.common.modules.delivery.form.*;
import com.backend.common.modules.delivery.model.*;
import com.backend.common.modules.delivery.service.CommCrmCompTrackingRecordService;
import com.backend.common.modules.delivery.service.DeliveryBusinessService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.form.ReportOrder4ManagCondition;
import com.backend.common.modules.report.model.management.ReportOrder4ManagListTO;
import com.backend.common.modules.report.model.management.ReportOrder4ManagementExporter;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.setting.form.*;
import com.backend.common.modules.setting.model.*;
import com.backend.common.modules.setting.service.PlatformMgntService;
import com.backend.common.service.CommCompUserService;
import com.backend.common.service.CommSysCompanyService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.annotation.RedisLock;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.web.BaseController;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.SysConfig;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompUserListTO;
import com.qcc.frame.jee.modules.sys.form.CompanyAddressUpdateForm;
import com.qcc.frame.jee.modules.sys.form.ToggleMainUserForm;
import com.qcc.frame.jee.modules.sys.model.CompanyAddressTO;
import com.qcc.frame.jee.modules.sys.model.CompanyChargeTO;
import com.qcc.frame.jee.modules.sys.service.SysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Api(tags = "平台管理")
@Controller
@RequestMapping(value = "${adminPath}/admin")
public class PlatformMgntController extends BaseController {
	@Autowired
	private PlatformMgntService platformMgntService;
	@Autowired
	private SysConfigService sysConfigService;
	@Autowired
	private CommTblCompReportOrderService commTblCompReportOrderService;
	@Autowired
	private DeliveryBusinessService deliveryBusinessService;
	@Autowired
	private TransactionBusinessService transactionBusinessService;
	@Autowired
	private CommCrmCompTrackingRecordService commCrmCompTrackingRecordService;
	@Autowired
	private CommSysCompanyService commSysCompanyService;
	@Autowired
	private CommCompUserService commCompUserService;

	// added for v1.8.7 KNZT-3878【优化】【收入确认】saveCompany重构，仅保存公司相关的数据，区分试用账户和签约账户的操作范围
	@ApiOperation(value = "保存公司账号基本信息", httpMethod = "POST", notes = "保存公司账号基本信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "公司ID, 用于更新, 如果为空就是添加", paramType = "body"),
			@ApiImplicitParam(name = "companyName", value = "公司名称", paramType = "body"),
			@ApiImplicitParam(name = "loginName", value = "主账号登录名", paramType = "body"),
			@ApiImplicitParam(name = "loginPwd", value = "密码(编辑初始化页面时该值不要赋值)", paramType = "body"),
			@ApiImplicitParam(name = "phone", value = "联系电话", paramType = "body"),
			@ApiImplicitParam(name = "email", value = "邮箱", paramType = "body"),
			@ApiImplicitParam(name = "subUserNum", value = "子账号数量", paramType = "body"),
			@ApiImplicitParam(name = "beginDate", value = "开始日期", paramType = "body"),
			@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "body"),
			@ApiImplicitParam(name = "businessScenario", value = "应用场景（最大长度2000）", paramType = "body"),
			@ApiImplicitParam(name = "dataSave", value = "数据存储模式（Y-保存，N-不保存）", paramType = "body"),
			@ApiImplicitParam(name = "innerAccount", value = "是否内部账号（true-是，false-否）", paramType = "body"),
			@ApiImplicitParam(name = "showCredit", value = "子账号是否展示加个（Y-是，N-否）", paramType = "body"),
			@ApiImplicitParam(name = "canUpgrade", value = "是否支持套餐升级（Y-是，N-否）", paramType = "body"),
			@ApiImplicitParam(name = "enableOpenApi", value = "开启OpenApi接口（true-是，false-否）", paramType = "body"),
			@ApiImplicitParam(name = "type", value = "账号类型（1-正式，2-试用）", paramType = "body"),
			@ApiImplicitParam(name = "trackingContent", value = "跟踪记录内容", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "saveCompanyBasicInfo", method=RequestMethod.POST)
	@ResponseBody
	public JsonSimpleResult<Company> saveCompanyBasicInfo(@RequestBody CompanyAdminForm form) {
		JsonSimpleResult<Company> result = new JsonSimpleResult<>();
		try {
			Company company = platformMgntService.saveOrUpdateCompanyBasicInfo(form);
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult(company);
		} catch (MessageException e) {
			if(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("日期不正确")){
				result.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
			}
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	// added for v1.8.7 KNZT-3878【优化】【收入确认】saveCompany重构，仅保存公司相关的数据，区分试用账户和签约账户的操作范围
	@ApiOperation(value = "试用账号充值", httpMethod = "POST", notes = "试用账号充值")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "公司ID, 用于更新, 如果为空就是添加", paramType = "body"),
			@ApiImplicitParam(name = "companyName", value = "公司名称", paramType = "body"),
			@ApiImplicitParam(name = "loginName", value = "主账号登录名", paramType = "body"),
			@ApiImplicitParam(name = "loginPwd", value = "密码(编辑初始化页面时该值不要赋值)", paramType = "body"),
			@ApiImplicitParam(name = "phone", value = "联系电话", paramType = "body"),
			@ApiImplicitParam(name = "email", value = "邮箱", paramType = "body"),
			@ApiImplicitParam(name = "subUserNum", value = "子账号数量", paramType = "body"),
			@ApiImplicitParam(name = "beginDate", value = "开始日期", paramType = "body"),
			@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "body"),
			@ApiImplicitParam(name = "proKey", value = "企查查专业版公司Key", paramType = "body"),
			@ApiImplicitParam(name = "proSecretKey", value = "企查查专业版公司密钥", paramType = "body"),
			@ApiImplicitParam(name = "proMainLoginName", value = "企查查专业版公司主帐号", paramType = "body"),
			@ApiImplicitParam(name = "yunjuKey", value = "企查查开放平台KEY", paramType = "body"),
			@ApiImplicitParam(name = "yunjuSecretKey", value = "企查查开放平台密钥", paramType = "body"),
			@ApiImplicitParam(name = "roleTable", value = "权限列表数据", paramType = "body"),
			@ApiImplicitParam(name = "functionTable", value = "服务列表数据", paramType = "body"),
			@ApiImplicitParam(name = "businessScenario", value = "应用场景（最大长度2000）", paramType = "body"),
			@ApiImplicitParam(name = "businessScenario", value = "应用场景（最大长度2000）", paramType = "body"),
			@ApiImplicitParam(name = "emailNotify", value = "邮件通知（Y-是，N-否）", paramType = "body"),
			@ApiImplicitParam(name = "type", value = "账号类型（默认2-试用）", paramType = "body"),
			@ApiImplicitParam(name = "chargeMode", value = "计费标准（1-合并，2-分开）", paramType = "body"),
			@ApiImplicitParam(name = "trackingContent", value = "跟踪记录内容", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "rechargeTrialCompany", method=RequestMethod.POST)
	@ResponseBody
	public JsonSimpleResult<Company> rechargeTrialCompany(@RequestBody CompanyAdminForm form) {
		JsonSimpleResult<Company> result = new JsonSimpleResult<>();
		try {
			Company company = platformMgntService.rechargeTrialCompany(form);
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult(company);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			// added for v2.0.6 ZS-452 【角色管理】角色信息编辑与保存 优化保存服务列表额度时不足的提示
			if(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("分配额度小于当前已消耗额度")){
				result.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
			}
			// added for V2.0.7 ZS-474 删除角色提醒，应该是黄色警示(对应角色：监控，尽调）
			if(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("移除权限")){
				result.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
			}

			// added for 【产业招商云平台II】【WEB】-优化-用户登录与企业账号编辑时，增加账号有效期校验 公司的结束日期必须大于开始日期
			if(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("日期不正确")){
				result.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
			}
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}


	@ApiOperation(value = "查询公司信息", httpMethod = "POST", notes = "查询公司信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition.name", value = "公司名称", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageNo", value = "分页页码", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageSize", value = "每页数量", paramType = "body")})
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "listCompany", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<CompanyListInfo> listCompany(@RequestBody CompanyListForm form) {
		JsonResultList<CompanyListInfo> result = new JsonResultList<CompanyListInfo>();
		try {
		// updated fro ZS-424【平台管理】列表框增加三列，展示服务列表已消耗额度与总额度
		Page<CompanyListInfo> page = platformMgntService.pageListCompanyInfo(form.getCondition());
		result.setStatus(Constants.Result.SUCCESS_STR);
		result.setResultList(page.getList());
		result.setTotalCount(page.getCount());
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	//added for v1.3.7 KNZT-1420
	@ApiOperation(value = "查询公司详情", httpMethod = "POST", notes = "查询公司详情")
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "company/detail", method=RequestMethod.POST)
	@ResponseBody
	public JsonSimpleResult<CompanyListInfo> getCompanyDetail(@RequestBody CompanyAdminForm form) {
		JsonSimpleResult<CompanyListInfo> result = new JsonSimpleResult<CompanyListInfo>();
		try {
			CompanyListInfo companyDetailInfo = platformMgntService.getCompanyDetailInfo(form.getId());
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult(companyDetailInfo);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	@ApiOperation(value = "删除公司", httpMethod = "POST", notes = "删除公司")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "ids", value = "公司ID, 多个用逗号分隔", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "deleteCompany", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> deleteCompany(@RequestBody CompanyAdminForm form) {
		JsonResult<String> result = new JsonResult<String>();
		try {

			platformMgntService.deleteCompany(StringUtils.split(form.getIds(), true));
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}
	
	@ApiOperation(value = "禁用或撤销禁用自助账号", httpMethod = "POST", notes = "禁用或撤销禁用自助账号")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "公司ID, 多个用逗号分隔", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "disableCompany", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> disableOrRetrieveCompany(@RequestBody CompanyAdminForm form) {
		JsonResult<String> result = new JsonResult<>();
		try {
			platformMgntService.disableOrRetrieveCompany(StringUtils.split(form.getIds(), true), form.getDisableAccount());
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	@ApiOperation(value = "获取系统角色列表信息", httpMethod = "POST", notes = "获取系统角色列表信息")
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "/getRoleTable", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<RoleInfoTO> getRoleTable() {
		JsonResultList<RoleInfoTO> result = new JsonResultList<>();
		try {
			List<RoleInfoTO> list = platformMgntService.getRoleList();
			result.setResultList(list);
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			logger.error("getRoleTable error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("查询失败");
		}
		return result;
	}

	@ApiOperation(value = "获取公司用户角色列表信息", httpMethod = "POST", notes = "获取用公司的户角色列表并标记已选中")
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "/getRoleTableInfo", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<RoleInfoTO> getRoleTableInfoWithCompanyId(@RequestBody CompanyFuncForm companyFuncForm) {
		JsonResultList<RoleInfoTO> result = new JsonResultList<>();
		try {
			List<RoleInfoTO> list = platformMgntService.getUserRoleWithSelectedList(companyFuncForm.getCompanyId());
			result.setResultList(list);
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			logger.error("getRoleTableInfoWithCompanyId error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("查询失败");
		}
		return result;
	}

	// removed for v1.9.8 KNZT-4538
//	@ApiOperation(value = "获取服务列表信息", httpMethod = "POST", notes = "根据角色获取服务列表信息")
//	@RequiresPermissions("sys:platform_mgnt:view")
//	@RequestMapping(value = "getFunctionTableInfoByRoleIds", method=RequestMethod.POST)
//	@ResponseBody
//	public JsonResultList<FunctionTableInfoTO> getFunctionTableInfoByRoleIds(@RequestBody CompanyFuncForm companyFuncForm) {
//		JsonResultList<FunctionTableInfoTO> result = new JsonResultList<>();
//		try {
//			List<FunctionTableInfoTO> list = platformMgntService.getFunctionTableInfoByRoleIds(companyFuncForm.getCompanyId(), companyFuncForm.getRoleIds());
//			result.setResultList(list);
//			result.setStatus(Constants.Result.SUCCESS_STR);
//		} catch (Exception e) {
//			logger.error("getFunctionTableInfoByRoleIds error", e);
//			result.setStatus(Constants.Result.FALSE_STR);
//			result.setMsg("查询失败");
//		}
//		return result;
//	}

	//added for v1.2.8 KNZT-1346 增加英文名称
	@ApiOperation(value = "获取开通试用账号参数", httpMethod = "POST", notes = "获取开通试用账号参数")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "/trial/getDefaultParam", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> getDefaultParam4CreateTrialAccount(@RequestBody CompanyAdminForm form) {
		JsonResult<String> result = new JsonResult<String>();
		try {
			SysConfig config = sysConfigService.getConfigByTypeAndKey(Constants.SysConfigType.CREATE_TRIAL_ACCOUNT,Constants.SysConfigKey.CREATE_TRIAL_ACCOUNT_DEFAULT_PARAM);
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult(config == null?null:config.getConfigValue());
		} catch (Exception e) {
			logger.error("getDefaultParam error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("查询失败");
		}
		return result;
	}

	/**
	 * added for KNZT-1426【优化】【国际版】国际版与开放平台处理生产环境存量数据DataPatch（账号关联，香港企业解锁额度）
	 * @return
	 */
	@ApiOperation(value = "存量用户数据创建开放平台账号", httpMethod = "POST", notes = "存量用户数据创建开放平台账号")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "/patch/kycUser", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchKycUser(@RequestBody List<String> idList) {
		JsonResult<String> result = new JsonResult<>();
		try {
			platformMgntService.patchKycUser(idList);
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			logger.error("patchKycUser error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("同步失败");
		}
		return result;
	}


	// added for v1.5.6 KNZT-2561
	@ApiOperation(value = "获取子账号列表", httpMethod = "POST", notes = "获取子账号列表")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition.page.pageNo", value = "分页页码", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageSize", value = "每页数量", paramType = "body"),
			@ApiImplicitParam(name = "condition.companyId", value = "公司ID", paramType = "body")})
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "listCompUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<CompUserListTO> listCompUser(@RequestBody CompUserListForm form) {
		JsonResultList<CompUserListTO> result = new JsonResultList<>();
		if(StringUtils.isBlank(form.getCondition().getCompanyId())){
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage("err.access"));
			return result;
		}
		Page<CompUserListTO> page = platformMgntService.listCompUser(form.getCondition());
		result.setStatus(Constants.Result.SUCCESS_STR);
		result.setResultList(page.getList());
		result.setTotalCount(page.getCount());
		return result;
	}

	// added for v2.0.8 chenbl KNZT-5815
	@ApiOperation(value = "删除用户", httpMethod = "POST", notes = "删除用户")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "ids", value = "用户ID", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "deleteCompUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> deleteCompUser(@RequestBody CompUserListForm form) {
		try {
			platformMgntService.deleteCompUser(StringUtils.split2Set(form.getIds(), true));
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
	}

	// added for v1.5.8 KNZT-2587
	@ApiOperation(value = "获取公司计费价目表", httpMethod = "POST", notes = "获取公司计费价目表")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "公司ID, 用于更新, 如果为空就是添加", paramType = "body")
	})
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "listChargeUnitByCompanyId", method = RequestMethod.POST)
	@ResponseBody
	public JsonResultList<TblReportChargeUnit> listChargeUnitByCompanyId(@RequestBody CompanyAdminForm form) {
		JsonResultList<TblReportChargeUnit> result = new JsonResultList<>();
		List<TblReportChargeUnit> chargeUnitTOList = platformMgntService.listChargeUnitByCompanyId(form.getId());
		result.setResultList(chargeUnitTOList);
		result.setStatus(Constants.Result.SUCCESS_STR);
		return result;
	}

	// updated for v1.9.8 KNZT-4538
	@ApiOperation(value = "编辑-获取公司计费价目表", httpMethod = "POST", notes = "编辑-获取公司计费价目表")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "公司ID", paramType = "body")
	})
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "getCompChargeUnitInfo", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<CompChargeUnitInfoTO> getCompChargeUnitInfo(@RequestBody CompanyAdminForm form) {
		return JsonResult.buildSuccess(platformMgntService.getCompChargeUnitInfo(form.getId()));
	}

	@ApiOperation(value = "获取账户所有启用的服务", httpMethod = "POST", notes = "获取账户所有启用的服务-展示")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "公司ID", paramType = "body")
	})
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "getCompanyEnabledService", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<CompanyServiceTO> getCompanyEnabledService(@RequestBody CompanyAdminForm form) {
		CompanyServiceTO CompanyServiceTO = platformMgntService.getCompanyEnabledService(form.getId());
		return JsonResult.buildSuccess(CompanyServiceTO);
	}


	/**
	 * added for v1.7.1 KNZT-3149【DATA PATCH】匹配原服务 Snapshot 20美金 至 KYC Basic， 匹配360 Company Profile 至 KYC Advanced （OCBC应该没有数据），做好相应的衔接沟通
	 * 仅本次上线使用
	 * @return
	 */
	@ApiOperation(value = "存量用户服务列表配置patch", httpMethod = "POST", notes = "存量用户服务列表配置patch")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "/patch/kycOrder", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchKycCompOrder() {
		JsonResult<String> result = new JsonResult<>();
		try {
			platformMgntService.patchKycCompOrder();
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			logger.error("patchKycCompOrder error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("同步失败");
		}
		return result;
	}


	@ApiOperation(value = "补偿生成订单报告", httpMethod = "POST", notes = "补偿生成订单报告")
	@RequestMapping(value = "/compensateCorpReport", method=RequestMethod.POST)
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@ResponseBody
	public JsonResult<String> compensateCorpReport(@RequestBody List<String> idList) {
		JsonResult<String> result = new JsonResult<>();
		try {
			commTblCompReportOrderService.compensateCorpReport(idList);
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (Exception e) {
			logger.error("compensateCorpReport error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("补偿失败");
		}
		return result;
	}

	@ApiOperation(value = "查询出境额度记录", httpMethod = "POST", notes = "查询出境额度信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition.searchKey", value = "关键词，支持名称搜索", paramType = "body"),
			@ApiImplicitParam(name = "condition.minOrderCreatedDate", value = "最小时间", paramType = "body"),
			@ApiImplicitParam(name = "condition.maxOrderCreatedDate", value = "最大时间", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageNo", value = "分页页码", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageSize", value = "每页数量", paramType = "body")})
	@RequiresPermissions("sys:platform_mgnt_supervisory_mgnt_outbound:view")
	@RequestMapping(value = "listOutbound", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<TblGlobalOutboundListSync> listOutboundByCondition(@RequestBody TblGlobalOutboundListSyncForm form) {
		JsonResultList<TblGlobalOutboundListSync> result = new JsonResultList<>();
			Page<TblGlobalOutboundListSync> page = platformMgntService.listOutboundByCondition(form.getCondition());
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResultList(page.getList());
			result.setTotalCount(page.getCount());

		return result;
	}



    // added for v1.8.8 KNZT-3324
	@ApiOperation(value = "查询合同详情", httpMethod = "POST", notes = "查询合同详情")
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "contract/detail", method=RequestMethod.POST)
	@ResponseBody
	public JsonSimpleResult<ContractDetailVO> getContractDetail(@RequestBody ContractCommForm form) throws MessageException {
		JsonSimpleResult<ContractDetailVO> result = new JsonSimpleResult<>();
		try {
			ContractDetailVO contractDetail = deliveryBusinessService.getContractDetail(form.getContractNo(), form.getWorkFlowNo(), form.getCompanyId());
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult(contractDetail);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	@ApiOperation(value = "交付合同", httpMethod = "POST", notes = "交付合同")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "contract/delivery", method=RequestMethod.POST)
	@ResponseBody
	@RedisLock(lockId = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
	public JsonSimpleResult<Company> delivery(@RequestBody ContractDeliveryForm form) throws MessageException {
		JsonSimpleResult<Company> result = new JsonSimpleResult<>();
		try {
			Company company = deliveryBusinessService.delivery(form);
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult(company);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}



	@ApiOperation(value = "确认合同产品账户余额", httpMethod = "POST", notes = "确认合同产品账户余额")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "contract/confirmRemainUnit", method=RequestMethod.POST)
	@ResponseBody
	@RedisLock(lockId = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
	public JsonSimpleResult<String> confirmRemainUnit(@RequestBody ContractCommForm form) throws MessageException {
		JsonSimpleResult<String> result = new JsonSimpleResult<>();
		try {
			deliveryBusinessService.confirmRemainUnit(form.getProdAccId());
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult("success");
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	@ApiOperation(value = "赠送产品额度", httpMethod = "POST", notes = "赠送产品额度")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "contract/giveProdUnit", method=RequestMethod.POST)
	@ResponseBody
	@RedisLock(lockId = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
	public JsonSimpleResult<String> giveProdUnit(@RequestBody ContractDeliveryGiveProdUnitListForm form) throws MessageException {
		JsonSimpleResult<String> result = new JsonSimpleResult<>();
		try {
			deliveryBusinessService.giveProdUnit(form);
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResult("success");
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		}
		return result;
	}

	
	// added for v1.8.7 KNZT-3881
	@ApiOperation(value = "查询交付记录", httpMethod = "POST", notes = "查询交付记录")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition.companyId", value = "公司ID", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageNo", value = "页码", paramType = "body"),
			@ApiImplicitParam(name = "condition.page.pageSize", value = "每页记录数", paramType = "body")})
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "/listDeliveryRecords", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<ChargeRecordsInfoListTO> listDeliveryRecords(@RequestBody ChargeRecordsForm form) {
		JsonResultList<ChargeRecordsInfoListTO> result = new JsonResultList<>();
		try {
			Page<ChargeRecordsInfoListTO> page = deliveryBusinessService.pageDeliveryRecords(form.getCondition());
			result.setStatus(Constants.Result.SUCCESS_STR);
			result.setResultList(page.getList());
			result.setTotalCount(page.getCount());
		} catch (Exception e) {
			logger.error("listDeliveryRecords error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("查询失败");
		}
		return result;
	}


	// added for v1.9.0 KNZT-4093
	@ApiOperation(value = "查询跟进记录", httpMethod = "POST", notes = "查询跟进记录")
	@RequiresPermissions("sys:platform_mgnt:view")
	@RequestMapping(value = "listCompTrackingRecord", method = RequestMethod.POST)
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition.companyId", value = "公司ID", paramType = "body")})
	@ResponseBody
	public JsonResultList<CompTrackingRecordTO> listCompTrackingRecord(@RequestBody CrmCompTrackingRecordForm form) {
		JsonResultList<CompTrackingRecordTO> result = new JsonResultList<>();
		if (form.getCondition() == null || StringUtils.isBlank(form.getCondition().getCompanyId())) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("err.access");
		}
		try {
			Page<CompTrackingRecordTO> page = commCrmCompTrackingRecordService.page(form.getCondition());
			result.setStatus(Constants.Result.SUCCESS_STR);
			if (page != null) {
				result.setResultList(page.getList());
				result.setTotalCount(page.getCount());
			}
		} catch (Exception e) {
			logger.error("listCompTrackingRecord error", e);
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg("查询失败");
		}
		return result;
	}

	// added for v1.9.3 KNZT-4193
	@ApiOperation(value = "操作试用申请", httpMethod = "POST", notes = "操作使用申请")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "trialApplyId", value = "试用申请ID", paramType = "body"),
			@ApiImplicitParam(name = "operateType", value = "操作类型", paramType = "body"),
			@ApiImplicitParam(name = "fileList", value = "文件列表", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt_self_account:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "operateTrialApply", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> operateTrialApply(@RequestBody TrialApplyOperateForm form) {
		try {
			platformMgntService.operateTrialApply(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage(), e.getArgs());
		}
	}

	// added for v1.9.3 KNZT-4193
	@ApiOperation(value = "分页查询自主账号试用申请", httpMethod = "POST", notes = "分页查询自主账号试用申请")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "condition", value = "查询条件、页码", paramType = "body")})
	@RequiresPermissions("sys:platform_mgnt_self_account:view")
	@RequestMapping(value = "pageTrialApply", method=RequestMethod.POST)
	@ResponseBody
	public JsonResultList<TrialApplyListVO> pageTrialApply(@RequestBody TrialApplyPageForm form) {
        Page<TrialApplyListVO> page = platformMgntService.pageTrialApply(form.getCondition());
        return JsonResultList.buildSuccess(page);
    }


	// added for v1.9.3 KNZT-4193
	@ApiOperation(value = "查询自主账号试用申请详情", httpMethod = "POST", notes = "查询自主账号试用申请详情")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "trialApplyId", value = "试用申请ID", paramType = "body")})
	@RequiresPermissions("sys:platform_mgnt_self_account:view")
	@RequestMapping(value = "getTrialApplyDetail", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<TrialApplyDetailVO> getTrialApplyDetail(@RequestBody TrialApplyOperateForm form) {
		TrialApplyDetailVO detail = platformMgntService.getTrialApplyDetail(form.getTrialApplyId());
		return JsonResult.buildSuccess(detail);
	}


	// added for v1.9.3 KNZT-4193
	@ApiOperation(value = "查询菜单计数", httpMethod = "POST", notes = "查询菜单计数")
	@RequestMapping(value = "getBriefCount", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<PlatformBriefCountTO> getBriefCount() {
		PlatformBriefCountTO detail = platformMgntService.getBriefCount();
		return JsonResult.buildSuccess(detail);
	}

	// added for v1.9.6 KNZT-4642
	@ApiOperation(value = "订单列表", httpMethod = "POST", notes = "订单列表")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "companyId", value = "客户公司ID", paramType = "body"),
			@ApiImplicitParam(name = "searchKey4Ol", value = "自助订单搜索", paramType = "body"),
			@ApiImplicitParam(name = "searchKey4Unit", value = "KA订单搜索", paramType = "body"),
			@ApiImplicitParam(name = "beginTimeBegin", value = "开始处理时间查询左边界", paramType = "body"),
			@ApiImplicitParam(name = "beginTimeEnd", value = "开始处理时间查询右边界", paramType = "body"),
			@ApiImplicitParam(name = "payType", value = "支付方式", paramType = "body"),
			@ApiImplicitParam(name = "payStatuses", value = "支付状态", paramType = "body"),
			@ApiImplicitParam(name = "rptStatuses", value = "订单状态", paramType = "body"),
			@ApiImplicitParam(name = "reportTypes", value = "报告类型", paramType = "body"),
			@ApiImplicitParam(name = "unitGroups", value = "地区", paramType = "body")})
	@RequestMapping(value = "/listReportOrder", method = RequestMethod.POST)
	@RequiresPermissions(value = {"sys:platform_mgnt_order:view"})
	@ResponseBody
	public JsonResultList<ReportOrder4ManagListTO> listReportOrder(@RequestBody ReportOrder4ManagCondition condition) {
		JsonResultList<ReportOrder4ManagListTO> result = new JsonResultList<>();
		Page<ReportOrder4ManagListTO> resultPage = commTblCompReportOrderService.listReportOrder4Management(condition);
		if (resultPage != null) {
			result.setResultList(resultPage.getList());
			result.setTotalCount(resultPage.getCount());
		}
		result.setStatus(Constants.Result.SUCCESS_STR);
		return result;
	}

	// added for v1.9.6 KNZT-4642
	@ApiOperation(value = "导出订单列表excel", httpMethod = "POST", notes = "导出订单列表excel")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "companyId", value = "客户公司ID", paramType = "body"),
			@ApiImplicitParam(name = "companyName", value = "客户公司名称", paramType = "body"),
			@ApiImplicitParam(name = "orderNo", value = "国际版或三方订单号", paramType = "body"),
			@ApiImplicitParam(name = "beginTimeBegin", value = "开始处理时间查询左边界", paramType = "body"),
			@ApiImplicitParam(name = "beginTimeEnd", value = "开始处理时间查询右边界", paramType = "body"),
			@ApiImplicitParam(name = "payType", value = "支付方式", paramType = "body"),
			@ApiImplicitParam(name = "payStatuses", value = "支付状态", paramType = "body"),
			@ApiImplicitParam(name = "rptStatuses", value = "订单状态", paramType = "body"),
			@ApiImplicitParam(name = "reportTypes", value = "报告类型", paramType = "body"),
			@ApiImplicitParam(name = "unitGroups", value = "地区", paramType = "body")})
	@RequestMapping(value = "/exportReportOrder", method = RequestMethod.POST)
	@RequiresPermissions(value = {"sys:platform_mgnt_order:view"})
	@ResponseBody
	public JsonResultList<String> exportExecutiveReportOrder(@RequestBody ReportOrder4ManagCondition condition, HttpServletResponse response) {
		JsonResultList<String> result = new JsonResultList<>();
		try {
			condition.removePage();
			Page<ReportOrder4ManagListTO> resultPage = commTblCompReportOrderService.listReportOrder4Management(condition);
			if (resultPage == null || CollectionUtils.isEmpty(resultPage.getList())) {
				result.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
				result.setMsg("导出失败，无数据");
				return result;
			}
			ReportOrder4ManagementExporter.build(condition.getPayType())
					.export(response, resultPage.getList());
			result.setStatus(Constants.Result.SUCCESS_STR);
		} catch (MessageException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			result.setMsg(I18NUtil.getMessage(e));
			logger.error("ExportExcel error ", e);
		} catch (IOException e) {
			result.setStatus(Constants.Result.FALSE_STR);
			logger.error("ExportExcel error ", e);
		}
		return result;
	}
	@ApiOperation(value = "重置secretKey", httpMethod = "POST", notes = "重置secretKey")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "resetSecretKey", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<Boolean> resetSecretKey(String id, String type) {
		try {
			platformMgntService.resetSecretKey(id, type);
			return JsonResult.buildSuccess(Boolean.TRUE);
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
	}

	@ApiOperation(value = "获取SecretKey", httpMethod = "POST", notes = "获取SecretKey")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "getSecretKey", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<CompApiKeyTO> getSecretKey(@RequestBody CompanyAdminForm form) {
		return JsonResult.buildSuccess(platformMgntService.getSecretKey(form.getId()));
	}

	// added for v2.0.0 chenbl KNZT-5094
	@ApiOperation(value = "保存香港查册人信息", httpMethod = "POST", notes = "保存香港查册人信息")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "saveHkSearcherInfo", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> saveHkSearcherInfo(@RequestBody HkSearcherInfoSaveForm form) {
		try {
			platformMgntService.saveHkSearcherInfo(form);
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
	}

	// added for v2.0.0 chenbl KNZT-5094
	@ApiOperation(value = "查看香港查册人信息", httpMethod = "POST", notes = "查看香港查册人信息")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "getHkSearcherInfo", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<HkSearcherInfo> getHkSearcherInfo(@RequestBody HkSearcherInfoSaveForm form) {
		try {
			MsgExceptionUtils.checkIsNull(form.getCompanyId());
			return JsonResult.buildSuccess(commSysCompanyService.getHkSearcherInfo(form.getCompanyId()));
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
	}


	// added for lvcy v2.0.3 KNZT-5404
	@ApiOperation(value = "生成代理查看订单参数", httpMethod = "POST", notes = "生成代理查看订单参数")
	@RequiresPermissions("sys:proxy-user-order:view")
	@RequestMapping(value = "generateProxyUserOrderParam", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<ProxyUserOrderParamTO> generateProxyUserOrderParam(@RequestBody OrderBaseForm form) {
		ProxyUserOrderParamTO res = platformMgntService.generateProxyUserOrderParam(form.getOrderId());
		return JsonResult.buildSuccess(res);
	}

	// added for v2.0.4 chenbl KNZT-5371
	@ApiOperation(value = "重置公司主账号MFA", httpMethod = "POST", notes = "重置公司主账号MFA")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "resetCompMainUserMfaSecret", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> resetCompMainUserMfaSecret(@RequestBody CompanyAdminForm form) {
		try {
			platformMgntService.resetCompMainUserMfaSecret(form.getId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
	}

	// added for v2.0.7 chenbl KNZT-5773
	@ApiOperation(value = "解锁公司主账号", httpMethod = "POST", notes = "解锁公司主账号")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "unlockCompMainUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> unlockCompMainUser(@RequestBody CompanyAdminForm form) {
		try {
			platformMgntService.unlockCompMainUser(form.getId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
	}

	@ApiOperation(value = "调整收入-获取订单概要信息", httpMethod = "POST", notes = "调整收入-获取订单概要信息")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "contract/correct-order/get", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<OrderInfo4CorrectTO> delivery(@RequestBody ContractCorrectForm form) {
		try {
			OrderInfo4CorrectTO orderInfo4Correct = transactionBusinessService.getOrderInfo4Correct(form.getOrderId());
			return JsonResult.buildSuccess(orderInfo4Correct);
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		}
    }

	@ApiOperation(value = "调整收入", httpMethod = "POST", notes = "调整收入")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "contract/correct-order", method=RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> correctMonthBillByOrder(@RequestBody ContractCorrectForm form) {
		try {
			transactionBusinessService.correctMonthBillByOrder(form.getOrderId(), form.getTrackingContent());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage());
		} catch (IOException e) {
			logger.error("correctMonthBillByOrder error", e);
			return JsonResult.buildFail("err.access");
		}
	}

	@ApiOperation(value = "获取订单流水字典", httpMethod = "POST", notes = "获取订单流水字典")
	@RequestMapping(value = "transaction-order-dict/get", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<TransactionOrderDictTO> getTransactionOrderDict() {
		return JsonResult.buildSuccess(transactionBusinessService.getTransactionOrderDict());
	}

	// added for v2.0.7 chenbl KNZT-6595
	@ApiOperation(value = "获取公司地址信息", httpMethod = "POST", notes = "获取公司地址信息")
	@RequiresPermissions(value = "sys:platform_mgnt:view")
	@RequestMapping(value = "getCompanyAddress", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<CompanyAddressTO> getCompanyAddress(@RequestBody CompanyAdminForm form) {
		return JsonResult.buildSuccess(commCompUserService.getCompanyAddress(form.getId()));
	}

	// added for v2.0.7 chenbl KNZT-6595
	@ApiOperation(value = "编辑公司地址信息", httpMethod = "POST", notes = "编辑公司地址信息")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "updateCompanyAddress", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> updateCompanyAddress(@RequestBody CompanyAddressUpdateForm form) {
		try {
			commCompUserService.updateCompanyAddress(form);
		} catch (MessageException e) {
			return JsonResult.buildFail(e.getMessage(), e.getArgs());
		}
		return JsonResult.buildSuccess();
	}

	// added for v2.1.8 chenbl KNZT-6899
	@ApiOperation(value = "切换主账号", httpMethod = "POST", notes = "切换主账号")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "companyId", value = "需要切换的公司ID", paramType = "body"),
			@ApiImplicitParam(name = "userId", value = "需要切换的子账号", paramType = "body")})
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "toggleMainUser", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> toggleMainUser(@RequestBody ToggleMainUserForm form) {
		try {
			commSysCompanyService.toggleMainUser(form.getCompanyId(), form.getUserId());
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error(e.getMessage(), e);
			return JsonResult.buildFail(e.getMessage(), e.getArgs());
		}
	}

	// added for v.2.2.7 fengsw KNZT-7785
	@ApiOperation(value = "获取用户充值信息", httpMethod = "POST", notes = "获取用户充值信息")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "getTopUpInfo", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<CompanyChargeTO> getTopUpInfo(@RequestBody CompanyAdminForm form) {
		return JsonResult.buildSuccess(commCompUserService.getTopUpInfo(form.getId()));
	}

	@ApiOperation(value = "重置充值限额", httpMethod = "POST", notes = "重置充值限额")
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "resetTopUpLimit", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> resetTopUpLimit(@RequestBody CompanyAdminForm form) {
		platformMgntService.resetTopUpLimit(form.getId(), form.getTopUpLimitAmount());
		return JsonResult.buildSuccess();
	}
}
