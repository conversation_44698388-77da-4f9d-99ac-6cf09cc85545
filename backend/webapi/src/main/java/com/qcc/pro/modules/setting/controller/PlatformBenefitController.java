package com.qcc.pro.modules.setting.controller;

import com.backend.common.modules.benefit.form.BenefitDeliveryForm;
import com.backend.common.modules.benefit.form.BenefitDeliveryQueryForm;
import com.backend.common.modules.benefit.model.BenefitDeliveryRecordTO;
import com.backend.common.modules.benefit.model.ContractBenefitWorkFlowDetailTO;
import com.backend.common.modules.benefit.service.BenefitBusinessService;
import com.backend.common.modules.benefit.service.CommTblBenefitDeliveryService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.service.MessageException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 平台管理-权益管理
 * <AUTHOR>
 * @datetime 10/6/2025 3:45 pm
 */
@Api(tags = "平台管理-权益管理")
@Controller
@RequestMapping(value = "${adminPath}/admin/benefit")
public class PlatformBenefitController {
    private static final Logger logger = LoggerFactory.getLogger(PlatformBenefitController.class);

    @Autowired
    private BenefitBusinessService benefitBusinessService;
    @Autowired
    private CommTblBenefitDeliveryService benefitDeliveryService;


    @ApiOperation(value = "获取合同权益工单详情", httpMethod = "POST", notes = "获取合同权益工单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workFlowNo", value = "工单编号", paramType = "body", required = true)
    })
    @RequestMapping(value = "/contract/benefit/workFlowDetail", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<ContractBenefitWorkFlowDetailTO> getContractBenefitWorkFlowDetailTO(@RequestBody BenefitDeliveryForm form) {
        try {
            return JsonResult.buildSuccess(benefitBusinessService.getContractBenefitWorkFlowDetailTO(form.getCompanyId(), form.getWorkFlowNo()));
        } catch (MessageException e) {
            return JsonResult.buildFail(e.getMessage());
        }
    }

    @ApiOperation(value = "权益交付", httpMethod = "POST", notes = "权益交付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workFlowNo", value = "工单编号", paramType = "body", required = true),
            @ApiImplicitParam(name = "companyId", value = "公司ID", paramType = "body", required = true),
            @ApiImplicitParam(name = "beginDate", value = "权益开始时间", paramType = "body", required = true)
    })
    @RequestMapping(value = "/delivery", method = RequestMethod.POST)
    @RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
    @ResponseBody
    public JsonResult<String> delivery(@RequestBody BenefitDeliveryForm form) {
        try {
            benefitBusinessService.delivery(form);
            return JsonResult.buildSuccess();
        } catch (MessageException e) {
            logger.warn("delivery message exception", e);
            return JsonResult.buildFail(e.getMessage());
        } catch (Exception e) {
            logger.error("delivery error", e);
            return JsonResult.buildFail("权益交付失败");
        }
    }


    @ApiOperation(value = "试用权益交付", httpMethod = "POST", notes = "权益交付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workFlowNo", value = "工单编号", paramType = "body", required = true),
            @ApiImplicitParam(name = "companyId", value = "公司ID", paramType = "body", required = true),
            @ApiImplicitParam(name = "beginDate", value = "权益开始时间", paramType = "body", required = true),
            @ApiImplicitParam(name = "endDate", value = "权益结束时间", paramType = "body", required = true),
            @ApiImplicitParam(name = "deliveryQuantity", value = "交付数量", paramType = "body", required = true),
            @ApiImplicitParam(name = "benefitType", value = "权益类型", paramType = "body", required = true)
    })
    @RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
    @RequestMapping(value = "/delivery-trial", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> deliveryForTrial(@RequestBody BenefitDeliveryForm form) {
        try {
            benefitBusinessService.deliveryForTrial(form);
            return JsonResult.buildSuccess();
        } catch (MessageException e) {
            logger.warn("delivery message exception", e);
            return JsonResult.buildFail(e.getMessage());
        } catch (Exception e) {
            logger.error("delivery error", e);
            return JsonResult.buildFail("权益交付失败");
        }
    }

    @ApiOperation(value = "查询权益交付记录", notes = "查询公司权益交付记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "公司ID", paramType = "query", required = true)
    })
    @RequiresPermissions("sys:platform_mgnt:view")
    @RequestMapping(value = "/delivery/record/query", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<List<BenefitDeliveryRecordTO>> listDeliveryRecords(@RequestBody BenefitDeliveryQueryForm form) {
        List<BenefitDeliveryRecordTO> records = benefitDeliveryService.listDeliveryRecordsByCompanyId(form.getCompanyId());
        return JsonResult.buildSuccess(records);
    }
} 