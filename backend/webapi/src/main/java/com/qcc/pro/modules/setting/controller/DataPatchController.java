package com.qcc.pro.modules.setting.controller;


import com.backend.common.modules.api.entity.TblCompApiOrderResp;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.form.TransactionPageForm;
import com.backend.common.modules.delivery.model.TransactionListV2TO;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.DeliveryBusinessService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.open.form.TrialApplyForm;
import com.backend.common.modules.setting.form.CompanyAdminForm;
import com.backend.common.modules.setting.service.DataPatchInterface;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.thread.ExecuteAfterSubmitOrderRunnable;
import com.backend.common.thread.TrialApplyRunnable;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.web.BaseController;
import com.qcc.frame.jee.modules.sys.excel.ExportExcel;
import io.swagger.annotations.Api;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Api(tags = "Data Patch")
@Controller
@RequestMapping(value = "${adminPath}/patch")
public class DataPatchController extends BaseController {
	@Autowired
	private DataPatchInterface dataPatchInterface;
	@Autowired
	private CommSysCompanyService commSysCompanyService;
	@Autowired
	private CommSysCompInfoFuncCountService funcCountService;
	@Autowired
	private TransactionBusinessService transactionBusinessService;

	// region ================================== 工具补偿接口 ==================================

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "tool/executeAfterSubmitOrderRunnable", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> executeAfterSubmitOrderRunnable(@RequestBody List<String> orderIdList) {
		for (String orderId : orderIdList) {
			new ExecuteAfterSubmitOrderRunnable(orderId).process();
		}
		return JsonResult.buildSuccess();
	}


	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "tool/runMonthlyBill", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> runMonthlyBill(@RequestBody List<String> contractNoList, @RequestParam(value = "targetYMString", required = false) String targetYMString) {
		dataPatchInterface.runMonthlyBill(contractNoList, targetYMString);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "tool/pushContractKZZ4ConfirmAmt", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> pushContractKZZ4ConfirmAmt(@RequestBody List<String> contractNoList, @RequestParam(value = "targetYMString", required = false) String targetYMString) {
		dataPatchInterface.pushConfirmAmountContract2KzzByManual(contractNoList, targetYMString);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "tool/compensateOrderTransaction", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> compensateOrderTransaction(@RequestBody List<String> orderIdList) {
		try {
			dataPatchInterface.compensateOrderTransaction(orderIdList);
		} catch (MessageException e) {
			logger.error("compensateOrderTransaction error", e);
			return JsonResult.buildFail(e);
		}
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "tool/updateFuncCountByValidProdAcc", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> updateFuncCountByValidProdAcc(@RequestBody List<String> companyIdList) {
		try {
			for (String companyId : companyIdList) {
				funcCountService.updateFuncCountByValidProdAcc(companyId);
			}
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error("compensateOrderTransaction error", e);
			return JsonResult.buildFail(e);
		}
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "tool/exportCompTransaction", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> exportCompTransaction(@RequestBody TransactionPageForm form,
													HttpServletResponse response) {
		try {
			form.getCondition().removePage();
			List<TransactionListV2TO> list = transactionBusinessService.pageTransaction4UserComp(form.getCondition());
			if (CollectionUtils.isEmpty(list)) {
				return JsonResult.buildFail("No data.");
			}
			new ExportExcel(null, TransactionListV2TO.class, true)
					.setDataList(list)
					.write(response, String.format("Purchase History_%s.xlsx", DateUtils.getDate(DateUtils.DATE_FORMAT_YMD)))
					.dispose();
			return JsonResult.buildSuccess();

		} catch (Exception e) {
			logger.error("ExportExcel error ", e);
			return JsonResult.buildFail(e.getMessage());
		}
	}

	// endregion ================================== 工具补偿接口 ==================================


	// region ================================== 刷数据接口 ==================================


	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "trialApplyRunnable", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> trialApplyRunnable(@RequestBody List<String> trialApplyIdList) {
		for (String trialApplyId : trialApplyIdList) {
			new TrialApplyRunnable(trialApplyId).process();
		}
		return JsonResult.buildSuccess();
	}


	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchEncryptUserId", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchEncryptUserId(@RequestBody List<String> userIdList) {
		try {
			dataPatchInterface.patchEncryptUserId(userIdList);
		} catch (MessageException e) {
			logger.error("patchEncryptUserId error", e);
			return JsonResult.buildFail(e);
		}
		return JsonResult.buildSuccess();
	}


	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchCompanySelfAccount", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchCompanySelfAccount(@RequestBody List<CompanyAdminForm> formList) {
		try {
			for (CompanyAdminForm form : formList) {
				dataPatchInterface.patchCompanySelfAccount(form.getCompanyName(), form.getLoginName(), form.getSubUserNum());
			}
			return JsonResult.buildSuccess();
		} catch (MessageException e) {
			logger.error("patchCompanySelfAccount error", e);
			return JsonResult.buildFail(e);
		}
	}

	// added for v2.0.8 chenbl KNZT-5830
	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "correctDelayedTaskKey", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> correctDelayedTaskKey() {
		dataPatchInterface.correctDelayedTaskKey();
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchSelfOrderTopUp", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchSelfOrderTopUp(@RequestBody(required = false) List<String> companyIdList) {
		dataPatchInterface.patchSelfOrderTopUp(companyIdList);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchSelfOrderTopUpByOrderId", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchSelfOrderTopUpByOrderId(@RequestBody List<String> orderIdList) {
		dataPatchInterface.patchSelfOrderTopUpByOrderId(orderIdList);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchTransactionCompanyRemainUnit", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchTransactionCompanyRemainUnit(@RequestBody(required = false) List<String> companyIdList) {
		dataPatchInterface.patchTransactionCompanyRemainUnit(companyIdList);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchUserTrialApply", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchUserTrialApply(@RequestBody List<TrialApplyForm> trialApplyFormList, @RequestParam(required = false, value="sendNotifyEmail") String sendNotifyEmail) {
		dataPatchInterface.patchUserTrialApply(trialApplyFormList, sendNotifyEmail);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchExternalApiData", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchExternalApiData(@RequestBody List<TblCompApiOrderResp> apiOrderList) {
		dataPatchInterface.patchExternalApiData4Customer(apiOrderList);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchExternalApiData/v2", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchExternalApiDataV2(@RequestBody List<String> apiOrderIdList) {
		dataPatchInterface.patchExternalApiData4CustomerV2(apiOrderIdList);
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchHkDoc", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchHkDoc() {
		dataPatchInterface.patchHkDoc();
		return JsonResult.buildSuccess();
	}

	@RequiresPermissions(value = {"sys:platform_mgnt:view", "sys:operate"}, logical = Logical.AND)
	@RequestMapping(value = "patchUiVersion", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<Void> patchUiVersion(@RequestBody(required = false) List<String> companyIdList) {
		dataPatchInterface.patchUiVersion(companyIdList);
		return JsonResult.buildSuccess();
	}

	// endregion ================================== 刷数据接口 ==================================


	@Autowired
	private DeliveryBusinessService deliveryBusinessService;
	@Autowired
	private CommTblContractDeliveryService contractDeliveryService;

	@RequestMapping(value = "delivery", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchExternalApiData(@RequestParam String id) throws MessageException {
		TblContractDelivery tblContractDelivery = contractDeliveryService.get(id);
		deliveryBusinessService.contractEffect(tblContractDelivery);
		return JsonResult.buildSuccess();
	}

	@RequestMapping(value = "monitorEventNotify", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult<String> patchMonitorEventNotify(@RequestBody List<String> userIdList, @RequestParam(value = "beginDate") String beginDateStr, @RequestParam(value = "endDate") String endDateStr) {
		Date beginDate = DateUtils.parseDate(beginDateStr, DateUtils.DATETIME_FORMAT);
		Date endDate = DateUtils.parseDate(endDateStr, DateUtils.DATETIME_FORMAT);
		dataPatchInterface.patchMonitorEventNotify(userIdList, beginDate, endDate);
		return JsonResult.buildSuccess();
	}



}
