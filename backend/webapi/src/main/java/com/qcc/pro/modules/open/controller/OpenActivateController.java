package com.qcc.pro.modules.open.controller;

import com.backend.common.modules.util.OpLogUtils;
import com.backend.common.service.CommCompUserService;
import com.qcc.frame.commons.ienum.OpIpLogTypeEnum;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.service.AttackLockedException;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.form.UserActivateForm;
import com.qcc.frame.jee.modules.sys.model.UserProfileTO;
import com.qcc.frame.jee.modules.sys.model.saas.network_attack.SearchCorpAttackCountTO;
import com.qcc.frame.jee.modules.sys.model.saas.network_attack.base.WarnOrMaxCountTO;
import com.qcc.frame.jee.modules.sys.service.NetworkAttackServUtils;
import com.qcc.frame.jee.modules.sys.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

// added for v2.0.4 chenbl KNZT-5442
@Api(tags = "用户激活")
@RestController
@RequestMapping(value = "/open/activate/")
public class OpenActivateController {
    @Autowired
    private UserService userService;
    @Autowired
    private CommCompUserService commUserService;
    @Autowired
    private NetworkAttackServUtils networkAttackServUtils;
    @Autowired
    private CommCompUserService commCompUserService;

    @ApiOperation(value = "用户申请激活", httpMethod = "POST", notes = "用户申请激活")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", paramType = "body"),
            @ApiImplicitParam(name = "newPassword", value = "修改密码", paramType = "body")})
    @ResponseBody
    @RequestMapping(value = "activateUser")
    public JsonResult<String> activateUser(@RequestBody UserActivateForm form, HttpServletRequest request) {
        String userId = null;
        try {
            WarnOrMaxCountTO limitCount = NetworkAttackServUtils.getWarnOrMaxCountByFunctionCd("activate_user", 10, 50);
            networkAttackServUtils.addAttackCountWithLockByClientIp("activate_user", StringUtils.getRemoteAddr(request), 1, new SearchCorpAttackCountTO(), limitCount, NetworkAttackServUtils.TIMEOUT_1_HOUR, null);
            userId = UserService.getAndValidUserIdToken(form.getUserId());
            MsgExceptionUtils.checkIsNull(userId, "err.activation.failed");
            form.setUserId(userId);
            form.setNewPassword(userService.decryptRSAForFront(form.getNewPassword()));
            boolean ifSuccess = commUserService.activateUser(form);
            if (ifSuccess) {
                OpLogUtils.saveOpLogByUserId(userId, OpIpLogTypeEnum.ACTIVATE_ACCOUNT);
            }
            return JsonResult.buildSuccess(String.valueOf(ifSuccess));
        } catch (MessageException e) {
            JsonResult<String> jsonResult = new JsonResult<>();
            OpLogUtils.saveOpLogByUserIdAndMessageException(userId, OpIpLogTypeEnum.ACTIVATE_FAILED, e);
            boolean set = UserService.setResult4checkPwd4Security(e, jsonResult);
            if (set) {
                return jsonResult;
            }
            return JsonResult.buildFail(e.getMessage());
        } catch (AttackLockedException e) {
            return JsonResult.buildFail("locked");
        }
    }

    @ApiOperation(value = "获取个人信息", httpMethod = "POST", notes = "获取个人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", paramType = "body")})
    @ResponseBody
    @RequestMapping(value = "getUserProfile")
    public JsonResult<UserProfileTO> getUserProfile(@RequestBody UserActivateForm form, HttpServletRequest request) {
        String userId = null;
        try {
            WarnOrMaxCountTO limitCount = NetworkAttackServUtils.getWarnOrMaxCountByFunctionCd("activate_user", 10, 50);
            networkAttackServUtils.addAttackCountWithLockByClientIp("activate_user", StringUtils.getRemoteAddr(request), 1, new SearchCorpAttackCountTO(), limitCount, NetworkAttackServUtils.TIMEOUT_1_HOUR, null);
            userId = UserService.getAndValidUserIdToken(form.getUserId());
            MsgExceptionUtils.checkIsNull(userId);
            return JsonResult.buildSuccess(commCompUserService.getUserProfile(userId));
        } catch (AttackLockedException e) {
            return JsonResult.buildFail("locked");
        } catch (Exception e) {
            return JsonResult.buildFail("err.access");
        }
    }
}
